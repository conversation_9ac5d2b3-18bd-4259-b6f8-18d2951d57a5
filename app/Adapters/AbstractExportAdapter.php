<?php

namespace App\Adapters;

use App\Enums\ExportType;
use App\Interfaces\ReportExportable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Query\Builder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromGenerator;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromView;

abstract class AbstractExportAdapter implements ReportExportable
{
    protected string $outputLocalFilePath;
    protected string $paperOrientation;

    protected Builder|\Illuminate\Database\Eloquent\Builder $query;
    protected FromCollection|FromView|FromQuery|FromGenerator $reportBuilder;
    protected array $reportData;
    protected string $reportViewName;

    public abstract function generate();

    public function setOutputLocalFilePath(string $outputLocalFilePath): ReportExportable
    {
        $this->outputLocalFilePath = $outputLocalFilePath;
        return $this;
    }

    public function setPaperOrientation(string $paperOrientation): ReportExportable
    {
        $this->paperOrientation = $paperOrientation;
        return $this;
    }

    public function setQuery(\Illuminate\Database\Eloquent\Builder|Builder $query): ReportExportable
    {
        $this->query = $query;
        return $this;
    }

    public function setReportBuilder(FromCollection|FromView|FromQuery|FromGenerator $reportBuilder): ReportExportable
    {
        $this->reportBuilder = $reportBuilder;
        return $this;
    }


    public function setReportData(array $reportData): ReportExportable
    {
        $this->reportData = $reportData;
        return $this;
    }

    public function setReportViewName(string $reportViewName): ReportExportable
    {
        $this->reportViewName = $reportViewName;
        return $this;
    }


}
