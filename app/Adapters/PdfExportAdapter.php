<?php

namespace App\Adapters;

use Barryvdh\Snappy\Facades\SnappyPdf;
use Illuminate\Support\Facades\Storage;

class PdfExportAdapter extends AbstractExportAdapter
{
    public function generate()
    {
        $absolute_path = Storage::disk('local')->path($this->outputLocalFilePath);

        $pdf = SnappyPdf::setPaper('a4')
            ->setOrientation($this->paperOrientation)
            ->loadView($this->reportViewName, ['query' => $this->query ?? null], $this->reportData ?? [], 'UTF-8');

        if (isset($this->options)) {
            foreach ($this->options as $option => $value) {
                $pdf->setOption($option, $value);
            }
        }

        $pdf->save($absolute_path, true);

        return $this;
    }

    public function getFileNameWithExtension($file_name): string
    {

        if (!preg_match("/\.pdf$/i", $file_name)) {
            $file_name .= '.pdf';
        }

        return $file_name;

    }

}
