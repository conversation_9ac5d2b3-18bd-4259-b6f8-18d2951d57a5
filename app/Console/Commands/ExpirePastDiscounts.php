<?php

namespace App\Console\Commands;

use App\Services\Billing\DiscountSettingService;
use Illuminate\Console\Command;

class ExpirePastDiscounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discounts:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire all discounts that have past the effective date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Running discount expiry script..");

        $service = app()->make(DiscountSettingService::class);
        $service->expirePastDiscounts();

        $this->info("End discount expiry script..");
    }
}
