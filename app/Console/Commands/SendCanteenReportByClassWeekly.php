<?php

namespace App\Console\Commands;

use App\Enums\ExportType;
use App\Exceptions\RepositoryException;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\ConfigHelper;
use App\Helpers\FileHelper;
use App\Mail\CanteenReportByClassWeeklyMail;
use App\Models\Config;
use App\Repositories\SemesterClassRepository;
use App\Services\DocumentPrintService;
use App\Services\Report\EcommerceCanteenReportService;
use App\Services\ReportPrintService;
use App\Services\SchoolProfileService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendCanteenReportByClassWeekly extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'canteen-report-by-class-weekly:send {--start-date=} {--end-date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send canteen report by class weekly';

    public function __construct(
        protected SemesterClassRepository $semesterClassRepository,
        protected EcommerceCanteenReportService $ecommerceCanteenReportService,
        protected SchoolProfileService $schoolProfileService,
        protected ReportPrintService $reportPrintService,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     * @throws RepositoryException
     */
    public function handle()
    {
        $notification_date = now()->addWeek()->startOfWeek();

        $report_start_date = $this->option('start-date') ?: $notification_date->toDateString();
        $report_end_date = $this->option('end-date') ?: $notification_date->endOfWeek()->toDateString();

        $filters = [
            'is_active' => true,
            'includes' => [
                'classModel'
            ]
        ];

        $query = $this->semesterClassRepository->getQuery($filters)
            ->whereNotNull('homeroom_teacher_id');

        $query->lazyById()->each(function ($semester_class) use ($report_start_date, $report_end_date) {
            try {
                $homeroom_teacher_email = Arr::get($semester_class, 'homeroomTeacher.user.email');

                if ($homeroom_teacher_email) {
                    $filters = [
                        'start_product_delivery_date' => $report_start_date,
                        'end_product_delivery_date' => $report_end_date,
                        'semester_class_ids' => [$semester_class->id]
                    ];

                    $class_name = $semester_class->classModel->name;
                    $this->generateReportAndSendEmail($filters, $class_name, $homeroom_teacher_email, $report_start_date, $report_end_date);
                    $this->info("Sending report to: {$homeroom_teacher_email} ({$class_name}) {$report_start_date} - {$report_end_date}");
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        });

        $notification_email = ConfigHelper::get(Config::CANTEEN_NOTIFICATION_EMAIL);

        if (!$notification_email) {
            return;
        }

        $filters = [
            'start_product_delivery_date' => $report_start_date,
            'end_product_delivery_date' => $report_end_date,
            'semester_class_ids' => $this->semesterClassRepository->getQuery($filters)->pluck('id')->toArray()
        ];

        $this->generateReportAndSendEmail($filters, 'All', $notification_email, $report_start_date, $report_end_date);
        $this->info("Sending report to: {$notification_email} (All) {$report_start_date} - {$report_end_date}");
    }

    protected function generateReportAndSendEmail($filters, $class_name, $email, $report_start_date, $report_end_date): void
    {
        $email = preg_replace('/\s+/', '', $email);
        $email_list = explode(',', $email);
        $data = $this->ecommerceCanteenReportService->getReportByClassWeekly($filters);

        if (empty($data['classes'])) {
            Mail::to($email_list)->send(new CanteenReportByClassWeeklyMail(null, $class_name, $report_start_date, $report_end_date));
            return;
        }

        $start_date = Carbon::parse($filters['start_product_delivery_date']);
        $end_date = Carbon::parse($filters['end_product_delivery_date']);

        $report_data = [
            'data' => $data,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'profile' => $this->schoolProfileService->getSchoolProfile()
        ];

        $report_view_name = 'reports.ecommerce.canteens.by-classes-weekly';
        $file_name = 'ecommerce-canteen-report-by-classes-weekly';

        $export_type = ExportType::PDF;
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        Mail::to($email_list)->send(new CanteenReportByClassWeeklyMail($url, $class_name, $report_start_date, $report_end_date));
    }
}
