<?php

namespace App\Console\Commands\Migrations;

use App\Enums\BookBinding;
use App\Enums\BookCondition;
use App\Enums\BookLoanSettingType;
use App\Enums\BookStatus;
use App\Enums\CardStatus;
use App\Enums\CardType;
use App\Enums\ClassStream;
use App\Enums\ClassSubjectTeacherType;
use App\Enums\ClassType;
use App\Enums\CompetitionBonusType;
use App\Enums\ConductRecordStatus;
use App\Enums\ContractorDepartment;
use App\Enums\ContractorStatus;
use App\Enums\Day;
use App\Enums\DietaryRestriction;
use App\Enums\EducationLevel;
use App\Enums\EmployeeStatus;
use App\Enums\EnglishLevel;
use App\Enums\Gender;
use App\Enums\GradingSchemeType;
use App\Enums\GuardianType;
use App\Enums\HostelBlockType;
use App\Enums\HostelMeritDemeritType;
use App\Enums\HostelRoomBedStatus;
use App\Enums\JobType;
use App\Enums\LeaveApplicationStatus;
use App\Enums\LibraryBookLoanPaymentStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Enums\MarriedStatus;
use App\Enums\MeritDemeritType;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\ProductCategory;
use App\Enums\RewardPunishmentRecordStatus;
use App\Enums\SchoolLevel;
use App\Enums\SubjectType;
use App\Enums\TimeslotTeacherType;
use App\Enums\UserSpecialSettingModule;
use App\Enums\UserSpecialSettingSubModule;
use App\Helpers\SystemHelper;
use App\Models\AttendanceInput;
use App\Models\Author;
use App\Models\Award;
use App\Models\Book;
use App\Models\BookCategory;
use App\Models\BookClassification;
use App\Models\BookLoanSetting;
use App\Models\BookSource;
use App\Models\BookSubClassification;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectContractor;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Club;
use App\Models\ClubCategory;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\ComprehensiveAssessmentCategory;
use App\Models\ComprehensiveAssessmentQuestion;
use App\Models\ConductRecord;
use App\Models\ConductSetting;
use App\Models\ConductSettingTeacher;
use App\Models\Contractor;
use App\Models\Country;
use App\Models\Course;
use App\Models\Department;
use App\Models\Education;
use App\Models\Employee;
use App\Models\EmployeeCategory;
use App\Models\EmployeeJobTitle;
use App\Models\EmployeeSession;
use App\Models\EmployeeSessionSetting;
use App\Models\GlAccount;
use App\Models\Grade;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\HealthConcern;
use App\Models\HostelBedAssignment;
use App\Models\HostelBlock;
use App\Models\HostelMeritDemeritSetting;
use App\Models\HostelRewardPunishmentRecord;
use App\Models\HostelRewardPunishmentSetting;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use App\Models\LeadershipPosition;
use App\Models\LeadershipPositionRecord;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\MeritDemeritRewardPunishment;
use App\Models\MeritDemeritSetting;
use App\Models\MigrationMapping;
use App\Models\PaymentMethod;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\Product;
use App\Models\Race;
use App\Models\Religion;
use App\Models\ResultSourceSubject;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentCategory;
use App\Models\RewardPunishmentRecord;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\School;
use App\Models\SchoolProfile;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\SocietyPosition;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentSocietyPosition;
use App\Models\Subject;
use App\Models\SubstituteRecord;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Models\UnpaidItem;
use App\Models\Uom;
use App\Models\User;
use App\Models\UserSpecialSetting;
use App\Models\WithdrawalReason;
use App\Services\HostelSavingsAccountService;
use App\Services\Timetable\TimetableService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Propaganistas\LaravelPhone\PhoneNumber;

class MigrateDataFromSMSV1 extends Command
{
    protected $isActual = false;
    protected $isTruncate = false;

    // CHANGE THESE 2 VALUES TO THE actual latest Year and Semester during migration
    protected $year = '2025';
    protected $semester = '1';
    protected $migrationStartYear = '2017';
    protected $v1DbConnection;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'v1:migrate {--step=0} {--actual} {--truncate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate data from v1';

    public function __construct(protected TimetableService $timetableService)
    {
        $this->v1DbConnection = DB::connection('smsv1');
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;
        $this->isTruncate = (bool) $this->option('truncate') ?? false;
        $step = $this->option('step');

        if ($step !== 0) {
            $this->$step();
        }

        // $this->migrateRace();
        // $this->migrateReligion();
        //$this->migrateGradeMaster();
        //$this->migrateStudents();
    }

    public function migrateRace()
    {

        $this->info("Migration for master_races");

        $from_data = $this->v1DbConnection->table('lookup_m')
            ->where('lookup_type', 'Race')
            ->where('lookup_active', 1)
            ->orderBy('lookup_urutan', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {

            $to_insert[] = [
                'id' => $d->lookup_value,
                'name' => json_encode(['en' => $d->lookup_name, 'zh' => strlen($d->lookup_cname) > 0 ? $d->lookup_cname : $d->lookup_name]),
                'sequence' => $order_counter--,
                'created_at' => now(),
            ];

        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                Race::insert($to_insert);

                DB::statement('ALTER SEQUENCE master_races_id_seq RESTART WITH ' . Race::count() + 1);

                // insert unknown race
                Race::create([
                    'name' => ['en' => 'UNKNOWN', 'zh' => "未知"],
                    'sequence' => 0,
                ]);

            });

            $this->info("Number of rows after insert: " . Race::count());
        }

    }


    public function migrateReligion()
    {

        $this->info("Migration for master_religions");

        $from_data = $this->v1DbConnection->table('lookup_m')
            ->where('lookup_type', 'Religion')
            ->where('lookup_active', 1)
            ->orderBy('lookup_urutan', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];
        $inserted_ids = [];     // religion data in v1 got duplicated ID problem

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {

            $inserted_ids[$d->lookup_value] = 1;

            $to_insert[] = [
                'id' => isset($inserted_ids[$d->lookup_value]) ? count($to_insert) + 1 : $d->lookup_value,
                'name' => json_encode(['en' => $d->lookup_name, 'zh' => strlen($d->lookup_cname) > 0 ? $d->lookup_cname : $d->lookup_name]),
                'sequence' => $order_counter--,
                'created_at' => now(),
            ];

        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                Religion::insert($to_insert);
                DB::statement('ALTER SEQUENCE master_religions_id_seq RESTART WITH ' . Religion::count() + 1);

                // insert unknown Religion
                Religion::create([
                    'name' => ['en' => 'UNKNOWN', 'zh' => "未知"],
                    'sequence' => 0,
                ]);

            });

            $this->info("Number of rows after insert: " . Religion::count());
        }

    }

    public function migrateEducation()
    {

        $this->info("Migration for master_educations");

        $from_data = $this->v1DbConnection->table('lookup_m')
            ->where('lookup_type', 'Education')
            ->where('lookup_active', 1)
            ->orderByRaw('CAST(lookup_value AS unsigned)')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->lookup_value,
                'name' => json_encode(['en' => trim($d->lookup_name), 'zh' => strlen($d->lookup_cname) > 0 ? trim($d->lookup_cname) : trim($d->lookup_name)]),
                'sequence' => $order_counter--,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                Education::insert($to_insert);
                $this->restartId(Education::class);
            });

            $this->info("Number of rows after insert: " . Education::count());
        }

    }

    public function migrateGradeMaster()
    {

        $this->info("Migration for master_grades");

        $from_data = $this->v1DbConnection->table('grade_form_m')->orderBy('sort_order', 'ASC')->get();
        $order_counter = count($from_data);
        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {

            $to_insert[] = [
                'id' => $d->grade_id,
                'name' => json_encode(['en' => $d->grade_english_name, 'zh' => $d->grade_chinese_name]),
                'sequence' => $order_counter--,
                'created_at' => now(),
            ];

        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                Grade::insert($to_insert);
                DB::statement('ALTER SEQUENCE master_grades_id_seq RESTART WITH ' . Grade::count() + 1);
            });

            $this->info("Number of rows after insert: " . Grade::count());
        }

    }

    public function migrateHealthConcerns()
    {
        $this->info("Migration for master_health_concerns");

        $from_data = $this->v1DbConnection->table('student_healthcard')
            ->select('disease')
            ->distinct()
            ->whereNotIn('disease', ['无', '-'])
            ->orderByRaw('CONVERT(disease USING GBK) ASC')  // ensure chinese strings are sorted by pinyin
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $to_insert[] = [
            'name' => json_encode(['en' => 'None', 'zh' => '无']),
            'sequence' => $order_counter + 1,
            'created_at' => now(),
        ];

        foreach ($from_data as $d) {
            $to_insert[] = [
                'name' => json_encode(['en' => trim($d->disease), 'zh' => trim($d->disease)]),
                'sequence' => $order_counter--,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                HealthConcern::insert($to_insert);
                DB::statement('ALTER SEQUENCE master_health_concerns_id_seq RESTART WITH ' . HealthConcern::count() + 1);
            });

            $this->info("Number of rows after insert: " . HealthConcern::count());
        }
    }

    public function migrateStudents()
    {

        $this->info("Migration for students");

        $countries = Country::all()->mapWithKeys(function ($item) {
            return [$item->getTranslation('name', 'zh') => $item];
        });

        $health_concerns = HealthConcern::all()->mapWithKeys(function ($item) {
            return [$item->getTranslation('name', 'zh') => $item];
        });

        $default_health_concern = $health_concerns['无'];

        $select_raws = [
            'student_m.student_id AS id',
            'student_m.*',
            'grade_form_m.grade_english_name AS grade_name',
            'class_m.class_code AS class_name',
            'city_m.city_name AS student_birthplace_name',
            'nationality_lookup.lookup_name AS student_nationality_name',
            'student_healthcard.disease AS disease',
        ];

        $from_data = $this->v1DbConnection->table('student_m')
            ->selectRaw(implode(',', $select_raws))
            ->join('student_class', function ($join) {
                $join->on('student_class.student_id', '=', 'student_m.student_id')
                    ->where('student_class.year', '=', $this->year)
                    ->where('student_class.semester', '=', $this->semester)
                    ->whereNull('student_class.date_leave');
            })
            ->join('class_m', 'class_m.class_id', '=', 'student_class.class_id')
            ->join('grade_form_m', 'grade_form_m.grade_id', '=', 'student_class.grade_id')
            ->leftJoin('city_m', 'city_m.city_id', '=', 'student_m.student_birthplace')
            ->leftJoin(DB::raw('lookup_m nationality_lookup'), function ($join) {
                $join->on('nationality_lookup.lookup_value', '=', 'student_m.student_nationality')
                    ->where('nationality_lookup.lookup_type', '=', 'Nationality')
                    ->where('nationality_lookup.lookup_active', 1);
            })
            ->leftJoin('student_healthcard', 'student_healthcard.student_id', '=', 'student_m.student_id');

        $student_from_count = $from_data->clone()->count();
        $this->info("Number of rows: " . $student_from_count);

        $unknown_race = Race::where(DB::raw('name->>\'en\''), 'UNKNOWN')->firstOrFail();
        $unknown_religion = Religion::where(DB::raw('name->>\'en\''), 'UNKNOWN')->firstOrFail();

        $student_count = Student::count();
        $user_count = User::count();
        $from_data
            ->lazyById(100, 'student_m.student_id', 'id')
            ->each(function ($d) use (&$countries, &$unknown_race, &$unknown_religion, &$student_from_count, $health_concerns, $default_health_concern, &$student_count, &$user_count) {
                $ic_passport = $this->_determineIcPassport($d);
                $address_components = $this->_determineAddress($d->student_address);
                $password = $ic_passport['nric'] !== null ? Hash::make($ic_passport['nric']) : Hash::make($ic_passport['passport_number']);

                $dataset = [
                    'photo' => [
                        'v1' => $d->student_photo,
                        'new' => null
                    ],
                    'student' => [
                        'user_id' => null,
                        'name' => ['en' => trim($d->student_name), 'zh' => trim($d->student_cname)],
                        'email' => $this->_cleanEmail($d->student_email),
                        'phone_number' => $this->_cleanPhoneNumber($d->student_handphone),
                        'phone_number_2' => $this->_cleanPhoneNumber($d->student_phone),
                        'nric' => $ic_passport['nric'],
                        'passport_number' => $ic_passport['passport_number'],
                        'admission_year' => $d->register_year,
                        'admission_grade_id' => $d->student_admission_grade ?? 1,
                        'join_date' => $d->joined_date ?? $d->register_date,        // some rows got empty joined_date, we fallback to register_date
                        'student_number' => $d->student_no,
                        'birthplace' => $d->student_birthplace_name,
                        'nationality_id' => $countries[$d->student_nationality_name]->id ?? 1,      // default to ID 1 = unknown if not found
                        'date_of_birth' => $d->student_dob,
                        'gender' => $this->_determineGender($d->student_sex),
                        'birth_cert_number' => !empty($d->student_birthcertificate) ? $d->student_birthcertificate : null,
                        'race_id' => !empty($d->student_race) ? $d->student_race : $unknown_race->id,
                        'religion_id' => !empty($d->student_religion) ? $d->student_religion : $unknown_religion->id,
                        'address' => $d->student_address,
                        'address_2' => $d->student_address2,
                        'city' => $address_components['city'],
                        'postal_code' => $address_components['postcode'],
                        'state_id' => $address_components['state'],
                        'country_id' => $address_components['country'],
                        'is_hostel' => $d->student_boarding,
                        'is_active' => $d->student_active,
                        'primary_school_id' => $d->primary_school,
                        'health_concern_id' => $d->disease !== null ? ($health_concerns[$d->disease]->id ?? $default_health_concern->id) : $default_health_concern->id,
                        'dietary_restriction' => $this->_determineStudentFood($d->food)
                    ],
                    'user' => [
                        'email' => $this->_cleanEmail($d->student_email),
                        'phone_number' => null,     // students only use email to login
                        'is_active' => $d->student_active,
                        'password' => $password,
                        'is_password_reset_required' => true,
                    ]
                ];

                $this->info("Processed for " . $d->student_id . ' - ' . $d->student_name . ' / ' . $d->student_cname);

                if ($this->isActual) {
                    $this->info("== ACTUAL ==");

                    $new_student = null;

                    DB::transaction(function () use (&$dataset, &$new_student, &$d) {

                        // does user exists with same phone number
                        if ($dataset['user']['phone_number'] !== null) {
                            $count = User::where('phone_number', 'LIKE', $dataset['user']['phone_number'] . '%')->count();

                            if ($count > 0) {
                                $suffix = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
                                $dataset['user']['phone_number'] = $dataset['user']['phone_number'] . $suffix[$count - 1];
                            }
                        }

                        $user = User::create($dataset['user']);
                        $dataset['student']['user_id'] = $user->id;

                        $new_student = Student::create($dataset['student']);

                        // add into migration mapping
                        MigrationMapping::updateOrCreate([
                            'model' => Student::class,
                            'old' => $d->student_id,
                            'new' => $new_student->id,
                        ]);

                        $this->info("Created student {$new_student->student_number} with User ID {$new_student->user_id}");
                    });

                    $student_count++;
                    $user_count++;

                    $this->info("Number of rows (Students) after insert: " . $student_count . '/' . $student_from_count);
                    $this->info("Number of rows (Users) after insert: " . $user_count);

                    // download student photo
                    $new_file_name = md5($dataset['student']['student_number']) . '.jpg';
                    $this->_downloadStudentPhoto($dataset['photo']['v1'], $new_file_name, $new_student);
                }
            });

    }

    public function _downloadStudentPhoto($file_name, $new_file_name, &$new_student)
    {

        // download student photo
        $v1_photo_path = '/var/www/pinhwa/images/data/student/' . $file_name;

        if ($file_name && Storage::disk('sftp-smsv1')->exists($v1_photo_path)) {
            $file_data = Storage::disk('sftp-smsv1')->get($v1_photo_path);

            if (Storage::disk('local')->put('migration/photos/' . $new_file_name, $file_data)) {
                $new_student->replaceMedia('photo', storage_path('app/migration/photos/' . $new_file_name));
            }

            $this->info("Updated student with photo " . $new_file_name);

        } else {
            $this->warn("Photo not found for student at " . $v1_photo_path);
        }

    }

    public function _downloadEmployeePhoto($file_name, $new_file_name, &$new_employee)
    {

        // download student photo
        $v1_photo_path = '/var/www/pinhwa/images/data/employee/' . $file_name;

        if ($file_name && Storage::disk('sftp-smsv1')->exists($v1_photo_path)) {

            $file_data = Storage::disk('sftp-smsv1')->get($v1_photo_path);

            if (Storage::disk('local')->put('migration/photos/' . $new_file_name, $file_data)) {
                Storage::disk('local')->put('migration/photos/' . $new_file_name, $file_data);

                $new_employee->replaceMedia('photo', storage_path('app/migration/photos/' . $new_file_name));
            }

            $this->info("Updated employee with photo " . $new_file_name);

        } else {
            $this->warn("Photo not found for employee at " . $v1_photo_path);
        }

    }

    public function migrateGuardians()
    {

        $this->info("Migration for guardians");

        $countries = Country::all()->mapWithKeys(function ($item) {
            return [$item->getTranslation('name', 'zh') => $item];
        });
        $students = Student::all()->mapWithKeys(function ($item) {
            return [$item->student_number => $item];
        });

        $from_data = $this->v1DbConnection->table('student_m')
            ->selectRaw('student_m.student_id AS id, student_m.*, father_nationality_lookup.lookup_name AS father_nationality_name,
                mother_nationality_lookup.lookup_name AS mother_nationality_name, guardian_nationality_lookup.lookup_name AS guardian_nationality_name,
                guardian_relation_lookup.lookup_name AS guardian_relation_name')
            ->join('student_class', function ($join) {
                $join->on('student_class.student_id', '=', 'student_m.student_id')
                    ->where('student_class.year', '=', $this->year)
                    ->where('student_class.semester', '=', $this->semester)
                    ->whereNull('student_class.date_leave');
            })
            ->leftJoin(DB::raw('lookup_m father_nationality_lookup'), function ($join) {
                $join->on('father_nationality_lookup.lookup_value', '=', 'student_m.father_nationality')
                    ->where('father_nationality_lookup.lookup_type', '=', 'Nationality')
                    ->where('father_nationality_lookup.lookup_active', 1);
            })
            ->leftJoin(DB::raw('lookup_m mother_nationality_lookup'), function ($join) {
                $join->on('mother_nationality_lookup.lookup_value', '=', 'student_m.mother_nationality')
                    ->where('mother_nationality_lookup.lookup_type', '=', 'Nationality')
                    ->where('mother_nationality_lookup.lookup_active', 1);
            })
            ->leftJoin(DB::raw('lookup_m guardian_nationality_lookup'), function ($join) {
                $join->on('guardian_nationality_lookup.lookup_value', '=', 'student_m.guardian_nationality')
                    ->where('guardian_nationality_lookup.lookup_type', '=', 'Nationality')
                    ->where('guardian_nationality_lookup.lookup_active', 1);
            })
            ->leftJoin(DB::raw('lookup_m guardian_relation_lookup'), function ($join) {
                $join->on('guardian_relation_lookup.lookup_value', '=', 'student_m.guardian_relation')
                    ->where('guardian_relation_lookup.lookup_type', '=', 'Relation')
                    ->where('guardian_relation_lookup.lookup_active', 1);
            });

        $from_data
            ->lazyById(100, 'student_m.student_id', 'id')
            ->each(function ($d) use (&$countries, &$students) {
                $this->_processGuardianMigration($d, $countries, $students);
            });
    }

    //library
    public function migrateBookSources()
    {
        $this->info("Migration for master_book_sources");

        $from_data = $this->v1DbConnection->table('book_source')
            ->orderBy('edesc', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->source_id,
                'name' => json_encode(['en' => $d->edesc, 'zh' => $d->cdesc]),
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                BookSource::insert($to_insert);
                $this->restartId(BookSource::class);
            });

            $this->info("Number of rows after insert: " . BookSource::count());
        }
    }

    public function migrateAuthors()
    {
        $this->info("Migration for master_authors");

        $author_1 = $this->_determineAuthorsFromBook('author_1');
        $author_2 = $this->_determineAuthorsFromBook('author_2');
        $author_3 = $this->_determineAuthorsFromBook('author_3');

        $authors = array_unique([...$author_1, ...$author_2, ...$author_3]);

        $authors = array_map('trim', $authors);

        $order_counter = count($authors);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($authors as $d) {
            $d = trim($d);

            if (!$d) {
                continue;
            }

            //remove all no meaning authors name
            if (in_array($d, ['-', '.', '——', '——', '、', '-0'])) {
                continue;
            }

            $to_insert[] = [
                'name' => $d,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (collect($to_insert)->chunk(10000) as $chunk) {
                    Author::insert($chunk->toArray());
                }
            });

            $this->info("Number of rows after insert: " . Author::count());
        }
    }

    public function migrateBooks()
    {
        $this->info("Migration for books");

        $from_data = $this->v1DbConnection->table('book_m')
            ->select([
                'book_m.*',
                DB::raw('book_m.book_id as id'),
                DB::raw('book_trans.book_condition as book_condition'),
                DB::raw('book_trans.book_periode_e as book_periode_e'),
                DB::raw('book_trans.book_periode_s as book_periode_s'),
                DB::raw('book_trans.book_periode_s as book_periode_l'),
                DB::raw('book_trans.book_periode_s as book_periode_o'),
            ])
            ->join('book_trans', 'book_trans.book_id', '=', 'book_m.book_id')
            ->orderBy('book_m.book_id', 'ASC');

        $from_data_count = $from_data->clone()->count();

        $this->info("Number of rows: " . $from_data_count);

        $sub_classification_ids = BookSubClassification::query()->pluck('book_classification_id', 'id')->toArray();
        $sub_classification_others = BookSubClassification::query()->where('name->en', 'OTHERS')->first();

        $bar = $this->output->createProgressBar($from_data_count);

        $chunk_size = 1000;

        if ($this->isActual) {
            $this->info("== ACTUAL ==");
            DB::beginTransaction();
        }

        $from_data->chunk($chunk_size, function ($data) use ($sub_classification_ids, $sub_classification_others, &$book_count, $bar, $chunk_size) {
            $to_insert = [];
            $book_loan_setting_data = [];

            foreach ($data as $d) {
                $purchase_price = $d->purchase_price;
                $lost_penalty_value = $d->selling_value;

                if ($purchase_price > 10000000) {
                    $purchase_price = 0;
                }

                if ($lost_penalty_value > 10000000) {
                    $lost_penalty_value = 0;
                }

                $sub_classification_id = isset($sub_classification_ids[$d->book_type])
                    ? $d->book_type : $sub_classification_others->id;

                $to_insert[] = [
                    'id' => $d->id,
                    'book_no' => $d->book_no,
                    'call_no' => $d->call_no,
                    'book_category_id' => $d->item_id,
                    'book_classification_id' => $sub_classification_ids[$sub_classification_id],
                    'book_sub_classification_id' => $sub_classification_id,
                    'title' => $d->title,
                    'series' => $d->series ?: null,
                    'edition' => $d->edition ?: null,
                    'remark' => $d->remark,
                    'topic' => $d->topic,
                    'location_1' => $d->location_1,
                    'location_2' => $d->location_2,
                    'isbn' => $d->isbn,
                    'binding' => $this->_determineBookBinding($d->binding),
                    'publisher' => $d->publisher,
                    'publisher_place' => $d->publish_place,
                    'published_date' => $this->_cleanDate($d->publish_date),
                    'book_page' => $d->book_page,
                    'book_size' => $d->book_size,
                    'words' => $d->words,
                    'cdrom' => $d->cdrom ?: false,
                    'status' => BookStatus::AVAILABLE,
                    'purchase_value' => $purchase_price ?: 0,
                    'lost_penalty_value' => $lost_penalty_value ?: 0,
                    'condition' => $this->_determineBookCondition($d->book_condition),
                    'book_language_id' => $d->language > 3 ? null : $d->language,
                    'entry_date' => $this->_cleanDate($d->entry_date),
                    'created_at' => now(),
                ];

                $book_loan_setting_data = array_merge($book_loan_setting_data, [
                    [
                        'book_id' => $d->id,
                        'type' => BookLoanSettingType::EMPLOYEE,
                        'loan_period_day' => $d->book_periode_e,
                        'can_borrow' => $d->for_instructor ?: 0
                    ],
                    [
                        'book_id' => $d->id,
                        'type' => BookLoanSettingType::STUDENT,
                        'loan_period_day' => $d->book_periode_s,
                        'can_borrow' => $d->for_student ?: 0
                    ],
                    [
                        'book_id' => $d->id,
                        'type' => BookLoanSettingType::LIBRARIAN,
                        'loan_period_day' => $d->book_periode_l,
                        'can_borrow' => $d->for_student ?: 0
                    ],
                    [
                        'book_id' => $d->id,
                        'type' => BookLoanSettingType::OTHERS,
                        'loan_period_day' => $d->book_periode_o,
                        'can_borrow' => $d->for_othermember ?: 0
                    ]
                ]);
            }

            if ($this->isActual) {
                Book::insert($to_insert);
                BookLoanSetting::insert($book_loan_setting_data);
            }

            $bar->advance($chunk_size);
        });


        if ($this->isActual) {
            $this->restartId(Book::class);
            $this->restartId(BookLoanSetting::class);
            DB::commit();
        }
    }

    public function migrateAuthorBook()
    {
        $this->info("Migration for author_book");

        $from_data = $this->v1DbConnection->table('book_m')->get();
        $authors = Author::query()->pluck('id', 'name')->toArray();
        $to_insert = [];

        foreach ($from_data as $d) {
            if ($d->author_1) {
                $author = trim($d->author_1);

                if (isset($authors[$author])) {
                    $to_insert[] = [
                        'book_id' => $d->book_id,
                        'author_id' => $authors[$author]
                    ];
                }
            }

            if ($d->author_2) {
                $author = trim($d->author_2);

                if (isset($authors[$author])) {
                    $to_insert[] = [
                        'book_id' => $d->book_id,
                        'author_id' => $authors[$author]
                    ];
                }
            }

            if ($d->author_3) {
                $author = trim($d->author_3);

                if (isset($authors[$author])) {
                    $to_insert[] = [
                        'book_id' => $d->book_id,
                        'author_id' => $authors[$author]
                    ];
                }
            }
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 1000) as $to_insert_chunk) {
                    DB::table('author_book')->insert($to_insert_chunk);
                };

            });

            $this->info("Number of rows after insert: " . DB::table('author_book')->count());
        }
    }

    public function migrateBookClassifications()
    {
        $this->info("Migration for master_book_classifications");

        $from_data = $this->v1DbConnection->table('book_classification')
            ->orderBy('edesc', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->id,
                'name' => json_encode(['en' => $d->edesc, 'zh' => $d->cdesc]),
                'code' => $d->code,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                $to_insert[] = [
                    'id' => 1001,
                    'code' => '1001',
                    'name' => json_encode(['en' => 'OTHERS', 'zh' => "其他"]),
                    'created_at' => now(),
                ];

                BookClassification::insert($to_insert);

                $this->restartId(BookClassification::class);
            });

            $this->info("Number of rows after insert: " . BookClassification::count());
        }
    }

    public function migrateBookSubClassifications()
    {
        $this->info("Migration for master_book_sub_classifications");

        $from_data = $this->v1DbConnection->table('book_type')
            ->orderBy('edesc', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->id,
                'book_classification_id' => $this->getBookClassificationId($d->edesc),
                'name' => json_encode(['en' => trim($d->edesc), 'zh' => trim($d->cdesc)]),
                'code' => $d->code ?: null,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                $to_insert[] = [
                    'id' => collect($to_insert)->sortBy([['id', 'desc']])->first()['id'] + 1,
                    'book_classification_id' => 1001,
                    'name' => json_encode(['en' => 'OTHERS', 'zh' => "其他"]),
                    'code' => null,
                    'created_at' => now(),
                ];

                BookSubClassification::insert($to_insert);

                $this->restartId(BookSubClassification::class);
            });


            $this->info("Number of rows after insert: " . BookSubClassification::count());
        }

    }

    public function migrateBookCategories()
    {
        $this->info("Migration for master_book_categories");

        $from_data = $this->v1DbConnection->table('book_item_m')
            ->orderBy('edesc', 'ASC')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->item_id,
                'name' => json_encode(['en' => $d->edesc, 'zh' => $d->cdesc]),
                'code' => $d->code,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                BookCategory::insert($to_insert);

                $this->restartId(BookCategory::class);
            });

            $this->info("Number of rows after insert: " . BookCategory::count());
        }
    }

    public function migrateLibraryStudentMember()
    {
        $this->info("Migration for library_members - student");

        $from_data = $this->v1DbConnection->table('member_m')
            ->where('member_type', 1)
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $old_student_ids = $from_data->pluck('user_id')->toArray();
        $new_students = MigrationMapping::query()
            ->with('modelable.firstActiveCard')
            ->where('model', Student::class)
            ->whereIn('old', $old_student_ids)
            ->get()
            ->pluck('modelable', 'old')
            ->toArray();

        $new_students = array_filter($new_students);

        foreach ($from_data as $d) {
            if (!isset($new_students[$d->user_id])) {
                continue;
            }

            $new_members = $new_students[$d->user_id];

            $to_insert[] = [
                'id' => $d->member_id,
                'type' => LibraryMemberType::STUDENT,
                'userable_type' => Student::class,
                'userable_id' => $new_members['id'],
                'card_number' => Arr::get($new_members, "first_active_card.card_number"),
                'member_number' => $new_members['student_number'],
                'name' => json_encode($new_members['name']),
                'phone_number' => $new_members['phone_number'],
                'email' => $new_members['email'],
                'gender' => $new_members['gender'],
                'nric' => $new_members['nric'],
                'passport_number' => $new_members['passport_number'],
                'is_librarian' => $d->librarian,
                'borrow_limit' => $d->book_borrow,
                'date_of_birth' => $new_members['date_of_birth'],
                'religion_id' => $new_members['religion_id'],
                'race_id' => $new_members['race_id'],
                'address' => $new_members['address'],
                'postcode' => $new_members['postal_code'],
                'city' => $new_members['city'],
                'state_id' => $new_members['state_id'],
                'country_id' => $new_members['country_id'],
                'register_date' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_from' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_to' => $this->_cleanDate($d->expired_date) ?: Carbon::parse('2099-01-01')->toDateString(),
                'is_active' => is_null($d->deleted_at),
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    LibraryMember::insert($data);
                }

                $this->restartId(LibraryMember::class);
            });

            $this->info("Number of rows after insert: " . LibraryMember::count());
        }
    }


    public function migrateLibraryEmployeeMember()
    {
        $this->info("Migration for library_members - employee");

        $from_data = $this->v1DbConnection->table('member_m')
            ->where('member_type', 2)
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $old_employee_ids = $from_data->pluck('user_id')->toArray();
        $new_employees = Employee::with('firstActiveCard')
            ->whereIn('id', $old_employee_ids)
            ->get()
            ->keyBy('id')
            ->toArray();

        foreach ($from_data as $d) {
            if (!isset($new_employees[$d->user_id])) {
                continue;
            }

            $new_members = $new_employees[$d->user_id];

            $to_insert[] = [
                'id' => $d->member_id,
                'type' => LibraryMemberType::EMPLOYEE,
                'userable_type' => Employee::class,
                'userable_id' => $new_members['id'],
                'card_number' => $new_members['first_active_card']['card_number'] ?? null,
                'member_number' => $new_members['employee_number'],
                'name' => json_encode($new_members['name']),
                'phone_number' => $new_members['phone_number'],
                'email' => $new_members['email'],
                'gender' => $new_members['gender'],
                'nric' => $new_members['nric'],
                'passport_number' => $new_members['passport_number'],
                'is_librarian' => $d->librarian,
                'borrow_limit' => $d->book_borrow,
                'date_of_birth' => $new_members['date_of_birth'],
                'religion_id' => $new_members['religion_id'],
                'race_id' => $new_members['race_id'],
                'address' => $new_members['address'],
                'postcode' => $new_members['postal_code'],
                'city' => $new_members['city'],
                'state_id' => $new_members['state_id'],
                'country_id' => $new_members['country_id'],
                'register_date' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_from' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_to' => $this->_cleanDate($d->expired_date) ?: Carbon::parse('2099-01-01')->toDateString(),
                'is_active' => is_null($d->deleted_at),
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    LibraryMember::insert($data);
                }

                $this->restartId(LibraryMember::class);
            });

            $this->info("Number of rows after insert: " . LibraryMember::count());
        }
    }

    public function migrateLibraryBookLoan()
    {
        $this->info("Migration for library_book_loans");

        $from_data = $this->v1DbConnection->table('book_loan')
            ->orderBy('book_loan_id', 'ASC')
            ->where('loan_date', '>=', '2024-01-01')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $loan_status = $d->return_date ? LibraryBookLoanStatus::RETURNED : LibraryBookLoanStatus::BORROWED;

            if ($d->lost_amount) {
                $loan_status = LibraryBookLoanStatus::LOST;
            }

            $penalty_payment_status = $loan_status == LibraryBookLoanStatus::BORROWED ? LibraryBookLoanPaymentStatus::UNPAID : LibraryBookLoanPaymentStatus::PAID;

            $to_insert[] = [
                'member_id' => $d->member_id,
                'book_id' => $d->book_id,
                'loan_date' => $d->loan_date,
                'due_date' => $d->due_date,
                'return_date' => $d->return_date,
                'lost_date' => null,
                'penalty_overdue_amount' => $d->overdue_amount ?: 0,
                'penalty_lost_amount' => $d->lost_amount ?: 0,
                'penalty_total_fine_amount' => $d->fine_amount ?: 0,
                'penalty_paid_amount' => $d->pay_amount ?: 0,
                'loan_status' => $loan_status,
                'penalty_payment_status' => $penalty_payment_status
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    LibraryBookLoan::insert($data);
                }

                $this->restartId(LibraryBookLoan::class);
            });

            $this->info("Number of rows after insert: " . LibraryBookLoan::count());
        }
    }

    public function updateBookStatus()
    {
        $books_borrowed = LibraryBookLoan::query()
            ->where('loan_status', LibraryBookLoanStatus::BORROWED)
            ->groupBy('book_id')
            ->pluck('book_id');

        $this->info("Number of books to be update to borrowed: " . count($books_borrowed));

        $books_lost = LibraryBookLoan::query()
            ->where('loan_status', LibraryBookLoanStatus::LOST)
            ->groupBy('book_id')
            ->pluck('book_id');

        $this->info("Number of books to be update to lost: " . count($books_lost));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            Book::query()
                ->whereIn('id', $books_borrowed)
                ->update(['status' => BookStatus::BORROWED]);

            Book::query()
                ->whereIn('id', $books_lost)
                ->update(['status' => BookStatus::LOST]);

            $this->info("Number of books updated to borrowed: " . count($books_borrowed));
            $this->info("Number of books updated to lost: " . count($books_lost));
        }
    }

    // Hostel Report
    public function migrateHostelStudentBlocks()
    {
        $this->info("Migration for hostel_blocks - student");

        $from_data = $this->v1DbConnection->table('block_m')->orderBy('block_id', 'ASC');

        $from_data_count = $from_data->clone()->count();
        $order_counter = $from_data_count;

        $this->info("Number of rows: " . $order_counter);

        $count = 0;

        $from_data->lazyById(100, 'block_id')
            ->each(function ($d) use ($from_data_count, &$count) {
                $to_insert = [
                    'name' => json_encode(['en' => $d->block_desc, 'zh' => $d->block_desc_chinese]),
                    'code' => strtoupper($d->block_desc),
                    'created_at' => now(),
                    'type' => HostelBlockType::STUDENT,
                ];

                if ($this->isActual) {
                    $this->info("== ACTUAL ==");

                    DB::transaction(function () use ($to_insert, $d) {
                        $id = HostelBlock::insertGetId($to_insert);

                        MigrationMapping::query()->create([
                            'model' => HostelBlock::class,
                            'old' => $d->block_id,
                            'old_table' => 'block_m',
                            'new' => $id,
                        ]);

                        $this->info("Created block {$d->block_desc} with Block ID {$id}");
                    });

                    $count++;

                    $this->info("Number of rows (Block) after insert: " . $count . '/' . $from_data_count);
                }
            });


        if ($this->isActual) {
            HostelBlock::insertGetId([
                'name' => json_encode(['en' => 'OTHERS', 'zh' => '其他']),
                'code' => 'OTHERS',
                'created_at' => now(),
                'type' => HostelBlockType::STUDENT,
            ]);

            $this->restartId(HostelBlock::class);
        }
    }

    public function migrateHostelEmployeeBlocks()
    {
        $this->info("Migration for hostel_blocks - employee");

        $from_data = $this->v1DbConnection->table('employee_block')->orderBy('sort_order', 'ASC');

        $from_data_count = $from_data->clone()->count();
        $order_counter = $from_data_count;

        $this->info("Number of rows: " . $order_counter);

        $count = 0;

        $from_data->lazyById(100, 'id')
            ->each(function ($d) use ($from_data_count, &$count) {
                $to_insert = [
                    'name' => json_encode(['en' => $d->block_cname, 'zh' => $d->block_cname]),
                    'code' => $d->block_cname,
                    'created_at' => now(),
                    'type' => HostelBlockType::EMPLOYEE,
                ];

                if ($this->isActual) {
                    $this->info("== ACTUAL ==");

                    DB::transaction(function () use ($to_insert, $d) {
                        $id = HostelBlock::insertGetId($to_insert);

                        MigrationMapping::query()->create([
                            'model' => HostelBlock::class,
                            'old_table' => 'employee_block',
                            'old' => $d->id,
                            'new' => $id,
                        ]);

                        $this->info("Created block {$d->block_cname} with Block ID {$id}");
                    });

                    $count++;

                    $this->info("Number of rows (Block) after insert: " . $count . '/' . $from_data_count);
                }
            });


        if ($this->isActual) {
            $this->restartId(HostelBlock::class);
        }
    }

    public function migrateHostelSavingAccounts()
    {
        $this->info("Migration for hostel saving account");

        $receipt_ids = $this->v1DbConnection
            ->table('saving_account')
            ->select(DB::raw('MAX(receipt_id) as receipt_id'), 'student_id')
            ->groupBy('student_id')
            ->pluck('receipt_id');

        $from_data = $this->v1DbConnection
            ->table('saving_account')
            ->select('receipt_id', 'receipt_no', 'student_id', 'balance')
            ->where('balance', '>', 0)
            ->whereIn('receipt_id', $receipt_ids)
            ->get();

        $order_counter = count($from_data);

        $this->info("Number of rows: " . $order_counter);

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            $system_user = Employee::with('user')
                ->where('employee_number', 'SYSTEM')
                ->first()
                ->user;

            auth()->login($system_user);
            $old_student_ids = $from_data->pluck('student_id')->toArray();

            $new_students = MigrationMapping::query()
                ->with('modelable.firstActiveCard')
                ->where('model', Student::class)
                ->whereIn('old', $old_student_ids)
                ->get()
                ->pluck('modelable', 'old');

            $payment_method = PaymentMethod::where('code', PaymentMethod::CODE_CASH)->firstOrFail();

            foreach ($from_data as $d) {
                if (!isset($new_students[$d->student_id])) {
                    continue;
                }

                app()->make(HostelSavingsAccountService::class)
                    ->setStudent($new_students[$d->student_id])
                    ->setAmount($d->balance)
                    ->setRemarks('Migrate from V1 balance')
                    ->setPaymentMethod($payment_method)
                    ->setPaymentDate(Carbon::now()->tz(config('school.timezone'))->startOfDay()->tz('UTC'))
                    ->setReferenceNo($d->receipt_no)
                    ->deposit();
            }
        }
    }

    public function migrateHostelMeritDemeritSettings()
    {
        $this->info("Migration for hostel_merit_demerit_settings");

        $from_data = DB::connection('smsv1')->table('setting_merit_demerit_h')->get();

        $to_insert = [];

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->setting_merit_demerit_id,
                'name' => $d->merit_demerit_item,
                'type' => $this->_determineMeritDemeritStatus($d->merit_demerit_status),
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    HostelMeritDemeritSetting::insert($data);
                }

                $this->restartId(HostelMeritDemeritSetting::class);
            });

            $this->info("Number of rows after insert: " . HostelMeritDemeritSetting::count());
        }
    }

    public function migrateHostelRewardPunishmentSettings()
    {
        $this->info("Migration for hostel_reward_punishment_settings");

        $from_data = DB::connection('smsv1')
            ->table('reward_punishment_setting_hostel')
            ->select('reward_punishment_setting_hostel.*', DB::raw('setting_merit_demerit_h.merit_demerit_point as points'))
            ->join('setting_merit_demerit_h', 'setting_merit_demerit_h.setting_merit_demerit_id', '=', 'reward_punishment_setting_hostel.reward_setting_merit_demerit_id')
            ->get();

        $to_insert = [];

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->reward_punishment_setting_hostel_id,
                'hostel_merit_demerit_setting_id' => $d->reward_setting_merit_demerit_id,
                'code' => strtoupper($d->reward_punishment_code),
                'name' => trim($d->reward_punishment_name),
                'points' => $d->points,
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    HostelRewardPunishmentSetting::insert($data);
                }

                $this->restartId(HostelRewardPunishmentSetting::class);
            });

            $this->info("Number of rows after insert: " . HostelRewardPunishmentSetting::count());
        }
    }

    public function migrateHostelStudentRooms()
    {
        $this->info("Migration for hostel_rooms - student");

        $from_data = $this->v1DbConnection->table('hostel_rooms_basic')->orderBy('room_id', 'ASC');

        $from_data_count = $from_data->clone()->count();
        $order_counter = $from_data_count;

        $this->info("Number of rows: " . $order_counter);

        $count = 0;

        $other_block = HostelBlock::query()->where('name->en', 'OTHERS')->first();

        $mappings = MigrationMapping::query()
            ->where('model', HostelBlock::class)
            ->where('old_table', 'block_m')
            ->pluck('new', 'old')
            ->toArray();

        $from_data->lazyById(500, 'room_id')
            ->each(function ($d) use ($from_data_count, &$count, $mappings, $other_block) {
                $to_insert = [
                    'hostel_block_id' => $mappings[$d->block_id] ?? $other_block->id,
                    'name' => $d->room_name,
                    'gender' => $this->_determineGender($d->gender),
                    'capacity' => $d->room_acc_number,
                    'remarks' => $d->room_remark,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                if ($this->isActual) {
                    $this->info("== ACTUAL ==");

                    DB::transaction(function () use ($to_insert, $d) {
                        $id = HostelRoom::insertGetId($to_insert);

                        $this->info("Created room {$d->room_name} with Room ID {$id}");
                    });

                    $count++;

                    $this->info("Number of rows (Room) after insert: " . $count . '/' . $from_data_count);
                }
            });


        if ($this->isActual) {
            $this->restartId(HostelRoom::class);
        }
    }

    public function migrateHostelEmployeeRooms()
    {
        $this->info("Migration for hostel_rooms - employee");

        $from_data = $this->v1DbConnection->table('employee_room_setting')->orderBy('id', 'ASC');

        $from_data_count = $from_data->clone()->count();
        $order_counter = $from_data_count;

        $this->info("Number of rows: " . $order_counter);

        $count = 0;

        $mappings = MigrationMapping::query()
            ->where('model', HostelBlock::class)
            ->where('old_table', 'employee_block')
            ->pluck('new', 'old')
            ->toArray();

        $from_data->lazyById(100, 'id')
            ->each(function ($d) use ($from_data_count, &$count, $mappings) {
                $to_insert = [
                    'hostel_block_id' => $mappings[$d->block],
                    'name' => $d->room_name,
                    'gender' => $this->_determineGender($d->gender),
                    'capacity' => $d->capacity,
                    'remarks' => $d->remark,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                if ($this->isActual) {
                    $this->info("== ACTUAL ==");

                    DB::transaction(function () use ($to_insert, $d) {
                        $id = HostelRoom::insertGetId($to_insert);

                        $this->info("Created room {$d->room_name} with Room ID {$id}");
                    });

                    $count++;

                    $this->info("Number of rows (Room) after insert: " . $count . '/' . $from_data_count);
                }
            });


        if ($this->isActual) {
            $this->restartId(HostelRoom::class);
        }
    }

    public function migrateScholarships()
    {

        $this->info("Migration for scholarships");

        $from_data = $this->v1DbConnection->table('scholarship_setting')->orderBy('scholarship_setting_id', 'ASC')->get();

        $this->info("Number of rows to be inserted: " . count($from_data));

        foreach ($from_data as $scholarship) {

            Scholarship::insert([
                'id' => $scholarship->scholarship_setting_id,
                'name' => json_encode(['en' => $scholarship->scholarship_name, 'zh' => $scholarship->scholarship_cname]),
                'award_body' => 'UNKNOWN',
                'is_internal' => true,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

        }

        $this->restartId(Scholarship::class);
        $this->info("Number of rows after insert: " . Scholarship::count());

    }

    public function migrateUnpaidFees()
    {

        //$url = 'https://sms.smpinhwa.edu.my/pinhwa/index.php?r=finance/paymentFpx/index3&PaymentT[year]=2025&PaymentT[month]=12&PaymentT[student_id]=9865&PaymentT[standard]=5';

        $latest_semester = SemesterSetting::where('is_current_semester', true)->firstOrFail();

        // get fee group from v1
        $fee_groups = $this->v1DbConnection->table('fee_group_m')->get();
        $fee_groups = $fee_groups->mapWithKeys(function ($item) {
            return [$item->fee_group_id => $item];
        });

        // get fee remission from v1
        $fee_remissions = $this->v1DbConnection->table('fee_remission_student')
            ->selectRaw('fee_remission_student.*, fee_group_m.month AS fee_group_month')
            ->join('fee_group_m', 'fee_group_m.fee_group_id', 'fee_remission_student.fee_group')
            ->where('year', $this->year)
            ->whereNotNull('sponsorship_1')
            ->get();

        $fee_remissions = $fee_remissions->mapWithKeys(function ($item) {
            return [$item->student_id . '-' . $item->fee_id => $item];
        });

        // UOMs
        $uoms = Uom::all()->mapWithKeys(function ($item) {
            return [$item->code => $item];
        });

        // fees
        $fees = $this->v1DbConnection->table('fee_m')
            ->where('year', $this->year)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->fee_id => $item];
            });

        // scholarships
        $scholarships = Scholarship::all()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        // Grades v3 mapping
        $grade_mapping = [
            1 => 'J1',
            2 => 'J2',
            3 => 'J3',
            4 => 'R',
            5 => 'S1',
            6 => 'S2',
            7 => 'S3',
        ];

        // v1 payment history
        /*$v1_payments = $this->v1DbConnection->table('payment_det_t')
            ->selectRaw('payment_det_t.*, payment_t.receipt_no, payment_t.receive_date, payment_t.amount, payment_type_1, cheque_no_1')
            ->join('payment_t', 'payment_t.payment_id', '=', 'payment_det_t.payment_id')
            ->where('year', $this->year)
            ->get()
            ->groupBy('student_id');*/

        // students in v3
        $students = Student::with([
            "latestPrimaryClassBySemesterSettings" => function ($query) use (&$latest_semester) {
                $query->with("semesterClass.classModel.grade");
                $query->where("semester_setting_id", $latest_semester->id);
            }
        ])
            ->selectRaw('migration_mapping.old AS old_student_id, students.*')
            ->join('migration_mapping', DB::raw('migration_mapping.new::int'), '=', 'students.id')
            //->orderBy('id', 'DESC')
            //->where('migration_mapping.old', 7914)
            ->get();

        $system_employee = SystemHelper::getSystemEmployee();

        foreach ($students as $student) {

            DB::transaction(function () use (&$student, &$scholarships, &$system_employee, &$fee_groups, &$fees, &$uoms, &$grade_mapping, &$fee_remissions) {
                $class = $student->currentSemesterPrimaryClass->semesterClass->classModel;
                $grade_id = $student->currentSemesterPrimaryClass->semesterClass->classModel->grade_id;

                $this->info("Processing student " . $student->name . ', v1 ID: ' . $student->old_student_id . ', v3 ID: ' . $student->id . ', grade ' . $grade_id);

                if (!$this->isActual) {
                    return;
                }

                // unpaid items
                $url = 'https://sms.smpinhwa.edu.my/pinhwa/index.php' . "?" . http_build_query([
                        'r' => 'finance/paymentFpx/index3',
                        'PaymentT[year]' => $this->year,
                        'PaymentT[month]' => 12,
                        'PaymentT[student_id]' => $student->old_student_id,
                        'PaymentT[standard]' => $grade_id
                    ]);

                $url = urldecode($url);
                $this->info($url);

                $response = Http::withoutVerifying()->get($url);

                $data = $response->json();

                $fee_remission_months = [];

                foreach ($data as $d) {

                    $fee_group_v1 = $fee_groups[$d['fee_group_id']];
                    $fee = $fees[$d['fee_id']];

                    $this->info($d['fee_name'] . ' - ' . $d['amount'] . ' for month ' . $d['month'] . '. Fee ID ' . $d['fee_id']);

                    // if student fee under fee_remission
                    $fee_remission = $fee_remissions[$d['student_id'] . '-' . $d['fee_id']] ?? null;

                    if ($fee_remission !== null && ($fee_group_v1->school_fee == 1 || $fee_group_v1->hostel_fee == 1)) {

                        // got discount/scholarship
                        $scholarship = $scholarships[$fee_remission->sponsorship_1] ?? null;

                        if ($scholarship === null) {
                            throw new \Exception('Please set up scholarship first. ID = ' . $fee_remission->sponsorship_1);
                        }

                        // oveerride fee with default fee object
                        $original_fee = $fees[$fee_remission->default_fee];

                        // award scholarship to student
                        if (!isset($fee_remission_months[$scholarship->id])) {
                            $fee_remission_months[$scholarship->id] = [];
                        }

                        $fee_remission_months[$scholarship->id][] = (int) $fee_remission->fee_group_month;

                        $fee = $original_fee;
                    }

                    if ($fee_group_v1->school_fee == 1) {
                        $product_category = ProductCategory::SCHOOL_FEES;
                        $uom = $uoms['MONTH'];
                        $gl_account_code = GlAccount::CODE_SCHOOL_FEES;

                        if ($class->stream === ClassStream::SCIENCE) {
                            $product_code = $grade_mapping[$class->grade_id] . '-S-FEE';
                            $product_name = 'SCHOOL FEES - ' . $grade_mapping[$class->grade_id] . ' SCIENCE';
                            $product_name_zh = '学费 - ' . $grade_mapping[$class->grade_id] . ' 理科';
                        } else {
                            if ($class->stream === ClassStream::COMMERCE) {
                                $product_code = $grade_mapping[$class->grade_id] . '-C-FEE';
                                $product_name = 'SCHOOL FEES - ' . $grade_mapping[$class->grade_id] . ' COMMERCE';
                                $product_name_zh = '学费 - ' . $grade_mapping[$class->grade_id] . ' 文商';
                            } else {
                                $product_code = $grade_mapping[$class->grade_id] . '-FEE';
                                $product_name = 'SCHOOL FEES - ' . $grade_mapping[$class->grade_id];
                                $product_name_zh = '学费 - ' . $grade_mapping[$class->grade_id];
                            }
                        }
                    } else {
                        if ($fee_group_v1->hostel_fee == 1) {
                            $product_category = ProductCategory::HOSTEL_FEES;
                            $gl_account_code = GlAccount::CODE_HOSTEL_FEES;
                            $uom = $uoms['MONTH'];

                            if (preg_match("/^外国生宿舍费.+6人房/", $fee->fee_desc)) {
                                $product_code = 'FHOSTEL6';
                                $product_name = 'HOSTEL FEES - 6 BED (FOREIGNER)';
                                $product_name_zh = '外国生宿舍费 - 6人房';
                            } else {
                                if (preg_match("/^外国生宿舍费.+4人房/", $fee->fee_desc)) {
                                    $product_code = 'FHOSTEL4';
                                    $product_name = 'HOSTEL FEES - 4 BED (FOREIGNER)';
                                    $product_name_zh = '外国生宿舍费 - 4人房';
                                } else {
                                    if (preg_match("/^宿舍费.+4人房/", $fee->fee_desc)) {
                                        $product_code = 'HOSTEL4';
                                        $product_name = 'HOSTEL FEES - 4 BED';
                                        $product_name_zh = '宿舍费 - 4人房';
                                    } else {
                                        if (preg_match("/^宿舍费.+6人房/", $fee->fee_desc)) {
                                            $product_code = 'HOSTEL6';
                                            $product_name = 'HOSTEL FEES - 6 BED';
                                            $product_name_zh = '宿舍费 - 6人房';
                                        } else {
                                            if (preg_match("/^宿舍费[0-9]+月$/", $fee->fee_desc)) {
                                                $product_code = 'HOSTEL';
                                                $product_name = 'HOSTEL FEES';
                                                $product_name_zh = '宿舍费';
                                            } else {
                                                $product_code = $fee->desc_code;
                                                $product_name = $fee->fee_desc;
                                                $product_name_zh = $fee->fee_desc;
                                            }
                                        }
                                    }
                                }
                            }
                        } else {

                            if (preg_match("/统考考试费/", $fee->fee_desc) || preg_match("/UEC EXAM/", $fee->fee_desc)) {
                                $product_code = 'UECEXAM';
                                $product_name = $product_name_zh = '高三统考考试费';
                                $gl_account_code = GlAccount::CODE_EXAM_FEES;
                                $product_category = ProductCategory::EXAM_FEES;
                            } else {
                                if (preg_match("/SPM考试费/", $fee->fee_desc)) {
                                    $product_code = 'SPMEXAM';
                                    $product_name = $product_name_zh = 'SPM考试费';
                                    $gl_account_code = GlAccount::CODE_EXAM_FEES;
                                    $product_category = ProductCategory::EXAM_FEES;
                                } else {
                                    if (preg_match("/PBS补习/", $fee->fee_desc)) {
                                        $product_code = 'PBSTUITION';
                                        $product_name = $product_name_zh = 'PBS补习费';
                                        $gl_account_code = GlAccount::CODE_TUITION_FEES;
                                        $product_category = ProductCategory::TUITION_FEES;
                                    } else {
                                        if (preg_match("/SPM补习/", $fee->fee_desc)) {
                                            $product_code = 'SPMTUITION';
                                            $product_name = $product_name_zh = 'SPM补习费';
                                            $gl_account_code = GlAccount::CODE_TUITION_FEES;
                                            $product_category = ProductCategory::TUITION_FEES;
                                        } else {
                                            if (preg_match("/高三.*补习费/", $fee->fee_desc)) {
                                                $product_code = 'S3TUITION';
                                                $product_name = $product_name_zh = '高三加强班补习费';
                                                $gl_account_code = GlAccount::CODE_TUITION_FEES;
                                                $product_category = ProductCategory::TUITION_FEES;
                                            } else {
                                                if (preg_match("/高中.*补习费/", $fee->fee_desc)) {
                                                    $product_code = 'STUITION';
                                                    $product_name = $product_name_zh = '高中加强班补习费';
                                                    $gl_account_code = GlAccount::CODE_TUITION_FEES;
                                                    $product_category = ProductCategory::TUITION_FEES;
                                                } else {
                                                    if (preg_match("/初中.*补习费/", $fee->fee_desc)) {
                                                        $product_code = 'JTUITION';
                                                        $product_name = $product_name_zh = '初中加强班补习费';
                                                        $gl_account_code = GlAccount::CODE_TUITION_FEES;
                                                        $product_category = ProductCategory::TUITION_FEES;
                                                    } else {
                                                        if (preg_match("/PBS书费/", $fee->fee_desc)) {
                                                            $product_code = 'PBSBOOK';
                                                            $product_name = $product_name_zh = 'PBS书费';
                                                            $gl_account_code = GlAccount::CODE_BOOK_FEES;
                                                            $product_category = ProductCategory::BOOK_FEES;
                                                        } else {
                                                            if (preg_match("/SPM书费/", $fee->fee_desc)) {
                                                                $product_code = 'SPMBOOK';
                                                                $product_name = $product_name_zh = 'SPM书费';
                                                                $gl_account_code = GlAccount::CODE_BOOK_FEES;
                                                                $product_category = ProductCategory::BOOK_FEES;
                                                            } else {
                                                                if (preg_match("/^新走读生$/", $fee->fee_desc)) {
                                                                    $product_code = 'SCHOOLREG';
                                                                    $product_name = $product_name_zh = '初一新走读生注册费';
                                                                    $gl_account_code = GlAccount::CODE_REGISTRATION_SCHOOL;
                                                                    $product_category = ProductCategory::REGISTRATION;
                                                                } else {
                                                                    if (preg_match("/^宿舍生注册费$/", $fee->fee_desc)) {
                                                                        $product_code = 'HOSTELREG';
                                                                        $product_name = $product_name_zh = '新学年宿舍生注册费';
                                                                        $gl_account_code = GlAccount::CODE_REGISTRATION_HOSTEL;
                                                                        $product_category = ProductCategory::REGISTRATION;
                                                                    } else {
                                                                        $product_code = $fee->desc_code;
                                                                        $product_name = $product_name_zh = $fee->fee_desc;
                                                                        $gl_account_code = GlAccount::CODE_OTHERS;
                                                                        $product_category = ProductCategory::OTHERS;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            $uom = $uoms['UNIT'];
                        }
                    }


                    $product = Product::updateOrCreate([
                        'code' => strtoupper($product_code),
                        'gl_account_code' => $gl_account_code,
                        'category' => $product_category,
                    ], [
                        'name' => ['en' => $product_name, 'zh' => $product_name_zh],
                        'uom_code' => $uom->code,
                        'is_active' => 1,
                        'unit_price' => $fee->amount,
                    ]);

                    // create unpaid item
                    $price = floatval(preg_replace('/[^\d.]/', '', $d['amount']));

                    UnpaidItem::firstOrCreate([
                        'bill_to_type' => Student::class,
                        'bill_to_id' => $student->id,
                        'status' => UnpaidItem::STATUS_UNPAID,
                        'description' => $product->getTranslation('name', 'zh'),
                        'product_id' => $product->id,
                        'gl_account_code' => $product->gl_account_code,
                        'period' => Carbon::create(year: $this->year, month: $d['month'], day: 1)->toDateString(),
                        'currency_code' => config('school.currency_code'),
                        'unit_price' => $price,
                        'quantity' => 1,
                        'amount_before_tax' => $price,
                        'created_by_employee_id' => $system_employee->id,
                    ]);

                }

                // if got scholarship
                if (count($fee_remission_months) > 0) {

                    foreach ($fee_remission_months as $scholarship_id => $months) {

                        $earliest_month = collect($months)->sort()->first();
                        $latest_month = collect($months)->sort()->last();

                        $this->info("Has scholarship from month {$earliest_month} to {$latest_month}");

                        ScholarshipAward::firstOrCreate([
                            'scholarship_id' => $scholarship_id,
                            'student_id' => $student->id,
                            'effective_from' => Carbon::create(year: $this->year, month: $earliest_month, day: 1),
                            'effective_to' => Carbon::create(year: $this->year, month: $latest_month, day: 1)->endOfMonth(),
                        ], [
                            'created_at' => now(),
                        ]);

                    }

                }


            });


        }


    }

    public function generateHostelBeds()
    {
        $this->info("Generate for hostel_room_beds");

        $from_data = HostelRoom::all();

        $to_insert = [];

        foreach ($from_data as $d) {
            if ($d->capacity <= 1) {
                $to_insert[] = [
                    'hostel_room_id' => $d->id,
                    'name' => $d->name,
                    'is_active' => true,
                    'status' => HostelRoomBedStatus::AVAILABLE,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            } else {
                for ($i = 1; $i <= $d->capacity; $i++) {
                    $to_insert[] = [
                        'hostel_room_id' => $d->id,
                        'name' => $d->name . '-' . $i,
                        'is_active' => true,
                        'status' => HostelRoomBedStatus::AVAILABLE,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }
        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(HostelRoomBed::class, $to_insert);
    }

    public function migrateHostelBedStudentAssignments()
    {
        $this->info("Migration for hostel_bed_assignments - student");

        $beds = HostelRoomBed::query()
            ->pluck('id', 'name')
            ->toArray();

        $system_user = User::query()
            ->where('email', '<EMAIL>')
            ->first();

        $old_student_ids = MigrationMapping::where('model', Student::class)
            ->pluck('new', 'old')
            ->toArray();

        $rows_to_insert = 0;

        $to_insert = [];

        DB::connection('smsv1')->table('dormitory_info')
            ->select('dormitory_info_id', 'in_date', 'out_date', 'room_name', 'bed_no', 'room_hostel_id', 'student_boarder_info_id', 'student_id', 'dormitory_info.create_date')
            ->join('student_board', 'dormitory_info.student_boarder_info_id', '=', 'student_board.student_board_id')
            ->join('hostel_rooms_basic', 'dormitory_info.room_hostel_id', '=', 'hostel_rooms_basic.room_id')
            ->where('dormitory_info.create_date', '>=', '2024-01-01')
            ->whereIn('student_id', array_keys($old_student_ids))
            ->orderBy('dormitory_info_id', 'ASC')
            ->chunkById(500, function ($chunk) use (&$to_insert, $beds, $system_user, $old_student_ids, &$rows_to_insert) {
                $chunk->each(function ($d) use (&$to_insert, $beds, $system_user, $old_student_ids, &$rows_to_insert) {
                    if (isset($beds[$d->room_name . '-' . $d->bed_no])) {

                        $to_insert[] = [
                            'assignable_type' => Student::class,
                            'assignable_id' => $old_student_ids[$d->student_id],
                            'hostel_room_bed_id' => $beds[$d->room_name . '-' . $d->bed_no],
                            'assigned_by' => $system_user->id,
                            'start_date' => $d->in_date,
                            'end_date' => $d->out_date,
                            'created_at' => now()
                        ];
                        $rows_to_insert++;
                    }

                });
            }, 'dormitory_info_id');

        $this->info("Number of rows to be inserted: " . $rows_to_insert);

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    HostelBedAssignment::insert($data);
                }

                $bed_ids = collect($to_insert)
                    ->whereNull('end_date')
                    ->pluck('hostel_room_bed_id')
                    ->unique()
                    ->toArray();

                HostelRoomBed::query()->whereIn('id', $bed_ids)->update(['status' => HostelRoomBedStatus::OCCUPIED]);
            });

            $this->info("Number of rows after insert: " . HostelBedAssignment::count());
        }
    }

    public function migrateHostelBedEmployeeAssignments()
    {
        $this->info("Migration for hostel_bed_assignments - employee");

        $from_data = DB::connection('smsv1')->table('employee_dormitory_info')
            ->select(
                DB::raw('employee_dormitory_info.check_in as check_in_date'),
                DB::raw('employee_dormitory_info.check_out as check_out_date'),
                'room_name',
                'bed_no',
                'room_hostel_id',
                'employee_boarder_info_id',
                'employee_id',
                'employee_dormitory_info.create_date'
            )
            ->join('employee_board', 'employee_dormitory_info.employee_boarder_info_id', 'employee_board.employee_board_id')
            ->join('employee_room_setting', 'employee_dormitory_info.room_hostel_id', 'employee_room_setting.id')
            ->orderBy('dormitory_info_id', 'ASC')
            ->get();
        $to_insert = [];

        $beds = HostelRoomBed::query()
            ->pluck('id', 'name')
            ->toArray();

        $system_user = User::query()
            ->where('email', '<EMAIL>')
            ->first();

        foreach ($from_data as $d) {
            $to_insert[] = [
                'assignable_type' => Employee::class,
                'assignable_id' => $d->employee_id,
                'hostel_room_bed_id' => $beds[$d->room_name],
                'assigned_by' => $system_user->id,
                'start_date' => $d->check_in_date,
                'end_date' => $d->check_out_date,
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(HostelBedAssignment::class, $to_insert);

        if ($this->isActual) {
            //Update bed to occupied
            $bed_ids = collect($to_insert)
                ->whereNull('end_date')
                ->pluck('hostel_room_bed_id')
                ->unique()
                ->toArray();

            HostelRoomBed::query()->whereIn('id', $bed_ids)->update(['status' => HostelRoomBedStatus::OCCUPIED]);
        }
    }


    public function _processGuardianMigration(&$d, &$countries, &$students)
    {

        $father_ic_passport = $this->_determineIcPassportRegex($d->father_ic);
        $father_password = $father_ic_passport['nric'] !== null ? Hash::make($father_ic_passport['nric']) : ($father_ic_passport['passport_number'] !== null ? Hash::make($father_ic_passport['passport_number']) : Hash::make('pinhwa@123'));

        $mother_ic_passport = $this->_determineIcPassportRegex($d->mother_ic);
        $mother_password = $mother_ic_passport['nric'] !== null ? Hash::make($mother_ic_passport['nric']) : ($mother_ic_passport['passport_number'] !== null ? Hash::make($mother_ic_passport['passport_number']) : Hash::make('pinhwa@123'));

        $guardian_ic_passport = $this->_determineIcPassportRegex($d->guardian_ic);
        $guardian_password = $guardian_ic_passport['nric'] !== null ? Hash::make($guardian_ic_passport['nric']) : ($guardian_ic_passport['passport_number'] !== null ? Hash::make($guardian_ic_passport['passport_number']) : Hash::make('pinhwa@123'));

        $dataset = [
            'FATHER' => null,
            'MOTHER' => null,
            'GUARDIAN' => null,
        ];

        if ($d->father_name !== null && strlen($d->father_name) > 2) {
            $dataset['FATHER'] = [
                'guardian' => [
                    'user_id' => null,
                    'name' => ['en' => trim($d->father_name), 'zh' => trim($d->father_cname)],
                    'email' => $this->_cleanEmail($d->father_email),
                    'phone_number' => $this->_cleanPhoneNumber($d->father_phone),
                    'nric' => $father_ic_passport['nric'],
                    'passport_number' => $father_ic_passport['passport_number'],
                    'nationality_id' => $countries[$d->father_nationality_name]->id ?? 1,       // default to UNKNOWN if not found
                    'race_id' => !empty($d->father_race) ? $d->father_race : null,
                    'religion_id' => !empty($d->father_religion) ? $d->father_religion : null,
                    'married_status' => $this->_cleanMaritalStatus($d->father_status),
                    'remarks' => $d->father_status,
                    'occupation' => $d->father_work,
                    'occupation_description' => $d->father_work_description,
                ],
                'user' => [
                    'email' => null,
                    'phone_number' => $this->_cleanPhoneNumber($d->father_phone),
                    'is_active' => true,
                    'password' => $father_password,
                ]
            ];
        }

        if ($d->mother_name !== null && strlen($d->mother_name) > 2) {
            $dataset['MOTHER'] = [
                'guardian' => [
                    'user_id' => null,
                    'name' => ['en' => trim($d->mother_name), 'zh' => trim($d->mother_cname)],
                    'email' => $this->_cleanEmail($d->mother_email),
                    'phone_number' => $this->_cleanPhoneNumber($d->mother_phone),
                    'nric' => $mother_ic_passport['nric'],
                    'passport_number' => $mother_ic_passport['passport_number'],
                    'nationality_id' => $countries[$d->mother_nationality_name]->id ?? 1,       // default to UNKNOWN if not found
                    'race_id' => !empty($d->mother_race) ? $d->mother_race : null,
                    'religion_id' => !empty($d->mother_religion) ? $d->mother_religion : null,
                    'married_status' => $this->_cleanMaritalStatus($d->mother_status),
                    'remarks' => $d->mother_status,
                    'occupation' => $d->mother_work,
                    'occupation_description' => $d->mother_work_description,
                ],
                'user' => [
                    'email' => null,
                    'phone_number' => $this->_cleanPhoneNumber($d->mother_phone),
                    'is_active' => true,
                    'password' => $mother_password,
                ]
            ];
        }

        if ($d->guardian_name !== null && strlen($d->guardian_name) > 2) {
            $dataset['GUARDIAN'] = [
                'guardian' => [
                    'user_id' => null,
                    'name' => ['en' => trim($d->guardian_name), 'zh' => trim($d->guardian_cname)],
                    'email' => $this->_cleanEmail($d->guardian_email),
                    'phone_number' => $this->_cleanPhoneNumber($d->guardian_phone),
                    'nric' => $guardian_ic_passport['nric'],
                    'passport_number' => $guardian_ic_passport['passport_number'],
                    'nationality_id' => $countries[$d->guardian_nationality_name]->id ?? 1,       // default to UNKNOWN if not found
                    'race_id' => !empty($d->guardian_race) ? $d->guardian_race : null,
                    'religion_id' => !empty($d->guardian_religion) ? $d->guardian_religion : null,
                    'married_status' => $this->_cleanMaritalStatus($d->guardian_status),
                    'remarks' => $d->guardian_status,
                    'occupation' => $d->guardian_work,
                    'occupation_description' => $d->guardian_work_description,
                ],
                'user' => [
                    'email' => null,
                    'phone_number' => $this->_cleanPhoneNumber($d->guardian_phone),
                    'is_active' => true,
                    'password' => $guardian_password,
                    'is_password_reset_required' => true,
                ]
            ];
        }

        $this->info("Processed guardians for student " . $d->student_id . ' - ' . $d->student_name . ' / ' . $d->student_cname);
        $this->info("Father: " . ($dataset['FATHER'] !== null ? $dataset['FATHER']['guardian']['name']['en'] : '-'));
        $this->info("Mother: " . ($dataset['MOTHER'] !== null ? $dataset['MOTHER']['guardian']['name']['en'] : '-'));
        $this->info("Guardian: " . ($dataset['GUARDIAN'] !== null ? $dataset['GUARDIAN']['guardian']['name']['en'] : '-'));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$dataset, &$students, &$d) {

                foreach ($dataset as $guardian_type => &$data) {

                    if ($data === null) {
                        continue;
                    }

                    // check whether guardian will similar info exists
                    $check_guardian = null;

                    if ($data['guardian']['nric'] !== null) {
                        $check_guardian = Guardian::where('nric', $data['guardian']['nric'])->first();
                    }

                    if ($check_guardian === null) {
                        $check_guardian = Guardian::where(DB::raw('name->>\'en\''), $data['guardian']['name']['en'])
                            ->first();
                    }

                    // if exists, update existing guardian info but dont create user.
                    if ($check_guardian !== null) {
                        $this->warn("Guardian exists, skip creation. Similar to {$check_guardian->id} - {$check_guardian->name}");

                        // assign guardian to student
                        $new_guardian = $check_guardian;

                    } else {
                        // if not exists, create user and create guardian

                        // does user exists with same phone number? if yes, we dont create new user
                        $user = null;

                        if ($data['user']['phone_number'] !== null) {
                            $user = User::query()
                                ->where('phone_number', '=', $data['user']['phone_number'])
                                ->first();
                        }

                        if ($user !== null) {
                            $this->warn("User account exists with same phone, not creating user.");
                            $data['guardian']['user_id'] = $user->id;
                        } else {

                            // if no email and phone, no need create user
                            if ($data['user']['email'] !== null || $data['user']['phone_number'] !== null) {
                                $user = User::create($data['user']);
                                $data['guardian']['user_id'] = $user->id;
                            }

                        }

                        $new_guardian = Guardian::create($data['guardian']);

                        $this->info("Created guardian {$new_guardian->name} as " . GuardianType::{$guardian_type}->value . " with User ID {$new_guardian->user_id}");

                    }

                    // link guardian to student
                    if (isset($students[$d->student_no])) {

                        GuardianStudent::firstOrCreate([
                            'guardian_id' => $new_guardian->id,
                            'type' => GuardianType::{$guardian_type},
                            'studenable_type' => Student::class,
                            'studenable_id' => $students[$d->student_no]->id,
                        ], [
                            'guardian_id' => $new_guardian->id,
                            'type' => GuardianType::{$guardian_type},
                            'studenable_type' => Student::class,
                            'studenable_id' => $students[$d->student_no]->id,
                            'relation_to_student' => $guardian_type === 'GUARDIAN' ? trim($d->guardian_relation_name) : null,
                        ]);
                    }

                }

            });

            $this->info("Number of rows (Guardians) after insert: " . Guardian::count());
            $this->info("Number of rows (Users) after insert: " . User::count());

        }
    }

    public function migrateHostelGuardians()
    {

        $this->info("Migration for hostel guardians");

        $students = Student::selectRaw('students.id AS new_id, migration_mapping.old AS old_id')
            ->leftJoin('migration_mapping', 'students.id', '=', DB::raw('migration_mapping.new::int'))
            ->get();

        $v1_student_ids = $students->pluck('old_id')->toArray();
        $old_to_new_student_mapping = $students->mapWithKeys(function ($item) {
            return [$item->old_id => $item->new_id];
        });

        $countries = Country::all()->mapWithKeys(function ($item) {
            return [$item->getTranslation('name', 'zh') => $item];
        });

        $guardians = $this->v1DbConnection
            ->table('student_board_family')
            ->selectRaw('student_board_family.*, nationality_lookup.lookup_name AS nationality_name, student_board.student_id')
            ->leftJoin(DB::raw('lookup_m nationality_lookup'), function ($join) {
                $join->on('nationality_lookup.lookup_value', '=', 'student_board_family.nationality')
                    ->where('nationality_lookup.lookup_type', '=', 'Nationality')
                    ->where('nationality_lookup.lookup_active', 1);
            })
            ->leftJoin('student_board', 'student_board_family.student_boarder_info_id', '=', 'student_board.student_board_id')
            ->whereIn('student_board.student_id', $v1_student_ids)
            ->where(function ($q) {
                $q->whereNotNull('english_name')->orWhereNotNull('chinese_name');
            })
            ->get();

        foreach ($guardians as $d) {

            $d->english_name = trim($d->english_name);
            $d->chinese_name = trim($d->chinese_name);

            if (($d->english_name === '-' || $d->english_name === '' || $d->english_name === null || preg_match("/\?\?/", $d->english_name)) &&
                ($d->chinese_name === '-' || $d->chinese_name === '' || $d->chinese_name === null || preg_match("/\?\?/", $d->chinese_name))
            ) {
                continue;
            }

            if ($this->isActual) {

                $this->info("== ACTUAL ==");

                $ic_passport = $this->_determineIcPassportRegex($d->identity_card);

                $guardian = Guardian::where('nric', $ic_passport['nric'])->orWhere(DB::raw('name->>\'en\''), $d->english_name)->first();
                if ($guardian) {
                    $this->warn("Guardian with NRIC " . $ic_passport['nric'] . " or name {$d->english_name} already exists, skip");
                }

                DB::transaction(function () use (&$d, &$old_to_new_student_mapping, &$countries, &$ic_passport, $guardian) {

                    if (!$guardian) {
                        $guardian = Guardian::create([
                            'user_id' => null,      // will not have login access
                            'name' => ['en' => $d->english_name, 'zh' => $d->chinese_name],
                            'phone_number' => $this->_cleanPhoneNumber($d->contact_no),
                            'nric' => $ic_passport['nric'],
                            'passport_number' => $ic_passport['passport_number'],
                            'nationality_id' => $countries[$d->nationality_name]->id ?? 1,       // default to UNKNOWN if not found
                        ]);
                    }

                    // link guardian to student
                    if (isset($old_to_new_student_mapping[$d->student_id])) {
                        GuardianStudent::firstOrCreate([
                            'guardian_id' => $guardian->id,
                            'studenable_type' => Student::class,
                            'studenable_id' => $old_to_new_student_mapping[$d->student_id],
                        ], [
                            'type' => GuardianType::GUARDIAN,
                        ]);
                    }

                    $this->info("Created guardian {$guardian->name} from hostel guardian records.");

                });

            }


        }

    }

    public function migrateEmployeeCategory()
    {

        $this->info("Migration for employee categories");

        $types = $this->v1DbConnection->table('employee_type_m')->orderBy('employee_type_id', 'ASC')->get();

        $this->info("Number of rows to be inserted: " . count($types));

        if ($this->isActual) {

            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$types) {

                foreach ($types as $t) {
                    EmployeeCategory::insert([
                        'id' => $t->employee_type_id,
                        'name' => json_encode(['en' => $t->employee_type_name, 'zh' => $t->employee_type_name]),
                        'description' => $t->employee_type_desc,
                        'is_teacher' => (bool) preg_match("/teacher/i", $t->employee_type_name),
                    ]);
                }

                DB::statement('ALTER SEQUENCE master_employee_category_id_seq RESTART WITH ' . EmployeeCategory::count() + 1);

            });

            $this->info("Number of rows after insert: " . EmployeeCategory::count());

        }

    }

    public function migrateEmployeeSession()
    {
        $this->info("Migration for master_employee_sessions and master_employee_session_settings");

        $from_data = $this->v1DbConnection->table('employee_shift')->get();

        $order_counter = count($from_data);

        $to_insert_employee_session = [];
        $to_insert_employee_session_setting = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert_employee_session[] = [
                'id' => $d->shift_id,
                'name' => json_encode(['en' => trim($d->english_desc), 'zh' => trim($d->chinese_desc)]),
                'is_active' => $d->shift_active,
                'determine_attendance_status' => false,
                'created_at' => now(),
            ];

            $to_insert_employee_session_setting = [
                ...$to_insert_employee_session_setting, ...[
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::MONDAY,
                        'start_time' => $d->in_monday,
                        'end_time' => $d->out_monday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::TUESDAY,
                        'start_time' => $d->in_tuesday,
                        'end_time' => $d->out_tuesday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::WEDNESDAY,
                        'start_time' => $d->in_wednesday,
                        'end_time' => $d->out_wednesday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::THURSDAY,
                        'start_time' => $d->in_thursday,
                        'end_time' => $d->out_thursday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::FRIDAY,
                        'start_time' => $d->in_friday,
                        'end_time' => $d->out_friday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::SATURDAY,
                        'start_time' => $d->in_saturday,
                        'end_time' => $d->out_saturday,
                        'created_at' => now(),
                    ],
                    [
                        'employee_session_id' => $d->shift_id,
                        'day' => Day::SUNDAY,
                        'start_time' => $d->in_sunday,
                        'end_time' => $d->out_sunday,
                        'created_at' => now(),
                    ]
                ]
            ];
        }

        $this->info("Number of employee session to be inserted: " . count($to_insert_employee_session));
        $this->info("Number of employee session setting to be inserted: " . count($to_insert_employee_session_setting));

        if ($this->isTruncate) {
            EmployeeSession::truncate();
            EmployeeSessionSetting::truncate();
        }
        $this->_insertData(EmployeeSession::class, $to_insert_employee_session);
        $this->_insertData(EmployeeSessionSetting::class, $to_insert_employee_session_setting);
    }

    public function migrateEmployees()
    {

        $this->info("Migration for employees");

        $employees = $this->v1DbConnection->table('employee_m')
            ->selectRaw('employee_m.*, employee_board.employee_board_id AS employee_board_id, marriage_status.lookup_name AS marriage_status_name')
            ->leftJoin('employee_board', function ($join) {
                $join->on('employee_board.employee_id', '=', 'employee_m.employee_id')
                    ->whereNull('check_out');
            })
            ->leftJoin(DB::raw('lookup_m marriage_status'), function ($join) {
                $join->on('marriage_status.lookup_value', '=', 'employee_m.employee_mariage_status')
                    ->where('marriage_status.lookup_type', '=', 'Married Status')
                    ->where('marriage_status.lookup_active', 1);
            })
            ->get();

        $this->info("Number of rows to be inserted: " . count($employees));

        $states = State::all()->mapWithKeys(function ($item) {
            return [$item->getTranslation('name', 'en') => $item];
        });

        $country_malaysia = Country::where(DB::raw('name->>\'en\''), 'Malaysia')->first();
        $country_unknown = Country::where(DB::raw('name->>\'en\''), 'Unknown')->first();

        $unknown_race = Race::where(DB::raw('name->>\'en\''), 'UNKNOWN')->first();
        $unknown_religion = Religion::where(DB::raw('name->>\'en\''), 'UNKNOWN')->first();

        foreach ($employees as $e) {

            $ic_or_passport = $this->_determineIcPassportRegex($e->employee_ic);
            $job_title = null;
            $password = $ic_or_passport['nric'] !== null ? Hash::make($ic_or_passport['nric']) : Hash::make($ic_or_passport['passport_number']);

            // if job title not exist, create
            if (!empty($e->employee_title) && !EmployeeJobTitle::where(DB::raw('name->>\'en\''), '=', $e->employee_title)->exists()) {
                $job_title = EmployeeJobTitle::create([
                    'name' => ['en' => $e->employee_title, 'zh' => $e->employee_title],
                ]);
            }

            $insert_data = [
                'employee' => [
                    'id' => $e->employee_id,
                    'user_id' => null,
                    'name' => json_encode(['en' => trim($e->employee_name), 'zh' => trim($e->employee_cname)]),
                    'employee_number' => $e->employee_no,
                    'email' => $this->_cleanEmail($e->employee_email) ?? "noemail{$e->employee_no}@smpinhwa.edu.my",
                    'personal_email' => $this->_cleanEmail($e->employee_email),
                    'phone_number' => $this->_cleanPhoneNumber($e->home_tel) ?? "+60300000000",
                    'date_of_birth' => $this->_cleanDate($e->employee_dob),
                    'gender' => $this->_determineGender($e->employee_sex),
                    'nric' => $ic_or_passport['nric'],
                    'passport_number' => $ic_or_passport['passport_number'],
                    'badge_no' => $e->badge_no,
                    'job_title_id' => $job_title?->id,
                    'employee_session_id' => $e->employee_session,
                    'status' => $this->_determineEmployeeStatus($e->employee_wstatus),
                    'religion_id' => !empty($e->religion) ? $e->religion : $unknown_religion->id,
                    'race_id' => !empty($e->race) ? $e->race : $unknown_race->id,
                    'address' => trim($e->home_address1),
                    'address_2' => trim($e->contact_address),
                    'postal_code' => trim($e->home_poskod),
                    'city' => trim($e->home_city),
                    'state_id' => $states[$e->home_state]->id ?? null,
                    'country_id' => $states[$e->home_state]->country_id ?? null,
                    'is_hostel' => $e->employee_board_id !== null,
                    'epf_number' => $e->employee_epf,
                    'employment_start_date' => $this->_cleanDate($e->employee_starwd),
                    'employment_end_date' => $this->_cleanDate($e->employee_stopwd),
                    'highest_education' => !empty($e->highest_education) ? EducationLevel::{strtoupper($e->highest_education)} ?? null : null,
                    'highest_education_country_id' => $e->highest_education_location === 'Local' ? $country_malaysia->id : $country_unknown->id,
                    'employment_type' => $this->_convertEmploymentType($e->employee_type),
                    'employee_category_id' => $e->emp_type,
                    'marriage_status' => $this->_convertMarriageStatus($e->marriage_status_name),
                    'created_at' => now(),
                ],
                'user' => [
                    'email' => null,
                    'phone_number' => $this->_cleanPhoneNumber($e->home_tel),
                    'is_active' => true,
                    'password' => $password,
                    'is_password_reset_required' => true,
                ],
                'v1' => [
                    'photo' => $e->employee_photo,
                ]
            ];

            $this->info("Processing employee {$e->employee_name}.");

            if ($this->isActual) {

                $this->info("== ACTUAL ==");

                DB::transaction(function () use (&$insert_data) {

                    $reuse_user = false;

                    // does user exists with same phone number, if yes, we reuse back same user for employee table user_id
                    if ($insert_data['user']['phone_number'] !== null && $insert_data['employee']['status'] === EmployeeStatus::WORKING) {

                        $user = User::where('phone_number', '=', $insert_data['user']['phone_number'])->first();

                        if ($user !== null) {
                            $reuse_user = true;
                            $insert_data['employee']['user_id'] = $user->id;
                        } else {
                            $reuse_user = false;
                            $user = User::create($insert_data['user']);
                            $insert_data['employee']['user_id'] = $user->id;
                        }

                    }

                    Employee::insert($insert_data['employee']);
                    $new_employee = Employee::where('id', $insert_data['employee']['id'])->first();

                    $new_file_name = md5($insert_data['employee']['employee_number']) . '.jpg';
                    $this->_downloadEmployeePhoto($insert_data['v1']['photo'], $new_file_name, $new_employee);
                    $this->info("Created employee {$new_employee->name} with User ID {$new_employee->user_id} as " . ($reuse_user ? 'REUSE' : 'NEW'));

                });

                $this->info("Number of rows after insert: " . Employee::count());

            }
        }

        if ($this->isActual) {
            $this->restartId(Employee::class);
        }
    }

    public function migrateSemesterSettings()
    {

        $this->info("Migration for semester settings");

        $data = $this->v1DbConnection->table('start_semester')
            ->orderBy('start_year', 'ASC')
            ->orderBy('start_semester', 'ASC')
            ->get();

        $this->info("Number of rows to be inserted: " . count($data));

        if ($this->isActual) {

            $this->info("== ACTUAL ==");

            $course = Course::where(DB::raw('name->>\'en\''), 'UEC')->firstOrFail();
            $years = SemesterYearSetting::all()->mapWithKeys(function ($item) {
                return [$item->year => $item];
            });

            DB::transaction(function () use (&$data, &$years, &$course) {

                foreach ($data as $d) {

                    // if year not found, ignore
                    if (!isset($years[$d->start_year])) {
                        continue;
                    }

                    $is_current = false;

                    if ($d->start_year == '2025' && $d->start_semester == '1') {
                        $is_current = true;
                    }

                    SemesterSetting::create([
                        'course_id' => $course->id,
                        'semester_year_setting_id' => $years[$d->start_year]->id,
                        'name' => $d->start_year . ' Sem ' . $d->start_semester,
                        'from' => $d->from_date,
                        'to' => $d->to_date,
                        'is_current_semester' => $is_current,
                    ]);
                }

            });

            $this->info("Number of rows after insert: " . SemesterSetting::count());

        }

    }


    public function migrateClass()
    {

        $this->info("Migration for classes");

        $data = $this->v1DbConnection->table('class_m')
            ->where('year', 2024)
            ->orderBy('grade_id')
            ->orderBy('class_code')
            ->get();

        $this->info("Number of rows to be inserted: " . count($data));

        if ($this->isActual) {

            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$data, &$years, &$course) {

                foreach ($data as $d) {

                    ClassModel::create([
                        'name' => ['en' => $d->class_english_name, 'zh' => $d->class_chinese_name],
                        'code' => $d->class_code,
                        'type' => ClassType::PRIMARY,
                        'grade_id' => $d->grade_id,
                        'is_active' => true,
                    ]);
                }

            });

            $this->info("Number of rows after insert: " . ClassModel::count());

        }

    }


    public function migrateSemesterClass()
    {

        $this->info("Migration for semester_classes");

        // need to migrate at least 6 years data
        for ($year = 2017; $year <= 2025; $year++) {

            $data = $this->v1DbConnection->table('class_m')
                ->where('year', $year)
                ->orderBy('grade_id')
                ->orderBy('class_code')
                ->get();

            $this->info("Number of rows to be inserted: " . count($data));

            if ($this->isActual) {

                $this->info("== ACTUAL ==");

                DB::transaction(function () use (&$data, &$year) {

                    $year_model = SemesterYearSetting::where('year', $year)->first();
                    $semester_settings = SemesterSetting::where('semester_year_setting_id', $year_model->id)->get();
                    $classes = ClassModel::all()->mapWithKeys(function ($item) {
                        return [$item->getTranslation('name', 'en') => $item];
                    });

                    foreach ($semester_settings as $semester_setting) {

                        foreach ($data as $d) {

                            if (!isset($classes[$d->class_english_name])) {
                                $class = ClassModel::create([
                                    'name' => ['en' => $d->class_english_name, 'zh' => $d->class_chinese_name],
                                    'code' => $d->class_code,
                                    'type' => ClassType::PRIMARY,
                                    'grade_id' => $d->grade_id,
                                    'is_active' => true,
                                ]);
                                $classes[$d->class_english_name] = $class;
                            } else {
                                $class = $classes[$d->class_english_name];
                            }

                            SemesterClass::create([
                                'semester_setting_id' => $semester_setting->id,
                                'class_id' => $class->id,
                                'homeroom_teacher_id' => $d->employee_id,
                                'is_active' => $semester_setting->name === '2025 Sem 1',
                                'default_grading_framework_id' => null,     // todo: implement mapping to default grading framework based on grade/stream
                            ]);
                        }
                    }


                });

                $this->info("Number of rows after insert: " . SemesterClass::count());

            }

        }

    }

    public function migrateStudentClasses()
    {

        $this->info("Migration for student classes");

        for ($year = 2017; $year <= 2025; $year++) {

            for ($sem = 1; $sem <= 2; $sem++) {

                $sem_name = $year . ' Sem ' . $sem;

                $this->info("Running for Sem {$sem_name}");

                $data = $this->v1DbConnection->table('student_class')
                    ->selectRaw('student_class.*, class_m.class_english_name AS class_name')
                    ->join('class_m', 'class_m.class_id', '=', 'student_class.class_id')
                    ->where('student_class.year', $year)
                    ->where('semester', $sem)
                    ->orderBy('grade_id')
                    ->orderBy('class_code')
                    ->get();

                $this->info("Number of rows to be inserted: " . count($data));

                if ($this->isActual) {

                    $this->info("== ACTUAL ==");

                    DB::transaction(function () use (&$data, &$year, &$sem, $sem_name) {

                        $year_model = SemesterYearSetting::where('year', $year)->first();
                        $semester_setting = SemesterSetting::where('semester_year_setting_id', $year_model->id)
                            ->where('name', $sem_name)
                            ->first();

                        $is_active = false;
                        if ($year == $this->year && $sem == $this->semester) {
                            $is_active = true;
                        }

                        $students = Student::selectRaw('students.id AS new_id, migration_mapping.old AS old_id')
                            ->leftJoin('migration_mapping', 'students.id', '=', DB::raw('migration_mapping.new::int'))
                            ->get();

                        $old_to_new_student_mapping = $students->mapWithKeys(function ($item) {
                            return [$item->old_id => $item->new_id];
                        });

                        $classes = ClassModel::all()->mapWithKeys(function ($item) {
                            return [$item->getTranslation('name', 'en') => $item];
                        });

                        $semester_classes = SemesterClass::all()->groupBy(['semester_setting_id', 'class_id']);

                        foreach ($data as $d) {

                            if (!isset($classes[$d->class_name])) {
                                $this->warn("Class {$d->class_name} not found. Skip");
                                continue;
                            }

                            // if not exists means the student is not an active student
                            if (!isset($old_to_new_student_mapping[$d->student_id])) {
                                $this->warn("Student not found for V1 student ID {$d->student_id}. Skip");
                                continue;
                            }

                            $this->info("Student class created for V1 student ID {$d->student_id} to {$d->class_name} with seat no {$d->seat_no}");

                            StudentClass::updateOrCreate([
                                'semester_setting_id' => $semester_setting->id,
                                'semester_class_id' => $semester_classes[$semester_setting->id][$classes[$d->class_name]->id][0]->id,
                                'class_type' => ClassType::PRIMARY,
                                'student_id' => $old_to_new_student_mapping[$d->student_id],
                            ], [
                                'seat_no' => $d->seat_no !== null ? trim($d->seat_no) : null,
                                'class_enter_date' => $d->date_enter,
                                'class_leave_date' => $d->date_leave,
                                'is_active' => $is_active
                            ]);
                        }

                    });

                    $this->info("Number of rows after insert: " . StudentClass::count());

                }

            }
        }


    }

    public function migrateStudentCards()
    {
        $this->info("Migration for student cards");

        $v1_data = $this->v1DbConnection->table('card_list')
            ->where('active', 1)
            ->whereNotNull('student_id')
            ->get();

        $this->info("Number of rows to be inserted: " . count($v1_data));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");
            $skip_count = 0;

            DB::transaction(function () use (&$v1_data, &$skip_count) {
                $students = Student::selectRaw('students.id AS new_id, migration_mapping.old AS old_id')
                    ->leftJoin('migration_mapping', 'students.id', '=', DB::raw('migration_mapping.new::int'))
                    ->where('migration_mapping.model', Student::class)
                    ->get();

                $old_to_new_student_mapping = $students->mapWithKeys(function ($item) {
                    return [$item->old_id => $item->new_id];
                });


                foreach ($v1_data as $d) {
                    // if not exists means the student is not an active student
                    if (!isset($old_to_new_student_mapping[$d->student_id])) {
                        $this->warn("Student not found for V1 student ID {$d->student_id}. Skip");
                        $skip_count++;
                        continue;
                    }

                    $this->info("Student card {$d->smart_card_id} created for student ID {$d->student_id}");

                    Card::create([
                        'userable_type' => Student::class,
                        'userable_id' => $old_to_new_student_mapping[$d->student_id],
                        'card_number' => $d->id1,
                        'card_number2' => $d->id2,
                        'card_number3' => $d->id3,
                        'card_type' => CardType::PROXIMITY,
                        'status' => CardStatus::ACTIVE,
                    ]);
                }

            });

            $this->info("Number of rows after insert: " . Card::count());
            $this->info("Number of rows skipped: " . $skip_count);
        }
    }

    public function migrateEmployeeCards()
    {
        $this->info("Migration for employee cards");

        $v1_data = $this->v1DbConnection->table('card_list')
            ->where('active', 1)
            ->whereNotNull('employee_id')
            ->get();

        $this->info("Number of rows to be inserted: " . count($v1_data));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$v1_data) {
                foreach ($v1_data as $d) {
                    $this->info("Employee card {$d->smart_card_id} created for employee ID {$d->employee_id}");

                    Card::create([
                        'userable_type' => Employee::class,
                        'userable_id' => $d->employee_id,
                        'card_number' => $d->id1,
                        'card_number2' => $d->id2,
                        'card_number3' => $d->id3,
                        'card_type' => CardType::PROXIMITY,
                        'status' => CardStatus::ACTIVE,
                    ]);
                }
            });

            $this->info("Number of rows after insert: " . Card::count());
        }
    }

    public function migrateLibraryOtherMember()
    {
        $this->info("Migration for library_members - others");

        $from_data = $this->v1DbConnection->table('member_m')
            ->where('member_type', 3)
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $old_others_ids = $from_data->pluck('user_id')->toArray();
        $new_others = DB::connection('smsv1')->table('othermember_m')
            ->whereIn('othermember_id', $old_others_ids)
            ->leftJoin('city_m', 'city_m.city_id', '=', 'othermember_m.city')
            ->get()
            ->keyBy('othermember_id');

        $states = State::with('country')
            ->get()
            ->keyBy('id');

        foreach ($from_data as $d) {
            if (!isset($new_others[$d->user_id])) {
                continue;
            }

            $new_member = json_decode(json_encode($new_others[$d->user_id]), 1);

            $ic_or_passport = $this->_determineIcPassportRegex($new_member['othermember_ic']);

            $address = $new_member['address_1'];

            if ($new_member['address_2']) {
                $address .= ', ' . $new_member['address_2'];
            }

            $to_insert[] = [
                'id' => $d->member_id,
                'type' => LibraryMemberType::OTHERS,
                'userable_type' => null,
                'userable_id' => null,
                'card_number' => null,
                'member_number' => $new_member['othermember_nomer'],
                'name' => json_encode(['en' => trim($new_member['othermember_name']), 'zh' => trim($new_member['othermember_cname'])]),
                'phone_number' => $this->_cleanPhoneNumber($new_member['telephone_1']),
                'email' => $this->_cleanEmail($new_member['othermember_email']),
                'gender' => $this->_determineGender($new_member['othermember_sex']),
                'nric' => $ic_or_passport['nric'],
                'passport_number' => $ic_or_passport['passport_number'],
                'is_librarian' => $d->librarian,
                'borrow_limit' => $d->book_borrow,
                'date_of_birth' => $this->_cleanDate($new_member['othermember_dob']),
                'religion_id' => $new_member['religion'],
                'race_id' => $new_member['race_id'],
                'address' => $address,
                'postcode' => $new_member['postcode'],
                'city' => $new_member['city_name'],
                'state_id' => $new_member['state_id'] ?: null,
                'country_id' => $new_member['state_id'] ? $states[$new_member['state_id']]->country->id : null,
                'register_date' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_from' => $this->_cleanDate($d->member_date) ?: Carbon::parse('2025-01-01')->toDateString(),
                'valid_to' => $this->_cleanDate($d->expired_date) ?: Carbon::parse('2099-01-01')->toDateString(),
                'is_active' => is_null($d->deleted_at),
                'created_at' => now(),
            ];
        }


        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use (&$to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    LibraryMember::insert($data);
                }

                $this->restartId(LibraryMember::class);
            });

            $this->info("Number of rows after insert: " . LibraryMember::count());
        }
    }

    public function migrateClubCategory()
    {
        $this->info("Migration for club_categories");

        $from_data = $this->v1DbConnection->table('club_category')->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->club_category_id,
                'name' => json_encode(['en' => trim($d->club_category_name), 'zh' => trim($d->club_category_cname)]),
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClubCategory::class, $to_insert);
    }

    public function migrateClubClass()
    {
        $this->info("Create class for club");

        $from_data = $this->v1DbConnection->table('club_m')->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'code' => $d->club_code,
                'name' => json_encode(['en' => trim($d->club_name), 'zh' => trim($d->club_cname)]),
                'type' => ClassType::SOCIETY,
                'is_active' => true,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClassModel::class, $to_insert);
    }

    public function migrateClub()
    {
        $this->info("Migration for clubs");

        $from_data = $this->v1DbConnection->table('club_m')->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->club_id,
                'code' => $d->club_code,
                'name' => json_encode(['en' => trim($d->club_name), 'zh' => trim($d->club_cname)]),
                'club_category_id' => $d->club_category_id,
                'created_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(Club::class, $to_insert);
    }

    public function migrateClubSubject()
    {
        $this->info("Update subject ID 21 as co-cu");

        $this->info("FOUND: " . Subject::where('id', 21)->first()->name);

        if ($this->isActual) {
            // subject ID 21 from SMS V1 is co-cu
            Subject::where('id', 21)
                ->update([
                    'type' => SubjectType::COCURRICULUM->value,
                    'code' => 'COCURRICULUM'
                ]);
        }

    }

    public function migrateClubClassSubject()
    {
        $this->info("Migration for club class_subject");

        $semesters = SemesterSetting::all();

//        $club_codes = $this->v1DbConnection->table('club_m')->pluck('club_code')->toArray();
        $classes = ClassModel::where('type', ClassType::SOCIETY)->get();
        $society_subject = Subject::where('code', SubjectType::COCURRICULUM->value)->firstOrFail();

        $count = 0;

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($semesters, $classes, $society_subject, &$count) {
                foreach ($semesters as $semester) {
                    foreach ($classes as $class) {
                        $semester_class_id = SemesterClass::insertGetId([
                            'semester_setting_id' => $semester->id,
                            'class_id' => $class->id,
                            'is_active' => true
                        ]);

                        ClassSubject::create([
                            'semester_class_id' => $semester_class_id,
                            'subject_id' => $society_subject->id,
                            'number_of_period_per_week' => 2,
                        ]);

                        $count++;
                    }
                }
            });

            $this->info("Number of rows to be inserted: " . $count);
        }
    }

    public function migrateClubClassSubjectStudent()
    {
        $this->info("Migration for club class_subject_student");

        $from_data = $this->v1DbConnection->table('student_club')
            ->select(['student_club.*', 'club_m.club_code'])
            ->join('club_m', 'club_m.club_id', '=', 'student_club.club_id')
            ->where('year', '>=', $this->migrationStartYear)
            ->get();

        $class_subjects = ClassSubject::query()
            ->with('semesterClass.semesterSetting', 'semesterClass.classModel', 'subject')
            ->whereHas('subject', function ($query) {
                $query->where('type', SubjectType::COCURRICULUM->value);
            })
            ->get();

        $old_student_ids = array_unique($from_data->pluck('student_id')->toArray());

        $new_student_ids = $this->_getStudentOldNewIds($old_student_ids);

        $class_subject_data = [];

        foreach ($class_subjects as $class_subject) {
            $class_subject_data[$class_subject->semesterClass->semesterSetting->name][$class_subject->semesterClass->classModel->code] = $class_subject->id;
        }

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $semester_name = $d->year . ' Sem ' . $d->semester;

            if (!isset($new_student_ids[$d->student_id])) {
                continue;
            }

            if (!isset($class_subject_data[$semester_name][$d->club_code])) {
                continue;
            }

            $to_insert[] = [
                'student_id' => $new_student_ids[$d->student_id],
                'class_subject_id' => $class_subject_data[$semester_name][$d->club_code],
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClassSubjectStudent::class, $to_insert, 0);
    }

    public function migrateClubStudentClass()
    {
        $this->info("Migration for club student_classes");

        $class_subject_students = ClassSubjectStudent::with('student', 'classSubject.semesterClass', 'classSubject.subject', 'classSubject.semesterClass.classModel')
            ->whereHas('classSubject.subject', function ($query) {
                $query->where('type', SubjectType::COCURRICULUM->value);
            })
            ->get();

        $club_insert_dates = $this->v1DbConnection->table('club_m')->pluck('insert_date', 'club_code')->toArray();

        $order_counter = count($class_subject_students);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($class_subject_students as $class_subject_student) {
            $enter_date = $this->_cleanDate($club_insert_dates[$class_subject_student->classSubject->semesterClass->classModel->code]);

            $to_insert[] = [
                'student_id' => $class_subject_student->student_id,
                'semester_setting_id' => $class_subject_student->classSubject->semesterClass->semester_setting_id,
                'semester_class_id' => $class_subject_student->classSubject->semesterClass->id,
                'class_type' => ClassType::SOCIETY,
                'class_enter_date' => $enter_date ?: now()->toDateTimeString(),
                'class_leave_date' => $class_subject_student->student->leave_date,
                'is_active' => $class_subject_student->student->is_active,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(StudentClass::class, $to_insert, 0);
    }

    public function migrateSubject()
    {
        $this->info("Create subject");

        $from_data = $this->v1DbConnection->table('course_m')
            ->select(DB::raw('course_category_m.category_course_english_name as type'), 'course_id', 'course_code', 'course_english_name', 'course_chinese_name')
            ->join('course_category_m', 'course_m.category_course_id', 'course_category_m.category_course_id')
            ->whereIn('course_category_m.category_course_english_name', ['MAJOR', 'ELECTIVE'])
            ->orderBy('course_id')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            //Change duplicate code to unique
            $code = $this->_determineSubjectCode($d->course_code, $d->course_id);

            $to_insert[] = [
                'id' => $d->course_id,
                'code' => $code,
                'name' => json_encode(['en' => trim($d->course_english_name), 'zh' => trim($d->course_chinese_name)]),
                'type' => trim($d->type),
                'sequence' => $order_counter--,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(Subject::class, $to_insert);
    }

    public function migrateClassSubject()
    {
        $this->info("Migration for class_subject");

        $subjects = Subject::whereIn('type', [SubjectType::MAJOR->value, SubjectType::ELECTIVE->value])
            ->pluck('id', 'code')->toArray();

        $semesterYears = SemesterYearSetting::with('semesterSettings')
            ->where('year', '>=', '2024')
            ->get();

        $years = [];

        foreach ($semesterYears as $semesterYear) {
            $years[$semesterYear->year] = $semesterYear->semesterSettings->pluck('id')->toArray();
        }

        //only need record start from 2024 as class also get 2024 only
        $assign_courses = $this->v1DbConnection->table('assign_course')
            ->select(
                DB::raw('course_m.course_id as course_id'),
                DB::raw('course_m.course_code as course_code'),
                DB::raw('class_m.class_code as class_code'),
                DB::raw('assign_course.year as year'),
                DB::raw('assign_course.employee_id as employee_id'),
                'no_periode_week'
            )
            ->join('class_m', 'assign_course.class_id', 'class_m.class_id')
            ->join('course_m', 'assign_course.course_id', 'course_m.course_id')
            ->join('course_category_m', 'course_m.category_course_id', 'course_category_m.category_course_id')
            ->whereIn('course_m.course_id', array_values($subjects))
            ->where('assign_course.year', '>=', '2024');

        $class_codes = $assign_courses->clone()->pluck('class_code');

        $classes = ClassModel::query()->whereIn('code', $class_codes)->pluck('id', 'code')->toArray();

        $count = 0;

        if ($this->isActual) {
            $this->info("== ACTUAL ==");
        }

        $class_subject_insert_count = 0;
        $class_subject_teacher_insert_count = 0;

        $actual_class_subject_insert_count = 0;
        $actual_class_subject_teacher_insert_count = 0;

        DB::transaction(function () use ($assign_courses, $years, $classes, $subjects, &$class_subject_insert_count, &$class_subject_teacher_insert_count, &$actual_class_subject_insert_count, &$actual_class_subject_teacher_insert_count) {


            $semester_classes = SemesterClass::whereIn('semester_setting_id', array_values($years))
                ->get()
                ->groupBy('semester_setting_id')
                ->map(function ($semester_class) {
                    return $semester_class->keyBy('class_id');
                });

            foreach ($assign_courses->get() as $assign_course) {
                $semester_ids = $years[$assign_course->year];

                $class_id = $classes[$assign_course->class_code];

                foreach ($semester_ids as $semester_id) {
                    $semester_class = $semester_classes[$semester_id][$class_id];

                    $class_subject_insert_count++;
                    $class_subject_teacher_insert_count++;

                    if ($this->isActual) {
                        $class_subject_id = ClassSubject::insertGetId([
                            'semester_class_id' => $semester_class->id,
                            'subject_id' => $assign_course->course_id,
                            'number_of_period_per_week' => $assign_course->no_periode_week,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        ClassSubjectTeacher::create([
                            'class_subject_id' => $class_subject_id,
                            'employee_id' => $assign_course->employee_id,
                            'type' => ClassSubjectTeacherType::PRIMARY,
                        ]);

                        $actual_class_subject_insert_count++;
                        $actual_class_subject_teacher_insert_count++;
                    }
                }
            }
        });

        $this->info("Number of rows to be inserted to [class_subjects]: " . $class_subject_insert_count);
        $this->info("Number of rows to be inserted to [class_subject_teacher]: " . $class_subject_teacher_insert_count);

        $this->info("Actual number of rows inserted [class_subjects]: " . $actual_class_subject_insert_count);
        $this->info("Actual number of rows inserted [class_subject_teacher]: " . $actual_class_subject_teacher_insert_count);
    }

    public function migrateStudentClassSubject()
    {
        $this->info("Migration for class_subject_student");

        $semester_classes = SemesterClass::withWhereHas('classSubjects.subject', function ($query) {
            $query->whereIn('type', [SubjectType::MAJOR->value, SubjectType::ELECTIVE->value]);
        })
            ->with('studentClasses')
            ->get();

        $to_insert = [];
        foreach ($semester_classes as $semester_class) {
            $student_ids = $semester_class->studentClasses?->pluck('student_id')->toArray();
            $class_subject_ids = $semester_class->classSubjects?->pluck('id')->toArray();

            foreach ($student_ids as $student_id) {
                foreach ($class_subject_ids as $class_subject_id) {
                    $to_insert[] = [
                        'student_id' => $student_id,
                        'class_subject_id' => $class_subject_id,
                    ];
                }
            }
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClassSubjectStudent::class, $to_insert, 0);

    }

    public function migrateContractor()
    {
        $this->info("Migration for contractors");

        $from_data = $this->v1DbConnection->table('trainer_m')
            ->where('status', 1)
            ->get();

        $order_counter = count($from_data);

        $this->info("Number of rows: " . $order_counter);

        if (!$this->isActual) {
            return;
        }

        $this->info("== ACTUAL ==");

        $count = 0;

        DB::transaction(function () use ($from_data, $order_counter, &$count) {
            $this->restartId(User::class);
            $this->restartId(Contractor::class, 1);
            $this->restartId(Card::class);
            foreach ($from_data as $d) {
                $email = "contractor-{$d->trainer_id}@gmail.com";
                $user = User::create([
                    'email' => $email,
                    'password' => Hash::make($email)
                ]);

                $contractor = Contractor::create([
                    'id' => $d->trainer_id,
                    'user_id' => $user->id,
                    'email' => $email,
                    'phone_number' => '+60300000000',
                    'contractor_number' => $d->trainer_no,
                    'department' => ContractorDepartment::COCURRICULUM,
                    'status' => ContractorStatus::ACTIVE,
                    'name' => ['en' => trim($d->trainer_name), 'zh' => trim($d->trainer_cname)],
                    'employed_date' => $d->employed_date,
                    'resignation_date' => null
                ]);

                $card = Card::create([
                    'userable_type' => Contractor::class,
                    'userable_id' => $contractor->id,
                    'name' => null,
                    'card_number' => $d->id1,
                    'card_number2' => $d->id2,
                    'card_number3' => $d->id3,
                    'card_type' => CardType::PROXIMITY,
                    'status' => CardStatus::ACTIVE,
                ]);

                $count++;

                $this->info("Created contractor {$contractor->contractor_number} with User ID {$contractor->user_id} and card number {$card->card_number}");
                $this->info("Number of rows (Contractors) after insert: " . $count . '/' . $order_counter);
            }

            $this->restartId(Contractor::class);
        });
    }

    public function migrateClassSubjectContractor()
    {
        $this->info("Migration for class_subject_contractor");

        $from_data = $this->v1DbConnection->table('club_soceity_trainer')
            ->select(
                DB::raw('club_m.club_code as club_code'),
                DB::raw('club_soceity_trainer.trainer_name as trainer_id'),
                DB::raw('t.trainer_cname as trainer_cname'),
                DB::raw('club_soceity_setting.year as year')
            )
            ->join('club_soceity_setting', 'club_soceity_setting.club_soceity_id', 'club_soceity_trainer.club_soceity_id')
            ->join('club_m', 'club_soceity_setting.club_id', 'club_m.club_id')
            ->join('trainer_m as t', 't.trainer_id', 'club_soceity_trainer.trainer_name')
            ->where('club_soceity_setting.year', '>=', '2024')
            ->get();

        $contractors = Contractor::select(['name', 'id'])->get()->map(function ($item) {
            $item->name = $item->getTranslation('name', 'zh');
            return $item;
        })
            ->pluck('id', 'name');

        $cocurriculum_subject = Subject::query()
            ->where('type', SubjectType::COCURRICULUM)
            ->where('code', 'COCURRICULUM')
            ->first();

        $class_subjects_data = [];
        $class_subjects = ClassSubject::query()
            ->with('semesterClass.classModel', 'semesterClass.semesterSetting.semesterYearSetting')
            ->where('subject_id', $cocurriculum_subject->id)
            ->get();

        foreach ($class_subjects as $class_subject) {
            $class_code = $class_subject->semesterClass->classModel->code;
            $year = $class_subject->semesterClass->semesterSetting->semesterYearSetting->year;
            $class_subjects_data[$class_code][$year][] = $class_subject->id;
        }

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $contractor_id = $contractors[$d->trainer_cname] ?? null;
            if (!$contractor_id) {
                continue;
            }
            $class_subject_ids = $class_subjects_data[$d->club_code][$d->year];

            foreach ($class_subject_ids as $class_subject_id) {
                $to_insert[] = [
                    'contractor_id' => $contractor_id,
                    'class_subject_id' => $class_subject_id
                ];
            }
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClassSubjectContractor::class, $to_insert, 0);
    }

    public function migrateCocoClassSubjectTeacher()
    {
        $this->info("Migration for class_subject_teacher (coco)");

        $from_data = $this->v1DbConnection->table('club_soceity_instructor as csi')
            ->select(
                DB::raw('c.club_code as club_code'),
                DB::raw('csi.employee_id as employee_id'),
                DB::raw('css.year as year')
            )
            ->join('club_soceity_setting as css', 'css.club_soceity_id', 'csi.club_soceity_id')
            ->join('club_m as c', 'css.club_id', 'c.club_id')
            ->where('css.year', '>=', '2024')
            ->get();

        $class_subjects_data = [];
        $class_subjects = ClassSubject::query()
            ->with('semesterClass.classModel', 'semesterClass.semesterSetting.semesterYearSetting')
            ->whereRelation('subject', 'code', 'COCURRICULUM')
            ->get();

        foreach ($class_subjects as $class_subject) {
            $class_code = $class_subject->semesterClass->classModel->code;
            $year = $class_subject->semesterClass->semesterSetting->semesterYearSetting->year;

            $class_subjects_data[$class_code][$year][] = $class_subject->id;
        }

        $existing_employee_ids = Employee::all()->pluck('id')->toArray();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            if (!in_array($d->employee_id, $existing_employee_ids)) {
                continue;
            }
            $class_subject_ids = $class_subjects_data[$d->club_code][$d->year];

            foreach ($class_subject_ids as $class_subject_id) {
                $to_insert[] = [
                    'employee_id' => $d->employee_id,
                    'class_subject_id' => $class_subject_id,
                    'type' => ClassSubjectTeacherType::SECONDARY
                ];
            }
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ClassSubjectTeacher::class, $to_insert, 0);
    }


    public function migratePrimarySchool()
    {
        $this->info("Migration for master_schools");

        $from_data = $this->v1DbConnection->table('primary_m')->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->primary_id,
                'name' => json_encode(['en' => $d->primary_name, 'zh' => $d->primary_cname]),
                'level' => SchoolLevel::PRIMARY,
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(School::class, $to_insert);
    }

    public function migrateSchoolProfile()
    {
        $this->info("Migration for master_schools");

        $from_data = $this->v1DbConnection->table('school_profile_m')
            ->select('school_profile_m.*', 'city_m.city_name')
            ->join('city_m', 'city_m.city_id', '=', 'school_profile_m.city_id')
            ->first();

        SchoolProfile::truncate();

        if ($this->isActual) {
            $this->info("== ACTUAL ==");
            SchoolProfile::create([
                'name' => [
                    'en' => $from_data->school_name,
                    'zh' => $from_data->school_cname
                ],
                'code' => $from_data->school_noreg,
                'short_name' => $from_data->school_shortname,
                'address' => $from_data->school_address,
                'country_id' => Country::where(DB::raw('name->>\'en\''), 'Malaysia')->first()->id,
                'state_id' => 1,
                'city' => $from_data->city_name,
                'postcode' => $from_data->school_postcode,
                'phone_1' => '+' . trim($from_data->school_telp),
                'phone_2' => '+' . trim($from_data->school_phone),
                'fax_1' => $from_data->school_fax,
                'email' => $from_data->school_email,
                'url' => $from_data->school_homepage,
            ]);
        }
    }

    public function migrateHostelRewardPunishmentRecords()
    {
        $this->info("Migration for hostel_reward_punishment_records");

        $employee_user = Employee::where('employee_number', Employee::SYSTEM_ID)->first()->user;

        $from_data = $this->v1DbConnection
            ->table('hostel_reward_punishment_entry')
            ->select('hostel_reward_punishment_entry.*', 'employee_id', 'student_id')
            ->join('student_board', 'hostel_reward_punishment_entry.student_board_id', '=', 'student_board.student_board_id')
            ->join('warden_settings', 'warden_settings.warden_id', '=', 'hostel_reward_punishment_entry.warden_hostel_id')
            ->where('hostel_reward_punishment_entry.date', '>=', '2024-01-01')
            ->get();

        $old_student_ids = $from_data->pluck('student_id');

        $new_students = MigrationMapping::query()
            ->where('model', Student::class)
            ->whereIn('old', $old_student_ids)
            ->get()
            ->pluck('new', 'old')
            ->toArray();

        $to_insert = [];

        foreach ($from_data as $d) {
            if (!isset($new_students[$d->student_id])) {
                continue;
            }

            $to_insert[] = [
                'person_in_charge_id' => $d->employee_id,
                'student_id' => $new_students[$d->student_id],
                'date' => $this->_cleanDate($d->date),
                'hostel_reward_punishment_setting_id' => $d->reward_punishment_hostel_id,
                'remark' => $d->remark,
                'created_by' => $employee_user->id,
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($to_insert) {
                foreach (array_chunk($to_insert, 500) as $data) {
                    HostelRewardPunishmentRecord::insert($data);
                }
            });

            $this->info("Number of rows after insert: " . HostelRewardPunishmentRecord::count());
        }
    }

    public function migrateCompetition()
    {
        $department_others = Department::query()->firstOrCreate([
            'code' => 'OTHERS',
        ], [
            'name' => ['en' => 'OTHERS', 'zh' => "其他"]
        ]);

        $from_data = $this->v1DbConnection->table('student_performance')
            ->select(
                DB::raw('item_m.item_id as item_id'),
                DB::raw('item_m.item_name as item_name'),
                DB::raw('student_performance.date as date')
            )
            ->join('item_m', 'item_m.item_id', 'student_performance.item_id')
            ->where('student_performance.date', '>=', '2024-01-01')
            ->groupBy('date', 'item_m.item_id', 'item_m.item_name')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'name' => $d->item_name,
                'department_id' => $department_others->id,
                'date' => $d->date,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(Competition::class, $to_insert);
    }

    public function migrateCompetitionRecord()
    {
        $award_others = Award::firstOrCreate([
            'name->en' => 'OTHERS',
        ], [
            'name' => ['en' => 'OTHERS', 'zh' => "其他"],
            'sequence' => 0,
            'is_active' => 1
        ]);

        $from_data = $this->v1DbConnection->table('student_performance')
            ->select(
                DB::raw('class_m.class_english_name as class_name'),
                DB::raw('item_m.item_name as item_name'),
                DB::raw('student_performance.year as year'),
                DB::raw('student_performance.semester as semester'),
                DB::raw('student_performance.date as date'),
                DB::raw('student_performance.student_id as student_id'),
                DB::raw('student_performance.mark as mark'),
                DB::raw('student_performance.type_of_bonus as type_of_bonus')
            )
            ->join('item_m', 'item_m.item_id', 'student_performance.item_id')
            ->join('class_m', 'class_m.class_id', 'student_performance.class_id')
            ->where('student_performance.date', '>=', '2024-01-01')
            ->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $competitions = Competition::all();
        $competition_data = [];

        foreach ($competitions as $competition) {
            $competition_data[$competition->date][$competition->name] = $competition->id;
        }

        $classes = ClassModel::query()->where('type', ClassType::PRIMARY)->get()->pluck('id', 'name');

        $semester_classes = SemesterClass::all()->groupBy(['semester_setting_id', 'class_id']);

        $semester_settings = SemesterSetting::all()->pluck('id', 'name');

        $old_student_ids = array_unique($from_data->pluck('student_id')->toArray());

        $new_student_ids = $this->_getStudentOldNewIds($old_student_ids);

        foreach ($from_data as $d) {
            if (!isset($new_student_ids[$d->student_id])) {
                continue;
            }

            $semester_name = $d->year . ' Sem ' . $d->semester;
            $semester_id = $semester_settings[$semester_name];
            $class_id = $classes[$d->class_name];

            $to_insert[] = [
                'competition_id' => $competition_data[$d->date][$d->item_name],
                'semester_class_id' => $semester_classes[$semester_id][$class_id][0]->id,
                'award_id' => $award_others->id,
                'mark' => $d->mark,
                'type_of_bonus' => $this->_determineTypeOfBonus($d->type_of_bonus),
                'student_id' => $new_student_ids[$d->student_id],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(CompetitionRecord::class, $to_insert);
    }

    public function migrateSocietyPosition()
    {
        $this->info("Migration for society_positions");

        $from_data = $this->v1DbConnection->table('executive_committe_setting')->get();

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->committe_setting_id,
                'code' => $d->committe_setting_code,
                'name' => json_encode(['en' => trim($d->description_name), 'zh' => trim($d->description_cname)]),
                'is_active' => true,
                'created_at' => now()
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(SocietyPosition::class, $to_insert);
    }

    public function migrateStudentSocietyPosition()
    {
        $this->info("Migration for student_society_positions");

        $class_subjects = ClassSubject::query()
            ->with('semesterClass.semesterSetting', 'semesterClass.classModel', 'subject')
            ->whereHas('subject', function ($query) {
                $query->where('type', SubjectType::COCURRICULUM->value);
            })
            ->get();

        $class_subject_data = [];

        foreach ($class_subjects as $class_subject) {
            $class_subject_data[$class_subject->semesterClass->semesterSetting->name][$class_subject->semesterClass->classModel->code] = $class_subject->semester_class_id;
        }

        $from_data = $this->v1DbConnection->table('member_category')
            ->select(
                DB::raw('student_club.student_id as student_id'),
                DB::raw('student_club.club_id as club_id'),
                DB::raw('member_category.year as year'),
                DB::raw('member_category.semester as semester'),
                DB::raw('member_category.committe_setting_id as committe_setting_id'),
                DB::raw('club_m.club_code as club_code')
            )
            ->join('student_club', 'student_club.student_club_id', '=', 'member_category.student_club_id')
            ->join('club_m', 'club_m.club_id', '=', 'student_club.club_id')
            ->get();

        $old_student_ids = array_unique($from_data->pluck('student_id')->toArray());

        $new_student_ids = $this->_getStudentOldNewIds($old_student_ids);

        $order_counter = count($from_data);

        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            if (!isset($new_student_ids[$d->student_id])) {
                continue;
            }
            $semester_name = $d->year . ' Sem ' . $d->semester;

            $to_insert[] = [
                'student_id' => $new_student_ids[$d->student_id],
                'semester_class_id' => $class_subject_data[$semester_name][$d->club_code],
                'society_position_id' => $d->committe_setting_id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(StudentSocietyPosition::class, $to_insert);
    }

    public function migrateComprehensiveAssessmentCategories()
    {
        $this->info("Migration for comprehensive_assessment_categories");

        $from_data = $this->v1DbConnection->table('comprehensive_quality_group')
            ->orderBy('sort_order', 'ASC')->get();
        $order_counter = count($from_data);
        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->id,
                'name' => json_encode(['en' => $d->group_desc, 'zh' => $d->group_cdesc]),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ComprehensiveAssessmentCategory::class, $to_insert);
    }

    public function migrateComprehensiveAssessmentQuestions()
    {
        $this->info("Migration for comprehensive_assessment_questions");

        $from_data = $this->v1DbConnection->table('comprehensive_quality_item')
            ->orderBy('sort_order', 'ASC')->get();
        $order_counter = count($from_data);
        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->id,
                'comprehensive_assessment_category_id' => $d->group_id,
                'question' => json_encode(['en' => $d->group_desc, 'zh' => $d->group_cdesc]),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(ComprehensiveAssessmentQuestion::class, $to_insert);
    }

    public function migrateMeritDemeritSettings()
    {
        $this->info("Migration for merit_demerit_settings");

        $from_data = $this->v1DbConnection->table('setting_merit_demerit_m')->get();

        $to_insert = [];

        foreach ($from_data as $d) {
            $to_insert[] = [
                'id' => $d->setting_merit_demerit_id,
                'name' => json_encode([
                    'en' => $d->item,
                    'zh' => $d->item
                ]),
                'type' => $d->merit_demerit_status == 1 ? MeritDemeritType::MERIT : MeritDemeritType::DEMERIT,
                'average_exam_marks' => $d->avg_bonus_point > 0 ? $d->avg_bonus_point : -$d->avg_deduction_point,
                'conduct_marks' => $d->conduct_bonus_point > 0 ? $d->conduct_bonus_point : -$d->conduct_deduction_point,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));
        $this->_insertData(MeritDemeritSetting::class, $to_insert);
    }

    public function migrateRewardPunishments()
    {
        $this->info("Migration for reward_punishments");

        $all_category = RewardPunishmentCategory::firstOrCreate(
            ['name->en' => 'ALL',],
            ['name' => ['en' => 'ALL', 'zh' => "全部"]]
        );

        $from_data = $this->v1DbConnection->table('trans_reward_punishment')->get();
        $merit_demerit_data = $this->v1DbConnection->table('setting_merit_demerit_m')->get();

        $to_insert = [];

        foreach ($from_data as $d) {
            $merit_demerit = $merit_demerit_data->where('setting_merit_demerit_id', $d->setting_merit_demerit_id)->first();
            $to_insert[] = [
                'id' => $d->trans_reward_punishment_id,
                'name' => json_encode([
                    'en' => $d->reward_punisment,
                    'zh' => $d->reward_punisment
                ]),
                'category_id' => $all_category->id,
                'average_exam_marks' => $merit_demerit->avg_bonus_point > 0 ? $merit_demerit->avg_bonus_point : -$merit_demerit->avg_deduction_point,
                'conduct_marks' => $merit_demerit->conduct_bonus_point > 0 ? $merit_demerit->conduct_bonus_point : -$merit_demerit->conduct_deduction_point,
                'display_in_report_card' => $merit_demerit->show_in_reportcard == 1 ? true : false,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));
        $this->_insertData(RewardPunishment::class, $to_insert);
    }

    public function migrateMeritDemeritRewardPunishments()
    {
        $this->info("Migration for merit_demerit_reward_punishments");

        $from_data = $this->v1DbConnection->table('trans_reward_punishment')->get();

        foreach ($from_data as $d) {
            $to_insert[] = [
                'merit_demerit_id' => $d->setting_merit_demerit_id,
                'reward_punishment_id' => $d->trans_reward_punishment_id
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));
        $this->_insertData(MeritDemeritRewardPunishment::class, $to_insert, 0);
    }

    public function migrateRewardPunishmentRecords()
    {
        $this->info("Migration for reward_punishment_records");

        $from_data = $this->v1DbConnection->table('trans_student_reward_punisment')
            ->where('date_of_sign', '>=', '2024-01-01')
            ->get();

        $merit_demerit_data = $this->v1DbConnection->table('setting_merit_demerit_m')->get();

        $to_insert = [];

        $old_student_ids = $from_data->pluck('student_id');

        $new_students = MigrationMapping::query()
            ->where('model', Student::class)
            ->whereIn('old', $old_student_ids)
            ->get()
            ->pluck('new', 'old')
            ->toArray();

        foreach ($from_data as $d) {
            if (!isset($new_students[$d->student_id])) {
                continue;
            }

            $merit_demerit = $merit_demerit_data->where('setting_merit_demerit_id', $d->setting_merit_demerit_id)->first();

            $to_insert[] = [
                'date' => $d->date,
                'student_id' => $new_students[$d->student_id],
                'reward_punishment_id' => $d->trans_reward_punishment_id,
                'average_exam_marks' => $merit_demerit->avg_bonus_point > 0 ? $merit_demerit->avg_bonus_point : -$merit_demerit->avg_deduction_point,
                'conduct_marks' => $merit_demerit->conduct_bonus_point > 0 ? $merit_demerit->conduct_bonus_point : -$merit_demerit->conduct_deduction_point,
                'display_in_report_card' => $merit_demerit->show_in_reportcard == 1 ? true : false,
                'status' => RewardPunishmentRecordStatus::POSTED->value,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));
        $this->_insertData(RewardPunishmentRecord::class, $to_insert);
    }

    public function migrateGradingWithGradingSchemeItem()
    {
        $this->info("Migration for master_conduct_gradings and master_conduct_grading_scheme_items");

        $from_data = $this->v1DbConnection->table('conduct_m')
            ->where('conduct_year', '=', '2024')
            ->where('conduct_semester', '=', '2')
            ->get();

        $order_counter = count($from_data);

        $this->info("Number of rows: " . $order_counter);

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($from_data) {

                $grading_scheme = GradingScheme::create([
                    'type' => GradingSchemeType::CONDUCT->value,
                    'code' => 'CONDUCT',
                    'name' => 'Default Conduct Grading Scheme',
                    'is_active' => true,
                ]);

                $grading_scheme_items = [];

                foreach ($from_data as $d) {
                    $grading_scheme_items[] = [
                        'grading_scheme_id' => $grading_scheme->id,
                        'name' => $d->conduct_grade,
                        'display_as_name' => $d->conduct_grade,
                        'from' => $d->conduct_from,
                        'to' => $d->conduct_to,
                        'extra_marks' => $d->conduct_range,
                        'created_at' => now()->toDateTimeString(),
                        'updated_at' => now()->toDateTimeString(),
                    ];
                }

                GradingSchemeItem::insert($grading_scheme_items);

                $this->info("Number of grading_schemes inserted: 1");
                $this->info("Number of grading_scheme_items inserted: " . count($grading_scheme_items));
            });
        }
    }

    public function migrateConductSettingWithConductSettingTeacher()
    {
        $this->info("Migration for conduct_settings and conduct_setting_teachers");

        $from_data = $this->v1DbConnection->table('trans_teacher_conduct_rating')
            ->select(
                DB::raw('class_m.class_english_name as class_name'),
                DB::raw('class_m.employee_id as class_employee_id'),
                DB::raw('trans_teacher_conduct_rating.*'),
            )
            ->join('class_m', 'class_m.class_id', 'trans_teacher_conduct_rating.class_id')
            ->where('trans_teacher_conduct_rating.year', '=', '2024')
            ->where('trans_teacher_conduct_rating.semester', '=', '2')
            ->get();

        $order_counter = count($from_data);

        $this->info("Number of rows: " . $order_counter);

        $grouped_data = $from_data->groupBy('class_name');

        $semester_setting = SemesterSetting::where('name', '2024 Sem 2')->first();

        $classes = ClassModel::where('type', ClassType::PRIMARY)->get()->pluck('id', 'name');

        $semester_classes = SemesterClass::where('semester_setting_id', $semester_setting->id)->pluck('id', 'class_id');

        $grading_scheme = GradingScheme::where('type', GradingSchemeType::CONDUCT->value)
            ->where('code', 'CONDUCT')
            ->first();

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($grouped_data, $semester_setting, $grading_scheme, $classes, $semester_classes) {
                $conduct_setting_teachers = [];

                foreach ($grouped_data as $class_name => $data) {
                    $semester_class_id = $semester_classes->get($classes[$class_name]);

                    $conduct_setting = ConductSetting::firstOrCreate([
                        'semester_class_id' => $semester_class_id,
                        'semester_setting_id' => $semester_setting->id,
                        'grading_scheme_id' => $grading_scheme->id,
                    ]);

                    if ($data->first()->class_employee_id) {
                        $conduct_setting_teachers[] = [
                            'conduct_setting_id' => $conduct_setting->id,
                            'employee_id' => $data->first()->class_employee_id,
                            'is_homeroom_teacher' => true,
                            'is_active' => true,
                            'created_at' => now()->toDateTimeString(),
                            'updated_at' => now()->toDateTimeString(),
                        ];
                    }

                    foreach ($data as $d) {
                        if ($data->first()->class_employee_id == $d->employee_id) {
                            continue;
                        }
                        $conduct_setting_teachers[] = [
                            'conduct_setting_id' => $conduct_setting->id,
                            'employee_id' => $d->employee_id,
                            'is_homeroom_teacher' => false,
                            'is_active' => true,
                            'created_at' => now()->toDateTimeString(),
                            'updated_at' => now()->toDateTimeString(),
                        ];
                    }
                }

                ConductSettingTeacher::insert($conduct_setting_teachers);

                $this->info("Number of ConductSettings inserted: " . count($grouped_data));
                $this->info("Number of ConductSettingTeachers inserted: " . count($conduct_setting_teachers));
            });
        }
    }

    public function migrateConductRecord()
    {
        $this->info("Migration for conduct_records");

        $from_data = $this->v1DbConnection->table('trans_conduct_mark')
            ->select(
                DB::raw('class_m.class_english_name as class_name'),
                DB::raw('trans_conduct_mark.*'),
            )
            ->join('class_m', 'class_m.class_id', 'trans_conduct_mark.class_id')
            ->where('trans_conduct_mark.year', '=', '2024')
            ->where('trans_conduct_mark.semester', '=', '2')
            ->get();

        $order_counter = count($from_data);

        $this->info("Number of rows: " . $order_counter);

        $old_student_ids = array_unique($from_data->pluck('student_id')->toArray());
        $new_student_ids = $this->_getStudentOldNewIds($old_student_ids);

        $semester_setting = SemesterSetting::where('name', '2024 Sem 2')->first();

        $classes = ClassModel::where('type', ClassType::PRIMARY)->get()->pluck('id', 'name');

        $semester_classes = SemesterClass::where('semester_setting_id', $semester_setting->id)->get()->pluck('id', 'class_id');

        $conduct_settings = ConductSetting::with('conductSettingTeachers')
            ->where('semester_setting_id', $semester_setting->id)
            ->get()
            ->keyBy(function ($conduct_setting) {
                return $conduct_setting->semester_setting_id . '//' . $conduct_setting->semester_class_id;
            });

        $grouped_data = $from_data->groupBy('class_name');

        if ($this->isActual) {
            $this->info("== ACTUAL ==");

            DB::transaction(function () use ($grouped_data, $semester_setting, $classes, $semester_classes, $conduct_settings, $new_student_ids) {

                $conduct_records = [];

                foreach ($grouped_data as $class_name => $data) {
                    if (!isset($classes[$class_name])) {
                        continue;
                    }

                    $semester_class_id = $semester_classes->get($classes[$class_name]);

                    if (!$semester_class_id) {
                        continue;
                    }

                    $conduct_setting = $conduct_settings->get($semester_setting->id . '//' . $semester_class_id);

                    if (!$conduct_setting) {
                        continue;
                    }

                    foreach ($data as $d) {
                        if (!isset($new_student_ids[$d->student_id])) {
                            continue;
                        }

                        $conduct_setting_teacher = $conduct_setting->conductSettingTeachers->where('employee_id', $d->employee_id)->first();

                        if (!$conduct_setting_teacher) {
                            continue;
                        }

                        $conduct_records[] = [
                            'conduct_setting_id' => $conduct_setting->id,
                            'conduct_setting_teacher_id' => $conduct_setting_teacher->id,
                            'student_id' => $new_student_ids[$d->student_id],
                            'marks' => $d->mark,
                            'status' => ConductRecordStatus::DRAFT->value,
                            'created_at' => now()->toDateTimeString(),
                            'updated_at' => now()->toDateTimeString(),
                        ];
                    }
                }

                foreach (array_chunk($conduct_records, 4000) as $data) {
                    ConductRecord::insert($data);
                }

                $this->info("Number of ConductRecords inserted: " . count($conduct_records));
            });
        }
    }

    public function migrateAttendance()
    {
        $absent_ids = $this->v1DbConnection->table('absent')
            ->select('absent_id')
            ->where('date_day', '>=', '2025-01-01')
            ->orderBy('absent_id')
            ->get()
            ->pluck('absent_id')
            ->toArray();

        $new_students = MigrationMapping::query()
            ->with('modelable.firstActiveCard')
            ->where('model', Student::class)
            ->get()
            ->pluck('modelable', 'old')
            ->toArray();

        $new_students = array_filter($new_students);

        $bar = $this->output->createProgressBar(count($absent_ids));

        foreach (array_chunk($absent_ids, 2000) as $chunked_ids) {
            $data = $this->v1DbConnection->table('absent')
                ->whereIn('absent_id', $chunked_ids)
                ->get()
                ->sortBy('absent_id');

            $to_insert_attendance_input = [];

            foreach ($data as $d) {
                if (!isset($new_students[$d->student_id])) {
                    continue;
                }

                $new_student = $new_students[$d->student_id];
                $time_in_datetime = $d->date_day . ' ' . $d->time_in;
                $time_out_datetime = $d->time_out ? $d->date_day . ' ' . $d->time_out : null;

                $time_in_datetime = Carbon::parse($time_in_datetime, config('school.timezone'))->tz('UTC')->toDateTimeString();
                $time_out_datetime = $time_out_datetime ? Carbon::parse($time_out_datetime, config('school.timezone'))->tz('UTC')->toDateTimeString() : null;

                $to_insert_attendance_input[] = [
                    'attendance_recordable_type' => Student::class,
                    'attendance_recordable_id' => $new_student['id'],
                    'card_id' => Arr::get($new_student, "first_active_card.id"),
                    'date' => $d->date_day,
                    'record_datetime' => $time_in_datetime,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                if ($time_out_datetime) {
                    $to_insert_attendance_input[] = [
                        'attendance_recordable_type' => Student::class,
                        'attendance_recordable_id' => $new_student['id'],
                        'card_id' => Arr::get($new_student, "first_active_card.id"),
                        'date' => $d->date_day,
                        'record_datetime' => $time_out_datetime,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }

            $this->info("Number of rows to be inserted: " . count($to_insert_attendance_input));

            $this->_insertData(AttendanceInput::class, $to_insert_attendance_input);

            $bar->advance(count($chunked_ids));
        }
    }

    public function attendancePostingForMigratedData()
    {
        $date_with_attendance = AttendanceInput::select('date')
            ->distinct()
            ->groupBy('date')
            ->pluck('date');

        $this->info('Number of dates with attendance: ' . count($date_with_attendance));

        if ($this->isActual) {
            DB::disableQueryLog();
            $dispatcher = DB::connection()->getEventDispatcher();
            DB::connection()->unsetEventDispatcher();

            foreach ($date_with_attendance as $date) {
                $this->info('Posting attendance for date: ' . $date);

                $this->call('posting:student-attendance-input', [
                    '--date' => $date,
                ]);
            }
            DB::enableQueryLog();
            DB::connection()->setEventDispatcher($dispatcher);
        }
    }


    public function migratePeriodGroup()
    {
        $this->info("Migration for period_groups");

        $to_insert = [
            [
                'name' => json_encode(['en' => 'Junior Period Group', 'zh' => '初中组合']),
                'number_of_periods' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => json_encode(['en' => 'Senior Period Group', 'zh' => '高中组合']),
                'number_of_periods' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(PeriodGroup::class, $to_insert);
    }

    public function migratePeriod()
    {
        $this->info("Migration for periods");

        $period_groups = PeriodGroup::all()
            ->keyBy(function ($value) {
                return $value->getTranslation('name', 'en');
            });

        $time_settings = $this->v1DbConnection->table('time_setting')
            ->where('year', $this->year)
            ->whereIn('grade_id', [1, 5])
            ->get()
            ->keyBy('grade_id');


        $periods = $this->v1DbConnection->table('time_period')
            ->whereIn('time_setting_id', $time_settings->pluck('time_setting_id')->toArray())
            ->orderBy('start_time')
            ->get()
            ->groupBy('time_setting_id');

        $periods_by_grade = [];
        $periods_by_grade['Junior Period Group'] = $periods[$time_settings[1]->time_setting_id]->toArray();
        $periods_by_grade['Senior Period Group'] = $periods[$time_settings[5]->time_setting_id]->toArray();

        $to_insert = [];

        foreach ($periods_by_grade as $period_group_name => $periods) {
            $period_labels = [];

            $period_group = $period_groups[$period_group_name];

            foreach ([1, 2, 3, 4, 5] as $day) {
                $period_data = 1;

                foreach ($periods as $period) {
                    $to_insert[] = [
                        'period' => $period_data,
                        'day' => $this->_determineDayFromInteger($day),
                        'period_group_id' => $period_group->id,
                        'from_time' => $period->start_time,
                        'to_time' => $period->end_time,
                        'display_group' => 1,
                        'created_at' => now()
                    ];

                    $period_labels[$period_data] ??= [
                        'period_group_id' => $period_group->id,
                        'period' => $period_data,
                        'name' => json_encode([
                            'en' => $period->period_name,
                            'zh' => $period->period_cname
                        ]),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $period_data++;
                }
            }
            $this->info("Number of rows to be inserted to period_labels: " . count($period_labels));
            $this->_insertData(PeriodLabel::class, $period_labels);

            if ($this->isActual) {
                $period_group->number_of_periods = count($periods);
                $period_group->save();
            }
        }


        $this->info("Number of rows to be inserted to periods: " . count($to_insert));

        $this->_insertData(Period::class, $to_insert);
    }

    public function migrateTimetable()
    {
        $this->info("Migration for timetable");

        $semester_classes = SemesterClass::query()
            ->with('semesterSetting.semesterYearSetting', 'classModel')
            ->whereRelation('semesterSetting.semesterYearSetting', 'year', '>=', '2024')
            ->whereRelation('classModel', 'type', ClassType::PRIMARY)
            ->get();

        $period_groups = PeriodGroup::query()->pluck('id', 'name')->mapWithKeys(function ($id, $name) {
            $decodedName = json_decode($name, true);
            return [$decodedName['en'] => $id];
        });

        if (!$this->isActual) {
            return;
        }

        DB::transaction(function () use ($semester_classes, $period_groups) {
            foreach ($semester_classes as $semester_class) {
                $timetable_name = $semester_class->classModel->name . ' Timetable ' . $semester_class->semesterSetting->name;
                $is_active = true;
                if ($semester_class->semesterSetting->semesterYearSetting->year != $this->year) {
                    $is_active = false;
                }
                $timetable = Timetable::create([
                    'name' => $timetable_name,
                    'semester_class_id' => $semester_class->id,
                    'is_active' => $is_active,
                    'period_group_id' => $this->_determinePeriodGroup($semester_class->classModel->name, $period_groups),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->timetableService
                    ->setTimetable($timetable)
                    ->createTimeSlotsViaPeriods();

                $this->info('Timetable  ' . $timetable_name . ' is created with period group ' . $timetable->periodGroup->name);
            }
        });


        $this->info("Number of rows after insert: " . Timetable::count());
    }

    public function migrateTimeslot()
    {
        $this->info("Migration for timeslots");

        $timetable_details = $this->v1DbConnection->table('timetable_detail')
            ->select(
                DB::raw('assign_course.employee_id as employee_id'),
                DB::raw('time_period.start_time as start_time'),
                DB::raw('time_period.end_time as end_time'),
                DB::raw('timetable_detail.day as day'),
                DB::raw('class_m.class_code as class_code'),
                DB::raw('assign_course.course_id as course_id'),
                DB::raw('timetable_version.year as year')
            )
            ->join('time_period', 'time_period.period_id', '=', 'timetable_detail.period_id')
            ->join('timetable_version', 'timetable_version.timetable_version_id', 'timetable_detail.timetable_version_id')
            ->join('assign_course', 'timetable_detail.assign_class_course_id', 'assign_course.assign_class_course_id')
            ->join('class_m', 'class_m.class_id', 'assign_course.class_id')
            ->where('timetable_version.year', '>=', '2024')
            ->get();

        $time_setting = $this->v1DbConnection->table('time_setting')
            ->where('year', '>=', '2024')
            ->get();

        $time_periods = $this->v1DbConnection->table('time_period')
            ->whereIn('time_setting_id', $time_setting->pluck('time_setting_id')->toArray())
            ->orderBy('start_time')
            ->get()
            ->keyBy('period_id');

        $english_classes = $this->v1DbConnection->table('english_class_requirement')
            ->where('year', '>=', '2024')
            ->whereNotNull('period_id')
            ->get();

        $english_classes_data = [];

        foreach ($english_classes as $english_class) {
            $english_periods = array_map('trim', explode(',', $english_class->period_id));

            foreach ($english_periods as $period) {
                $start_time = $time_periods[$period]->start_time;
                $end_time = $time_periods[$period]->end_time;

                $english_classes_data[$english_class->year][$english_class->grade_id][$this->_determineDayFromInteger($english_class->day)->value][$start_time . '-' . $end_time] = 'English Class';
            }
        }

        $class_subjects = ClassSubject::query()->get();

        $class_subjects_data = [];

        foreach ($class_subjects as $class_subject) {
            $class_subjects_data[$class_subject->semester_class_id][$class_subject->subject_id] = $class_subject->id;
        }

        $timetables = Timetable::with('timeslots', 'semesterClass.classModel', 'periodGroup')->get();

        $timetable_details_data = [];

        foreach ($timetable_details as $timetable_detail) {
            $timetable_details_data[$timetable_detail->year][$timetable_detail->class_code][$this->_determineDayFromInteger($timetable_detail->day)->value][$timetable_detail->start_time . '-' . $timetable_detail->end_time] = [
                'course_id' => $timetable_detail->course_id,
                'employee_id' => $timetable_detail->employee_id
            ];
        }

        $this->info("Number of rows: " . count($timetable_details));

        $count = 0;

        if (!$this->isActual) {
            return;
        }

        $to_insert = [];

        $placeholder_data = $this->_getTimeslotPlaceholders();

        DB::transaction(function () use ($timetables, $timetable_details_data, $class_subjects_data, $placeholder_data, $english_classes_data, &$to_insert, &$count) {
            foreach ($timetables as $timetable) {
                foreach ($timetable->timeslots as $timeslot) {
                    if (!isset($timetable_details_data[$timetable->semesterClass->semesterSetting->semesterYearSetting->year][$timetable->semesterClass->classModel->code][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to])) {
                        if (isset($placeholder_data[$timetable->periodGroup->name][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to])) {
                            $placeholder = $placeholder_data[$timetable->periodGroup->name][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to];
                            if ($placeholder == '班务' && $timetable->semesterClass->homeroom_teacher_id) {
                                $to_insert[] = [
                                    'timeslot_id' => $timeslot->id,
                                    'employee_id' => $timetable->semesterClass->homeroom_teacher_id,
                                    'type' => TimeslotTeacherType::PRIMARY
                                ];
                            }
                            $timeslot->placeholder = $placeholder_data[$timetable->periodGroup->name][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to];
                            $timeslot->save();
                        }

                        if (isset($english_classes_data[$timetable->semesterClass->semesterSetting->semesterYearSetting->year][$timetable->semesterClass->classModel->grade_id][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to])) {
                            $timeslot->placeholder = $english_classes_data[$timetable->semesterClass->semesterSetting->semesterYearSetting->year][$timetable->semesterClass->classModel->grade_id][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to];
                            $timeslot->save();
                        }
                        continue;
                    }

                    $data_map = $timetable_details_data[$timetable->semesterClass->semesterSetting->semesterYearSetting->year][$timetable->semesterClass->classModel->code][$timeslot->day->value][$timeslot->attendance_from . '-' . $timeslot->attendance_to];
                    $subject_id = $data_map['course_id'];

                    $class_subject_id = $class_subjects_data[$timetable->semester_class_id][$subject_id];

                    $timeslot->class_subject_id = $class_subject_id;
                    $timeslot->save();

                    $this->info('Updated timeslot: ' . $timeslot->id . ' with class_subject_id: ' . $class_subject_id);

                    $to_insert[] = [
                        'timeslot_id' => $timeslot->id,
                        'employee_id' => $data_map['employee_id'],
                        'type' => TimeslotTeacherType::PRIMARY
                    ];

                    $count++;
                }
            }
        });

        $this->info("Number of rows inserted: " . $count);

        $this->_insertData(TimeslotTeacher::class, $to_insert, 0);
    }

    #Cocu Timetable
    public function migrateCocurriculumPeriodGroup()
    {
        $this->info("Migration for cocurriculum period_groups");

        $to_insert = [
            [
                'name' => json_encode(['en' => 'Cocurriculum Period Group', 'zh' => '课外活动组合']),
                'number_of_periods' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(PeriodGroup::class, $to_insert);
    }

    public function migrateCocurriculumPeriod()
    {
        $this->info("Migration for cocurriculum periods");

        $coco_period_group = PeriodGroup::query()
            ->where('name->en', 'Cocurriculum Period Group')
            ->first();

        $junior_period_group = PeriodGroup::query()
            ->where('name->en', 'Junior Period Group')
            ->first();

        $periods = Period::query()
            ->where('period_group_id', $junior_period_group->id)
            ->get();

        $period_labels = PeriodLabel::query()
            ->where('period_group_id', $junior_period_group->id)
            ->get();

        $periods_data = [];
        $periods_label_data = [];

        foreach ($periods as $period) {
            $periods_data[] = [
                'period' => $period->period,
                'day' => $period->day,
                'period_group_id' => $coco_period_group->id,
                'from_time' => $period->from_time,
                'to_time' => $period->to_time,
                'display_group' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        foreach ($period_labels as $period_label) {
            $periods_label_data[] = [
                'period_group_id' => $coco_period_group->id,
                'period' => $period_label->period,
                'name' => json_encode([
                    'en' => $period_label->getTranslation('name', 'en'),
                    'zh' => $period_label->getTranslation('name', 'zh')
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if ($this->isActual) {
            $coco_period_group->number_of_periods = count($periods_label_data);
            $coco_period_group->save();
        }

        $this->info("Number of rows to be inserted to periods: " . count($periods_data));

        $this->_insertData(Period::class, $periods_data);
        $this->_insertData(PeriodLabel::class, $periods_label_data);
    }

    public function migrateCocurriculumTimetable()
    {
        $this->info("Migration for cocurriculum timetable");

        $semester_classes = SemesterClass::query()
            ->with('semesterSetting.semesterYearSetting', 'classModel')
            ->whereRelation('semesterSetting.semesterYearSetting', 'year', '>=', '2024')
            ->whereRelation('classModel', 'type', ClassType::SOCIETY)
            ->get();

        $coco_period_group = PeriodGroup::query()
            ->where('name->en', 'Cocurriculum Period Group')
            ->first();

        if (!$this->isActual) {
            return;
        }

        foreach ($semester_classes as $semester_class) {
            $timetable_name = $semester_class->classModel->name . ' Timetable ' . $semester_class->semesterSetting->name;
            $is_active = true;

            if ($semester_class->semesterSetting->semesterYearSetting->year != $this->year) {
                $is_active = false;
            }

            $timetable = Timetable::create([
                'name' => $timetable_name,
                'semester_class_id' => $semester_class->id,
                'is_active' => $is_active,
                'period_group_id' => $coco_period_group->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->timetableService
                ->setTimetable($timetable)
                ->createTimeSlotsViaPeriods();

            $this->info('Timetable  ' . $timetable_name . ' is created with period group ' . $timetable->periodGroup->name);
        }
    }

    public function migrateCocurriculumTimeslot()
    {
        $this->info("Migration for cocurriculum timeslots");

        $cocu_data = $this->_getCocurriculumTimeslot();

        $semesters = SemesterSetting::query()
            ->whereRelation('semesterYearSetting', 'year', '>=', '2024')
            ->get();

        $cocu_subject = Subject::query()
            ->where('type', SubjectType::COCURRICULUM)
            ->first();

        $class_subjects_data = [];

        $class_subjects = ClassSubject::query()
            ->with(['semesterClass.classModel', 'teachers'])
            ->where('subject_id', $cocu_subject->id)
            ->get();

        foreach ($class_subjects as $class_subject) {
            $teachers = $class_subject->teachers;
            $employee_ids = [];
            foreach ($teachers as $teacher) {
                $employee_ids[$teacher->id]['type'] = $teacher->pivot->type->value;
            }

            $class_subjects_data[$class_subject->semesterClass->classModel->name] = [
                'class_subject_id' => $class_subject->id,
                'employee_ids' => $employee_ids
            ];
        }

        $timetables_data = Timetable::query()
            ->pluck('id', 'name')
            ->toArray();

        if (!$this->isActual) {
            return;
        }

        foreach ($cocu_data as $data) {
            foreach ($semesters as $semester) {
                foreach ($data['cocurriculum_names'] as $cocurriculum_name) {
                    $timetable_name = $cocurriculum_name . ' Timetable ' . $semester->name;

                    foreach ($data['timeslots'] as $timeslot) {
                        $timeslot_model = Timeslot::query()
                            ->where('day', $data['day'])
                            ->whereRelation('period', 'from_time', $timeslot['original_time_from'])
                            ->whereRelation('period', 'to_time', $timeslot['original_time_to'])
                            ->where('timetable_id', $timetables_data[$timetable_name])
                            ->first();

                        if (!$timeslot_model) {
                            $this->warn("Timeslot not found for $cocurriculum_name, day {$data['day']->value}, {$timeslot['original_time_from']} - {$timeslot['original_time_to']}");
                            continue;
                        }

                        $timeslot_model->update([
                            'class_subject_id' => $class_subjects_data[$cocurriculum_name]['class_subject_id'],
                            'attendance_from' => $timeslot['new_time_from'],
                            'attendance_to' => $timeslot['new_time_to'],
                        ]);

                        $timeslot_model->teachers()->sync($class_subjects_data[$cocurriculum_name]['employee_ids']);

                        $this->info("Updated $cocurriculum_name timeslot from {$timeslot['original_time_from']} - {$timeslot['original_time_to']} to {$timeslot['new_time_from']} - {$timeslot['new_time_to']}");
                    }
                }
            }
        }
    }

    public function migratePeriodAttendance()
    {
        $min_from_data_id = $this->v1DbConnection->table('class_attendance_course_teacher')
            ->select(DB::raw('min(class_attendance_course_teacher_id) as min_id'))
            ->where('attendance_date', '>=', '2025-01-01')
            ->first()
            ->min_id;

        $time_period_by_year = $this->v1DbConnection->table('time_setting as ts')
            ->select('tp.period_id', 'ts.year')
            ->join('time_period as tp', 'tp.time_setting_id', 'ts.time_setting_id')
            ->where('ts.year', '>=', '2024')
            ->pluck('year', 'period_id');

        $new_students = $this->_getStudentOldNewIds();

        $old_student_ids = array_keys($new_students);

        $from_data = $this->v1DbConnection->table('class_attendance_course_teacher')
            ->select(
                'class_attendance_course_teacher.class_attendance_course_teacher_id',
                DB::raw('class_attendance_course_teacher.attendance_date as attendance_date'),
                DB::raw('class_m.class_code as class_code'),
                DB::raw('time_period.period_id as period_id'),
                DB::raw('time_period.start_time as start_time'),
                DB::raw('time_period.end_time as end_time'),
                DB::raw('class_attendance_course_teacher.course_id as course_id'),
                DB::raw('class_attendance_course_teacher.student_id as student_id'),
                DB::raw('class_attendance_course_teacher.employee_id as employee_id'),
                DB::raw('class_attendance_course_teacher.status_attendance as status_attendance')
            )
            ->join('class_m', 'class_m.class_id', 'class_attendance_course_teacher.class_id')
            ->join('time_period', 'time_period.period_id', 'class_attendance_course_teacher.period_id')
            ->where('class_attendance_course_teacher.class_attendance_course_teacher_id', '>=', $min_from_data_id)
            ->whereIn('class_attendance_course_teacher.student_id', $old_student_ids)
            ->orderBy('class_attendance_course_teacher_id');

        $placeholder_data = $this->_getTimeslotPlaceholders();

        $timeslots = Timeslot::with([
            'classSubject.semesterClass.classModel',
            'classSubject.semesterClass.semesterSetting.semesterYearSetting'
        ])->get();

        $timeslot_data = [];

        $period_groups = PeriodGroup::query()->pluck('id', 'name')->mapWithKeys(function ($id, $name) {
            $decodedName = json_decode($name, true);
            return [$decodedName['en'] => $decodedName['en']];
        });

        foreach ($timeslots as $timeslot) {
            $timeslot_day = $timeslot->day->value;
            $timeslot_time = $timeslot->attendance_from . '-' . $timeslot->attendance_to;
            if ($timeslot->class_subject_id) {
                //Set class code and subject id as filter key
                $class_code = $timeslot->classSubject->semesterClass->classModel->code;
                $year = $timeslot->classSubject->semesterClass->semesterSetting->semesterYearSetting->year;
                $subject_id = $timeslot->classSubject->subject_id;

                $timeslot_data[$class_code][$year][$subject_id][$timeslot_day][$timeslot_time] = $timeslot->id;
            } else {
                //Set placeholder as filter key
                $timeslot_data[$timeslot->placeholder][$timeslot_day][$timeslot_time] = $timeslot->id;
            }
        }

        $order_counter = $from_data->count();

        $this->info("Number of rows: " . $order_counter);

        $bar = $this->output->createProgressBar($order_counter);

        $from_data->chunk(10000, function ($data) use ($timeslot_data, $new_students, $placeholder_data, $time_period_by_year, $bar, $period_groups) {
            $to_insert = [];

            foreach ($data as $d) {
                $day = Carbon::parse($d->attendance_date)->format('l');
                $day = strtoupper($day);
                $timeslot_id = null;
                $time_period = $d->start_time . '-' . $d->end_time;
                $year = $time_period_by_year[$d->period_id];
                $period_group_name = $this->_determinePeriodGroup($d->class_code, $period_groups);
                if (!isset($new_students[$d->student_id])) {
                    $this->info("Student not found, id = " . $d->student_id);
                    continue;
                }

                //Get timeslot id with class and subject id
                if (isset($timeslot_data[$d->class_code][$year][$d->course_id][$day][$time_period])) {
                    $timeslot_id = $timeslot_data[$d->class_code][$year][$d->course_id][$day][$time_period];
                }

                //Get timeslot id with placeholder
                if (isset($placeholder_data[$period_group_name][$day][$time_period])) {
                    $placeholder = $placeholder_data[$period_group_name][$day][$time_period];
                    $timeslot_id = $timeslot_data[$placeholder][$day][$time_period];
                }

                if (!$timeslot_id) {
                    $info_data = [
                        'class_attendance_course_teacher_id' => $d->class_attendance_course_teacher_id,
                        'class_code' => $d->class_code,
                        'course_id' => $d->course_id,
                        'day' => $day,
                        'time_period' => $time_period,
                        'year' => $year,
                    ];

                    $info_string = implode(', ', array_map(
                        function ($key, $value) {
                            return "$key = $value";
                        },
                        array_keys($info_data),
                        $info_data
                    ));

//                    $this->info("Timeslot not found: " . $info_string);
                    continue;
                }

                $to_insert[] = [
                    'student_id' => $new_students[$d->student_id],
                    'date' => $d->attendance_date,
                    'timeslot_type' => Timeslot::class,
                    'timeslot_id' => $timeslot_id,
                    'updated_by_employee_id' => $d->employee_id,
                    'status' => $this->_determinePeriodAttendanceStatus($d->status_attendance),
                    'period' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            if ($to_insert) {
                $this->info("Period Attendance to be inserted: " . count($to_insert));
                $this->_insertData(PeriodAttendance::class, $to_insert);
            }

            $bar->advance(count($data));
        });

        if ($this->isActual) {
            DB::statement("
            UPDATE period_attendances pa
            SET period = p.period
            FROM timeslots t, periods p
            WHERE pa.timeslot_id = t.id
            AND t.period_id = p.id;
        ");
        }
    }

    public function migratePeriodAttendancePlaceholderOnly()
    {
        $min_from_data_id = $this->v1DbConnection->table('class_attendance_course_teacher')
            ->select(DB::raw('min(class_attendance_course_teacher_id) as min_id'))
            ->where('attendance_date', '>=', '2025-01-01')
            ->first()
            ->min_id;

        $time_period_by_year = $this->v1DbConnection->table('time_setting as ts')
            ->select('tp.period_id', 'ts.year')
            ->join('time_period as tp', 'tp.time_setting_id', 'ts.time_setting_id')
            ->where('ts.year', '>=', '2024')
            ->pluck('year', 'period_id');

        $new_students = $this->_getStudentOldNewIds();

        $old_student_ids = array_keys($new_students);

        $v3_leave_applications = LeaveApplication::selectRaw('leave_applications.*, leave_application_periods.date, leave_application_periods.period')
            ->join('leave_application_periods', 'leave_application_periods.leave_application_id', 'leave_applications.id')
            ->where('status', 'APPROVED')
            ->where('leave_applicable_type', Student::class)
            ->get();

        $v3_leave_applications_grouped = [];

        foreach ($v3_leave_applications as $leave_application) {
            $v3_leave_applications_grouped[$leave_application->leave_applicable_id][$leave_application->date][$leave_application->period] = $leave_application;
        }

        $from_data = $this->v1DbConnection->table('class_attendance_course_teacher')
            ->select(
                'class_attendance_course_teacher.class_attendance_course_teacher_id',
                DB::raw('class_attendance_course_teacher.attendance_date as attendance_date'),
                DB::raw('class_m.class_code as class_code'),
                DB::raw('time_period.period_id as period_id'),
                DB::raw('time_period.start_time as start_time'),
                DB::raw('time_period.end_time as end_time'),
                DB::raw('class_attendance_course_teacher.course_id as course_id'),
                DB::raw('class_attendance_course_teacher.student_id as student_id'),
                DB::raw('class_attendance_course_teacher.employee_id as employee_id'),
                DB::raw('class_attendance_course_teacher.status_attendance as status_attendance')
            )
            ->join('class_m', 'class_m.class_id', 'class_attendance_course_teacher.class_id')
            ->join('time_period', 'time_period.period_id', 'class_attendance_course_teacher.period_id')
            ->where('class_attendance_course_teacher.class_attendance_course_teacher_id', '>=', $min_from_data_id)
            ->whereIn('class_attendance_course_teacher.student_id', $old_student_ids)
            //->where('student_id', 9863)
            ->orderBy('class_attendance_course_teacher_id');

        $placeholder_data = $this->_getTimeslotPlaceholders();

        $timeslots = Timeslot::with([
            'period',
            'timetable.semesterClass.classModel',
            'timetable.semesterClass.semesterSetting.semesterYearSetting',
            'classSubject.semesterClass.classModel',
            'classSubject.semesterClass.semesterSetting.semesterYearSetting'
        ])->get();

        $timeslot_data = [];

        $period_groups = PeriodGroup::query()->pluck('id', 'name')->mapWithKeys(function ($id, $name) {
            $decodedName = json_decode($name, true);
            return [$decodedName['en'] => $decodedName['en']];
        });

        foreach ($timeslots as $timeslot) {
            $timeslot_day = $timeslot->day->value;
            $timeslot_time = $timeslot->attendance_from . '-' . $timeslot->attendance_to;
            if ($timeslot->class_subject_id) {
                //Set class code and subject id as filter key
                $class_code = $timeslot->classSubject->semesterClass->classModel->code;
                $year = $timeslot->classSubject->semesterClass->semesterSetting->semesterYearSetting->year;
                $subject_id = $timeslot->classSubject->subject_id;

                $timeslot_data[$class_code][$year][$subject_id][$timeslot_day][$timeslot_time] = $timeslot;
            } else {

                $class_code = $timeslot->timetable->semesterClass->classModel->code;
                $year = $timeslot->timetable->semesterClass->semesterSetting->semesterYearSetting->year;

                //Set placeholder as filter key
                $timeslot_data[$class_code][$year][$timeslot->placeholder][$timeslot_day][$timeslot_time] = $timeslot;
            }
        }

        $order_counter = $from_data->count();

        $this->info("Number of rows: " . $order_counter);

        $bar = $this->output->createProgressBar($order_counter);

        $from_data->chunk(10000, function ($data) use ($timeslot_data, $new_students, $placeholder_data, $time_period_by_year, $bar, $period_groups, &$v3_leave_applications_grouped) {
            $to_insert = [];

            foreach ($data as $d) {
                $day = Carbon::parse($d->attendance_date)->format('l');
                $day = strtoupper($day);
                $timeslot_id = null;
                $timeslot = null;

                $time_period = $d->start_time . '-' . $d->end_time;
                $year = $time_period_by_year[$d->period_id];
                $period_group_name = $this->_determinePeriodGroup($d->class_code, $period_groups);
                if (!isset($new_students[$d->student_id])) {
                    $this->info("Student not found, id = " . $d->student_id);
                    continue;
                }

                //Get timeslot id with placeholder
                if (isset($placeholder_data[$period_group_name][$day][$time_period])) {
                    $placeholder = $placeholder_data[$period_group_name][$day][$time_period];
                    $timeslot = $timeslot_data[$d->class_code][$year][$placeholder][$day][$time_period];
                    $timeslot_id = $timeslot->id;
                } else {
                    continue;
                }

                if ($placeholder !== '班务') {
                    $this->error('NOT 班务');
                    die();
                }


                if (!$timeslot_id) {
                    $info_data = [
                        'class_attendance_course_teacher_id' => $d->class_attendance_course_teacher_id,
                        'class_code' => $d->class_code,
                        'course_id' => $d->course_id,
                        'day' => $day,
                        'time_period' => $time_period,
                        'year' => $year,
                    ];

                    $info_string = implode(', ', array_map(
                        function ($key, $value) {
                            return "$key = $value";
                        },
                        array_keys($info_data),
                        $info_data
                    ));

//                    $this->info("Timeslot not found: " . $info_string);
                    continue;
                }


                // if got leave application
                $leave_application = $v3_leave_applications_grouped[$new_students[$d->student_id]][$d->attendance_date][$timeslot->period->period] ?? null;

                /*if ( $leave_application ) {
                    $this->warn("Leave application {$leave_application->id} found {$leave_application->reason} for student on {$d->attendance_date} period {$timeslot->period->period}");
                }*/

                $to_insert[] = [
                    'student_id' => $new_students[$d->student_id],
                    'date' => $d->attendance_date,
                    'timeslot_type' => Timeslot::class,
                    'timeslot_id' => $timeslot_id,
                    'updated_by_employee_id' => $d->employee_id,
                    'status' => $this->_determinePeriodAttendanceStatus($d->status_attendance),
                    'period' => $timeslot->period->period,
                    'leave_application_id' => $leave_application !== null ? $leave_application->id : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            if ($to_insert) {
                $this->info("Period Attendance to be inserted: " . count($to_insert));
                $this->_insertData(PeriodAttendance::class, $to_insert);
            }

            $bar->advance(count($data));
        });

        if ($this->isActual) {
            DB::statement("
            UPDATE period_attendances pa
            SET period = p.period
            FROM timeslots t, periods p
            WHERE pa.timeslot_id = t.id
            AND t.period_id = p.id;
        ");
        }
    }


    public function patchClassAttendancesUsingV1LeaveRecords()
    {
        $this->info("Patch migrated period_attendances records with v1 leave data");

        $v1_leave_periods = $this->v1DbConnection->table('leave_record as lr')
            ->join('leave_relation as lr2', 'lr.leave_group_id', '=', 'lr2.leave_group_id')
            ->join('time_period as tp', 'tp.period_id', '=', 'lr2.period_id')
            ->where('lr.leave_from_date', '>=', '2025-01-01')
            ->where('lr.leave_from_date', '<=', '2025-05-31')
            ->whereIn('attendance_category_id', [2, 3])     // 2 = absent, 3 = late
            ->distinct()
            ->get();

        $v1_leave_periods = $v1_leave_periods->groupBy('student_id');

        $v3_leave_applications = LeaveApplication::selectRaw('leave_applications.*, leave_application_periods.date, leave_application_periods.period')
            ->join('leave_application_periods', 'leave_application_periods.leave_application_id', 'leave_applications.id')
            ->where('status', 'APPROVED')
            ->where('leave_applicable_type', Student::class)
            ->get();

        $v3_leave_applications_grouped = [];

        foreach ($v3_leave_applications as $leave_application) {
            $v3_leave_applications_grouped[$leave_application->leave_applicable_id][$leave_application->date][$leave_application->period] = $leave_application;
        }

        $semester_settings = SemesterSetting::whereRelation('semesterYearSetting', 'year', '>=', '2024')
            ->get();

        $student_new_to_old_mappings = $this->_getStudentOldNewIds();
        $student_new_to_old_mappings = array_flip($student_new_to_old_mappings);

        $timeslots = Timeslot::with([
            'period',
            'timetable.semesterClass.classModel',
            'timetable.semesterClass.semesterSetting.semesterYearSetting',
        ])
            ->whereHas('teachers')
            ->get();

        $timeslot_data = [];

        foreach ($timeslots as $timeslot) {
            $timeslot_day = $timeslot->day->value;
            $timeslot_time = $timeslot->attendance_from . '-' . $timeslot->attendance_to;

            $class_code = $timeslot->timetable->semesterClass->classModel->code;
            $year = $timeslot->timetable->semesterClass->semesterSetting->semesterYearSetting->year;

            $timeslot_data[$class_code][$year][$timeslot_day][$timeslot_time] = $timeslot;
        }

        DB::transaction(function () use (
            &$v1_leave_periods,
            $student_new_to_old_mappings,
            $semester_settings,
            &$timeslot_data,
            &$v3_leave_applications_grouped,
        ) {
            $update_count = 0;

            Student::with([
                'classes' => function (HasMany $query) use ($semester_settings) {
                    $query->whereIn('class_type', [ClassType::PRIMARY, ClassType::ENGLISH])
                        ->where('is_active', true)
                        ->whereIn('semester_setting_id', $semester_settings->pluck('id')->toArray());

                    $query->with(['semesterClass.timetable.periodGroup.periodLabels', 'semesterClass.classModel']);
                },
                'periodAttendances.timeslot.period'
            ])
                //->where('id', 986)
                ->lazyById(200, 'id')
                ->each(function ($student) use (
                    &$v1_leave_periods,
                    &$student_new_to_old_mappings,
                    &$semester_settings,
                    &$update_count,
                    &$timeslot_data,
                    &$v3_leave_applications_grouped,
                ) {
                    if (!isset($student_new_to_old_mappings[$student->id])) {
                        $this->error("Student new to old mapping not found for {$student->name}");
                        return;
                    }

                    $this->info("Processing student " . $student->name . ', v1 ID: ' . $student_new_to_old_mappings[$student->id] . ', v3 ID: ' . $student->id);

                    $student_leave_records = $v1_leave_periods[$student_new_to_old_mappings[$student->id]] ?? null;

                    if ($student_leave_records) {
                        foreach ($student_leave_records as $student_leave_record) {
                            $student_period_attendances = $student
                                ->periodAttendances
                                ->where('date', $student_leave_record->leave_from_date)
                                ->groupBy('date')
                                ->map(function ($period_attendances) {
                                    return $period_attendances->keyBy('timeslot.period.period');
                                });

                            $leave_semester_setting = $semester_settings->where('from', '<=', $student_leave_record->leave_from_date)
                                ->where('to', '>=', $student_leave_record->leave_from_date)
                                ->first();

                            if ($student->classes->where('semester_setting_id', $leave_semester_setting->id)->count() == 0) {
                                continue;
                            }

                            $student_timetable = $student->classes
                                ->where('semester_setting_id', $leave_semester_setting->id)
                                ->first()
                                ->semesterClass
                                ->timetable;

                            $timetable_period_labels = $student_timetable->periodGroup->periodLabels;

                            $period_label = $timetable_period_labels->firstWhere(function ($period_label) use ($student_leave_record) {
                                return $period_label->getTranslation('name', 'zh') == $student_leave_record->period_cname;
                            });

                            $student_period_attendance = $student_period_attendances[$student_leave_record->leave_from_date][$period_label->period] ?? null;

                            $v1_status = 'UNKNOWN';

                            if ($student_leave_record->attendance_category_id == 2) {
                                $v1_status = 'ABSENT';
                            } else {
                                if ($student_leave_record->attendance_category_id == 3) {
                                    $v1_status = 'LATE';
                                }
                            }

                            if ($student_period_attendance) {

                                $this->info("Record found! v3 " . $student_period_attendance->date . ', ' . $student_period_attendance->status . ', ' . $student_period_attendance->timeslot->period->from_time . ' - ' . $student_period_attendance->timeslot->period->to_time);
                                $this->info("Record found! v1 " . $student_leave_record->leave_from_date . ', ' . $v1_status . ', ' . $student_leave_record->start_time . ' - ' . $student_leave_record->end_time);

                            } else {

                                $classes = $student->classes
                                    ->where('semester_setting_id', $leave_semester_setting->id)
                                    ->values();

                                if (count($classes) > 2) {
                                    $this->error("This is abnormal");
                                    dd($classes->toArray());
                                }

                                $primary_timeslot_found = false;

                                foreach ([ClassType::PRIMARY, ClassType::ENGLISH] as $class_type) {

                                    if ($class_type == ClassType::ENGLISH && $primary_timeslot_found) {
                                        continue;
                                    }

                                    $student_class = $classes->where('class_type', $class_type)->first();

                                    if ($student_class === null) {
                                        continue;
                                    }

                                    $class = $student_class->semesterClass->classModel;

                                    $day = Carbon::parse($student_leave_record->leave_from_date)->format('l');
                                    $day = strtoupper($day);
                                    $time_period = $student_leave_record->start_time . '-' . $student_leave_record->end_time;

                                    $this->warn("to insert missing data for {$class->code} " . $student_leave_record->leave_from_date . ', ' . $v1_status . ', ' . $student_leave_record->start_time . ' - ' . $student_leave_record->end_time);

                                    $timeslot = $timeslot_data[$class->code][$student_leave_record->year][$day][$time_period] ?? null;

                                    if ($timeslot === null) {
                                        $this->error("Timeslot not found for " . $class->code . ", " . $day . ", " . $time_period);
                                        continue;
                                    }
                                    if ($class_type == ClassType::PRIMARY && $timeslot !== null) {
                                        $primary_timeslot_found = true;
                                    }

                                    $leave_application = $v3_leave_applications_grouped[$student->id][$student_leave_record->leave_from_date][$timeslot->period->period] ?? null;

                                    if ($leave_application) {
                                        $this->warn("Leave application {$leave_application->id} found {$leave_application->reason} for student on {$student_leave_record->leave_from_date} period {$timeslot->period->period}");
                                    }

                                    if ($this->isActual) {

                                        $present_period_attendance = PeriodAttendance::where([
                                            'student_id' => $student->id,
                                            'date' => $student_leave_record->leave_from_date,
                                            'timeslot_type' => Timeslot::class,
                                            'timeslot_id' => $timeslot->id,
                                            'status' => PeriodAttendanceStatus::PRESENT->value,
                                        ])->first();

                                        if ($present_period_attendance) {
                                            continue;
                                        }

                                        PeriodAttendance::updateOrCreate(
                                            [
                                                'student_id' => $student->id,
                                                'date' => $student_leave_record->leave_from_date,
                                                'timeslot_type' => Timeslot::class,
                                                'timeslot_id' => $timeslot->id,
                                            ],
                                            [
                                                'leave_application_id' => $leave_application !== null ? $leave_application->id : null,
                                                'updated_by_employee_id' => null,
                                                'status' => $this->_determinePeriodAttendanceStatus($student_leave_record->attendance_category_id),
                                                'period' => $timeslot->period->period,
                                                'created_at' => now(),
                                                'updated_at' => now(),
                                            ]
                                        );

                                    }

                                    $update_count++;
                                }
                            }
                        }
                    } else {
                        $this->info("No leave record found.");
                    }
                });

            $this->info("Number of period attendances updated: {$update_count}");
        });

    }

    public function migrateLeaveApplicationType()
    {
        $this->info("Migration for v1:attendance_category -> v3:leave_application_types");

        $attendance_categories = $this->v1DbConnection->table('attendance_category as ac')
            ->select('ac.*')
            ->join('leave_record as lr', 'lr.attendance_category_id', 'ac.attendance_category_id')
            ->where('lr.leave_from_date', '>=', '2024-01-01')
            ->distinct()
            ->orderBy('ac.attendance_category_id')
            ->get();

        if ($attendance_categories->count() != 15) {
            $this->error("Attendance category count is not 15");
            return;
        }

        DB::transaction(function () use ($attendance_categories) {
            $to_create_data = 0;
            $created_data = 0;

            foreach ($attendance_categories as $attendance_category) {
                if ($attendance_category->title_chinese == '缺席' || $attendance_category->title_chinese == '迟到') {
                    continue;
                }

                $to_create_data++;

                if (!$this->isActual) {
                    continue;
                }

                $leave_application_type = LeaveApplicationType::create([
                    'name' => [
                        'en' => $attendance_category->title_chinese,
                        'zh' => $attendance_category->title_chinese,
                    ],
                    'is_present' => false,
                    'average_point_deduction' => $attendance_category->avg_point_deduction,
                    'conduct_point_deduction' => $attendance_category->conduct_point_deduction,
                ]);

                MigrationMapping::create([
                    'model' => LeaveApplicationType::class,
                    'old' => $attendance_category->attendance_category_id,
                    'old_table' => 'attendance_category',
                    'new' => $leave_application_type->id,
                ]);

                $created_data++;
            }

            $this->info("Number of leave application types to be created: {$to_create_data}");
            $this->info("Number of leave application types created: {$created_data}");
        });
    }

    // TODO: Lucas to test this
    public function migrateLeaveApplication()
    {
        $this->info("Migration for v1:leave_record -> v3:leave_applications");

        /**
         * 2 = 缺席
         * 3 = 迟到
         * If leave application type is absent or late, skip.
         */
        $leave_group_ids = $this->v1DbConnection->table('leave_record as lr')
            ->select('leave_group_id')
            ->where('lr.leave_from_date', '>=', '2024-01-01')
            ->where('lr.leave_from_date', '<=', '2025-05-31')
            ->whereNotIn('attendance_category_id', [2, 3])
            ->distinct()
            ->get()
            ->pluck('leave_group_id')
            ->toArray();

        $leave_application_type_mappings = MigrationMapping::query()
            ->where('model', LeaveApplicationType::class)
            ->where('old_table', 'attendance_category')
            ->pluck('new', 'old')
            ->toArray();

        $v3_leave_application_types = LeaveApplicationType::all()->keyBy('id');

        $student_mappings = $this->_getStudentOldNewIds();

        $semester_settings = SemesterSetting::whereRelation('semesterYearSetting', 'year', '>=', 2024)
            ->get();

        $students = Student::with([
            'classes' => function (HasMany $query) use ($semester_settings) {
                $query->where('class_type', ClassType::PRIMARY)
                    ->whereIn('semester_setting_id', $semester_settings->pluck('id')->toArray());

                $query->with('semesterClass.timetable.periodGroup.periodLabels');
            }
        ])
            ->get()
            ->keyBy('id');

        $bar = $this->output->createProgressBar(count($leave_group_ids));


        DB::transaction(function () use (
            &$to_insert_leave_application_periods,
            $leave_group_ids,
            $leave_application_type_mappings,
            $v3_leave_application_types,
            $student_mappings,
            $semester_settings,
            $students,
            $bar
        ) {
            foreach (array_chunk($leave_group_ids, 2500) as $chunked_leave_group_ids) {
                $leave_records = $this->v1DbConnection->table('leave_record as lr')
                    ->whereIn('lr.leave_group_id', $chunked_leave_group_ids)
                    ->get()
                    ->groupBy(['leave_group_id', 'student_id']);

                $leave_record_periods = $this->v1DbConnection->table('leave_relation as lr')
                    ->join('time_period as  tp', 'tp.period_id', 'lr.period_id')
                    ->whereIn('lr.leave_group_id', $chunked_leave_group_ids)
                    ->get()
                    ->groupBy('leave_group_id');

                $to_insert_leave_application_periods = [];

                foreach ($chunked_leave_group_ids as $leave_group_id) {
                    $processing_leave_record_by_students = $leave_records[$leave_group_id];
                    foreach ($processing_leave_record_by_students as $old_student_id => $processing_leave_records) {
                        $new_student_id = $student_mappings[$old_student_id] ?? null;
                        if (!$new_student_id) {
                            $this->warn("Student not found for V1 student ID {$old_student_id}. Skip");
                            continue;
                        }
                        $student = $students[$new_student_id];

                        $leave_semester_setting = $semester_settings->where('from', '<=', $processing_leave_records[0]->leave_from_date)
                            ->where('to', '>=', $processing_leave_records[0]->leave_from_date)
                            ->first();

                        $student_timetable = $student->classes
                            ->where('semester_setting_id', $leave_semester_setting->id)
                            ->first()
                            ->semesterClass
                            ->timetable;

                        $timetable_period_labels = $student_timetable->periodGroup->periodLabels;

                        $new_leave_application_type_id = $leave_application_type_mappings[$processing_leave_records[0]->attendance_category_id];
                        $v3_leave_application = $v3_leave_application_types[$new_leave_application_type_id];


                        if ($this->isActual) {
                            $leave_application = LeaveApplication::create([
                                'leave_applicable_type' => Student::class,
                                'leave_applicable_id' => $new_student_id,
                                'status' => LeaveApplicationStatus::APPROVED->value,
                                'leave_application_type_id' => $new_leave_application_type_id,
                                'reason' => $processing_leave_records[0]->mark,
                                'remarks' => null,
                                'is_present' => $v3_leave_application->is_present,
                                'average_point_deduction' => $v3_leave_application->average_point_deduction,
                                'conduct_point_deduction' => $v3_leave_application->conduct_point_deduction,
                                'is_full_day' => false,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);

                            foreach ($processing_leave_records as $processing_leave_record) {
                                foreach ($leave_record_periods[$leave_group_id] as $leave_record_period) {
                                    $period_label = $timetable_period_labels->firstWhere(function ($period_label) use ($leave_record_period) {
                                        return $period_label->getTranslation('name', 'zh') == $leave_record_period->period_cname;
                                    });

                                    $to_insert_leave_application_periods[] = [
                                        'leave_application_id' => $leave_application->id,
                                        'date' => $processing_leave_record->leave_from_date,
                                        'period_label_id' => $period_label->id,
                                        'period' => $period_label->period,
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                    ];
                                }
                            }

                        }

                        $this->info("Leave record: {$processing_leave_records[0]->leave_group_id} - {$processing_leave_records[0]->student_id}");
                    }
                }
                if ($this->isActual) {
                    $this->_insertData(LeaveApplicationPeriod::class, $to_insert_leave_application_periods);
                }
                $bar->advance(count($chunked_leave_group_ids));
            }
        });
    }

    // TODO: Lucas to test this
    public function migrateLeaveApplicationPeriodToPeriodAttendance()
    {
        $leave_application_student_ids = LeaveApplication::select('leave_applicable_id')
            ->distinct()
            ->where('leave_applicable_type', Student::class)
            ->pluck('leave_applicable_id')
            ->toArray();

        $semester_settings = SemesterSetting::whereRelation('semesterYearSetting', 'year', '>=', 2024)
            ->get();

        $updated_count = 0;
        Student::with([
            'classes' => function (HasMany $query) use ($semester_settings) {
                $query->where('class_type', ClassType::PRIMARY)
                    ->whereIn('semester_setting_id', $semester_settings->pluck('id')->toArray());

                $query->with('semesterClass.timetable.periodGroup.periodLabels');
            },
            'periodAttendances.timeslot.period'
        ])
            ->whereIn('id', $leave_application_student_ids)
            ->chunk(500, function ($students) use (&$updated_count) {
                $this->info("Processing " . count($students) . " students");
                $leave_applications = LeaveApplication::with('leaveApplicationPeriods')
                    ->where('leave_applicable_type', Student::class)
                    ->whereIn('leave_applicable_id', $students->pluck('id')->toArray())
                    ->get()
                    ->groupBy('leave_applicable_id');

                foreach ($students as $student) {
                    $leave_application_periods = $leave_applications[$student->id]->pluck('leaveApplicationPeriods')->flatten();
                    $leave_application_period_dates = $leave_application_periods->pluck('date')->unique()->toArray();

                    $student_period_attendances = $student
                        ->periodAttendances
                        ->whereIn('date', $leave_application_period_dates)
                        ->groupBy('date')
                        ->map(function ($period_attendances) {
                            return $period_attendances->keyBy('timeslot.period.period');
                        });

                    foreach ($leave_application_periods as $leave_application_period) {
                        $period_attendance = $student_period_attendances[$leave_application_period->date][$leave_application_period->period] ?? null;
                        if (isset($period_attendance)) {
                            if ($this->isActual) {
                                $period_attendance->update([
                                    'leave_application_id' => $leave_application_period->leave_application_id,
                                ]);
                                $updated_count++;
                            }
                        }
                    }
                }
            });
        $this->info("Number of period attendances updated: {$updated_count}");
    }

    public function migrateAbsentLateRecords()
    {
        $this->info("Migration for absent and late records");

        $leave_group_ids = $this->v1DbConnection->table('leave_record as lr')
            ->select('leave_group_id')
            ->where('lr.leave_from_date', '>=', '2024-01-01')
            ->whereIn('attendance_category_id', [2, 3])
            ->distinct()
            ->get()
            ->pluck('leave_group_id')
            ->toArray();

        $semester_settings = SemesterSetting::whereRelation('semesterYearSetting', 'year', '>=', 2024)
            ->get();

        $leave_records = $this->v1DbConnection->table('leave_record as lr')
            ->whereIn('lr.leave_group_id', $leave_group_ids)
            ->get()
            ->groupBy(['student_id', 'leave_group_id']);

        $leave_record_periods = $this->v1DbConnection->table('leave_relation as lr')
            ->join('time_period as  tp', 'tp.period_id', 'lr.period_id')
            ->whereIn('lr.leave_group_id', $leave_group_ids)
            ->get()
            ->groupBy('leave_group_id');

        $student_new_to_old_mappings = $this->_getStudentOldNewIds();
        $student_new_to_old_mappings = array_flip($student_new_to_old_mappings);

        DB::transaction(function () use (
            $leave_group_ids,
            $leave_records,
            $leave_record_periods,
            $student_new_to_old_mappings,
            $semester_settings
        ) {
            $update_count = 0;
            Student::with([
                'classes' => function (HasMany $query) use ($semester_settings) {
                    $query->where('class_type', ClassType::PRIMARY)
                        ->whereIn('semester_setting_id', $semester_settings->pluck('id')->toArray());

                    $query->with('semesterClass.timetable.periodGroup.periodLabels');
                },
                'periodAttendances.timeslot.period'
            ])->lazyById(200, 'id')
                ->each(function ($student) use (
                    $leave_records,
                    $leave_record_periods,
                    $student_new_to_old_mappings,
                    $semester_settings,
                    &$update_count
                ) {
                    if (!isset($student_new_to_old_mappings[$student->id])) {
                        return;
                    }
                    $this->info("Processing student " . $student->name . ', v1 ID: ' . $student_new_to_old_mappings[$student->id] . ', v3 ID: ' . $student->id);
                    $student_leave_record_by_groups = $leave_records[$student_new_to_old_mappings[$student->id]] ?? null;
                    if ($student_leave_record_by_groups) {
                        foreach ($student_leave_record_by_groups as $leave_group_id => $student_leave_records) {
                            $student_leave_record_periods = $leave_record_periods[$leave_group_id];
                            foreach ($student_leave_records as $student_leave_record) {
                                $student_period_attendances = $student
                                    ->periodAttendances
                                    ->where('date', $student_leave_record->leave_from_date)
                                    ->groupBy('date')
                                    ->map(function ($period_attendances) {
                                        return $period_attendances->keyBy('timeslot.period.period');
                                    });

                                $leave_semester_setting = $semester_settings->where('from', '<=', $student_leave_record->leave_from_date)
                                    ->where('to', '>=', $student_leave_record->leave_from_date)
                                    ->first();

                                $student_timetable = $student->classes
                                    ->where('semester_setting_id', $leave_semester_setting->id)
                                    ->first()
                                    ->semesterClass
                                    ->timetable;

                                $timetable_period_labels = $student_timetable->periodGroup->periodLabels;

                                foreach ($student_leave_record_periods as $student_leave_record_period) {
                                    $period_label = $timetable_period_labels->firstWhere(function ($period_label) use ($student_leave_record_period) {
                                        return $period_label->getTranslation('name', 'zh') == $student_leave_record_period->period_cname;
                                    });

                                    $student_period_attendance = $student_period_attendances[$student_leave_record->leave_from_date][$period_label->period] ?? null;

                                    if ($student_period_attendance) {
                                        if (
                                            $student_leave_record->attendance_category_id == 2 && $student_period_attendance->status == 'ABSENT' ||
                                            $student_leave_record->attendance_category_id == 3 && $student_period_attendance->status == 'LATE'
                                        ) {
                                            continue;
                                        }

                                        if ($this->isActual) {

                                            $student_period_attendance->update([
                                                'status' => $student_leave_record->attendance_category_id == 2 ? 'ABSENT' : 'LATE',
                                            ]);
                                            $update_count++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

            $this->info("Number of period attendances updated: {$update_count}");
        });


    }

    public function migrateLeadershipPosition()
    {
        $this->info("Migration for master_leadership_positions");

        $from_data = $this->v1DbConnection->table('leadership_type')->orderBy('order_by', 'ASC')->get();
        $order_counter = count($from_data);
        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        foreach ($from_data as $d) {

            $to_insert[] = [
                'id' => $d->leadership_id,
                'name' => json_encode(['en' => $d->leadership_english_name, 'zh' => $d->leadership_chinese_name]),
                'sequence' => $order_counter--,
                'is_active' => true,
                'created_at' => now(),
            ];

        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(LeadershipPosition::class, $to_insert);
    }

    public function migrateLeadershipPositionRecord()
    {
        $this->info("Migration for leadership_position_records");

        $from_data = $this->v1DbConnection->table('class_leadership')
            ->join('class_m', 'class_m.class_id', 'class_leadership.class_id')
            ->where('class_leadership.year', '>=', 2024)
            ->get();
        $order_counter = count($from_data);
        $to_insert = [];

        $this->info("Number of rows: " . $order_counter);

        $system_user = User::query()
            ->where('email', '<EMAIL>')
            ->first();
        $student_ids = $this->_getStudentOldNewIds();
        $semester_classes = SemesterClass::query()
            ->with(['semesterSetting', 'classModel'])
            ->whereRelation('semesterSetting.semesterYearSetting', 'year', '>=', 2024)
            ->get();
        $semester_classes_data = [];

        foreach ($semester_classes as $semester_class) {
            $semester_classes_data[$semester_class->semesterSetting->name][$semester_class->classModel->code] = [
                'semester_class_id' => $semester_class->id,
                'semester_setting_id' => $semester_class->semester_setting_id
            ];
        }

        foreach ($from_data as $d) {
            $semester_name = $d->year . ' Sem ' . $d->semester;

            if (!isset($student_ids[$d->student_id])) {
                $this->info('Student id (old) : ' . $d->student_id . ' not found.');
                continue;
            }

            $semester_class = $semester_classes_data[$semester_name][$d->class_code];

            $to_insert[] = [
                'semester_class_id' => $semester_class['semester_class_id'],
                'semester_setting_id' => $semester_class['semester_setting_id'],
                'student_id' => $student_ids[$d->student_id],
                'leadership_position_id' => $d->leadership_id,
                'created_by' => $system_user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->info("Number of rows to be inserted: " . count($to_insert));

        $this->_insertData(LeadershipPositionRecord::class, $to_insert);
    }

    // WARNING!!!! OUTDATED, DUN RUN
    public function migrateEnglishClass()
    {
        $this->info("Migration for english class");

        $english_classes_from_data = $this->v1DbConnection->table('trans_english_class')
            ->where('trans_english_class.year', '=', $this->year)
            ->where('flag_authorize', '=', 'N')
            ->get();

        $english_levels_from_data = $this->v1DbConnection->table('english_name_list')
            ->leftJoin('student_m', function ($join) {
                $join->on('student_m.student_id', '=', 'english_name_list.student_id');
            })
            ->selectRaw('english_name_list.*, student_m.student_leave_date')
            ->where('english_name_list.year', '=', $this->year)
            ->where('flag_authorize', '=', 'N')
            ->get();

        $this->info("v1 : Number of english classes found: " . count($english_classes_from_data));
        $this->info("v1 : Number of english levels grouped found: " . count($english_levels_from_data));

        $english_levels_from_data = $english_levels_from_data->groupBy('english_class_id');

        //  get the first sem for $this->year
        $semester_setting = SemesterSetting::whereRelation('semesterYearSetting', 'year', $this->year)->first();

        $english_language_subject = Subject::query()->whereTranslations('name', 'English Language', 'ILIKE', false)->first();
        $english_oral_subject = Subject::query()->whereTranslations('name', 'English Oral', 'ILIKE', false)->first();


        if (!$this->isActual) {
            return;
        }

        DB::transaction(function () use (
            $english_classes_from_data,
            $english_levels_from_data,
            $semester_setting,
            $english_language_subject,
            $english_oral_subject,
        ) {
            $info_class_count = 0;
            $info_semester_class_count = 0;
            $info_class_subject_count = 0;
            $info_old_student_number = 0;
            $info_new_student_number = 0;
            $info_class_subject_teacher_count = 0;
            $info_student_classes_count = 0;
            $info_class_subject_students_count = 0;

            foreach ($english_classes_from_data as $d) {
                $english_level = $this->_convertEnglishLevel($d->english_level);

                $class = ClassModel::create([
                    'code' => $d->english_class_name,
                    'name' => [
                        'en' => $d->english_class_name,
                        'zh' => $d->english_class_name,
                    ],
                    'type' => ClassType::ENGLISH,
                    'english_level' => $english_level,
                    'grade_id' => $d->grade_id,
                    'is_active' => true,
                    'stream' => ClassStream::NOT_APPLICABLE,
                ]);

                $info_class_count++;

                $semester_class = SemesterClass::create([
                    'semester_setting_id' => $semester_setting->id,
                    'class_id' => $class->id,
                    'homeroom_teacher_id' => $d->employee_id,
                    'is_active' => true,
                    'default_grading_framework_id' => null,
                ]);

                $info_semester_class_count++;

                $class_subject_oral = ClassSubject::create([
                    'semester_class_id' => $semester_class->id,
                    'subject_id' => $english_oral_subject->id,
                    'number_of_period_per_week' => 0,
                ]);

                $class_subject_language = ClassSubject::create([
                    'semester_class_id' => $semester_class->id,
                    'subject_id' => $english_language_subject->id,
                    'number_of_period_per_week' => 7, // 2025 uses 7, need to change if there's any update
                ]);

                $info_class_subject_count += 2;

                // unique teacher ids
                $teacher_ids = [];

                $teacher_ids[] = $d->employee_id;

                if (isset($d->reading_id) && !in_array($d->reading_id, $teacher_ids)) {
                    $teacher_ids[] = $d->reading_id;
                }

                if (isset($d->writing_id) && !in_array($d->writing_id, $teacher_ids)) {
                    $teacher_ids[] = $d->writing_id;
                }

                if (isset($d->grammar_id) && !in_array($d->grammar_id, $teacher_ids)) {
                    $teacher_ids[] = $d->grammar_id;
                }


                $class_subject_teacher_payload = [];

                foreach ($teacher_ids as $index => $teacher_id) {
                    if ($index == 0) { // index 0 is always primary teacher
                        $class_subject_teacher_payload[] = [
                            'class_subject_id' => $class_subject_oral->id,
                            'employee_id' => $teacher_id,
                            'type' => ClassSubjectTeacherType::PRIMARY->value,
                        ];

                        $class_subject_teacher_payload[] = [
                            'class_subject_id' => $class_subject_language->id,
                            'employee_id' => $teacher_id,
                            'type' => ClassSubjectTeacherType::PRIMARY->value,
                        ];
                    } else {
                        $class_subject_teacher_payload[] = [
                            'class_subject_id' => $class_subject_oral->id,
                            'employee_id' => $teacher_id,
                            'type' => ClassSubjectTeacherType::SECONDARY->value,
                        ];

                        $class_subject_teacher_payload[] = [
                            'class_subject_id' => $class_subject_language->id,
                            'employee_id' => $teacher_id,
                            'type' => ClassSubjectTeacherType::SECONDARY->value,
                        ];
                    }
                }

                ClassSubjectTeacher::insert($class_subject_teacher_payload);

                $info_class_subject_teacher_count += count($class_subject_teacher_payload);


                $english_level_for_class = $english_levels_from_data[$d->english_class_id] ?? null;
                $english_level_for_class_key_by_student_id = $english_level_for_class->keyBy('student_id');

                if (!$english_level_for_class) {
                    // No students for this class
                    continue;
                }

                $english_level_student_ids = array_unique($english_level_for_class->pluck('student_id')->toArray());
                $new_student_ids = $this->_getStudentOldNewIds($english_level_student_ids);

                $info_old_student_number += count($english_level_student_ids);
                $info_new_student_number += count($new_student_ids);

                $student_classes_payload = [];
                $class_subject_students_payload = [];

                foreach ($english_level_student_ids as $index => $old_student_id) {
                    if (!isset($new_student_ids[$old_student_id])) {
                        continue;
                    }
                    if (!isset($english_level_for_class_key_by_student_id[$old_student_id])) {
                        continue;
                    }

                    $student_leave_date = $english_level_for_class_key_by_student_id[$old_student_id]->student_leave_date;

                    $student_classes_payload[] = [
                        'semester_setting_id' => $semester_setting->id,
                        'semester_class_id' => $semester_class->id,
                        'class_type' => $class->type,
                        'student_id' => $new_student_ids[$old_student_id],
                        'seat_no' => $index + 1, // incremental
                        'class_enter_date' => now()->toDateString(),
                        'class_leave_date' => $student_leave_date,
                        'is_active' => $student_leave_date == null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $class_subject_students_payload[] = [
                        'student_id' => $new_student_ids[$old_student_id],
                        'class_subject_id' => $class_subject_oral->id,
                    ];

                    $class_subject_students_payload[] = [
                        'student_id' => $new_student_ids[$old_student_id],
                        'class_subject_id' => $class_subject_language->id,
                    ];
                }

                StudentClass::insert($student_classes_payload);
                $info_student_classes_count += count($student_classes_payload);

                ClassSubjectStudent::insert($class_subject_students_payload);
                $info_class_subject_students_count += count($class_subject_students_payload);
            }

            $this->info("\nNumber of classes inserted: " . $info_class_count);
            $this->info("Number of semester classes inserted: " . $info_semester_class_count);
            $this->info("Number of class subjects inserted: " . $info_class_subject_count);
            $this->info("Number of class subject teachers inserted: " . $info_class_subject_teacher_count);
            $this->info("Number of student classes inserted: " . $info_student_classes_count);
            $this->info("Number of class subject students inserted: " . $info_class_subject_students_count);
            $this->info("\nNumber of old students found: " . $info_old_student_number);
            $this->info("Number of new students found: " . $info_new_student_number);
        });
    }

    /**
     * To initialize timetable and timeslots for all English subjects.
     * Require migrateEnglishClass to run before running this.
     * @return void
     */
    public function migrateEnglishClassInitTimetable()
    {

        $this->info("Migration for english class init timetable");

        if (!$this->isActual) {
            return;
        }

        DB::transaction(function () {

            // create a new period group for English classes
            $junior_period_group = PeriodGroup::with(['periods', 'periodLabels'])->where(DB::raw('name->>\'en\''), 'Junior Period Group')->first();
            $english_period_group = $junior_period_group->replicate();
            $english_period_group->name = [
                'en' => 'English Class Period Group',
                'zh' => '英语班组合'
            ];
            $english_period_group->save();

            foreach ($junior_period_group->periods as $period) {
                $new_period = $period->replicate();
                $new_period->period_group_id = $english_period_group->id;
                $new_period->save();
            }
            foreach ($junior_period_group->periodLabels as $period_label) {
                $new_period_label = $period_label->replicate();
                $new_period_label->period_group_id = $english_period_group->id;
                $new_period_label->save();
            }

            $this->info("Created new english period group - ID " . $english_period_group->id);

            // foreach english class, initialize timetable object
            $latest_semester_setting = SemesterSetting::where('is_current_semester', true)->first();

            $english_semester_classes = SemesterClass::whereHas('classModel', function ($q) {
                $q->where('type', ClassType::ENGLISH->value);
            })->where('semester_setting_id', $latest_semester_setting->id)->get();

            foreach ($english_semester_classes as $semester_class) {

                $this->info("Init timetable for english subject {$semester_class->classModel->code}");

                $timetable = Timetable::updateOrCreate([
                    'name' => 'English ' . $semester_class->classModel->code . ' Timetable ' . $semester_class->semesterSetting->name
                ], [
                    'semester_class_id' => $semester_class->id,
                    'period_group_id' => $english_period_group->id,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $this->timetableService
                    ->setTimetable($timetable)
                    ->createTimeSlotsViaPeriods();
            }
        });

    }

    /**
     * Migrate v1 english class timetable timeslots to skribble learn.
     * Make sure run migrateEnglishClassInitTimetable first before running this.
     * @return void
     * @throws \Throwable
     */
    public function migrateEnglishClassTimeslots()
    {

        $this->info("Migration for english class timeslots");
//
        if (!$this->isActual) {
            return;
        }

        // updating timeslot with the correct english classes
        $timetables = Timetable::with(['semesterClass.classModel.grade', 'semesterClass.semesterSetting'])
            ->whereHas('semesterClass.semesterSetting', function ($q) {
                $q->where('is_current_semester', true);
            })
            ->whereHas('semesterClass.classModel', function ($q) {
                $q->where('type', ClassType::ENGLISH->value);
            })
            ->where('is_active', true)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->semesterClass->classModel->code => $item];
            });

        $english_subject = Subject::where('code', '03')->first();

        $class_subjects = ClassSubject::with([
            'semesterClass.classModel',
            'teachers'
        ])->where('subject_id', $english_subject->id)
            ->whereHas('semesterClass.classModel', function ($q) {
                $q->where('type', ClassType::ENGLISH->value);
            })
            ->whereHas('semesterClass.semesterSetting', function ($q) {
                $q->where('is_current_semester', true);
            })
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->semesterClass->classModel->code => $item];
            });

        $updated_timeslot_count = 0;
        $updated_timetable_count = 0;

        DB::transaction(function () use (&$v1_data, &$timetables, &$class_subjects, &$updated_timeslot_count, &$updated_timetable_count) {
            $english_timetable_timeslots = $this->getEnglishTimeslot();

            foreach ($timetables as $timetable) {
                $english_timeslot = $english_timetable_timeslots[$timetable->semesterClass->classModel->grade->name];
                foreach ($english_timeslot as $day => $period) {
                    $timeslots = Timeslot::where('timetable_id', $timetable->id)
                        ->where('day', $day)
                        ->whereHas('period', function ($q) use ($period) {
                            $q->whereIn('period', $period);
                        })
                        ->get();

                    foreach ($timeslots as $timeslot) {
                        $class_subject_id = $class_subjects[$timetable->semesterClass->classModel->code]->id;

                        $timeslot->update([
                            'class_subject_id' => $class_subject_id,
                        ]);

                        $employee_ids = [];
                        foreach ($class_subjects[$timetable->semesterClass->classModel->code]->teachers as $teacher) {
                            $employee_ids[$teacher->id]['type'] = $teacher->pivot->type->value;
                        }

                        $timeslot->teachers()->sync($employee_ids);

                        $updated_timeslot_count++;
                    }
                }
                $updated_timetable_count++;
            }

            $this->info("Number of timeslots updated: {$updated_timeslot_count}");
            $this->info("Number of timetables updated: {$updated_timetable_count}");
            $this->info("Expected number of timeslots updated: " . count($timetables) * 7);
        });
    }

    public function migrateSubstituteTeachers()
    {

        $this->info("Migration for substitute teachers");

        $v1_data = $this->v1DbConnection->table('substitute_management AS sm')
            ->selectRaw('sm.*, class_m.class_code, class_m.class_english_name, course_m.course_code, course_m.course_english_name, tp.period_name,
            tp.no_period, tp.start_time, tp.end_time, original_teacher.employee_no AS original_employee_no, original_teacher.employee_name AS original_employee_name,
            replacement_teacher.employee_no AS replacement_employee_no, replacement_teacher.employee_name AS replacement_employee_name')
            ->join('class_m', 'class_m.class_id', '=', 'sm.class_id')
            ->join('course_m', 'course_m.course_id', '=', 'sm.course_id')
            ->join('time_period AS tp', 'tp.period_id', '=', 'sm.period')
            ->join('employee_m AS original_teacher', 'original_teacher.employee_id', '=', 'sm.employee_id')
            ->join('employee_m AS replacement_teacher', 'replacement_teacher.employee_id', '=', 'sm.subs_employee_id')
            ->where('substitute_date', '>=', '2025-01-01')
            ->orderBy('sm.substitute_id', 'DESC')
            ->get();

        $timetables = Timetable::with(['semesterClass.classModel', 'semesterClass.semesterSetting'])
            ->whereHas('semesterClass.semesterSetting', function ($q) {
                $q->where('is_current_semester', true);
            })
            ->where('is_active', true)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->semesterClass->classModel->code => $item];
            });

        $timeslots = Timeslot::with(['period', 'classSubject.subject'])->get()->mapWithKeys(function ($item) {
            return [$item->timetable_id . '-' . $item->day->value . '-' . $item->period->from_time . '-' . $item->period->to_time => $item];
        });

        $employees = Employee::selectRaw('COALESCE(old_employee_number, employee_number) AS match_employee_number, old_employee_number, employee_number, id, name')->get()->keyBy('match_employee_number');

        $to_be_inserted = [];

        foreach ($v1_data as $d) {

            $v1_compare = $d->class_code . '-' . $d->substitute_date . '-' . $d->start_time . '-' . $d->end_time . '-' . trim($d->course_english_name) . '-' . trim($d->replacement_employee_name);
            $this->info('[v1] ' . $v1_compare);

            if (!isset($timetables[$d->class_code])) {
                $this->warn('Timetable not found for ' . $d->class_code);
                continue;
            }

            $timetable = $timetables[$d->class_code];
            $day = strtoupper(Carbon::parse($d->substitute_date)->format('l'));
            $timeslot = $timeslots[$timetable->id . '-' . $day . '-' . $d->start_time . '-' . $d->end_time];

            if (!isset($employees[$d->replacement_employee_no])) {
                $this->warn("Substitute teacher not found for {$d->replacement_employee_no}");
                continue;
            }

            if (!isset($employees[$d->original_employee_no])) {
                $this->warn("Original teacher not found for {$d->original_employee_no}");
                continue;
            }

            $substitute_teacher = $employees[$d->replacement_employee_no];
            $original_teacher = $employees[$d->original_employee_no];

            // determine timeslot ID
            $to_be_inserted[] = [
                'timeslot_id' => $timeslot->id,
                'substitute_teacher_id' => $substitute_teacher->id,
                'substitute_date' => $d->substitute_date,
                'day' => $day,
                'requestor_id' => $original_teacher->id,
                'period_id' => $timeslot->period_id,
                'class_subject_id' => $timeslot->class_subject_id,
                'allowance' => $d->substitute_allowance,
                'remarks' => $d->remarks ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $v3_compare = $timetable->semesterClass->classModel->code . '-' . $d->substitute_date . '-' . $timeslot->period->from_time . '-' . $timeslot->period->to_time . '-' . trim($timeslot->classSubject->subject->name) . '-' . trim($substitute_teacher->name);

            $this->info('[v3] ' . $v3_compare);

            if ($v1_compare != $v3_compare) {
                $this->error("Not match!");
                continue;
            }

        }

        if ($this->isActual) {
            foreach (collect($to_be_inserted)->chunk(100) as $chunk) {
                SubstituteRecord::insert($chunk->toArray());
            }

            $this->info('v1 rows ' . count($v1_data) . ' - v3 rows ' . SubstituteRecord::count());
        }

    }


    public function migrateHostelPersonInCharge()
    {

        $this->info("Migration for substitute teachers");

        $v1_data = $this->v1DbConnection->table('warden_settings')
            ->selectRaw('employee_m.employee_id, employee_no, employee_name, employee_cname')
            ->join('employee_m', 'employee_m.employee_id', '=', 'warden_settings.employee_id')
            ->orderBy('warden_id', 'ASC')
            ->where('warden_year', 2025)
            ->get();

        $employees = Employee::selectRaw('COALESCE(old_employee_number, employee_number) AS match_employee_number, old_employee_number, employee_number, user_id, id, name')->get()->keyBy('match_employee_number');

        foreach ($v1_data as $d) {

            $employee = $employees[$d->employee_no];

            $this->info("Create hostel PIC: " . $employee->name);

            if ($this->isActual) {
                UserSpecialSetting::updateOrCreate([
                    'module' => UserSpecialSettingModule::HOSTEL->value,
                    'submodule' => UserSpecialSettingSubModule::HOSTEL_PIC->value,
                    'user_id' => $employee->user_id,
                ], [
                    'custom_permissions' => null,
                ]);
            }

        }


    }

    public function migrateWithdrawalReason()
    {

        $this->info("Migration for withdrawal reason");

        $v1_data = $this->v1DbConnection->table('withdrawal_reason')
            ->orderBy('withdrawal_reason_id', 'ASC')
            ->get();

        $order_counter = count($v1_data);

        $to_be_inserted = [];

        foreach ($v1_data as $d) {

            $this->info("Inserting " . $d->withdrawal_desc . ', ' . $d->withdrawal_cdesc);

            $to_be_inserted[] = [
                'id' => $d->withdrawal_reason_id,
                'name' => json_encode(['en' => $d->withdrawal_desc, 'zh' => $d->withdrawal_cdesc]),
                'description' => null,
                'sequence' => $order_counter--,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if ($this->isActual) {
            DB::transaction(function () use ($to_be_inserted) {
                WithdrawalReason::insert($to_be_inserted);
                $this->restartId(WithdrawalReason::class);
            });
        }

    }

    public function migrateExemptStudents()
    {
        $this->info("Migration for exempted students");

        $v1_data = $this->v1DbConnection->table('trans_major_details')
            ->join('trans_major', 'trans_major.major_course_id', '=', 'trans_major_details.major_course_id')
            ->where('trans_major.year', 2025)
            ->where('trans_major.semester', 1)
            ->get();

        $this->info("Number of records found: " . count($v1_data));
        $old_student_ids = $v1_data->pluck('student_id');
        $new_student_ids = $this->_getStudentOldNewIds($old_student_ids);

        $processed = 0;
        DB::transaction(function () use ($v1_data, $new_student_ids, &$processed) {
            foreach ($v1_data as $data) {
                $student_id = $new_student_ids[$data->student_id];
                $subject_id = $data->course_id;

                $update = ResultSourceSubject::whereRelation('resultSource.studentGradingFramework', 'student_id', $student_id)
                    ->whereRelation('resultSource.studentGradingFramework', 'academic_year', '2025')
                    ->whereRelation('resultSource.studentGradingFramework', 'is_active', true)
                    ->whereHas('resultSource', function ($query) {
                        $query->whereIn('code', ['SEM1EXAM', 'SEM1FINALEXAM']);
                    })
                    ->where('subject_id', $subject_id)
                    //->get();
                    ->update(['is_exempted' => true]);

                if ($update) {
                    $processed++;
                }
            }
        });
        $this->info("Number of records processed: " . $processed);
    }

    public function _cleanDate($date, $format = 'Y-m-d')
    {

        $date = trim($date);

        if ($date == null || $date === '0000-00-00' || strlen($date) !== 10) {
            return null;
        }

        $dateTime = DateTime::createFromFormat($format, $date);

        if (!$dateTime) {
            return null;
        }

        if ($dateTime->format($format) !== $date) {
            return null;
        }

        return $date;
    }

    public function _convertMarriageStatus($value)
    {
        $value = strtoupper(trim($value));

        if (empty($value)) {
            return null;
        }

        return MarriedStatus::{$value} ?? null;
    }

    public function _convertEmploymentType($value)
    {

        switch (trim($value)) {
            case 'Full Time':
                return JobType::FULL_TIME;
            case 'Part Time':
                return JobType::PART_TIME;
            case 'Temporary':
                return JobType::TEMPORARY;
            default:
                return JobType::UNKNOWN;
        }

    }

    public function _convertEnglishLevel($value)
    {
        return match ($value) {
            1 => EnglishLevel::STARTER,
            2 => EnglishLevel::ELEMENTARY,
            3 => EnglishLevel::PRE_INTERMEDIATE,
            4 => EnglishLevel::INTERMEDIATE,
            5 => EnglishLevel::UPPER_INTERMEDIATE,
            6 => EnglishLevel::ADVANCED_1,
            7 => EnglishLevel::ADVANCED_2,
            8 => EnglishLevel::ADVANCED_3,
            default => throw new \Exception('Invalid English level: ' . $value),
        };
    }

    public function _determineEmployeeStatus($status)
    {
        if ($status == 0) {
            return EmployeeStatus::WORKING;
        } else {
            if ($status == 1) {
                return EmployeeStatus::RESIGNED;
            }
        }

        return null;
    }

    public function _cleanPhoneNumber($number)
    {

        // remove non-numeric characters
        $number = preg_replace("/\D/", '', $number);

        if (strlen($number) === 0) {
            return null;
        }

        // format into international format
        if (preg_match("/^65/", $number)) {
            $number = (string) new PhoneNumber($number, 'SG');
        } else {
            if (preg_match("/^62/", $number)) {
                $number = (string) new PhoneNumber($number, 'ID');
            } else {
                if (preg_match("/^886/", $number)) {
                    $number = (string) new PhoneNumber($number, 'TW');
                } else {
                    if (preg_match("/^86/", $number)) {
                        $number = (string) new PhoneNumber($number, 'CN');
                    } else {
                        if (preg_match("/^66/", $number)) {
                            $number = (string) new PhoneNumber($number, 'TH');
                        } else {
                            $number = (string) new PhoneNumber($number, 'MY');
                        }
                    }
                }
            }
        }

        return trim($number);

    }

    public function _cleanMaritalStatus($status)
    {

        $mapping = [
            '已婚' => MarriedStatus::MARRIED,
            '己婚' => MarriedStatus::MARRIED,
            '以婚' => MarriedStatus::MARRIED,
            '夫妻' => MarriedStatus::MARRIED,
            'married' => MarriedStatus::MARRIED,
            'marriage' => MarriedStatus::MARRIED,
            '健康' => MarriedStatus::MARRIED,
            '结婚' => MarriedStatus::MARRIED,
            '良好' => MarriedStatus::MARRIED,
            '离婚' => MarriedStatus::DIVORCED,
            'divorced' => MarriedStatus::DIVORCED,
            'divorce' => MarriedStatus::DIVORCED,
            '已离婚' => MarriedStatus::DIVORCED,
            '单亲' => MarriedStatus::SINGLE,
            'single' => MarriedStatus::SINGLE,
            '离异' => MarriedStatus::SEPARATED,
            '分居' => MarriedStatus::DIVORCED,
            'widow' => MarriedStatus::WIDOWED,
            '' => MarriedStatus::UNKNOWN,
            '-' => MarriedStatus::UNKNOWN,
        ];

        $status = trim(strtolower($status));
        return $mapping[$status] ?? MarriedStatus::UNKNOWN;

    }

    public function _cleanEmail($email)
    {

        if ($email == null || strlen($email) <= 5) {
            return null;
        }

        return trim(strtolower($email));

    }

    public function _determineIcPassport($row)
    {

        $is_malaysian = $row->student_nationality == 1;

        $nric = null;
        $passport_number = null;

        if ($is_malaysian) {
            $nric = preg_replace("/\D/", '', $row->student_ic);
        } else {
            $passport_number = $row->student_ic;
        }

        return [
            'nric' => $nric,
            'passport_number' => $passport_number,
        ];

    }


    public function _determineIcPassportRegex($data)
    {

        $nric = null;
        $passport_number = null;

        // remove non alphabet and numeric chars
        $data = preg_replace("/\W/", '', $data);

        if (strlen($data) === 0 || $data == null) {
            return [
                'nric' => null,
                'passport_number' => null,
            ];
        }

        if (preg_match("/^\d{12}/", $data)) {
            $nric = $data;
        } else {
            if (preg_match("/^[a-zA-Z]+\d+/", $data)) {
                $passport_number = $data;
            }
        }

        return [
            'nric' => $nric,
            'passport_number' => $passport_number,
        ];

    }

    public function _determineGender($gender)
    {
        switch (strtoupper($gender)) {
            case 'M':
                return Gender::MALE;
            case 'F':
                return Gender::FEMALE;
            default:
                return Gender::UNKNOWN;
        }
    }

    public function _determineAddress($address)
    {

        $postcode = null;
        $city = null;
        $state = null;
        $country = null;

        // postcode
        $matches = [];
        $matched = preg_match("/\d{5}/", $address, $matches);

        if ($matched && count($matches) > 0) {
            $postcode = $matches[0];
        }

        // city
        $city_list = [
            'Seberang Jaya',
            'Butterworth',
            'Putrajaya',
            'Cyberjaya',
            'Ipoh',
            'Shah Alam',
            'Subang Jaya',
            'Petaling Jaya',
            'Bandar Sunway',
            'Tanjung Karang',
            'Kuala Selangor',
            'Sekinchan',
            'Puchong',
            'Rawang',
            'Bestari Jaya',
            'Jenjarom',
            'Batu Arang',
            'Seri Kembangan',
            'Bukit Jalil',
            'Mont Kiara',
            'Sungai Buloh',
            'Setapak',
            'Sri Petaling',
            'Banting',
            'Kepong',
            'Sepang',
            'Semenyih',
            'Bandar Sri Damansara',
            'Damansara Perdana',
            'Damansara Utama',
            'Damansara Jaya',
            'Ampang',
            'Kapar',
            'Bandar Bukit Raja',
            'Cheras',
            'George Town',
            'Georgetown',
            'Kuantan',
            'Sungai Petani',
            'Johor Bahru',
            'Kota Bharu',
            'Kota Bahru',
            'Melaka',
            'Kota Kinabalu',
            'Seremban',
            'Sandakan',
            'Kuching',
            'Kluang',
            'Muar',
            'Pasir Gudang',
            'Kuala Terengganu',
            'Sibu',
            'Taiping',
            'Kajang',
            'Miri',
            'Teluk Intan',
            'Kulai',
            'Alor Setar',
            'Bukit Mertajam',
            'Lahad Datu',
            'Segamat',
            'Tumpat',
            'Keningau',
            'Batu Pahat',
            'Batu Gajah',
            'Bayan Lepas',
            'Port Dickson',
            'Bintulu',
            'Tawau',
            'Labuan',
            'Kuala Lumpur',
            'Klang',
        ];

        foreach ($city_list as $c) {
            $matches = [];
            $matched = preg_match("/" . $c . "/i", $address, $matches);

            if ($matched && count($matches) > 0) {
                $city = $matches[0];
                break;
            }
        }

        // state & country
        $states = State::all();

        foreach ($states as $s) {

            $name = $s->getTranslation('name', 'en');

            if (preg_match("/" . $name . "/i", $address)) {
                $state = $s->id;
                $country = $s->country_id;
                break;
            }
        }

        return [
            'city' => $city !== null ? strtoupper($city) : null,
            'postcode' => $postcode,
            'state' => $state,
            'country' => $country,
        ];

    }

    public function getBookClassificationId(string $edesc): int
    {
        $first_character = substr($edesc, 0, 1);

        if ($first_character == 'E') {
            $first_character = substr($edesc, 1, 1);
        }
        switch ($first_character) {
            case 0 :
                return 100;
            case 1 :
                return 101;
            case 2 :
                return 201;
            case 3 :
                return 301;
            case 4 :
                return 401;
            case 5 :
                return 501;
            case 6 :
                return 601;
            case 7 :
                return 701;
            case 8 :
                return 801;
            case 9 :
                return 901;
            default :
                return 1001;
        }
    }

    public function _determineStudentFood($food): DietaryRestriction
    {
        switch ($food) {
            case 1:
                return DietaryRestriction::VEGETARIAN;
            default:
                return DietaryRestriction::NONE;
        }

    }

    public function _determineAuthorsFromBook(string $column): array
    {
        return $this->v1DbConnection->table('book_m')
            ->select($column)
            ->whereNotNull($column)
            ->orWhere($column, '<>', '')
            ->get()
            ->pluck($column)
            ->toArray();
    }

    public function _determineBookBinding($binding): ?BookBinding
    {
        return match ($binding) {
            1 => BookBinding::HARD_COVER,
            2 => BookBinding::SOFT_COVER,
            default => null,
        };

    }

    public function restartId($class, $next_id = null)
    {
        $next_id = $next_id ?: $class::query()->select('id')->orderBy('id', 'desc')->first()->id + 1;
        $table_name = resolve($class)->getTable();
        DB::statement("ALTER SEQUENCE {$table_name}_id_seq RESTART WITH {$next_id}");
    }

    public function _determineBookCondition($condition)
    {
        return match ($condition) {
            "G" => BookCondition::GOOD,
            "D" => BookCondition::DAMAGE,
            "V" => BookCondition::MISSING,
            "W" => BookCondition::WRITE_OFF,
            default => null,
        };
    }

    public function _determineMeritDemeritStatus($status)
    {
        switch ($status) {
            case 'Merit' :
                return HostelMeritDemeritType::MERIT;
            case 'Demerit' :
                return HostelMeritDemeritType::DEMERIT;
        }
    }

    public function _determineSubjectCode($code, $id)
    {
        if ($code == '096' || $code == '69' || $code == '22') {
            $code = $code . '-' . $id;
        }

        return $code;
    }

    public function _determineTypeOfBonus($type_of_bonus)
    {
        switch ($type_of_bonus) {
            case 1 :
                return CompetitionBonusType::OFF_CAMPUS;
            case 2 :
                return CompetitionBonusType::PERFORMANCE;
        }
    }

    private function _insertData(string $class, array $to_insert, $restart_id = 1)
    {
        if (!$this->isActual) {
            return;
        }

        $this->info("== ACTUAL ==");

        DB::disableQueryLog();
        $dispatcher = DB::connection()->getEventDispatcher();
        DB::connection()->unsetEventDispatcher();

        DB::transaction(function () use ($to_insert, $class, $restart_id) {
            foreach (array_chunk($to_insert, 1000) as $data) {
                app($class)->insert($data);
            }

            if ($restart_id) {
                $this->restartId($class);
            }
        });

        DB::enableQueryLog();
        DB::connection()->setEventDispatcher($dispatcher);

        $this->info("Number of rows " . app($class)->getTable() . " after inserted: " . app($class)->count());
    }

    private function _getStudentOldNewIds($old_ids = []): array
    {
        $data = MigrationMapping::query()
            ->where('model', Student::class);

        if ($old_ids) {
            $data->whereIn('old', $old_ids);
        }

        return $data->get()
            ->pluck('new', 'old')
            ->toArray();
    }

    private function _determineClassType($type)
    {
        $type = trim($type);

        switch ($type) {
            case "MAJOR" :
                return ClassType::PRIMARY;
            case "ELECTIVE" :
                return ClassType::ELECTIVE;
        }
    }

    private function _determineDayFromInteger($day)
    {
        switch ($day) {
            case 1 :
                return Day::MONDAY;
            case 2 :
                return Day::TUESDAY;
            case 3 :
                return Day::WEDNESDAY;
            case 4 :
                return Day::THURSDAY;
            case 5 :
                return Day::FRIDAY;
            case 6 :
                return Day::SATURDAY;
            case 7 :
                return Day::SUNDAY;
        }
    }

    private function _determinePeriodAttendanceStatus($attendance_category_id)
    {
        switch ($attendance_category_id) {
            case 1:
                return PeriodAttendanceStatus::PRESENT;
            case 2:
                return PeriodAttendanceStatus::ABSENT;
            case 3:
                return PeriodAttendanceStatus::LATE;
        }
    }

    private function _determinePeriodGroup($name, $period_groups)
    {
        if ($name[0] == 'J') {
            return $period_groups['Junior Period Group'];
        }

        return $period_groups['Senior Period Group'];
    }

    private function _getTimeslotPlaceholders(): array
    {
        return [
            'Junior Period Group' => [
                'MONDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '11:55:00-12:35:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'PBS',
                    '16:00:00-16:35:00' => 'PBS'
                ],
                'TUESDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '11:55:00-12:35:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'PBS',
                    '16:00:00-16:35:00' => 'PBS'
                ],
                'WEDNESDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '11:55:00-12:35:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'Cocurriculum Requirement',
                    '16:00:00-16:35:00' => 'Cocurriculum Requirement'
                ],
                'THURSDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '11:55:00-12:35:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                ],
                'FRIDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '11:55:00-12:35:00' => '午休',
                    '14:35:00-15:10:00' => 'Cocurriculum Requirement',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'Cocurriculum Requirement',
                ],
            ],
            'Senior Period Group' => [
                'MONDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '12:35:00-13:15:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                ],
                'TUESDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '12:35:00-13:15:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                ],
                'WEDNESDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '12:35:00-13:15:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'Cocurriculum Requirement',
                    '16:00:00-16:35:00' => 'Cocurriculum Requirement'
                ],
                'THURSDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '12:35:00-13:15:00' => '午休',
                    '15:10:00-15:20:00' => '班务',
                ],
                'FRIDAY' => [
                    '07:31:00-07:45:00' => '班务',
                    '09:00:00-09:20:00' => '早休',
                    '10:35:00-10:40:00' => '休息',
                    '12:35:00-13:15:00' => '午休',
                    '14:35:00-15:10:00' => 'Cocurriculum Requirement',
                    '15:10:00-15:20:00' => '班务',
                    '15:20:00-15:55:00' => 'Cocurriculum Requirement',
                ],
            ]
        ];
    }

    private function _getCocurriculumTimeslot()
    {
        return [
            [
                'day' => Day::WEDNESDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:25:00',
                        'new_time_to' => '15:55:00',
                    ],
                    [
                        'original_time_from' => '16:00:00',
                        'original_time_to' => '16:35:00',
                        'new_time_from' => '16:00:00',
                        'new_time_to' => '16:45:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '童军团',
                    '学生警察部队',
                    '圣约翰救伤队',
                    '合唱团',
                    '戏剧',
                    '华文',
                    '国文',
                    '英文',
                    '日文A',
                    '手作',
                    '史地',
                    '书艺',
                    '影片制作',
                    '时事研究',
                    '总务服务团',
                    '环保',
                    '园艺',
                    '校讯组',
                    '游泳—男',
                    '外展',
                    '歌曲创作'
                ]
            ],
            [
                'day' => Day::WEDNESDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:25:00',
                        'new_time_to' => '15:55:00',
                    ],
                    [
                        'original_time_from' => '16:00:00',
                        'original_time_to' => '16:35:00',
                        'new_time_from' => '16:00:00',
                        'new_time_to' => '16:55:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '电脑动画A'
                ]
            ],
            [
                'day' => Day::WEDNESDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:25:00',
                        'new_time_to' => '15:55:00',
                    ],
                    [
                        'original_time_from' => '16:00:00',
                        'original_time_to' => '16:35:00',
                        'new_time_from' => '16:00:00',
                        'new_time_to' => '17:30:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '烘焙',
                    '烹饪'
                ]
            ],
            [
                'day' => Day::FRIDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '14:35:00',
                        'original_time_to' => '15:10:00',
                        'new_time_from' => '14:35:00',
                        'new_time_to' => '15:10:00',
                    ],
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:20:00',
                        'new_time_to' => '15:55:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '学长团',
                    '华乐团',
                    '管乐团',
                    '口琴',
                    '弦乐团',
                    '舞蹈',
                    '醒狮团',
                    '24节令鼓队',
                    '吉他乐团',
                    '韩文',
                    '日文B',
                    '美术',
                    '陶艺',
                    '围棋',
                    '中国象棋',
                    '西洋棋',
                    '创意数学',
                    '科学',
                    '摄影',
                    '机器人',
                    '漫画绘制',
                    '美编与设计',
                    '图书管理',
                    '大传-广播组',
                    '跆拳道',
                    '武术',
                    '瑜伽',
                    '游泳—女',
                    '花式跳绳',
                ]
            ],
            [
                'day' => Day::FRIDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '14:35:00',
                        'original_time_to' => '15:10:00',
                        'new_time_from' => '14:35:00',
                        'new_time_to' => '15:10:00',
                    ],
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:20:00',
                        'new_time_to' => '16:05:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '电脑动画B',
                ]
            ],
            [
                'day' => Day::FRIDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '14:35:00',
                        'original_time_to' => '15:10:00',
                        'new_time_from' => '14:35:00',
                        'new_time_to' => '15:10:00',
                    ],
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:20:00',
                        'new_time_to' => '16:15:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '扯铃',
                ]
            ],
            [
                'day' => Day::FRIDAY,
                'timeslots' => [
                    [
                        'original_time_from' => '14:35:00',
                        'original_time_to' => '15:10:00',
                        'new_time_from' => '14:35:00',
                        'new_time_to' => '15:10:00',
                    ],
                    [
                        'original_time_from' => '15:20:00',
                        'original_time_to' => '15:55:00',
                        'new_time_from' => '15:20:00',
                        'new_time_to' => '16:30:00',
                    ],
                ],
                'cocurriculum_names' => [
                    '古筝乐团',
                    '会心'
                ]
            ],
        ];
    }

    private function getEnglishTimeslot(): array
    {
        return [
            'JUNIOR 1' => [
                'MONDAY' => [
                    11, 12 // 第7节，第8节
                ],
                'TUESDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'THURSDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'FRIDAY' => [
                    8 // 第5节
                ]
            ],
            'JUNIOR 2' => [
                'MONDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'TUESDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'WEDNESDAY' => [
                    11, 12 // 第7节，第8节
                ],
                'THURSDAY' => [
                    11 // 第7节
                ],
            ],
            'JUNIOR 3' => [
                'MONDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'TUESDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'WEDNESDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'THURSDAY' => [
                    13 // 第9节
                ],
            ],
            'SENIOR 1' => [
                'MONDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'WEDNESDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'THURSDAY' => [
                    13 // 第9节
                ],
                'FRIDAY' => [
                    9, 10 // 第6节，第7节
                ],
            ],
            'SENIOR 2' => [
                'MONDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'WEDNESDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'THURSDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'FRIDAY' => [
                    12 // 第8节
                ],
            ],
            'SENIOR 3' => [
                'TUESDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'WEDNESDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'THURSDAY' => [
                    6 // 第4节
                ],
                'FRIDAY' => [
                    5, 6 // 第3节，第4节
                ],
            ],

        ];
    }
}
