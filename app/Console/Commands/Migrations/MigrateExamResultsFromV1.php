<?php

namespace App\Console\Commands\Migrations;

use App\Models\Exam;
use App\Models\MigrationMapping;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateExamResultsFromV1 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'results:migrate {--grade=} {--class=} {--student=} {--year=2024} {--semester=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $grade = $this->option('grade');
        $class = $this->option('class');
        $student = $this->option('student');
        $year = $this->option('year');
        $semester = $this->option('semester');

        $students_missing_subject = [];
        $student_missing_cocu = [];
        $sgf_not_found = [];

        $v1DbConnection = DB::connection('smsv1');

        if ( $grade === null && $class === null && $student === null ) {
            $this->error("Please specify at least a grade, class or student");
            return false;
        }

        $query = $v1DbConnection->table('exam_result_semester')
            ->leftJoin('course_m', 'course_m.course_id', '=', 'exam_result_semester.course_id')
            ->where('year', $year)
            ->where('semester', $semester);


        // Co-cu
        $cocu_query = $v1DbConnection->table('cocurriculum_result_detail')
            ->join('cocurriculum_result', 'cocurriculum_result.cocurriculum_result_id', '=', 'cocurriculum_result_detail.cocurriculum_result_id')
            ->where('year', $year)
            ->where('semester', $semester);

        if ( $grade !== null ) {

            $grade = strtoupper($grade);

            $grade_mapping = [
                'J1' => 1,
                'J2' => 2,
                'J3' => 3,
                'S1' => 5,
                'S2' => 6,
                'S3' => 7,
            ];

            $student_ids = $v1DbConnection->table('student_class')->select('student_id')
                ->where('year', $year)
                ->where('semester', $semester)
                ->where('grade_id', $grade_mapping[$grade])
                ->get();

            $query->whereIn('student_id', $student_ids->pluck('student_id'));
            $cocu_query->whereIn('student_id', $student_ids->pluck('student_id'));

        }

        if ( $class !== null ) {

            $class = strtoupper($class);

            $v1_class = $v1DbConnection->table('class_m')
                ->where('year', $year)
                ->where('class_code', $class)
                ->first();

            $student_ids = $v1DbConnection->table('student_class')->select('student_id')
                ->where('year', $year)
                ->where('semester', $semester)
                ->where('class_id', $v1_class->class_id)
                ->get();

            $query->whereIn('student_id', $student_ids->pluck('student_id'));
            $cocu_query->whereIn('student_id', $student_ids->pluck('student_id'));

        }

        if ( $student !== null ) {

            $student = strtoupper($student);

            $v1_student = $v1DbConnection->table('student_m')
                ->select('student_id')
                ->where('student_no', $student)
                ->first();

            $query->where('student_id', $v1_student->student_id);
            $cocu_query->where('student_id', $v1_student->student_id);

        }

        $exam_data = $query->get()->groupBy('student_id');
        $cocu_data = $cocu_query->get()->keyBy('student_id');

        $exam = Exam::where('code', "{$year}S{$semester}TOTAL")->firstOrFail();

        foreach ( $exam_data as $student_id => $student_data ) {

            $data = MigrationMapping::query()
                ->where('model', Student::class)
                ->where('old', $student_id)
                ->first();

            if ( $data === null ){
                $this->warn("Unable to find student via mapping - " . $student_id);
                continue;
            }

            $v3_student_id = $data->new;

            $sgf = StudentGradingFramework::where('student_id', $v3_student_id)
                ->where('is_active', true)
                ->first();

            if ( $sgf === null ){
                $this->error("SGF not found for V3 student ID " . $v3_student_id);
                $sgf_not_found[] = $v3_student_id;
                continue;
            }

            $result_source = ResultSource::where('student_grading_framework_id', $sgf->id)
                ->whereHas('exams', function($q) use (&$exam) {
                    $q->where('exams.id', $exam->id);
                })->firstOrFail();

            foreach ( $student_data as $d ) {

                $subject = Subject::where('id', $d->course_id)->firstOrFail();

                $result_source_subject = ResultSourceSubject::where('result_source_id', $result_source->id)
                    ->where('subject_id', $subject->id)
                    ->first();

                $this->info("Updating for student ID {$v3_student_id}, subject {$subject->name}, marks = {$d->semester_mark}");

                if ( $result_source_subject === null ) {
                    $this->error('Result source subject for ' . $subject->code . ' not found');
                    $students_missing_subject[] = $v3_student_id;
                    continue;
                }

                ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject->id)
                    ->where('code', 'FINAL')
                    ->update([
                        'actual_score' => $d->semester_mark
                    ]);
            }

            // co-cu results
            $cocu_marks = $cocu_data[$student_id]->mark;

            // ID 21 = cocu subject
            $subject = Subject::where('code', 'COCURRICULUM')->firstOrFail();

            $this->info("Cocu subject found - ID " . $subject->id);

            $result_source_subject = ResultSourceSubject::where('result_source_id', $result_source->id)
                ->where('subject_id', $subject->id)
                ->first();

            if ( $result_source_subject === null ) {
                $this->error('Result source subject for ' . $subject->code . ' not found');
                $student_missing_cocu[] = $v3_student_id;
                continue;
            }

            $this->info("Updating for student ID {$v3_student_id}, subject {$subject->name}, marks = {$cocu_marks}");

            ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject->id)
                ->where('code', 'FINAL')
                ->update([
                    'actual_score' => $cocu_marks
                ]);


        }

        $students_missing_subject = array_unique($students_missing_subject);
        $this->info("DONE!");
        $this->info("Number of students missing result source subject: ". count($students_missing_subject));
        $this->info("Student List: ");

        foreach($students_missing_subject as $student){
            $this->info($student. ",");
        }

        $this->info("Number of students missing cocu: ". count($student_missing_cocu));
        $this->info("Student List: ");

        foreach($student_missing_cocu as $student){
            $this->info($student. ",");
        }

        $this->info("Number of students missing grading framework: ". count($sgf_not_found));
        $this->info("Student List: ");
        echo '<pre>'; print_r($sgf_not_found); echo '</pre>';

    }
}
