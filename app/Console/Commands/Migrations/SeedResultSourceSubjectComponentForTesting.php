<?php

namespace App\Console\Commands\Migrations;

use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SeedResultSourceSubjectComponentForTesting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'result-source-subject-component-test:seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $result_source_subject_components = ResultSourceSubjectComponent::all();

        echo 'Seeding components, please wait...'."\n";
        foreach ($result_source_subject_components as $component){
            $component->update(['actual_score' => rand(1, 100)]);
        }

        $seeds = [
            [
                'class_code' => 'J111',
                'subject_id' =>  18, // Code 21, Name Art
            ],
            [
                'class_code' => 'J212',
                'subject_id' =>  19, // Code 22-19 , Name Computer Studies

            ],
            [
                'class_code' => 'J312',
                'subject_id' =>  57, // Code 03, Name English

            ],
            [
                'class_code' => 'S1C1',
                'subject_id' =>  4, // Code 05, Name Adv Mathematics

            ],
            [
                'class_code' => 'S1S3',
                'subject_id' =>  7, // Code 09, Name Biology

            ],
            [
                'class_code' => 'S2C1',
                'subject_id' =>  23, // Code 69-23, Name Lisan Bahasa Malaysia

            ],
            [
                'class_code' => 'S2S1',
                'subject_id' =>  19, // Code 22-19 , Name Computer Studies

            ],
            [
                'class_code' => 'S3C1',
                'subject_id' =>  23, // Code 69-23, Name Lisan Bahasa Malaysia
            ],
            [
                'class_code' => 'S3S2',
                'subject_id' =>  73, // Code 69-73, Name Outward Bound
            ]
        ];


        foreach ($seeds as $seed){
            echo 'Setting Components to Null for: '. $seed['class_code']."\n";

            $result_source_subjects = ResultSourceSubject::with('components')
                ->where('subject_id', $seed['subject_id'])
                ->whereRelation('resultSource.studentGradingFramework.student.primaryClass.semesterClass.classModel', 
                    'code', $seed['class_code'])
                ->get();
        
            foreach ($result_source_subjects as $result_source_subject){
                $result_source_subject->components()->update(['actual_score' => null]);
            }
        }

    }
}
