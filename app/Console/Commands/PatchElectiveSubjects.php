<?php

namespace App\Console\Commands;

use App\Enums\ClassType;
use App\Enums\SubjectType;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchElectiveSubjects extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:elective-subjects {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    private bool $isActual;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;

        $data = DB::connection('smsv1')->table('trans_elective')
            ->join('course_m', 'course_m.course_id', '=', 'trans_elective.course_id')
            ->where('year', 2025)
            ->where('semester', 1)
            ->get()
            ->keyBy('course_code');

        $elective_subjects = Subject::with('classSubjects')
            ->where('type', SubjectType::ELECTIVE->value)
            ->whereIn('code', $data->pluck('course_code')->unique()->toArray())
            ->get();

        $student_mappings = DB::table('migration_mapping')
            ->where('model', Student::class)
            ->get()
            ->pluck('new', 'old')
            ->toArray();

        DB::transaction(function () use ($elective_subjects, $data, $student_mappings) {
            foreach ($elective_subjects as $elective_subject) {
                foreach ($elective_subject->classSubjects as $class_subject) {
                    if ($this->isActual) {
                        $class_subject->students()->sync([]);
                    }
                }

                if ($this->isActual) {
                    $class = ClassModel::firstOrCreate([
                        'code' => $elective_subject->code,
                        'type' => ClassType::ELECTIVE,
                    ], [
                        'name' => $elective_subject->getTranslations('name'),
                        'is_active' => true,
                        'stream' => 'NA'
                    ]);

                    $semester_class = SemesterClass::firstOrCreate([
                        'semester_setting_id' => SemesterSetting::where('code', '2025SEM1')->firstOrFail()->id,
                        'class_id' => $class->id,
                    ], [
                        'is_active' => true,
                    ]);

                    $class_subject = ClassSubject::firstOrCreate([
                        'semester_class_id' => $semester_class->id,
                        'subject_id' => $elective_subject->id,
                    ], [
                        'number_of_period_per_week' => 0,
                    ]);

                    $student_ids = DB::connection('smsv1')->table('trans_elective_details')
                        ->where('id_elective_course', $data[$elective_subject->code]->id_elective_course)
                        ->get()
                        ->pluck('student_id');

                    $new_student_ids = array_map(function ($student_id) use ($student_mappings) {
                        return $student_mappings[$student_id];
                    }, $student_ids->toArray());

                    $class_subject->students()->sync($new_student_ids);

                    foreach ($new_student_ids as $student_id) {
                        StudentClass::firstOrCreate([
                            'student_id' => $student_id,
                            'semester_class_id' => $semester_class->id,
                            'semester_setting_id' => $semester_class->semester_setting_id,
                            'class_type' => ClassType::ELECTIVE,
                        ], [
                            'is_active' => true,
                            'is_latest_class_in_semester' => true,
                            'class_enter_date' => '2025-01-01'
                        ]);
                    }
                }
            }
        });

        $this->info("Done!");
    }
}
