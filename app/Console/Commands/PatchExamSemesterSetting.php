<?php

namespace App\Console\Commands;

use App\Enums\ExamSemesterSettingCategory;
use App\Models\ExamSemesterSetting;
use Illuminate\Console\Command;

class PatchExamSemesterSetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:exam-semester-setting';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $data = [
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 1,
                'from_date' => '2024-11-05',
                'to_date' => '2025-05-09',
            ],
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 2,
                'from_date' => '2024-11-05',
                'to_date' => '2025-05-09',
            ],
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 3,
                'from_date' => '2024-11-05',
                'to_date' => '2025-05-09',
            ],
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 5,
                'from_date' => '2024-10-09',
                'to_date' => '2025-05-09',
            ],
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 6,
                'from_date' => '2024-11-05',
                'to_date' => '2025-05-09',
            ],
            [
                'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
                'semester_setting_id' => 17,
                'grade_id' => 7,
                'from_date' => '2024-11-05',
                'to_date' => '2025-05-09',
            ],
        ];

        ExamSemesterSetting::insert($data);
    }
}
