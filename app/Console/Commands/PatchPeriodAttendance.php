<?php

namespace App\Console\Commands;

use App\Enums\AttendanceStatus;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\PeriodAttendance;
use App\Models\Student;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Repositories\LeaveApplicationPeriodRepository;
use App\Repositories\PeriodAttendanceRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchPeriodAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:period-attendance {--actual} {--exclude-patch-period-attendance-status} {--exclude-patch-has-mark-deduction} {--exclude-remove-timeslot-teacher} {--exclude-patch-leave-application}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch period attendance status from absent/late to present based on school attendance';

    protected $date;
    protected $isActual;
    protected int $updatedCount;
    protected $excludePatchPeriodAttendanceStatus;
    protected $excludePatchHasMarkDeduction;
    protected $excludeRemoveTimeslotTeacher;
    protected $excludePatchLeaveApplication;

    public function __construct(
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;
        $this->excludePatchPeriodAttendanceStatus = (bool) $this->option('exclude-patch-period-attendance-status') ?? false;
        $this->excludePatchHasMarkDeduction = (bool) $this->option('exclude-patch-has-mark-deduction') ?? false;
        $this->excludeRemoveTimeslotTeacher = (bool) $this->option('exclude-remove-timeslot-teacher') ?? false; 
        $this->excludePatchLeaveApplication = (bool) $this->option('exclude-patch-leave-application') ?? false; 

        DB::transaction(function() {
            if ($this->excludePatchPeriodAttendanceStatus == false) {
                $this->patchPeriodAttendanceStatus(); // Update period attendance to present if school attendance is present/late (2025-04-21 - 2025-04-23)
            }
            if ($this->excludePatchHasMarkDeduction == false) {
                $this->patchPeriodAttendanceHasMarkDeduction(); // Update all period_attendances.has_mark_deduction to be equal with timeslots.has_mark_deduction
            }
            if ($this->excludeRemoveTimeslotTeacher == false) {
                $this->removeTimeslotTeacher(); // Remove all timeslot_teacher for Thursday/Friday second 班务
            }
            if ($this->excludePatchLeaveApplication == false) {
                $this->patchCheckOutForgotTapCardLeaveApplication(); // start from 2025-04-21, if school attendance PRESENT but check out status = null (forgot tap card), create (早退/无签离）leave application (if period 15 don't have leave application)
            }
        });
    }

    public function patchPeriodAttendanceStatus()
    {
        // get all absent or late class attendances
        $period_attendances_group_by_date_and_student_id = PeriodAttendance::with(['student'])
            ->whereIn('date', ['2025-04-21', '2025-04-22', '2025-04-23'])
            ->where('status', '!=', PeriodAttendanceStatus::PRESENT->value) // absent or late
            ->get()
            ->groupBy(['date', 'student_id'])
            ->all();

        if (count($period_attendances_group_by_date_and_student_id) == 0) {
            return;
        }

        foreach ($period_attendances_group_by_date_and_student_id as $date => $period_attendances_group_by_student_id) {
            // students
            $student_ids = array_keys($period_attendances_group_by_student_id->toArray());

            $school_attendances = Attendance::where('date', $date)
                ->whereIn('attendance_recordable_id', $student_ids)
                ->where('attendance_recordable_type', Student::class)
                ->get()
                ->keyBy('attendance_recordable_id')
                ->all();

            $period_attendances_to_be_created = [];
            $period_attendances_to_be_deleted = [];
            foreach ($period_attendances_group_by_student_id as $student_id => $period_attendances) {

                $school_attendance = $school_attendances[$student_id];

                if ($school_attendance->status == AttendanceStatus::ABSENT) {
                    continue;
                }

                foreach ($period_attendances->sortBy('period') as $period_attendance) {
                    $period_attendances_to_be_deleted[] = $period_attendance->id;
                    $period_attendances_to_be_created[] = [
                        'id' => $period_attendance->id,
                        'student_id' => $period_attendance->student_id,
                        'date' => $period_attendance->date,
                        'timeslot_type' => $period_attendance->timeslot_type,
                        'timeslot_id' => $period_attendance->timeslot_id,
                        'updated_by_employee_id' => $period_attendance->updated_by_employee_id,
                        'status' => PeriodAttendanceStatus::PRESENT->value,
                        'leave_application_id' => $period_attendance->leave_application_id,
                        'period' => $period_attendance->period,
                        'created_at' => $period_attendance->created_at,
                        'updated_at' => now(),
                    ];
                }

                $periods = implode(',', $period_attendances->sortBy('period')->pluck('period')->toArray());
                $this->info("Student id: {$student_id}, updating class attendance period $periods to PRESENT");
            }

            if ($this->isActual) {
                $this->periodAttendanceRepository->batchDeleteByIds($period_attendances_to_be_deleted);
                $this->periodAttendanceRepository->insert($period_attendances_to_be_created);
            }
        }
    }

    public function patchPeriodAttendanceHasMarkDeduction()
    {
        $this->updatedCount = 0;
        PeriodAttendance::with([
            "timeslot" => function ($query) {
                $query->select(['id', 'has_mark_deduction']);
            }
        ])
            ->select(['id', 'timeslot_type', 'timeslot_id', 'has_mark_deduction'])
            ->chunkById(300, function ($items){
                foreach ($items as $item) {
                    if (get_class($item->timeslot) == Timeslot::class && ($item->has_mark_deduction != $item->timeslot->has_mark_deduction)) {
                        if ($this->isActual) {
                            $item->update([
                                'has_mark_deduction' => $item->timeslot->has_mark_deduction
                            ]);
                        }
                        $this->updatedCount++;
                    }
                }
            });

            $is_actual = $this->isActual ? 'true' : 'false';
            $this->info("Patched {$this->updatedCount} period attendance's has_mark_deduction (is actual = {$is_actual})");
    }

    public function removeTimeslotTeacher()
    {
        $query = TimeslotTeacher::query()
            ->whereHas('timeslot.period', function ($q) {
                $q->where('period', 15)->whereIn('day', [Day::THURSDAY, Day::FRIDAY]);
            });
        $count = $query->count();

        if ($this->isActual) {
            $query->delete();
        }

        $is_actual = $this->isActual ? 'true' : 'false';
        $this->info("Deleted {$count} timeslot teacher (is actual = {$is_actual})");
    }

    public function patchCheckOutForgotTapCardLeaveApplication()
    {
        $this->updatedCount = 0;
        $leave_application_type = LeaveApplicationType::where('code', '12')->firstOrFail(); // 早退/无签离 code is 12

        $attendances_group_by_date = Attendance::query()
            ->where('attendance_recordable_type', Student::class)
            ->where('date', '>=', '2025-04-21')
            ->where('status', AttendanceStatus::PRESENT)
            ->whereNull('check_out_status')
            ->get()
            ->groupBy(['date'])
            ->all();

        foreach($attendances_group_by_date as $date => $attendances) {
            $existing_leave_application = $this->leaveApplicationPeriodRepository->getAll([
                'period' => 15,
                'leave_applicable_type' => Student::class,
                'leave_applicable_id' => $attendances->pluck('attendance_recordable_id')->unique()->toArray(),
                'leave_application_status' => LeaveApplicationStatus::APPROVED->value,
                'leave_application_date' => $date,
            ])
                ->keyBy('leaveApplication.leave_applicable_id')
                ->all();


            foreach ($attendances as $attendance) {
                if (!isset($existing_leave_application[$attendance->attendance_recordable_id])) {
                    if ($this->isActual) {
                        $leave_application = LeaveApplication::create([
                            'leave_applicable_type' => Student::class,
                            'leave_applicable_id' => $attendance->attendance_recordable_id,
                            'status' => LeaveApplicationStatus::APPROVED,
                            'leave_application_type_id' => $leave_application_type->id,
                            'reason' => $leave_application_type->getTranslation('name', 'en'),
                            'remarks' => null,
                            'is_present' => $leave_application_type->is_present,
                            'is_full_day' => false,
                            'average_point_deduction' => $leave_application_type->average_point_deduction,
                            'conduct_point_deduction' => $leave_application_type->conduct_point_deduction,
                        ]);
                        LeaveApplicationPeriod::create([
                            'leave_application_id' => $leave_application->id,
                            'date' => $date,
                            'period' => 15, // second 班务
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                    $this->updatedCount++;
                }
            }
        }

        $is_actual = $this->isActual ? 'true' : 'false';
        $this->info("Created {$this->updatedCount} leave application (is actual = {$is_actual})");
    }
}
