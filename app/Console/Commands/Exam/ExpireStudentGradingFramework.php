<?php

namespace App\Console\Commands\Exam;

use App\Repositories\StudentGradingFrameworkRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ExpireStudentGradingFramework extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:expire-student-grading-framework';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set is_active to false for all student grading frameworks where the current date exceeds the effective_to date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Starting ExpireStudentGradingFramework script..");
        $repository = app()->make(StudentGradingFrameworkRepository::class);
        $repository->expireStudentGradingFrameworkByDate(Carbon::now()->tz(config('school.timezone'))->subDay()->toDateString());
        $this->info("End CloseExpiredStudentGradingFramework script..");
    }
}
