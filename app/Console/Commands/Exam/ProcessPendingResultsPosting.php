<?php

namespace App\Console\Commands\Exam;

use App\Jobs\GenerateStudentReportCardJob;
use App\Jobs\StudentResultPostingJob;
use App\Models\ResultsPostingHeader;
use App\Repositories\ResultsPostingHeaderRepository;
use App\Services\Exam\ExamResultsPostingService;
use Illuminate\Console\Command;
use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Support\Facades\Cache;
use Log;

class ProcessPendingResultsPosting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'results-posting:process {--actual} {--unit-test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process any PENDING status exam posting, 1 by 1';
    private bool $isActual, $isUnitTest, $isSuccessful;
    protected ?ResultsPostingHeader $resultsPostingHeader = null;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;
        $this->isUnitTest = (bool) $this->option('unit-test') ?? false;
        $this->isSuccessful = false;

        $this->resultsPostingHeader = app()->make(ResultsPostingHeaderRepository::class)
            ->first([
                'status' => ResultsPostingHeader::STATUS_PENDING,
                'order_by' => ['id' => 'ASC']
            ], false);

        if ( $this->resultsPostingHeader === null ) {
            $this->info("No pending exam results posting found.");
            Log::info("No pending exam results posting found.");
            return 0;
        }

        $this->info("Start pending results posting command.");
        $this->resultsPostingHeader->addLog("Start pending results posting command.");

        // create lock to prevent other processes to accidentally process the same posting for 2 hours
        try{

            Cache::lock('posting-lock-' . $this->resultsPostingHeader->id, 7200)->block(1, function () {

                if ( !$this->isActual ) {
                    $this->info("Mock Running for exam results posting {$this->resultsPostingHeader->code}");
                    Log::info("Mock Running for exam results posting {$this->resultsPostingHeader->code}");
                    return;
                }

                // update to processing
                $this->resultsPostingHeader->initProgressTrackingKeys();
                $this->updateStatusToInProgress();

                // get all the student IDs from the posting header, and dispatch job 1 by 1
                foreach ( $this->resultsPostingHeader->student_ids as $student_id ) {

                    if ( app()->environment('testing') ) {
                        StudentResultPostingJob::dispatch($this->resultsPostingHeader, $student_id);
                    }else{
                        StudentResultPostingJob::dispatch($this->resultsPostingHeader, $student_id)->onConnection('results-posting')->onQueue('results-posting');
                    }

                    $this->info('Dispatched results posting job for student ' . $student_id);
                    $this->resultsPostingHeader->addLog('Dispatched results posting job for student ' . $student_id);
                }

                // check for posting completion for all students
                // max check up to 90 minutes
                $max_time = now()->addMinutes(90);

                while (true) {

                    if ( now()->isAfter($max_time) ) {
                        $this->processingTimeout();
                        break;
                    }

                    // once posting is all completed for every student, we calculate grade and class level stats
                    $status = $this->resultsPostingHeader->isPostingForAllStudentsCompleted();

                    $this->info('Posting results for each student. Progress: ' . $status['processed'] . '/' . $status['total']);
                    $this->resultsPostingHeader->addLog('Posting results for each student. Progress: ' . $status['processed'] . '/' . $status['total']);

                    if ( $this->isUnitTest || $status['is_completed'] ) {

                        $this->info('All students results posting completed.');
                        $this->resultsPostingHeader->addLog('All students results posting completed.');

                        $this->calculateRankings();
                        $this->generatePdfReportCard();
                        $this->allStudentsPostingCompleted();
                        break;

                    }

                    sleep(1);

                }

                // finally we update the resultpostingheader status to completed
                if ( $this->isSuccessful ) {
                    $this->updateStatusToCompleted();
                    $this->resultsPostingHeader->clearProgressTrackingKeys();
                }


            });

            return 1;

        }catch(LockTimeoutException $e) {
            $this->info('Unable to obtain lock for processing.');
            $this->resultsPostingHeader->addLog('Unable to obtain lock for processing.');

            return -1;
        }catch(\Exception $e){
            \Log::info($e->getMessage());
            \Log::info($e->getTraceAsString());

            $this->info('Error when processing posting - ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->resultsPostingHeader->addLog('Error when processing posting - ' . $e->getMessage() . "\n" . $e->getTraceAsString());

        }finally{
            $this->resultsPostingHeader->clearProgressTrackingKeys();
        }

        return false;

    }

    public function updateStatusToInProgress(){

        $this->info('Posting header status updated to in progress.');
        $this->resultsPostingHeader->addLog('Posting header status updated to in progress.');

        $this->resultsPostingHeader->status = ResultsPostingHeader::STATUS_IN_PROGRESS;
        $this->resultsPostingHeader->processing_start = now();

        return $this->resultsPostingHeader->save();
    }
    public function updateStatusToCompleted(){

        $this->info('Posting header status updated to completed.');
        $this->resultsPostingHeader->addLog('Posting header status updated to completed.');

        $this->resultsPostingHeader->status = ResultsPostingHeader::STATUS_COMPLETED;
        $this->resultsPostingHeader->processing_end = now();

        return $this->resultsPostingHeader->save();
    }
    public function updateStatusToError(){

        $this->info('Posting header status updated to error.');
        $this->resultsPostingHeader->addLog('Posting header status updated to error.');

        $this->resultsPostingHeader->status = ResultsPostingHeader::STATUS_ERROR;
        $this->resultsPostingHeader->processing_end = now();

        return $this->resultsPostingHeader->save();
    }

    public function processingTimeout() {
        $this->isSuccessful = false;
    }

    public function allStudentsPostingCompleted() {
        $this->isSuccessful = true;
    }

    public function calculateRankings() {

        $this->info('Calculate rankings for all students results posting in this batch.');
        $this->resultsPostingHeader->addLog('Calculate rankings for all students results posting in this batch.');

        // each results posting line item with calculate_rank = true. Calculate percentile, class rank, grade rank
        $service = app()->make(ExamResultsPostingService::class);

        $service
            ->setResultsPostingHeader($this->resultsPostingHeader)
            ->updateClassAndGradeRankingsForPostingLineItems();

        $this->info('Calculate rankings completed.');
        $this->resultsPostingHeader->addLog('Calculate rankings completed.');

    }

    public function generatePdfReportCard() {

        $this->info('Generating PDF report card for all students.');
        $this->resultsPostingHeader->addLog('Generating PDF report card for all students.');

        // get all the student IDs from the posting header, and dispatch job 1 by 1
        foreach ( $this->resultsPostingHeader->student_ids as $student_id ) {

            if ( app()->environment('testing') ) {
                GenerateStudentReportCardJob::dispatch($this->resultsPostingHeader, $student_id);
            }else{
                GenerateStudentReportCardJob::dispatch($this->resultsPostingHeader, $student_id)->onConnection('report-card')->onQueue('report-card');
            }

            $this->info('Dispatched report card generation job for student ' . $student_id);
            $this->resultsPostingHeader->addLog('Dispatched report card generation job for student ' . $student_id);
        }


        $this->info('Completed generating PDF report card for all students.');
        $this->resultsPostingHeader->addLog('Completed generating PDF report card for all students.');
    }
}
