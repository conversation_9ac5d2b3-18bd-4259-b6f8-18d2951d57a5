<?php

namespace App\Helpers;

use Exception;

class ErrorCodeHelper
{
    const LOCALE_FILE = 'system_error.';
    const WALLET_ERROR = 'WALLET_ERROR';
    const TERMINAL_ERROR = 'TERMINAL_ERROR';
    const PAYMENT_GATEWAY_ERROR = 'PAYMENT_GATEWAY_ERROR';
    const HOSTEL_ERROR = 'HOSTEL_ERROR';
    const CLUB_ERROR = 'CLUB_ERROR';
    const DISCIPLINE_MANAGEMENT_ERROR = 'DISCIPLINE_MANAGEMENT_ERROR';
    const USER_ERROR = 'USER_ERROR';
    const ENROLLMENT_ERROR = 'ENROLLMENT_ERROR';
    const CALENDAR_ERROR = 'CALENDAR_ERROR';
    const CLASS_ERROR = 'CLASS_ERROR';
    const CARD_ERROR = 'CARD_ERROR';
    const GUARDIAN_ERROR = 'GUARDIAN_ERROR';
    const CLASS_SUBJECT_ERROR = 'CLASS_SUBJECT_ERROR';
    const COMPREHENSIVE_ASSESSMENT_ERROR = 'COMPREHENSIVE_ASSESSMENT_ERROR';
    const LEADERSHIP_POSITION_ERROR = 'LEADERSHIP_POSITION_ERROR';
    const ECOMMERCE_ERROR = 'ECOMMERCE_ERROR';
    const MASTER_DATA_ERROR = 'MASTER_DATA_ERROR';
    const ROLE_ERROR = 'ROLE_ERROR';
    const ANNOUNCEMENT_ERROR = 'ANNOUNCEMENT_ERROR';
    const ISBN_ERROR = 'ISBN_ERROR';
    const EMPLOYEE_ERROR = 'EMPLOYEE_ERROR';
    const VALIDATION_ERROR = 'VALIDATION_ERROR';
    const STUDENT_ERROR = 'STUDENT_ERROR';
    const CONTRACTOR_ERROR = 'CONTRACTOR_ERROR';
    const UNPAID_ITEM_ASSIGNMENT_ERROR = 'UNPAID_ITEM_ASSIGNMENT_ERROR';
    const SCHOLARSHIP_ERROR = 'SCHOLARSHIP_ERROR';
    const PRODUCT_ERROR = 'PRODUCT_ERROR';
    const BILLING_DOCUMENT_ERROR = 'BILLING_DOCUMENT_ERROR';

    const REPORT_ERROR = 'REPORT_ERROR';
    const SOCIETY_POSITION_ERROR = 'SOCIETY_POSITION_ERROR';
    const COUNSELLING_CASE_RECORD_ERROR = 'COUNSELLING_CASE_RECORD_ERROR';
    const ACCOUNTING_ERROR = 'ACCOUNTING_ERROR';
    const LEAVE_APPLICATION_ERROR = 'LEAVE_APPLICATION_ERROR';
    const GRADING_FRAMEWORK_ERROR = 'GRADING_FRAMEWORK_ERROR';

    const TIMETABLE_ERROR = 'TIMETABLE_ERROR';
    const GENERIC_ERROR = 'GENERIC_ERROR';
    const COMPETITION_ERROR = 'COMPETITION_ERROR';
    const MAINTENANCE_ERROR = 'MAINTENANCE_ERROR';
    const ATTENDANCE_ERROR = 'ATTENDANCE_ERROR';
    const TIMESLOT_OVERRIDE_ERROR = 'TIMESLOT_OVERRIDE_ERROR';
    const DEADLINE_ERROR = 'DEADLINE_ERROR';
    const LIBRARY_ERROR = 'LIBRARY_ERROR';

    // VERY THE VERY IMPORTANT: PLEASE ALWAYS KEEP /lang/{locale}/system_error.php UP TO DATE
    const ERROR_CODES = [
        self::GENERIC_ERROR => [
            '403' => '403',   // forbidden
        ],

        self::WALLET_ERROR => [
            '1001' => '1001', // Invalid signature for wallet auth
            '1002' => '1002', // User has insufficient balance
            '1003' => '1003', // Invalid User.
            '1004' => '1004', // Invalid Order Reference No
            '1005' => '1005', // Unable to obtain transaction lock for transfer.
            '1006' => '1006', // User has no permission to access the selected wallet
            '1007' => '1007', // Payment type not supported,
            '1008' => '1008', // User does not have a wallet.
            '1009' => '1009', // Default currency not found.
            '1010' => '1010', // User has been inactivated.
            '1012' => '1012', // Transaction is not allowed to refund.
        ],
        self::PAYMENT_GATEWAY_ERROR => [
            '2001' => '2001', // Transaction record not found.
            '2002' => '2002', // Invalid callback response
            '2003' => '2003', // The payment has already been processed.
            '2004' => '2004', // Wallet transaction record not found.
            '2005' => '2005', // Unable to obtain transaction lock for unpaid items.
        ],
        self::HOSTEL_ERROR => [
            '3001' => '3001', // 'Maximum capacity exceeded.'
            '3002' => '3002', // 'The block cannot be deleted as it contains existing rooms.'
            '3003' => '3003', // 'The room cannot be deleted as it contains existing beds.'
            '3004' => '3004', // 'The bed cannot be deleted as it has been occupied before.'
            '3005' => '3005', // 'One or more students do not exist.'
            '3006' => '3006', // 'One or more beds do not exist or occupied.'
            '3007' => '3007', // 'One or more employees do not exist.'
            '3008' => '3008', // 'One or more students already have a bed assignment.'
            '3009' => '3009', // 'One or more students do not have a bed assignment.'
            '3010' => '3010', // 'One or more employees already have a bed assignment.'
            '3011' => '3011', // 'One or more employees do not have a bed assignment.'
            '3012' => '3012', // 'The category cannot be deleted as it contains existing items.'
        ],
        self::USER_ERROR => [
            '4001' => '4001', // User does not have the right permissions.
            '4002' => '4002', // User not found.
            '4003' => '4003', // Unable to send OTP. Please try again later.
            '4004' => '4004', // Invalid OTP.
            '4005' => '4005', // Maximum number of OTP requests reached.
            '4006' => '4006', // The provided credentials does not match our records.
            '4007' => '4007', // Your account has been inactivated, please contact admin.
            '4008' => '4008', // You do not have permission to access this resource.
            '4009' => '4009', // The given phone number is invalid.
            '4010' => '4010', // Email or phone number is required to create user.
            '4011' => '4011', // This guardian is not belongs to the user.
        ],
        self::ENROLLMENT_ERROR => [
            '5001' => '5001', // Payment cannot be made at this step
        ],
        self::CLASS_ERROR => [
            '6001' => '6001', // Unable to assign class to semester.
            '6002' => '6002', // Unable to assign class to student.
            '6003' => '6003', // Unable to assign semester to class.
            '6004' => '6004', // The class cannot be deleted because it is currently being used.
            '6005' => '6005', // The semester class cannot be deleted because it is currently being used.
            '6006' => '6006', // Unable to do auto-assignment for seats because there is already a manual seat assignment in the semester.
            '6007' => '6007', // Unable to assign multiple classes to multiple semesters.
        ],
        self::GUARDIAN_ERROR => [
            '7001' => '7001', //Email is required to create guardian user.
            '7002' => '7002', //Email guardian already exists.
            '7003' => '7003', //Phone number is required to create guardian user.
            '7004' => '7004', //Guardian cannot be deleted once is linked.
            '7005' => '7005', // Guardian :guardian_name already existed.
        ],
        self::CARD_ERROR => [
            '8001' => '8001' // Please deactivate the existing active card first.
        ],
        self::COMPREHENSIVE_ASSESSMENT_ERROR => [
            '9001' => '9001', // The category cannot be deleted as it contains existing questions.
        ],
        self::DISCIPLINE_MANAGEMENT_ERROR => [
            '10001' => '10001', // Merit demerit setting cannot be deleted because it is currently being used.
            '10002' => '10002', // Reward punishment category cannot be deleted because it is currently being used.
            '10003' => '10003', // Reward punishment sub category cannot be deleted because it is currently being used.
            '10004' => '10004', // Reward punishment cannot be deleted because it is currently being used.
        ],
        self::CLUB_ERROR => [
            '12001' => '12001', // This club category cannot be deleted because it is being used.
            '12002' => '12002', // This club cannot be deleted because it is being used.
        ],
        self::ECOMMERCE_ERROR => [
            '13001' => '13001', // Cannot delete product category that has subcategories.
            '13002' => '13002', // Cannot delete product sub category that has products.
            '13003' => '13003', // Cannot delete product group that is linked to products.
            '13004' => '13004', // Cannot delete product that has been ordered before.
            '13005' => '13005',
            '13006' => '13006',
            '13007' => '13007',
            '13008' => '13008',
            '13009' => '13009', // Cannot delete product tag that is linked to products.
            '13010' => '13010', //Merchant type not found.
            '13011' => '13011', // Canteen is not available now. Please try again between :opening_time and :cutoff_time.
            '13012' => '13012', // Canteen is not available now.
            '13013' => '13013', // Product delivery date is required.
            '13014' => '13014', // Product delivery date must greater than or equal to today.
            '13015' => '13015', // Selected product is not available on the selected delivery date.
            '13016' => '13016', // Cannot delete merchant that has products.
        ],
        self::LEADERSHIP_POSITION_ERROR => [
            '14001' => '14001', // Leadership position cannot be deleted because it is being used.
        ],
        self::CLASS_SUBJECT_ERROR => [
            '15001' => '15001', // Unable to assign students to class subjects.
            '15002' => '15002', // One or more students do not exist.
            '15003' => '15003', // One or more teachers do not exist.
        ],
        self::MASTER_DATA_ERROR => [
            '20001' => '20001', // This master data cannot be deleted because it is being used.
        ],
        self::TERMINAL_ERROR => [
            '21001' => '21001', // Invalid signature.
            '21002' => '21002', // Terminal cannot be deleted because it is being used.
        ],
        self::ISBN_ERROR => [
            '22001' => '22001', // Invalid ISBN.
        ],
        self::LIBRARY_ERROR => [
            '23001' => '23001' // Only lost book can be recovered.
        ],
        self::ROLE_ERROR => [
            '25001' => '25001', // Role cannot be deleted because it is being used.
        ],
        self::ANNOUNCEMENT_ERROR => [
            '26001' => '26001', // The announcement cannot be cancelled.
            '26002' => '26002', // Student as recipient or guardian as recipient is required.
            '26003' => '26003', // You do not have permission to delete this resource.
            '26004' => '26004', // Unsupported image [:mime_type] attached to message.
        ],
        self::EMPLOYEE_ERROR => [
            '27001' => '27001', // Cannot resign an employee that is not working.
            '27002' => '27002', // Effective date of resignation cannot be before employment start date.
            '27003' => '27003', // Cannot reinstate an employee that is still working.
            '27004' => '27004', // Cannot transfer an employee that is not working.
            '27005' => '27005', // Effective date of transfer cannot be before employment start date.
            '27006' => '27006', // New job title is required for employee transfer.
        ],
        self::VALIDATION_ERROR => [
            '28001' => '28001', // The phone number field must be a valid phone number.
        ],
        self::STUDENT_ERROR => [
            '29001' => '29001', // Student cannot be deleted because currently assigned to a bed.
            '29002' => '29002', // Inactive student is not allowed to perform leave school action.
            '29003' => '29003', // Effective date of student leave school cannot be before student join date.
            '29004' => '29004', // Student with bed assignment is not allowed to leave/graduate.
            '29005' => '29005', // Only student with left status can perform return school action.
            '29006' => '29006', // No eligible guardian was found to transfer the student's remaining balance. Ensure at least one guardian has wallet.
            '29007' => '29007', // Semester setting is required for student to return school.
            '29008' => '29008', // Semester class is required for student to return school.
            '29009' => '29009', // Student not found.
            // PinHwaStudentService
            '29010' => '29010', // Invalid grade id.
            '29011' => '29011', // Unsupported admission type to get grade and init next number.
        ],
        self::REPORT_ERROR => [
            '30001' => '30001' // Selected month is not within selected semester period.
        ],
        self::COUNSELLING_CASE_RECORD_ERROR => [
            '32001' => '32001', // Only employees are able to update counselling case record.
        ],
        self::SOCIETY_POSITION_ERROR => [
            '33001' => '33001', // Unable to assign positions to students.
        ],
        self::MAINTENANCE_ERROR => [
            '34001' => '34001', // Site is currently under maintenance.
            '34002' => '34002', // New app version available. Kindly update to the latest version.
        ],
        self::PRODUCT_ERROR => [
            '35001' => '35001' // Product cannot be deleted because it is being used.
        ],
        self::ACCOUNTING_ERROR => [
            '36001' => '36001', // All fees must be paid sequentially.
            '36002' => '36002', // All fees under the same month must be paid together.
            '36003' => '36003', // Can only pay fees for 1 account at a time.
            '36004' => '36004', // Must select to pay at least 1 fee.
            '36005' => '36005', // Fee is not valid for payment.
            '36006' => '36006', // Invalid fee payment request
            '36007' => '36007', // Unsupported billing document type
            '36008' => '36008', // Invalid manual payment request
            '36009' => '36009', // Pending fees need to be paid before paying new fees.
            '36010' => '36010', // Payable amount must be more than RM1, please pay the fee along with other items.
            '36011' => '36011', // Billing document currently have pending transactions.
            '36012' => '36012', // Missing required data for billing document posting.
        ],
        self::BILLING_DOCUMENT_ERROR => [
            '37001' => '37001' // Invalid billing document to make payment.
        ],
        self::CONTRACTOR_ERROR => [
            '38001' => '38001', // Contractor cannot be deleted because it is being used.
        ],
        self::SCHOLARSHIP_ERROR => [
            '39001' => '39001', // Scholarship cannot be deleted because it is being used.
            '39002' => '39002', // Students with these ids :ids cannot be removed from the scholarship.
        ],
        self::TIMETABLE_ERROR => [
            '40001' => '40001', // Period must be unique in same day.
            '40002' => '40002', // From to time must be unique in same day.
            '40003' => '40003', // Each day must have same set of periods.
            '40004' => '40004', // Each day must have same set of from to time.
            '40005' => '40005', // Timetable already assigned period group before,
            '40006' => '40006', // Incorrect timeslots data
            '40007' => '40007', // Unable to resolve student to get student timetable
            '40008' => '40008', // Unable to retrieve period group's labels. Class not found for student :student_name
            '40009' => '40009', // Unable to retrieve period group's labels. Timetable not found for students :student_names
            '40010' => '40010', // Timetable not found for student :student_name on :date.
        ],
        self::COMPETITION_ERROR => [
            '41001' => '41001', // Semester class not found for student :student_name on :date
        ],
        self::ATTENDANCE_ERROR => [
            '42001' => '42001', // Missing required fields to create attendance period override
            '42002' => '42002', // Attendance period for the student/employee & date already exists
            '42003' => '42003', // Invalid student id :student_id.
            '42004' => '42004', // No employee account was found for the logged-in user.
            '42005' => '42005', // Please select at least one student/employee/contractor to perform attendance posting.
            '42006' => '42006', // Attendance already taken / 出席已被记录
            '42007' => '42007', // Invalid card / 无法识别此卡
            '42008' => '42008', // Timetable not set up. Please contact admin / 课程表未设置，请联系管理员
            '42009' => '42009', // Invalid timeslot type.
            '42010' => '42010', // Substitute teacher cannot same as requestor.
            '42011' => '42011', // Card has been inactivated / 此卡已被停用
        ],
        self::UNPAID_ITEM_ASSIGNMENT_ERROR => [
            '43001' => '43001', // 'Fee cannot be deleted because it is being used.'
        ],
        self::LEAVE_APPLICATION_ERROR => [
            '50001' => '50001', // Leave application type cannot be deleted because it is being used.
            '50002' => '50002', // Invalid period label was found in :period_group_name.
            '50003' => '50003', // Missing required data
            '50004' => '50004', // Cannot approve leave application because has existing individual period override that was manually created on :date for student :student.
            '50005' => '50005', // Unable to delete leave application as it's being used
            '50006' => '50006', // Not allowed to edit approved leave application.
            '50007' => '50007', // Please fix all validation issues before changing status.
            '50008' => '50008', // Creation or update is not allowed for student :student as a leave application already exists for the same period. (:from - :to)
        ],
        self::CALENDAR_ERROR => [
            '51001' => '51001', // Default calendar existed for this year.
            '51002' => '51002', // Unable to delete default calendar.
        ],
        self::TIMESLOT_OVERRIDE_ERROR => [
            '52001' => '52001', // Timeslot override cannot be deleted because class attendance has already been taken.
            '52002' => '52002', // Duplicate timeslot overrides for the same period on the same date are not allowed. :error_message
            '52003' => '52003', // Only allowed to create timeslot overrides for future dates.
        ],
        self::DEADLINE_ERROR => [
            '53001' => '53001', // Unable to update records because deadline has passed.
        ],
        self::GRADING_FRAMEWORK_ERROR => [
            '60001' => '60001', // Unable to update student grading framework due to conflicting grading framework ids'
            '60002' => '60002' // Unable to apply grading framework, student has existing marks. Please check exam posting pre-checks and remove exam marks related to the student
        ]
    ];

    /**
     * @throws Exception
     */
    public static function throwError(string $category, int $code, array $translation_attribute = []): void
    {
        throw new Exception(self::getTranslatedErrorMessage($category, $code, $translation_attribute), $code);
    }

    public static function getTranslatedErrorMessage(string $category, string $code, array $translation_attribute = []): string
    {
        return trans(self::LOCALE_FILE . self::ERROR_CODES[$category][$code], $translation_attribute);
    }

    public static function getSentryIgnoredErrorCodes(): array
    {
        $ignored_error_codes = [];
        foreach (self::ERROR_CODES as $codes) {
            foreach ($codes as $code => $message) {
                $ignored_error_codes[] = $code;
            }
        }

        return $ignored_error_codes;
    }
}
