<?php

namespace App\Listeners;

use App\Events\ClassSubjectStudentUpdatedEvent;
use App\Events\ExamSubjectExemptionEvent;
use App\Models\Exam;
use App\Models\Student;
use App\Models\Subject;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;

class SyncStudentGradingFramework implements ShouldQueue
{
    public $connection = 'event-listeners';
    public $queue = 'event-listeners';
    public $delay = 0;
    public $tries = 1;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ClassSubjectStudentUpdatedEvent|ExamSubjectExemptionEvent $event): void
    {
        $service = app()->make(StudentGradingFrameworkService::class);

        if ($event instanceof ClassSubjectStudentUpdatedEvent) {
            $student_ids = $event->getStudentIDs();

            $students = Student::whereIn('id', $student_ids)
                ->with(['gradingFrameworks.gradingFramework', 'primaryClass'])
                ->whereRelation('gradingFrameworks', 'is_active', true)
                ->get();

            foreach ($students as $student) {
                $student_grading_framework = $student->gradingFrameworks->first();
                $grading_framework = $student_grading_framework->gradingFramework;

                $effective_from = Carbon::parse($student_grading_framework->effective_from);
                $effective_to = Carbon::parse($student_grading_framework->effective_to);

                $service->setStudent($student)
                    ->setGradingFramework($grading_framework)
                    ->setStudentGradingFramework($student_grading_framework)
                    ->updateGradingFramework($effective_from, $effective_to);
            }
        } else {
            $input = $event->getInput();
            $records = $input['records'];

            $subject = Subject::where('id', $input['subject_id'])->first();
            $exam = Exam::where('id', $input['exam_id'])->first();

            foreach ($records as $record) {
                $student_grading_framework = Student::where('id', $record['student_id'])->first()->activeGradingFramework();
                $service->setStudentGradingFramework($student_grading_framework)
                    ->setConfigForExemptedSubjects($subject, $exam, $record['is_exempted']);
            }

        }

    }
}
