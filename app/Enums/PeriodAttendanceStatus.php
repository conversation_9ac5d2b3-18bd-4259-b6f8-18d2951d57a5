<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum PeriodAttendanceStatus: string implements IEnum
{
    use EnumOption;

    case PRESENT = 'PRESENT';
    case ABSENT = 'ABSENT';
    case LATE = 'LATE';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::PRESENT => 'Present',
            self::ABSENT => 'Absent',
            self::LATE => 'Late',
        };
    }

    public static function getPeriodAttendanceTranslation($value): array
    {
        return match ($value) {
            self::PRESENT->value => ['en' => 'Present', 'zh' => '出席'],
            self::ABSENT->value => ['en' => 'Absent', 'zh' => '缺席'],
            self::LATE->value => ['en' => 'Late', 'zh' => '迟到'],
        };
    }

    public static function getTranslatedPeriodAttendance($value): string
    {
        return match ($value) {
            self::PRESENT->value => trans('attendance.attend'),
            self::ABSENT->value => trans('attendance.absent'),
            self::LATE->value => trans('attendance.late'),
        };
    }

    public static function getStatusForReport($status, $school_attendance_status)
    {
        if ($status === PeriodAttendanceStatus::PRESENT->value) {
            if ($school_attendance_status === null || $school_attendance_status === AttendanceStatus::ABSENT->value) {
                return '∞';
            } else {
                return '1';
            }
        } else {
            return match ($status) {
                'ABSENT' => '0',
                'LATE' => 'ɸ',
                default => '',
            };
        }

    }
}
