<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum ExamSemesterSettingCategory: string implements IEnum
{
    use EnumOption;

    case ATTENDANCE = 'Attendance';
    case MERIT_DEMERIT = 'Merit/Demerit';
    case MARK_DEDUCT = 'Mark Deduct';
    case MARK_DEDUCT_RETAIN = 'Mark Deduct (Retain)';
    case OFF_CAMPUS = 'Off Campus';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::ATTENDANCE =>'Attendance',
            self::MERIT_DEMERIT =>'Merit/Demerit',
            self::MARK_DEDUCT =>'Mark Deduct',
            self::MARK_DEDUCT_RETAIN => 'Mark Deduct (Retain)',
            self::OFF_CAMPUS => 'Off Campus',
        };
    }
}
