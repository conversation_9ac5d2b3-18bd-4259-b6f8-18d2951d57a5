<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum EnrollmentAction: string implements IEnum
{
    use EnumOption;

    case SAVE_AS_DRAFT = 'SAVE_AS_DRAFT';

    case NEXT = 'NEXT';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::SAVE_AS_DRAFT => 'Save as draft',
            self::NEXT => 'Next',
        };
    }
}
