<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum EnrollmentStatus: string implements IEnum
{
    use EnumOption;

    // Status change from DRAFT -> PENDING_PAYMENT (If payment is required) -> SUBMITTED -> APPROVED (Add student into waiting list) -> ENROLLED (Add student into system)
    // Status change from DRAFT -> PENDING_PAYMENT (If payment is required) -> SUBMITTED -> REJECTED
    case DRAFT = 'DRAFT';
    case PENDING_PAYMENT = 'PENDING_PAYMENT';
    case PAYMENT_FAILED = 'PAYMENT_FAILED';
    case SUBMITTED = 'SUBMITTED';
    case APPROVED = 'APPROVED';
    case REJECTED = 'REJECTED'; // Dead end
    case ENROLLED = 'ENROLLED'; // Dead end

    public static function getLabel($value): string
    {
        return match ($value) {
            self::DRAFT => 'Draft',
            self::PENDING_PAYMENT => 'Pending Payment',
            self::PAYMENT_FAILED => 'Payment Failed',
            self::SUBMITTED => 'Submitted',
            self::APPROVED => 'Approved',
            self::REJECTED => 'Rejected',
            self::ENROLLED => 'Enrolled',
        };
    }
}
