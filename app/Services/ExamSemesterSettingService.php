<?php

namespace App\Services;

use App\Repositories\ExamSemesterSettingRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ExamSemesterSettingService
{
    private ExamSemesterSettingRepository $examSemesterSettingRepository;

    public function __construct(ExamSemesterSettingRepository $exam_semester_setting_repository)
    {
        $this->examSemesterSettingRepository = $exam_semester_setting_repository;
    }

    public function getAllPaginatedExamSemesterSettings($filters = []): LengthAwarePaginator
    {
        return $this->examSemesterSettingRepository->getAllPaginated($filters);
    }

    public function getAllExamSemesterSettings($filters = []): Collection
    {
        return $this->examSemesterSettingRepository->getAll($filters);
    }

    public function bulkCreateOrUpdateExamSemesterSettings($data): ?Model
    {
        $semester_setting_id = $data['semester_setting_id'];
        $exam_semester_settings = $data['exam_semester_settings'];

        return DB::transaction(function () use ($semester_setting_id, $exam_semester_settings) {

            foreach ($exam_semester_settings as $exam_semester_setting){
                $this->examSemesterSettingRepository->updateOrCreate([
                    'semester_setting_id' => $semester_setting_id,
                    'category' => $exam_semester_setting['category'],
                    'grade_id' => $exam_semester_setting['grade_id']
                ],  
                $exam_semester_setting);
            }
        });
    }
}
