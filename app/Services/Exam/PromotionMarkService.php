<?php

namespace App\Services\Exam;

use App\Repositories\PromotionMarkRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PromotionMarkService
{
    public function __construct(
        protected PromotionMarkRepository $promotionMarkRepository
    ) {
    }

    public function getAllPromotionMarks($filters = []): Collection
    {
        return $this->promotionMarkRepository->getAll($filters);
    }

    public function getAllPaginatedPromotionMarks($filters = []): LengthAwarePaginator
    {
        return $this->promotionMarkRepository->getAllPaginated($filters);
    }

    public function bulkCreateOrUpdatePromotionMark($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            foreach ($data as $input) {
                $this->promotionMarkRepository->updateOrCreate(['semester_class_id' => $input['semester_class_id']], $input);
            }
        });
    }
}
