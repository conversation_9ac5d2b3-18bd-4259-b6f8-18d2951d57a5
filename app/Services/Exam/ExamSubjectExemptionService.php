<?php

namespace App\Services\Exam;

use App\Events\ExamSubjectExemptionEvent;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ResultSourceSubject;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Repositories\SemesterClassRepository;
use App\Services\SemesterSettingService;
use Illuminate\Support\Facades\DB;


class ExamSubjectExemptionService
{

    protected SemesterSettingService $semesterSettingService;
    protected SemesterClassRepository $semesterClassRepository;

    public function __construct(
        SemesterSettingService $semester_setting_service,
        SemesterClassRepository $semester_class_repository
    ) {
        $this->semesterSettingService = $semester_setting_service;
        $this->semesterClassRepository = $semester_class_repository;
    }

    public function setExamSubjectExemption(array $input){

        return DB::transaction(function () use ($input) {
            $records = collect($input['records']);
            $exempted_records = $records->where('is_exempted', true)->pluck('student_id')->toArray();
            $non_exempted_records = $records->where('is_exempted', false)->pluck('student_id')->toArray();

            ResultSourceSubject::where([ 'subject_id' => $input['subject_id']])
                ->whereHas('resultSource.studentGradingFramework', function($query) use ($exempted_records){
                    $query->whereIn('student_id', $exempted_records);
                })
                ->whereHas('resultSource.exams', function($query) use ($input) {
                    $query->where('exam_id', $input['exam_id']);
                })
                //->whereRelation('resultSource', 'code', $input['result_source_code'])
                ->update(['is_exempted' => true]);

            ResultSourceSubject::where([ 'subject_id' => $input['subject_id']])
                ->whereHas('resultSource.studentGradingFramework', function($query) use ($non_exempted_records){
                    $query->whereIn('student_id', $non_exempted_records);
                })
                ->whereHas('resultSource.exams', function($query) use ($input) {
                    $query->where('exam_id', $input['exam_id']);
                })
                ->update(['is_exempted' => false]);

            // subject id
            // records
            ExamSubjectExemptionEvent::dispatch($input);
            return $this;
        });
    }

    public function getEligibleSubjectsForExemption()
    {

        $semester_setting_ids = $this->semesterSettingService->getCurrentSemesterSettings()->pluck('id')->toArray();

        $current_semester_classes = $this->semesterClassRepository->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
        ]);

        $class_subjects = ClassSubject::with(['subject'])
            ->whereIn('semester_class_id', $current_semester_classes->pluck('id'))
            ->get();

        return $class_subjects->pluck('subject')->unique()->values();
    }


    public function getEligibleClassesForExemption(array $input)
    {

        $semester_setting_ids = $this->semesterSettingService->getCurrentSemesterSettings()->pluck('id')->toArray();
        $current_semester_classes = $this->semesterClassRepository->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
        ]);

        $class_subjects = ClassSubject::with(['semesterClass.classModel'])
            ->whereIn('semester_class_id', $current_semester_classes->pluck('id'))
            ->where('subject_id', $input['subject_id'])
            ->get();

        return $class_subjects->pluck('semesterClass.classModel')->unique()->values();

    }

    public function getStudentExamExemptionStatus(array $input){

        $semester_setting_ids = $this->semesterSettingService->getCurrentSemesterSettings()->pluck('id')->toArray();
        $current_semester_class = $this->semesterClassRepository->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
            'class_id' => $input['class_id']
        ]);
        
        $class_subjects = ClassSubject::whereIn('semester_class_id', $current_semester_class->pluck('id'))
            ->where('subject_id', $input['subject_id'])
            ->get();

        $class_subject_students = ClassSubjectStudent::where('class_subject_id', $class_subjects->pluck('id'))->get();

        $data = StudentGradingFramework::selectRaw('students.id AS student_id, students.name AS student_name, students.student_number AS student_number, 
            result_source_subjects.id AS result_source_subject_id, result_source_subjects.is_exempted, 
            student_grading_frameworks.id AS student_grading_framework_id, result_sources.id AS result_source_id')
            ->join('students', 'student_grading_frameworks.student_id', '=', 'students.id')
            ->join('result_sources', 'student_grading_frameworks.id', '=', 'result_sources.student_grading_framework_id')
            ->join('result_source_exams', 'result_sources.id', '=', 'result_source_exams.result_source_id')
            ->join('result_source_subjects', 'result_sources.id', '=', 'result_source_subjects.result_source_id')
            ->whereIn('student_grading_frameworks.student_id', $class_subject_students->pluck('student_id'))
            ->where('result_source_exams.exam_id', $input['exam_id'])
            ->where('student_grading_frameworks.is_active', true)
            ->where('result_source_subjects.subject_id', $input['subject_id'])
            ->orderBy(DB::raw('students.name->>\'en\''), 'ASC')
            ->get()
            ->transform(function($item){
                $item->student_name = json_decode($item->student_name);
                return $item;
            });

        return $data;
    }
}
