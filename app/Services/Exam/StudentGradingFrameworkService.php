<?php

namespace App\Services\Exam;

use App\Helpers\ErrorCodeHelper;
use App\Interfaces\IReportCardOutput;
use App\Models\ClassSubject;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Repositories\ExamRepository;
use App\Repositories\GradingSchemeRepository;
use App\Repositories\StudentGradingFrameworkRepository;
use App\Repositories\SubjectRepository;
use App\Services\Exam\Output\PinHwaReportCardOutputV1Service;
use App\Services\SemesterSettingService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StudentGradingFrameworkService
{
    protected Student $student;
    protected GradingFramework $gradingFramework;
    protected StudentGradingFramework $studentGradingFramework;
    protected StudentGradingFrameworkRepository $studentGradingFrameworkRepository;
    protected SemesterSettingService $semesterSettingService;
    protected IReportCardOutput $reportCardOutputService;
    private Collection $validSubjects;
    private Collection $validExams;
    private Collection $validGradingSchemes;

    const OUTPUT_SERVICE_PATH_PREFIX = 'App\\Services\\Exam\\Output\\';

    public function __construct(StudentGradingFrameworkRepository $studentGradingFrameworkRepository,
        SemesterSettingService $semesterSettingService)
    {
        $this->studentGradingFrameworkRepository = $studentGradingFrameworkRepository;
        $this->semesterSettingService = $semesterSettingService;
    }

    public function groupValidatorFields(string $key, array $value, array &$errors, string $field): bool
    {
        if (str_starts_with($key, $field)) {
            isset($errors[$field]) ? $errors[$field] = array_merge($errors[$field], $value) : $errors[$field] = $value;
            return true;
        }
        return false;
    }

    public function validateConfiguration($config_array, $throw_exception = false, $is_unit_test = false)
    {

        $errors = collect([]);

        $valid_subject_codes = app()->make(SubjectRepository::class)->getAll()->keyBy('code');
        $valid_exam_codes = app()->make(ExamRepository::class)->getAll()->keyBy('code');
        $valid_grading_schemes = app()->make(GradingSchemeRepository::class)->getAll(['is_active' => true])->keyBy('code');

        // check basic structure
        $validator = Validator::make($config_array, [
            'grading_framework_code' => 'required|string|max:32|regex:/^[a-zA-Z0-9\_]+$/',
            'output_system_components' => 'required|array',
            'output_system_components.*' => 'required|string',
            'result_sources' => 'required|array|min:1',
            'result_sources.*.code' => 'required|string|max:32|regex:/^[a-zA-Z0-9\_]+$/',
            'result_sources.*.name' => 'required|array',
            'result_sources.*.name.en' => 'string',
            'result_sources.*.subjects' => 'required|array',
            'result_sources.*.subjects.*' => 'array',
            'result_sources.*.subjects.*.code' => ['required', 'string', 'max:32', 'regex:/^[a-zA-Z0-9\_\-]+$/'],
            'result_sources.*.subjects.*.weightage_multiplier' => 'nullable|numeric|min:0|max:10',
            'result_sources.*.subjects.*.is_exempted' => 'required|boolean',
            'result_sources.*.subjects.*.grading_type' => ['required', 'string', Rule::in([ResultSourceSubject::GRADING_TYPE_GRADE, ResultSourceSubject::GRADING_TYPE_SCORE])],
            'result_sources.*.subjects.*.components' => 'array|required_if:result_sources.*.subjects.*.grading_type,' . ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_sources.*.subjects.*.components.*' => 'array',
            'result_sources.*.subjects.*.components.*.code' => 'required|string|max:32|regex:/^[a-zA-Z0-9\_]+$/',
            'result_sources.*.subjects.*.components.*.name' => 'required|array',
            'result_sources.*.subjects.*.components.*.name.en' => 'string',
            'result_sources.*.subjects.*.components.*.weightage_percent' => 'required|numeric|min:0|max:100',
            'result_sources.*.exams' => 'required|array',
            'result_sources.*.exams.*' => 'string',
            'output' => 'required|array',
            'output.*.code' => 'required|string|max:32|regex:/^[a-zA-Z0-9\_]+$/',
            'output.*.name' => 'required|array',
            'output.*.name.en' => 'required|string',
            'output.*.service' => 'required|string',
            'output.*.is_final' => 'required|boolean',
            'output.*.components' => 'required|array',
            'output.*.components.*' => 'array',
            'output.*.components.*.code' => 'required|string|max:32|regex:/^[a-zA-Z0-9\_\-]+$/',
            'output.*.components.*.subject_code' => 'nullable|required_without:output.*.components.*.name|string|max:32',
            'output.*.components.*.grading_scheme_code' => 'nullable|required_with:output.*.components.*.subject_code|string|max:32',
            'output.*.components.*.name' => 'nullable|required_without:output.*.components.*.subject_code|array',
            'output.*.components.*.name.en' => 'string',
            'output.*.components.*.total_formula' => 'nullable|required_without:output.*.components.*.label_formula|string',
            'output.*.components.*.label_formula' => 'nullable|required_without:output.*.components.*.total_formula|string',
            'output.*.components.*.priority' => 'nullable|numeric',
            'output.*.components.*.calculate_rank' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            $raw_errors = collect($validator->getMessageBag())->toArray();

            $validator_errors = [];
            $groups = ['grading_framework_code', 'output_system_components', 'result_sources', 'output'];
            foreach ($raw_errors as $key => $value) {
                foreach ($groups as $group) {
                    if ($this->groupValidatorFields($key, $value, $validator_errors, $group)) {
                        break;
                    };
                }
            }

            if ($throw_exception) {
                $validationException = ValidationException::withMessages($validator_errors);
                throw $validationException;
            }

            return $validator_errors;
        }

        $config_array = collect($config_array);

        $report_card_output_service = app()->make(PinHwaReportCardOutputV1Service::class);

        $result_source_unique_codes = [];
        $exams_unique_codes = [];
        $valid_result_source_subjects = [];

        foreach ($config_array['result_sources'] as $result_source) {

            $subjects_unique_codes = [];

            if (!isset($result_source_unique_codes[strtoupper($result_source['code'])])) {
                $result_source_unique_codes[strtoupper($result_source['code'])] = 0;
            } else {
                $result_source_unique_codes[strtoupper($result_source['code'])]++;
            }

            if (!isset($valid_result_source_subjects[strtoupper($result_source['code'])])) {
                $valid_result_source_subjects[strtoupper($result_source['code'])] = [];
            }
            foreach ($result_source['subjects'] as $subject) {

                $components_weightage_percent_sum = 0;
                $components_unique_codes = [];

                if (!isset($subjects_unique_codes[strtoupper($subject['code'])])) {
                    $subjects_unique_codes[strtoupper($subject['code'])] = 0;
                } else {
                    $subjects_unique_codes[strtoupper($subject['code'])]++;
                }

                if (!isset($valid_result_source_subjects[strtoupper($result_source['code'])][strtoupper($subject['code'])])) {
                    $valid_result_source_subjects[strtoupper($result_source['code'])][strtoupper($subject['code'])] = [];
                }

                foreach ($subject['components'] as $component) {
                    $components_weightage_percent_sum = bcadd($components_weightage_percent_sum, $component['weightage_percent'], 2);

                    if (!isset($components_unique_codes[strtoupper($component['code'])])) {
                        $components_unique_codes[strtoupper($component['code'])] = 0;
                    } else {
                        $components_unique_codes[strtoupper($component['code'])]++;
                    }

                    if (!isset($valid_result_source_subjects[strtoupper($result_source['code'])][strtoupper($subject['code'])][strtoupper($component['code'])])) {
                        $valid_result_source_subjects[strtoupper($result_source['code'])][strtoupper($subject['code'])][strtoupper($component['code'])] = [];
                    }
                }

                // All result source subject components must sum up to 100 if got components
                if (count($subject['components']) > 0 && bccomp($components_weightage_percent_sum, 100, 2) !== 0) {
                    $errors->push('Components\' weightage_percent must sum up to 100 for subject code ' . $subject['code']);
                }

                // Each result source subject components must have unique code within the same subject
                arsort($components_unique_codes);

                foreach ($components_unique_codes as $component_code => $count) {
                    if ($count > 0) {
                        $errors->push('Duplicated component code "' . $component_code . '". All components\' code must be unique for subject code ' . $subject['code']);
                    }
                }

                // Each result source subject must have valid subject code
                if (!isset($valid_subject_codes[strtoupper($subject['code'])])) {
                    $errors->push('Subject code "' . $subject['code'] . '" is invalid in result source ' . $result_source['code']);
                }
            }

            // Each result source subject code must be unique within the same result source
            arsort($subjects_unique_codes);

            foreach ($subjects_unique_codes as $subject_code => $count) {
                if ($count > 0) {
                    $errors->push('Duplicated subject code "' . $subject_code . '". All subjects\' code must be unique for result source ' . $result_source['code']);
                }
            }


            foreach ($result_source['exams'] as $exam) {

                // Each exam can only be used once inside a SGF
                if (!isset($exams_unique_codes[strtoupper($exam)])) {
                    $exams_unique_codes[strtoupper($exam)] = 0;
                } else {
                    $exams_unique_codes[strtoupper($exam)]++;
                }

                // Each result source exam tagging must have valid exam code
                if (!isset($valid_exam_codes[strtoupper($exam)])) {
                    $errors->push('Exam code "' . $exam . '" is invalid in result source ' . $result_source['code']);
                }

            }
        }

        // Each result source must have unique code within the same grading framework
        arsort($result_source_unique_codes);

        foreach ($result_source_unique_codes as $result_source_code => $count) {
            if ($count > 0) {
                $errors->push('Duplicated result source code "' . $result_source_code . '". All result sources\' code must be unique for grading framework ' . $config_array['grading_framework_code']);
            }
        }

        arsort($exams_unique_codes);

        foreach ($exams_unique_codes as $exam_code => $count) {
            if ($count > 0) {
                $errors->push('Duplicated exam code "' . $exam_code . '" used. Each exam code can only be assigned once.');
            }
        }

        // Validate output
        $output_unique_codes = [];
        $final_output_count = 0;

        foreach ($config_array['output'] as $output) {

            if (!isset($output_unique_codes[strtoupper($output['code'])])) {
                $output_unique_codes[strtoupper($output['code'])] = 0;
            } else {
                $output_unique_codes[strtoupper($output['code'])]++;
            }

            if (isset($output['is_final']) && $output['is_final'] == 1) {
                $final_output_count++;
            }

            // Each report card output must contain all compulsory components declared at Grading framework output_system_components
            $components_code = collect($output['components'])->pluck('code')->unique()->toArray();

            if (isset($config_array['output_system_components']) && count($config_array['output_system_components']) > 0 && !empty(array_diff($config_array['output_system_components'], $components_code))) {
                $errors->push('Grading Framework Output "' . $output['code'] . '" components must contain all mandatory components from "output_system_components"');
            }

            // Report card output specified service must exists
            $service_name = $output['service'];

            if (!class_exists(self::OUTPUT_SERVICE_PATH_PREFIX . $service_name)) {
                $errors->push('Grading Framework Output "' . $output['code'] . '" service "' . $service_name . '" is invalid.');
            }

            $output_component_unique_codes = [];

            foreach ($output['components'] as $output_component) {

                if (!isset($output_component_unique_codes[strtoupper($output_component['code'])])) {
                    $output_component_unique_codes[strtoupper($output_component['code'])] = 0;
                } else {
                    $output_component_unique_codes[strtoupper($output_component['code'])]++;
                }

                // Each report card output component grading scheme code must be valid if provided.
                if (isset($output_component['grading_scheme_code']) && !isset($valid_grading_schemes[strtoupper($output_component['grading_scheme_code'])])) {
                    $errors->push('Grading Scheme "' . $output_component['grading_scheme_code'] . '" is invalid in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                }

                // Each report card output component with subject must have valid subject code
                if (isset($output_component['subject_code']) && !isset($valid_subject_codes[strtoupper($output_component['subject_code'])])) {
                    $errors->push('Subject code "' . $output_component['subject_code'] . '" is invalid in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                }

                // Each report card output component with subject must have code === subject code
                if (isset($output_component['subject_code']) && $output_component['subject_code'] != $output_component['code']) {
                    $errors->push('Output component code must be same as subject code if subject code is provided in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                }

                // Validate each total_formula and label_formula in report card output component to ensure they are legit/correct formula and can be parsed
                if (isset($output_component['total_formula'])) {
                    $success = $report_card_output_service->validate($output_component['total_formula']);

                    if ($success !== true) {
                        $errors->push('Bad total_formula (' . $success . ') in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                    } else {
                        // validate references for RESULTSOURCE and SUBJECT in each formula
                        if (preg_match("/RESULTSOURCE\[(.+)\].+/U", $output_component['total_formula'], $rs_matches)) {

                            if (count($rs_matches) === 2 && !isset($valid_result_source_subjects[$rs_matches[1]])) {
                                $errors->push('Invalid total_formula result source code ' . $rs_matches[1] . ' in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                            } else {
                                if (preg_match("/\.SUBJECT\[(.+)\].+/U", $output_component['total_formula'], $rs_subject_matches)) {

                                    if (count($rs_subject_matches) === 2 && !isset($valid_result_source_subjects[$rs_matches[1]][$rs_subject_matches[1]])) {
                                        $errors->push('Invalid total_formula subject code ' . $rs_subject_matches[1] . ' in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                                    }

                                }
                            }
                        }
                    }

                }
                if (isset($output_component['label_formula'])) {
                    $success = $report_card_output_service->validate($output_component['label_formula']);

                    if ($success !== true) {
                        $errors->push('Bad label_formula (' . $success . ') in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                    } else {
                        // validate references for RESULTSOURCE and SUBJECT in each formula
                        if (preg_match("/RESULTSOURCE\[(.+)\].+/U", $output_component['label_formula'], $rs_matches)) {

                            if (count($rs_matches) === 2 && !isset($valid_result_source_subjects[$rs_matches[1]])) {
                                $errors->push('Invalid label_formula result source code ' . $rs_matches[1] . ' in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                            } else {
                                if (preg_match("/\.SUBJECT\[(.+)\].+/U", $output_component['label_formula'], $rs_subject_matches)) {

                                    if (count($rs_subject_matches) === 2 && !isset($valid_result_source_subjects[$rs_matches[1]][$rs_subject_matches[1]])) {
                                        $errors->push('Invalid label_formula subject code ' . $rs_subject_matches[1] . ' in Output Component ' . $output['code'] . ' > ' . $output_component['code']);
                                    }

                                }
                            }
                        }
                    }
                }


            }

            //Each report card output component must have unique code within the same report card output
            arsort($output_component_unique_codes);

            foreach ($output_component_unique_codes as $output_component_code => $count) {
                if ($count > 0) {
                    $errors->push('Duplicated grading framework output component code "' . $output_component_code . '". All component codes must be unique for Output code ' . $output['code']);
                }
            }

        }

        // Can only have 1 is_final report card output within a Grading framework
        if ($final_output_count > 1) {
            $errors->push('Must have only 1 Output with is_final = 1 setting for Grading Framework ' . $config_array['grading_framework_code']);
        } else {
            if ($final_output_count === 0) {
                $errors->push('Must have at least 1 Output with is_final = 1 setting for Grading Framework ' . $config_array['grading_framework_code']);
            }
        }

        //Each report card output must have unique code within the same grading framework
        arsort($output_unique_codes);

        foreach ($output_unique_codes as $output_code => $count) {
            if ($count > 0) {
                $errors->push('Duplicated grading framework output code "' . $output_code . '". All output codes must be unique for grading framework ' . $config_array['grading_framework_code']);
            }
        }

        if (count($errors) === 0) {
            return true;
        }

        if ($throw_exception) {
            $validationException = ValidationException::withMessages($errors->toArray());
            throw $validationException;
        }
        return $errors;

    }

    // Use to apply new grading framework
    public function applyGradingFrameworkForPeriod(Carbon $effective_from, Carbon $effective_to, string $academic_year)
    {
        $student_primary_class = $this->student->primaryClass;

        // skip if student doesn't have primary class, usually inactive students
        if ( $student_primary_class === null ) {
            return $this;
        }
        
        $current_student_grading_frameworks = $this->studentGradingFrameworkRepository
            ->getAll([
                'academic_year' => $academic_year,
                'student_id' => $this->student->id,
            ]);
        
        // should only be one, but looping through just in case
        foreach ($current_student_grading_frameworks as $current_student_grading_framework){
            $this->deleteStudentGradingFramework($current_student_grading_framework);
        } 
        
        // Maps master grading framework to subjects taken by student
        $config = $this->mapConfigToValidSubjects();
        // validate json configuration
        $this->validateConfiguration($config, true);
        \DB::transaction(function () use (&$config, &$effective_from, &$effective_to, $academic_year) {

            $this->studentGradingFrameworkRepository->expireAllForStudent($this->student);
            $valid_exams = app()->make(ExamRepository::class)->getAll()->keyBy('code');
            $valid_subjects = app()->make(SubjectRepository::class)->getAll()->keyBy('code');
            $valid_grading_schemes = app()->make(GradingSchemeRepository::class)->getAll(['is_active' => true])->keyBy('code');

            $this->setValidExams($valid_exams);
            $this->setValidSubjects($valid_subjects);
            $this->setValidGradingSchemes($valid_grading_schemes);

            $this->studentGradingFramework = $this->studentGradingFrameworkRepository->create([
                'student_id' => $this->student->id,
                'grading_framework_id' => $this->gradingFramework->id,
                'effective_from' => $effective_from->toDateString(),
                'effective_to' => $effective_to->toDateString(),
                'academic_year' => $academic_year,
                'is_active' => true,
                'configuration' => $config,
            ]);

            // setup result source (and tag exam)
            foreach ($config['result_sources'] as $result_source_config) {
                $this->insertResultSources($result_source_config);
            }

            foreach ($config['output'] as $output_config) {
                $this->insertReportCardOutput($output_config);
            }

        });

        return $this;

    }

    // Use to update existing grading framework
    public function updateGradingFramework(Carbon $effective_from, Carbon $effective_to): static
    {
        $student_primary_class = $this->student->primaryClass;

        // set to inactive and skip, student doesn't have primary class, usually inactive students
        if ( $student_primary_class === null ) {
            $this->studentGradingFrameworkRepository->update($this->studentGradingFramework, ['is_active' => false]);
            return $this;
        }

        // Maps master grading framework to subjects taken by student
        $grading_framework = $this->getGradingFramework();
        $config = $this->mapConfigToValidSubjects();

        $this->validateConfiguration($config, true);
        $student_grading_framework = $this->studentGradingFramework;

        if ($student_grading_framework->grading_framework_id !== $grading_framework->id) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::GRADING_FRAMEWORK_ERROR, 60001);
        }

        DB::transaction(function () use ($config, $effective_from, $effective_to, $student_grading_framework) {

            $updated_student_grading_framework = $this->studentGradingFrameworkRepository->update($student_grading_framework, [
                'effective_from' => $effective_from->toDateString(),
                'effective_to' => $effective_to->toDateString(),
                'is_active' => true,
                'configuration' => $config
            ]);

            // Update grading framework
            $this->setStudentGradingFramework($updated_student_grading_framework);

            $valid_exams = app()->make(ExamRepository::class)->getAll()->keyBy('code');
            $valid_subjects = app()->make(SubjectRepository::class)->getAll()->keyBy('code');
            $valid_grading_schemes = app()->make(GradingSchemeRepository::class)->getAll(['is_active' => true])->keyBy('code');
            $this->setValidExams($valid_exams);
            $this->setValidSubjects($valid_subjects);
            $this->setValidGradingSchemes($valid_grading_schemes);

            $this->updateResultSourcesFromStudentGradingFrameworkConfig();
            $this->updateReportCardOutputFromStudentGradingFrameworkConfig();
        });

        return $this;

    }

    private function updateResultSourcesFromStudentGradingFrameworkConfig(): void
    {
        $student_grading_framework = $this->studentGradingFramework;
        $student_grading_framework_config = $student_grading_framework->configuration;

        // If empty, expected behavior is to remove all result sources
        $config_result_sources = $student_grading_framework_config['result_sources'] ?? [];

        $student_grading_framework->load(['resultSources.exams', 'resultSources.subjects', 'resultSources.subjects.components']);
        $actual_result_sources = $student_grading_framework->resultSources->keyBy('code');

        // 1. Remove unwanted result sources
        $config_result_sources_code = collect($config_result_sources)->pluck('code')->toArray();
        $actual_result_sources->whereNotIn('code', $config_result_sources_code)->each(function ($result_source) {
            $result_source->exams()->detach();

            foreach($result_source->subjects as $subject){
                $subject->components()->delete();
            }

            $result_source->subjects()->delete();
            $result_source->delete();
        });

        // Refresh actual result sources after deletion
        $student_grading_framework->load('resultSources');
        $actual_result_sources = $student_grading_framework->resultSources->keyBy('code');

        $available_result_source_code_before_create = $actual_result_sources->keys();

        // 2. Add new result sources, if result new config result source not in actual result sources, then create it
        $config_result_sources_key_by_code = collect($config_result_sources)->keyBy('code');
        $config_result_sources_key_by_code
            ->whereNotIn('code', $actual_result_sources->keys())
            ->each(function ($result_source_config) {
                $this->insertResultSources($result_source_config);
            });

        // 3. Update existing result sources
        $config_result_sources_key_by_code
            ->whereIn('code', $available_result_source_code_before_create)
            ->each(function ($result_source_config) use ($actual_result_sources) {
                $this->updateExistingResultSource($result_source_config);
            });
    }

    private function insertResultSources($result_source_config)
    {
        $valid_exams = $this->getValidExams();
        $valid_subjects = $this->getValidSubjects();

        $result_source = $this->studentGradingFramework->resultSources()->create([
            'student_grading_framework_id' => $this->studentGradingFramework->id,
            'code' => strtoupper($result_source_config['code']),
            'name' => $result_source_config['name'],
        ]);

        // setup result source exam
        if (count($result_source_config['exams']) > 0) {

            $exam_ids = [];

            foreach ($result_source_config['exams'] as $exam_code) {

                if (!isset($valid_exams[$exam_code])) {
                    throw new \Exception('Exam code ' . $exam_code . ' not found in list of exams');
                }

                $exam_ids[] = $valid_exams[$exam_code]->id;
            }

            $result_source->exams()->sync($exam_ids);

        }

        // setup result source subjects
        $subject_inserts = [];
        $subject_components_inserts = [];

        foreach ($result_source_config['subjects'] as $subject_config) {
            $subject_inserts[] = [
                'result_source_id' => $result_source->id,
                'subject_id' => $valid_subjects[$subject_config['code']]->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
            ];
        }

        $result_source_subjects = $result_source->subjects()->createMany($subject_inserts)->keyBy('subject_id');

        // setup result source subject components
        foreach ($result_source_config['subjects'] as $subject_config) {

            foreach ($subject_config['components'] as $component_config) {
                $subject_components_inserts[] = [
                    'result_source_subject_id' => $result_source_subjects[$valid_subjects[$subject_config['code']]->id]->id,
                    'code' => strtoupper($component_config['code']),
                    'name' => json_encode($component_config['name']),
                    'weightage_percent' => $component_config['weightage_percent'],
                    'created_at' => now(),
                ];

            }
        }

        ResultSourceSubjectComponent::insert($subject_components_inserts);
    }

    private function updateExistingResultSource($result_source_config)
    {
        $valid_exams = $this->getValidExams();
        $valid_subjects = $this->getValidSubjects();

        $result_source = $this->studentGradingFramework->resultSources()->where('code', strtoupper($result_source_config['code']))->first();

        $result_source->update([
            'name' => $result_source_config['name'],
        ]);

        // 1. Update result source exam
        $exam_ids = [];
        if (isset($result_source_config['exams']) && count($result_source_config['exams']) > 0) {
            foreach ($result_source_config['exams'] as $exam_code) {
                if (!isset($valid_exams[$exam_code])) {
                    throw new \Exception('Exam code ' . $exam_code . ' not found in list of exams');
                }

                $exam_ids[] = $valid_exams[$exam_code]->id;
            }
        }

        $result_source->exams()->sync($exam_ids);

        $existing_result_source_subjects = $result_source->subjects->keyBy('subject_id');

        $new_config_subject_codes = collect($result_source_config['subjects'])->pluck('code')->toArray();
        $new_config_subject_codes = array_map('strval', $new_config_subject_codes);
        $needed_subjects = $valid_subjects->filter(function ($subject, $key) use ($new_config_subject_codes) {
            return in_array((string) $key, $new_config_subject_codes, true);
        })->keyBy('code');

        // 2. Delete unwanted result source subjects
        $needed_subject_ids = $needed_subjects->pluck('id')->toArray();
        $existing_result_source_subjects->whereNotIn('subject_id', $needed_subject_ids)->each(function ($result_source_subject) {
            $result_source_subject->components()->delete();
            $result_source_subject->delete();
        });

        // Refresh existing result source subjects after deletion
        $result_source->load('subjects.components');
        $existing_result_source_subjects = $result_source->subjects->keyBy('subject_id');

        $existing_result_source_subjects->each(function ($result_source_subject) use ($result_source_config) {

            // 3. Update result source subject
            $needed_subject = collect($result_source_config['subjects'])
                ->where('code', $result_source_subject->subject->code)
                ->first();

            $result_source_subject->update([
                'weightage_multiplier' => $needed_subject['weightage_multiplier'] ?? null,
                'grading_type' => $needed_subject['grading_type'],
            ]);

            $needed_components = $needed_subject['components'];

            // 3. Delete unwanted result source subject components
            $needed_component_codes = array_column($needed_components, 'code');
            $result_source_subject->components->whereNotIn('code', $needed_component_codes)->each(function ($result_source_subject_component) {
                $result_source_subject_component->delete();
            });

            $result_source_subject->load('components');

            // 4. Update result source subject components
            $result_source_subject->components->each(function ($result_source_subject_component) use ($needed_components, $result_source_subject) {
                $needed_component = collect($needed_components)->where('code', $result_source_subject_component->code)->first();
                $result_source_subject_component->update([
                    'name' => $needed_component['name'],
                    'weightage_percent' => $needed_component['weightage_percent'],
                ]);
            });
        });
        // 5. Add new result source subjects
        $existing_result_source_subject_codes = $existing_result_source_subjects->pluck('subject.code')->map('strval')->toArray();
        collect($result_source_config['subjects'])
            ->each(function ($subject) use ($result_source, $valid_subjects, $existing_result_source_subject_codes) {

                // If not in existing result source subjects, then create it
                if (!in_array((string) $subject['code'], $existing_result_source_subject_codes, true)) {
                    $new_result_source_subject = $result_source->subjects()->create([
                        'result_source_id' => $result_source->id,
                        'subject_id' => $valid_subjects[$subject['code']]->id,
                        'weightage_multiplier' => $subject['weightage_multiplier'] ?? null,
                        'is_exempted' => $subject['is_exempted'],
                        'grading_type' => $subject['grading_type'],
                        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
                    ]);

                    foreach ($subject['components'] as $component) {
                        $new_result_source_subject->components()->create([
                            'code' => strtoupper($component['code']),
                            'name' => $component['name'],
                            'weightage_percent' => $component['weightage_percent'],
                        ]);
                    }
                }
            });
    }

    public function updateReportCardOutputFromStudentGradingFrameworkConfig()
    {
        $student_grading_framework = $this->studentGradingFramework;
        $student_grading_framework_config = $student_grading_framework->configuration;

        // If empty, expected behavior is to remove all output components
        $config_outputs = $student_grading_framework_config['output'] ?? [];

        $student_grading_framework->load(['outputs', 'outputs.components']);
        $actual_outputs = $student_grading_framework->outputs->keyBy('code');

        // 1. Remove unwanted report card outputs
        $config_output_code = collect($config_outputs)->pluck('code')->toArray();
        $actual_outputs->whereNotIn('code', $config_output_code)->each(function ($output) {
            $output->components()->delete();
            $output->delete();
        });

        // Refresh actual report card output after deletion
        $student_grading_framework->load('outputs');
        $actual_outputs = $student_grading_framework->outputs->keyBy('code');

        $available_output_codes_before_create = $actual_outputs->keys();

        // 2. Add new report card output, if result new config result source not in actual result sources, then create it
        $config_outputs_key_by_code = collect($config_outputs)->keyBy('code');
        $config_outputs_key_by_code
            ->whereNotIn('code', $actual_outputs->keys())
            ->each(function ($output_config) {
                $this->insertReportCardOutput($output_config);
            });

        // 3. Update existing report card output and add any new components
        $config_outputs_key_by_code
            ->whereIn('code', $available_output_codes_before_create)
            ->each(function ($output_config) use ($actual_outputs) {
                $this->updateExistingReportCardOutput($output_config);
            });
    }

    private function insertReportCardOutput($output_config)
    {
        $valid_subjects = $this->getValidSubjects();
        $valid_grading_schemes = $this->getValidGradingSchemes();

        $output_components_inserts = [];
        $output = $this->studentGradingFramework->outputs()->create([
            'student_grading_framework_id' => $this->studentGradingFramework->id,
            'code' => strtoupper($output_config['code']),
            'name' => $output_config['name'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        foreach($output_config['components'] as $output_component_config){
            $name = $output_component_config['name'] ?? null;

            if ($name === null && isset($output_component_config['subject_code'])) {
                $name = $valid_subjects[$output_component_config['subject_code']]->getTranslations('name');  // name in subject table is also jsonb format
            }


            $output_components_inserts[] = [
                'report_card_output_id' => $output->id,
                'code' => strtoupper($output_component_config['code']),
                'name' => json_encode($name),
                'subject_id' => isset($output_component_config['subject_code']) ? $valid_subjects[$output_component_config['subject_code']]->id : null,
                'grading_scheme_id' => isset($output_component_config['grading_scheme_code']) ? $valid_grading_schemes[$output_component_config['grading_scheme_code']]->id : null,
                'total_formula' => $output_component_config['total_formula'] ?? null,
                'label_formula' => $output_component_config['label_formula'] ?? null,
                'resolve_priority' => $output_component_config['priority'] ?? 0,
                'calculate_rank' => $output_component_config['calculate_rank'] ?? 0,
                'weightage_multiplier' => $output_component_config['weightage_multiplier'] ?? null,
                'output_type' => $output_component_config['output_type'] ?? null,
                'created_at' => now(),
            ];
        }

        // setup report card output components
        ReportCardOutputComponent::insert($output_components_inserts);
    }

    public function updateExistingReportCardOutput($output_config)
    {
        $valid_subjects = $this->getValidSubjects();
        $valid_grading_schemes = $this->getValidGradingSchemes();
        $output = $this->studentGradingFramework->outputs()->where('code', strtoupper($output_config['code']))->first();

        // Update report card output
        $output->update([
            'name' => $output_config['name'] ?? null,
            'is_final' => $output_config['is_final']
        ]);

        // Delete unwanted report card output component
        $existing_output_components = $output->components->keyBy('code');
        $new_config_output_component_codes = collect($output_config['components'])->pluck('code')->toArray();
        $new_config_output_component_codes = array_map('strval', $new_config_output_component_codes);
        $existing_output_components->whereNotIn('code', $new_config_output_component_codes)->each(function ($result_source_subject_component) {
            $result_source_subject_component->delete();
        });

        // Refresh report card output components
        $output->load('components');
        $existing_output_components = $output->components->keyBy('code');
        $needed_components = $output_config['components'];

        // Update Existing Report Card Output Components
        $output->components?->each(function ($output_component) use ($needed_components, $valid_subjects, $valid_grading_schemes) {
            $needed_component = collect($needed_components)->where('code', $output_component->code)->first();

            $name = $needed_component['name'] ?? null;

            if ($name === null && isset($needed_component['subject_code'])) {
                $name = $valid_subjects[$needed_component['subject_code']]->getTranslations('name');  // name in subject table is also jsonb format
            }

            $output_component->update([
                'name' => $name,
                'grading_scheme_id' => isset($needed_component['grading_scheme_code']) ? $valid_grading_schemes[$needed_component['grading_scheme_code']]->id : null,
                'total_formula' => $needed_component['total_formula'] ?? null,
                'label_formula' => $needed_component['label_formula'] ?? null,
                'resolve_priority' => $needed_component['priority'] ?? 0,
                'calculate_rank' => $needed_component['calculate_rank'] ?? 0,
                'output_type' => $needed_component['output_type'] ?? null,
                'weightage_multiplier' => $needed_component['weightage_multiplier'] ?? null,
            ]);
        });

        // Refresh report card output components
        $output->load('components');
        $existing_output_components = $output->components->keyBy('code');
        $needed_components = $output_config['components'];

        // Add remaining report card output components
        collect($output_config['components'])->each(function ($component) use ($output, $existing_output_components, $valid_subjects, $valid_grading_schemes) {
            $existing_output_component_codes = $existing_output_components->keys()->map('strval')->toArray();

            $name = $component['name'] ?? null;

            if ($name === null && isset($component['subject_code'])) {
                $name = $valid_subjects[$component['subject_code']]->getTranslations('name');  // name in subject table is also jsonb format
            }

            if (!in_array( (string) $component['code'], $existing_output_component_codes, true)){
                $output->components()->create([
                    'report_card_output_id' => $output->id,
                    'code' => $component['code'],
                    'name' => $name,
                    'subject_id' => isset($component['subject_code']) ? $valid_subjects[$component['subject_code']]->id : null,
                    'grading_scheme_id' => isset($component['grading_scheme_code']) ? $valid_grading_schemes[$component['grading_scheme_code']]->id : null,
                    'total_formula' => $component['total_formula'] ?? null,
                    'label_formula' => $component['label_formula'] ?? null,
                    'resolve_priority' => $component['priority'] ?? 0,
                    'calculate_rank' => $component['calculate_rank'] ?? 0,
                    'output_type' => $component['output_type'] ?? null,
                    'weightage_multiplier' => $component['weightage_multiplier'] ?? null,
                    'created_at' => now(),
                ]);
            }
        });
    }

    public function mapConfigToValidSubjects(){

        // Always load the master grading framework first
        $config = $this->getGradingFramework()->configuration;
        $student = $this->getStudent();

        $student_primary_class = $student->primaryClass;

        if ( $student_primary_class === null ) {
            throw new \Exception('Student does not have primary class');
        }

        $semester_setting_id = $student_primary_class->semester_setting_id;

        $valid_subjects = ClassSubject::with('subject')
            ->whereRelation('students', 'id', $student->id)
            ->whereRelation('semesterClass', 'semester_setting_id', $semester_setting_id)
            ->whereRelation('semesterClass', 'is_active', true)
            ->get()
            ->pluck('subject');

        // Remove result source subjects from config where student is not taking the subject
        foreach($config['result_sources'] as $result_source_key => $result_source){
            foreach($result_source['subjects'] as $result_source_subject_key => $result_source_subject){
                if (!$valid_subjects->contains('code', $result_source_subject['code'])){
                    unset($config['result_sources'][$result_source_key]['subjects'][$result_source_subject_key]);
                }
            }
        }

        // Remove output component from config where student is not taking the subject
        foreach($config['output'] as $output_key => $output){
            foreach($output['components'] as $output_component_key => $output_component){
                if (isset($output_component['subject_code']) && !$valid_subjects->contains('code', $output_component['subject_code'])){
                        unset($config['output'][$output_key]['components'][$output_component_key]);
                }
            }
        }
        return $config;
    }

    public function setConfigForExemptedSubjects(Subject $subject, Exam $exam, bool $is_exempted): self{
        $student_grading_framework = $this->getStudentGradingFramework();

        $config = $student_grading_framework->configuration;

        // Remove result source subjects from config where student is not taking the subject
        foreach($config['result_sources'] as &$result_source){

            if (!in_array($exam->code, $result_source['exams'])){
                continue;
            }

            foreach($result_source['subjects'] as &$result_source_subject){
                if($result_source_subject['code'] == $subject->code){
                    $result_source_subject['is_exempted'] = $is_exempted;
                }
            }
        }

        $student_grading_framework->update([
            'configuration' => $config
        ]);

        return $this;
    }

    public function deleteStudentGradingFramework(StudentGradingFramework $student_grading_framework)
    {
        // Deleting result source component, subjects, exam relation and source (in that order)
        DB::transaction(function () use ($student_grading_framework){
            $result_sources = $student_grading_framework->resultSources;

            foreach ($result_sources as $result_source){
                $result_source_subjects = $result_source->subjects;
    
                foreach($result_source_subjects as $result_source_subject){
                    $result_source_subject_components = $result_source_subject->components;
    
                    foreach($result_source_subject_components as $result_source_subject_component){
                        if (!is_null($result_source_subject_component->actual_score)){
                            ErrorCodeHelper::throwError(ErrorCodeHelper::GRADING_FRAMEWORK_ERROR, 60002);
                        }
    
                        $result_source_subject_component->delete();
                    }   
                    $result_source_subject->delete();
                }
                $result_source->exam()->detach();
                $result_source->delete();
            }
    
            $report_card_outputs = $student_grading_framework->outputs;
    
            foreach ($report_card_outputs as $report_card_output){
                $report_card_output_components = $report_card_output->components;
    
                foreach ($report_card_output_components as $report_card_output_component){
                    $report_card_output_component->delete();
                }
                $report_card_outputs->delete();
            }
    
            $student_grading_framework->delete();
        });
    }


    public function getStudent(): Student
    {
        return $this->student;
    }

    public function setStudent(Student $student): StudentGradingFrameworkService
    {
        $this->student = $student;
        return $this;
    }

    public function getGradingFramework(): GradingFramework
    {
        return $this->gradingFramework;
    }

    public function setGradingFramework(GradingFramework $gradingFramework): StudentGradingFrameworkService
    {
        $this->gradingFramework = $gradingFramework;
        return $this;
    }

    public function getStudentGradingFramework(): StudentGradingFramework
    {
        return $this->studentGradingFramework;
    }

    public function setStudentGradingFramework(StudentGradingFramework $studentGradingFramework): StudentGradingFrameworkService
    {
        $this->studentGradingFramework = $studentGradingFramework;
        return $this;
    }

    private function setValidSubjects($valid_subjects): StudentGradingFrameworkService
    {
        $this->validSubjects = $valid_subjects;
        return $this;
    }

    private function getValidSubjects(): Collection
    {
        return $this->validSubjects;
    }

    private function setValidExams($valid_exams): StudentGradingFrameworkService
    {
        $this->validExams = $valid_exams;
        return $this;
    }

    private function getValidExams(): Collection
    {
        return $this->validExams;
    }

    private function setValidGradingSchemes($valid_grading_schemes): StudentGradingFrameworkService
    {
        $this->validGradingSchemes = $valid_grading_schemes;
        return $this;
    }

    private function getValidGradingSchemes(): Collection
    {
        return $this->validGradingSchemes;
    }

}

