<?php

namespace App\Services\Exam;

use App\Models\ResultsPostingHeader;
use App\Repositories\ResultsPostingHeaderRepository;

class ResultsPostingHeaderService
{
    private array $data = [];

    public function __construct(
        protected ResultsPostingHeaderRepository $resultsPostingHeaderRepository
    ) {
    }

    public function getAllResultsPostingHeader(array $filters = [])
    {
        return $this->resultsPostingHeaderRepository->getAll($filters);

    }

    public function getAllPaginatedResultsPostingHeader(array $filters = [])
    {
        return $this->resultsPostingHeaderRepository->getAllPaginated($filters);
    }


    public function getResultsPostingHeaderByGrade(array $filters = [])
    {
        $filters['status'] = ResultsPostingHeader::STATUS_COMPLETED;
        $filters['active_report_card_only'] = true;
        return $this->resultsPostingHeaderRepository
            ->getAll($filters);
    }

    public function getResultsPostingHeaderByStudent(int $student_id)
    {
        $filters = [
            'student_id' => $student_id,
            'active_report_card_only' => true,
        ];

        return $this->resultsPostingHeaderRepository
            ->getAll($filters);
    }
}
