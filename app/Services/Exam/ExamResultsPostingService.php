<?php

namespace App\Services\Exam;

use App\Exceptions\ExamResultsPostingException;
use App\Interfaces\IReportCardOutput;
use App\Models\Grade;
use App\Models\ReportCardOutput;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Repositories\ReportCardOutputRepository;
use App\Repositories\ResultsPostingHeaderRepository;
use App\Repositories\ResultsPostingLineItemRepository;
use App\Repositories\StudentReportCardRepository;
use App\Repositories\StudentRepository;

class ExamResultsPostingService
{
    protected array $resultSources;
    protected ReportCardOutput $reportCardOutput;
    protected ResultsPostingHeader $resultsPostingHeader;

    protected array $outputValues;

    protected IReportCardOutput $reportCardOutputService;

    protected Grade $grade;
    protected SemesterSetting $semesterSetting;
    protected string $reportCardOutputCode;
    protected string $reportCardTemplateServiceName;
    protected array $metadata;
    protected string $publishDate;

    public function __construct(
        protected ResultsPostingHeaderRepository $resultsPostingHeaderRepository,
        protected ReportCardOutputRepository $reportCardOutputRepository
    ) {

    }

    public function setReportCardOutputService($class_name)
    {

        $full_class_name = 'App\\Services\\Exam\\Output\\' . $class_name;

        if (!class_exists($full_class_name)) {
            throw new ExamResultsPostingException('Report Card Output class not found: ' . $class_name);
        }

        $this->reportCardOutputService = app()->make($full_class_name);
        return $this;
    }

    public function runChecks($return_errors_as_list = false)
    {

        // check result sources data
        $errors = $this->checkDataEntryCompleteness();

        if ($return_errors_as_list) {
            return $errors;
        }

        if (count($errors) > 0) {
            throw new \Exception(join(', ', $errors));
        }

        return $this;

    }

    public function getEligibleMasterGradingFrameworks($filters)
    {

        $grading_frameworks = StudentGradingFramework::query()
            ->with('gradingFramework')
            ->whereRelation('student.classes.semesterClass', 'semester_setting_id', $filters['semester_setting_id'])
            ->whereRelation('student.classes.semesterClass.classModel', 'grade_id', $filters['grade_id'])
            ->get()
            ->map(function ($item) {
                return $item->gradingFramework;
            })
            ->unique();

        return $grading_frameworks;
    }


    public function getEligibleReportCardOutputCodes(array $filters)
    {

        $student_grading_framework = StudentGradingFramework::query()
            ->with('outputs', fn($query) => $query->orderBy('id'))
            ->where('grading_framework_id', $filters['grading_framework_id'])
            ->first();

        return $student_grading_framework?->outputs;
    }

    public function createPostingSession()
    {

        if (!isset($this->grade) || !isset($this->semesterSetting) || !isset($this->reportCardOutputCode) || !isset($this->reportCardTemplateServiceName)) {
            throw new ExamResultsPostingException('Grade, SemesterSetting, ReportCardOutputCode and ReportCardTemplateServiceName are required for posting.');
        }

        $this->validateCanCreateNewPostingSession($this->grade, $this->semesterSetting);

        // validate output code is valid
        if (!$this->validateReportCardOutputCode($this->reportCardOutputCode, $this->grade, $this->semesterSetting)) {
            throw new ExamResultsPostingException('Unable to find report card output with code ' . $this->reportCardOutputCode);
        }
        // validate report card template service exists
        if (!$this->validateReportCardTemplateService($this->reportCardTemplateServiceName)) {
            throw new ExamResultsPostingException('Unable to find report card template service: ' . $this->reportCardTemplateServiceName);
        }

        $now = now(config('school.timezone'));

        $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($this->grade, $this->semesterSetting);

        return $this->resultsPostingHeaderRepository->create([
            'code' => $now->format('Ymd') . '-' . $now->getTimestampMs(),
            'report_card_output_code' => $this->reportCardOutputCode,
            'report_card_template_service' => $this->reportCardTemplateServiceName,
            'grade_id' => $this->grade->id,
            'semester_setting_id' => $this->semesterSetting->id,
            'status' => ResultsPostingHeader::STATUS_PENDING,
            'student_ids' => $students->pluck('id')->sort()->values()->toArray(),
            'posted_by_employee_id' => \Auth::user()->employee->id,
            'publish_date' => $this->getPublishDate(),
            'posted_at' => now(),
            'metadata' => $this->metadata ?? null,
        ]);

    }

    public function validateCanCreateNewPostingSession(Grade $grade, SemesterSetting $semester)
    {

        // only employee can create posting
        if (\Auth::user()->employee === null) {
            throw new ExamResultsPostingException('Only employee is able to create exam results posting.');
        }

        // dont allow create new posting session when there's an existing one running
        if ($this->resultsPostingHeaderRepository->hasCurrentlyProcessingPosting($grade, $semester)) {
            throw new ExamResultsPostingException('Unable to create new exam results posting because there is an existing running posting for Grade ' . $grade->name . ' Semester ' . $semester->name);
        }

        return true;
    }

    public function validateReportCardTemplateService($report_card_template_service)
    {
        $full_class_name = 'App\\Services\\Exam\\ReportCard\\' . $report_card_template_service;
        return class_exists($full_class_name);
    }

    public function validateReportCardOutputCode($report_card_output_code, Grade $grade, SemesterSetting $semester)
    {

        // get all students in grade in semester
        $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($grade, $semester, ['gradingFrameworks']);

        // get all unique student grading framework ID from students
        $grading_framework_ids = collect([]);

        $students->map(function ($value) use (&$grading_framework_ids) {
            if ($value->activeGradingFramework() !== null) {
                $grading_framework_ids->push($value->activeGradingFramework()->id);
            }
        });

        if (count($students) === 0) {
            throw new ExamResultsPostingException('Students not found for Grade ' . $grade->name . ' in Semester ' . $semester->name);
        }

        $grading_framework_ids = $grading_framework_ids->unique()->values()->toArray();

        if (count($grading_framework_ids) === 0) {
            throw new ExamResultsPostingException('No grading frameworks found for students in Grade' . $grade->name . ' in Semester ' . $semester->name);
        }

        // check whether reportcardoutput exists
        $outputs = $this->reportCardOutputRepository->getAll([
            'code' => $report_card_output_code,
            'student_grading_framework_id' => $grading_framework_ids,
        ]);

        return $outputs->count() > 0;
    }


    public function runPosting()
    {

        // compile components score into ResultSourceSubject table
        $this->reportCardOutputService
            ->setStudentGradingFramework($this->reportCardOutput->studentGradingFramework)
            ->setResultsPostingHeader($this->resultsPostingHeader)
            ->compileResultSourceScoresUsedInOutput($this->reportCardOutput)
            ->evaluateAllComponentsForOutput($this->reportCardOutput);

        $this->setOutputValues($this->reportCardOutputService->getOutputValues());

        return $this;
    }

    public function savePostingOutput()
    {

        $to_be_inserted = [];
        $output_component_ids = [];

        $student_grading_framework = $this->reportCardOutput->studentGradingFramework;
        $student_id = $student_grading_framework->student_id;

        // get grade/class details
        $student_grading_framework->load(['student.currentSemesterPrimaryClass.semesterClass.classModel']);
        $student_class = $student_grading_framework->student->currentSemesterPrimaryClass;

        if ($student_class === null) {
            throw new ExamResultsPostingException('Unable to resolve student current semester primary class');
        }

        $semester_class_id = $student_class->semester_class_id;
        $grade_id = $student_class->semesterClass->classModel->grade_id;

        foreach ($this->getOutputValues() as $output_code => $output_items) {
            foreach ($output_items as $output_component_code => $item) {
                // write output values to DB
                $to_be_inserted[] = [
                    'header_id' => $this->resultsPostingHeader->id,
                    'report_card_output_component_id' => $item['report_card_output_component']->id,
                    'report_card_output_component_code' => $output_component_code,
                    'student_id' => $student_id,
                    'grade_id' => $grade_id,
                    'semester_class_id' => $semester_class_id,
                    'subject_id' => $item['subject_id'],
                    'grading_scheme_id' => $item['grading_scheme_id'],
                    'grading_framework_id' => $student_grading_framework->grading_framework_id,
                    'total' => $item['total'],
                    'total_grade' => $item['total_grade'] !== null ? json_encode($item['total_grade']) : null,
                    'label' => $item['label'],
                    'calculate_rank' => $item['report_card_output_component']->calculate_rank,
                    'weightage_multiplier' => $item['weightage_multiplier'],
                    'output_type' => $item['output_type'],
                    'weightage_total' => $item['weightage_total'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $output_component_ids[] = $item['report_card_output_component']->id;

            }
        }

        \DB::transaction(function () use (&$to_be_inserted, &$output_component_ids, $student_id) {

            // if got previous data from previous postings, archive and delete
            app()->make(ResultsPostingLineItemRepository::class)->archivePreviousData($student_id, $output_component_ids);

            // insert new data
            ResultsPostingLineItem::insert($to_be_inserted);

        });

        return $this;

    }


    public function checkDataEntryCompleteness()
    {

        // for the specified resultSources, make sure all subjects that are not exempted have marks or grade entered.
        return $this->reportCardOutputService
            ->setStudentGradingFramework($this->reportCardOutput->studentGradingFramework)
            ->validateResultSourcesInOutputBeforeCompilation($this->reportCardOutput, false);

    }

    public function calculatePositionInClass()
    {
        $net_averages = ResultsPostingLineItem::where('header_id', $this->resultsPostingHeader->id)
            ->where('report_card_output_component_code', 'SYS_NET_AVG')
            ->orderBy('total', 'DESC');
    }

    public function updateClassAndGradeRankingsForPostingLineItems()
    {

        $line_items = ResultsPostingLineItem::where('header_id', $this->resultsPostingHeader->id)
            ->where('calculate_rank', true)
            ->orderBy('total', 'DESC')      // important to sort by total score, highest first
            ->orderBy('student_id', 'ASC')      // if same total, order by student ID
            ->get();

        $grade_grouped_results = [];
        $grade_percentile_list = [];
        $grade_population = [];

        $class_grouped_results = [];
        $class_population = [];

        foreach ($line_items as $line_item) {

            if (!isset($grade_grouped_results[$line_item->report_card_output_component_code])) {
                $grade_grouped_results[$line_item->report_card_output_component_code] = [];
            }
            if (!isset($grade_grouped_results[$line_item->report_card_output_component_code][$line_item->grade_id])) {
                $grade_grouped_results[$line_item->report_card_output_component_code][$line_item->grade_id] = collect([]);
                $grade_population[$line_item->report_card_output_component_code][$line_item->grade_id] = 0;
            }

            $grade_grouped_results[$line_item->report_card_output_component_code][$line_item->grade_id]->push($line_item);
            $grade_population[$line_item->report_card_output_component_code][$line_item->grade_id]++;

            if (!isset($class_grouped_results[$line_item->report_card_output_component_code])) {
                $class_grouped_results[$line_item->report_card_output_component_code] = [];
            }
            if (!isset($class_grouped_results[$line_item->report_card_output_component_code][$line_item->semester_class_id])) {
                $class_grouped_results[$line_item->report_card_output_component_code][$line_item->semester_class_id] = collect([]);
                $class_population[$line_item->report_card_output_component_code][$line_item->semester_class_id] = 0;
            }

            $class_grouped_results[$line_item->report_card_output_component_code][$line_item->semester_class_id]->push($line_item);
            $class_population[$line_item->report_card_output_component_code][$line_item->semester_class_id]++;

        }

        // calculate grade percentiles
        foreach ($grade_grouped_results as $output_component_code => $results) {
            foreach ($results as $grade_id => $line_items) {
                foreach ($line_items as $index => $line_item) {

                    // (0.5 * F) must have a minimum value of 1

                    // formula = (CF + (0.5 * F)) / N * 100
                    // CF = number of students with lesser score than this student
                    // F = number of students with same score as this student
                    // N = all students count

                    $cf = $line_items->where('total', '<', $line_item->total)->count();
                    $f = $line_items->where('total', '=', $line_item->total)->count();
                    $f = $f == 1 ? 2 : $f; // this is to force the expression (0.5 * f) to have a minimum value of 1
                    $n = count($line_items);

                    $percentile = round(($cf + (0.5 * $f)) / $n * 100, 2);

                    $grade_percentile_list[$line_item->id] = [
                        'percentile' => $percentile,
                        'percentile_component' => [
                            'cf' => $cf,
                            'f' => $f,
                            'n' => $n
                        ],
                        'total' => $line_item->total,
                        'report_card_output_component_code' => $line_item->report_card_output_component_code,
                        'grade_id' => $line_item->grade_id,
                    ];

                }
            }
        }

        \DB::transaction(function () use (&$grade_grouped_results, &$class_grouped_results, &$grade_population, &$class_population, &$grade_percentile_list) {

            foreach ($grade_grouped_results as $output_component_code => $results) {
                foreach ($results as $grade_id => $line_items) {
                    $rank = 1;

                    foreach ($line_items as $index => $line_item) {

                        // if current line item same total as previous line item, dont increase rank
                        // if students score is 80, 80, 79 (ranking should be 1,1,3)
                        if ($index > 0 && bccomp($line_item->total, $line_items[$index - 1]->total, 2) !== 0) {
                            $rank = $index + 1;
                        }

                        ResultsPostingLineItem::where('id', $line_item->id)
                            ->update([
                                'grade_population' => $grade_population[$output_component_code][$grade_id],
                                'grade_rank' => $rank,
                                'grade_percentile_rank' => $grade_percentile_list[$line_item->id]['percentile'],
                            ]);

                    }
                }
            }

            foreach ($class_grouped_results as $output_component_code => $results) {
                foreach ($results as $semester_class_id => $line_items) {
                    $rank = 1;

                    foreach ($line_items as $index => $line_item) {

                        // if current line item same total as previous line item, dont increase rank
                        // if students score is 80, 80, 79 (ranking should be 1,1,3)
                        if ($index > 0 && bccomp($line_item->total, $line_items[$index - 1]->total, 2) !== 0) {
                            $rank = $index + 1;
                        }

                        ResultsPostingLineItem::where('id', $line_item->id)
                            ->update([
                                'class_population' => $class_population[$output_component_code][$semester_class_id],
                                'class_rank' => $rank
                            ]);

                    }
                }
            }

        });

    }

    public function allowReportCardVisibleByPostingHeader()
    {
        app()->make(StudentReportCardRepository::class)->changeReportCardVisibilityByHeader($this->resultsPostingHeader, true);
    }

    public function disallowReportCardVisibleByPostingHeader()
    {
        app()->make(StudentReportCardRepository::class)->changeReportCardVisibilityByHeader($this->resultsPostingHeader, false);
    }

    public function allowReportCardVisibleByStudent(Student $student)
    {
        app()->make(StudentReportCardRepository::class)->changeReportCardVisibilityByStudent($this->resultsPostingHeader, $student, true);
    }

    public function disallowReportCardVisibleByStudent(Student $student)
    {
        app()->make(StudentReportCardRepository::class)->changeReportCardVisibilityByStudent($this->resultsPostingHeader, $student, false);
    }

    public function resetCurrentPostingToHeader(ResultsPostingHeader $restore_to_header)
    {

        \DB::transaction(function () use ($restore_to_header) {
            app()->make(ResultsPostingLineItemRepository::class)
                ->archivePreviousDataByHeader($this->resultsPostingHeader)
                ->restoreLineItemsByHeader($restore_to_header);
        });

        return $this;

    }

    public function getResultSources(): array
    {
        return $this->resultSources;
    }

    public function setResultSources(array $resultSources): ExamResultsPostingService
    {
        $this->resultSources = $resultSources;
        return $this;
    }

    public function getReportCardOutput(): ReportCardOutput
    {
        return $this->reportCardOutput;
    }

    public function setReportCardOutput(ReportCardOutput $reportCardOutput): ExamResultsPostingService
    {
        $this->reportCardOutput = $reportCardOutput;
        return $this;
    }

    public function getOutputValues(): array
    {
        return $this->outputValues;
    }

    public function setOutputValues(array $outputValues): ExamResultsPostingService
    {
        $this->outputValues = $outputValues;
        return $this;
    }

    public function getResultsPostingHeader(): ResultsPostingHeader
    {
        return $this->resultsPostingHeader;
    }

    public function setResultsPostingHeader(ResultsPostingHeader $resultsPostingHeader): ExamResultsPostingService
    {
        $this->resultsPostingHeader = $resultsPostingHeader;
        return $this;
    }

    public function getReportCardOutputService(): IReportCardOutput
    {
        return $this->reportCardOutputService;
    }

    public function getGrade(): Grade
    {
        return $this->grade;
    }

    public function setGrade(Grade $grade): ExamResultsPostingService
    {
        $this->grade = $grade;
        return $this;
    }

    public function getSemesterSetting(): SemesterSetting
    {
        return $this->semesterSetting;
    }

    public function setSemesterSetting(SemesterSetting $semesterSetting): ExamResultsPostingService
    {
        $this->semesterSetting = $semesterSetting;
        return $this;
    }

    public function getReportCardOutputCode(): string
    {
        return $this->reportCardOutputCode;
    }

    public function setReportCardOutputCode(string $reportCardOutputCode): ExamResultsPostingService
    {
        $this->reportCardOutputCode = $reportCardOutputCode;
        return $this;
    }

    public function getReportCardTemplateServiceName(): string
    {
        return $this->reportCardTemplateServiceName;
    }

    public function setReportCardTemplateServiceName(string $reportCardTemplateServiceName): ExamResultsPostingService
    {
        $this->reportCardTemplateServiceName = $reportCardTemplateServiceName;
        return $this;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function setMetadata(array $metadata): ExamResultsPostingService
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function setPublishDate($publish_date): self
    {
        $this->publishDate = $publish_date;
        return $this;
    }

    public function getPublishDate()
    {
        return $this->publishDate;
    }

}
