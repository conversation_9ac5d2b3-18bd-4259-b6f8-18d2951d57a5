<?php

namespace App\Services\Exam;

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Repositories\ExamRepository;
use App\Repositories\ResultSourceSubjectComponentRepository;
use App\Repositories\ResultSourceSubjectRepository;
use App\Repositories\SemesterClassRepository;
use App\Services\SemesterSettingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ExamResultsDataEntryService
{
    protected Subject $subject;
    protected Student $student;
    protected Exam $exam;
    protected Employee $employee;
    protected ClassModel $class;

    protected ResultSourceSubject $resultSourceSubject;
    protected ResultSourceSubjectComponent $resultSourceSubjectComponent;

    protected string $subjectComponentCode;
    protected ?float $score;
    protected ?string $grade;

    protected ResultSourceSubjectComponentRepository $resultSourceSubjectComponentRepository;
    protected ResultSourceSubjectRepository $resultSourceSubjectRepository;

    public function __construct(
        ResultSourceSubjectComponentRepository $result_source_subject_component_repository,
        ResultSourceSubjectRepository $result_source_subject_repository
    ) {
        $this->resultSourceSubjectComponentRepository = $result_source_subject_component_repository;
        $this->resultSourceSubjectRepository = $result_source_subject_repository;
    }

    public function saveUsingResultSourceSubject()
    {

        if ($this->resultSourceSubject->data_entry_status === ResultSourceSubject::DATA_ENTRY_STATUS_POSTED) {
            throw new \Exception('Unable to perform data entry as data status is Posted for subject ' . $this->resultSourceSubject->subject->name);
        }

        if ($this->resultSourceSubject->isScoreGradingType()) {

            if (!isset($this->score)) {
                throw new \Exception('Score is required when grading_type is SCORE');
            }
            if (!isset($this->resultSourceSubjectComponent)) {
                throw new \Exception('Subject Component is required when grading_type is SCORE');
            }

            // update into result source subject components table if it's score, since we can break down score by components
            ResultSourceSubjectComponent::where('id', $this->resultSourceSubjectComponent->id)
                ->update([
                    'actual_score' => $this->score,
                    'data_entry_employee_id' => $this->employee->id,
                    'data_entry_at' => now('UTC'),
                ]);
        } else {
            if ($this->resultSourceSubject->isGradeGradingType()) {

                if (!isset($this->grade)) {
                    throw new \Exception('Grade is required when grading_type is GRADE');
                }

                // update into result source subject table if it's grade, since grade won't have components.
                ResultSourceSubject::where('id', $this->resultSourceSubject->id)->update([
                    'actual_score_grade' => $this->grade,
                    'data_entry_employee_id' => $this->employee->id,
                    'data_entry_at' => now('UTC'),
                ]);

            }
        }

        return $this;

    }

    public function save()
    {

        if (!isset($this->employee) || !isset($this->student) || !isset($this->exam) || !isset($this->subject)) {
            throw new \Exception('Employee, Student, Exam, Subject are required.');
        }

        $result_source = $this->getResultSource();

        $result_source_subject = $this->determineResultSourceSubject($result_source);

        if ($result_source_subject->data_entry_status === ResultSourceSubject::DATA_ENTRY_STATUS_POSTED) {
            throw new \Exception('Unable to perform data entry as data status is Posted for subject ' . $result_source_subject->subject->name);
        }

        if ($result_source_subject->isScoreGradingType()) {

            if (!isset($this->score)) {
                throw new \Exception('Score is required when grading_type is SCORE');
            }
            if (!isset($this->subjectComponentCode)) {
                throw new \Exception('Subject Component Code is required when grading_type is SCORE');
            }

            // update into result source subject components table if it's score, since we can break down score by components
            ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject->id)
                ->where('code', $this->subjectComponentCode)
                ->update([
                    'actual_score' => $this->score,
                    'data_entry_employee_id' => $this->employee->id,
                    'data_entry_at' => now('UTC'),
                ]);
        } else {
            if ($result_source_subject->isGradeGradingType()) {

                if (!isset($this->grade)) {
                    throw new \Exception('Grade is required when grading_type is GRADE');
                }

                // update into result source subject table if it's grade, since grade won't have components.
                ResultSourceSubject::where('id', $result_source_subject->id)->update([
                    'actual_score_grade' => $this->grade,
                    'data_entry_employee_id' => $this->employee->id,
                    'data_entry_at' => now('UTC'),
                ]);

            }
        }

        return $this;

    }

    public function reopenPostedEntriesByResultSourceSubject()
    {

        if (!isset($this->resultSourceSubject)) {
            throw new \Exception('Please specify a result source subject.');
        }

        $this->resultSourceSubject->update([
            'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
            'posted_at' => null,
            'actual_score' => null,     // this should be re-computed during posting from components
        ]);

    }

    public function getEligibleSubjectsForTeacher()
    {
        $class_subject_teachers = ClassSubjectTeacher::where('employee_id', $this->employee->id)->get();

        $semester_settings = app()->make(SemesterSettingService::class)->getCurrentSemesterSettings();
        $semester_setting_ids = $semester_settings->pluck('id')->toArray();

        $current_semester_classes = app()->make(SemesterClassRepository::class)->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
        ]);

        $class_subjects = ClassSubject::with(['subject'])->whereIn('id', $class_subject_teachers->pluck('class_subject_id'))
            ->whereIn('semester_class_id', $current_semester_classes->pluck('id'))
            ->get();

        return $class_subjects->pluck('subject')->unique()->values();
    }


    public function getEligibleClassesForTeacherAndSubject()
    {

        $class_subject_teachers = ClassSubjectTeacher::where('employee_id', $this->employee->id)->get();

        $semester_settings = app()->make(SemesterSettingService::class)->getCurrentSemesterSettings();
        $semester_setting_ids = $semester_settings->pluck('id')->toArray();

        $current_semester_classes = app()->make(SemesterClassRepository::class)->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
        ]);

        $class_subjects = ClassSubject::with(['semesterClass.classModel'])->whereIn('id', $class_subject_teachers->pluck('class_subject_id'))
            ->whereIn('semester_class_id', $current_semester_classes->pluck('id'))
            ->where('subject_id', $this->subject->id)
            ->get();

        return $class_subjects->pluck('semesterClass.classModel')->values();

    }


    public function getEligibleStudentsAndScores()
    {

        $class_subject_teachers = ClassSubjectTeacher::where('employee_id', $this->employee->id)->get();

        $semester_settings = app()->make(SemesterSettingService::class)->getCurrentSemesterSettings();
        $semester_setting_ids = $semester_settings->pluck('id')->toArray();

        $current_semester_classes = app()->make(SemesterClassRepository::class)->getAll([
            'is_active' => true,
            'semester_setting_id' => $semester_setting_ids,
            'class_id' => $this->class->id,
        ]);

        $class_subjects = ClassSubject::whereIn('id', $class_subject_teachers->pluck('class_subject_id'))
            ->whereIn('semester_class_id', $current_semester_classes->pluck('id'))
            ->where('subject_id', $this->subject->id)
            ->get();

        $class_subject_students = ClassSubjectStudent::where('class_subject_id', $class_subjects->pluck('id'))->get();

        $data = StudentGradingFramework::selectRaw('students.id AS student_id, students.name AS student_name, students.student_number AS student_number, result_source_subjects.id AS result_source_subject_id,
            result_source_subjects.grading_type, result_source_subjects.is_exempted, result_source_subjects.data_entry_status,
            result_source_subjects.actual_score_grade, result_source_subject_components.id AS component_id, result_source_subject_components.name AS component_name,
            result_source_subject_components.code AS component_code, result_source_subject_components.actual_score AS component_actual_score,
            result_source_subject_components.weightage_percent AS component_weightage_percent, student_grading_frameworks.id AS student_grading_framework_id, result_sources.id AS result_source_id')
            ->join('students', 'student_grading_frameworks.student_id', '=', 'students.id')
            ->join('result_sources', 'student_grading_frameworks.id', '=', 'result_sources.student_grading_framework_id')
            ->join('result_source_exams', 'result_sources.id', '=', 'result_source_exams.result_source_id')
            ->join('result_source_subjects', 'result_sources.id', '=', 'result_source_subjects.result_source_id')
            ->leftJoin('result_source_subject_components', 'result_source_subjects.id', '=', 'result_source_subject_components.result_source_subject_id')
            ->whereIn('student_grading_frameworks.student_id', $class_subject_students->pluck('student_id'))
            ->where('result_source_exams.exam_id', $this->exam->id)
            ->where('student_grading_frameworks.is_active', true)
            ->where('result_source_subjects.subject_id', $this->subject->id)
            ->orderBy(DB::raw('students.name->>\'en\''), 'ASC')
            ->orderBy(DB::raw('result_source_subject_components.name->>\'en\''), 'ASC')
            ->get();

        $final_data = [];

        foreach ($data as $d) {

            if ($d->component_id !== null) {
                $component = [
                    'component_id' => $d->component_id,
                    'component_name' => $d->component_name,
                    'component_code' => $d->component_code,
                    'component_actual_score' => $d->component_actual_score,
                    'component_weightage_percent' => $d->component_weightage_percent,
                ];
            } else {
                $component = [];
            }

            if (!isset($final_data[$d->result_source_subject_id])) {
                $append = $d->toArray();
                $append['components'] = [];

                if (count($component) > 0) {
                    $append['components'][] = $component;
                }

                unset($append['component_id']);
                unset($append['component_name']);
                unset($append['component_code']);
                unset($append['component_actual_score']);
                unset($append['component_weightage_percent']);

                $final_data[$d->result_source_subject_id] = $append;
            } else {
                $final_data[$d->result_source_subject_id]['components'][] = $component;
            }
        }

        foreach ($final_data as &$data) {
            $data['student_name'] = json_decode($data['student_name']);

            foreach ($data['components'] as &$component) {
                $component['component_name'] = json_decode($component['component_name']);
            }
        }

        return collect($final_data)->values();

    }


    public function getStudentGradingFramework()
    {

        // eager cache entire class as usually exam results is entered class by class
        if (!Cache::has('active-student-grading-framework-' . $this->student->id)) {
            $students_same_class = $this->student->currentSemesterPrimaryClass->semesterClass->students;

            $frameworks = StudentGradingFramework::whereIn('student_id', $students_same_class->pluck('id'))
                ->where('is_active', true)
                ->get();

            foreach ($frameworks as $framework) {
                Cache::put('active-student-grading-framework-' . $framework->student_id, $framework, 3600);
            }
        }

        return Cache::remember('active-student-grading-framework-' . $this->student->id, 3600, function () {
            return StudentGradingFramework::where('student_id', $this->student->id)
                ->where('is_active', true)
                ->firstOrFail();
        });

    }

    public function getResultSource()
    {

        $student_grading_framework = $this->getStudentGradingFramework();

        return Cache::remember('result-sources-for-sgf-' . $student_grading_framework->id . '-exam-' . $this->exam->id, 3600, function () use (&$student_grading_framework) {
            return ResultSource::where('student_grading_framework_id', $student_grading_framework->id)
                ->whereHas('exams', function ($query) {
                    return $query->where('exams.id', $this->exam->id);
                })
                ->first();
        });

    }

    public function determineResultSourceSubject(ResultSource $result_source)
    {
        return Cache::remember('result-source-subject-' . $result_source->id . '-subject-' . $this->subject->id, 3600, function () use (&$result_source) {
            return ResultSourceSubject::where('result_source_id', $result_source->id)
                ->where('subject_id', $this->subject->id)
                ->firstOrFail();
        });
    }

    public function getEligibleExamsForDataEntry()
    {
        return app()->make(ExamRepository::class)->getAll(['results_entry_period_open' => true]);
    }

    public function getSubject(): Subject
    {
        return $this->subject;
    }

    public function setSubject(Subject $subject): ExamResultsDataEntryService
    {
        $this->subject = $subject;
        return $this;
    }

    public function getStudent(): Student
    {
        return $this->student;
    }

    public function setStudent(Student $student): ExamResultsDataEntryService
    {
        $this->student = $student;
        return $this;
    }

    public function getExam(): Exam
    {
        return $this->exam;
    }

    public function setExam(Exam $exam): ExamResultsDataEntryService
    {
        $this->exam = $exam;
        return $this;
    }

    public function getEmployee(): Employee
    {
        return $this->employee;
    }

    public function setEmployee(Employee $employee): ExamResultsDataEntryService
    {
        $this->employee = $employee;
        return $this;
    }

    public function getSubjectComponentCode(): string
    {
        return $this->subjectComponentCode;
    }

    public function setSubjectComponentCode(string $subjectComponentCode): ExamResultsDataEntryService
    {
        $this->subjectComponentCode = $subjectComponentCode;
        return $this;
    }

    public function getScore(): float
    {
        return $this->score;
    }

    public function setScore(?float $score): ExamResultsDataEntryService
    {
        $this->score = $score;
        return $this;
    }

    public function getGrade(): string
    {
        return $this->grade;
    }

    public function setGrade(?string $grade): ExamResultsDataEntryService
    {
        $this->grade = $grade;
        return $this;
    }

    public function getClass(): ClassModel
    {
        return $this->class;
    }

    public function setClass(ClassModel $class): ExamResultsDataEntryService
    {
        $this->class = $class;
        return $this;
    }

    public function getResultSourceSubjectComponent(): ResultSourceSubjectComponent
    {
        return $this->resultSourceSubjectComponent;
    }

    public function setResultSourceSubjectComponent(ResultSourceSubjectComponent $resultSourceSubjectComponent): ExamResultsDataEntryService
    {
        $this->resultSourceSubjectComponent = $resultSourceSubjectComponent;
        return $this;
    }

    public function getResultSourceSubject(): ResultSourceSubject
    {
        return $this->resultSourceSubject;
    }

    public function setResultSourceSubject(ResultSourceSubject $resultSourceSubject): ExamResultsDataEntryService
    {
        $this->resultSourceSubject = $resultSourceSubject;
        return $this;
    }


}
