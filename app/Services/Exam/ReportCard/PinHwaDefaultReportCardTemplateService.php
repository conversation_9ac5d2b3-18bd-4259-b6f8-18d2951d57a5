<?php

namespace App\Services\Exam\ReportCard;

use App\Adapters\PdfExportAdapter;
use App\Interfaces\IReportCardTemplate;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentReportCard;
use App\Repositories\StudentReportCardRepository;
use Carbon\Carbon;

class PinHwaDefaultReportCardTemplateService implements IReportCardTemplate
{
    protected Student $student;
    protected ResultsPostingHeader $resultsPostingHeader;

    // const TEMPLATE_NAME = 'pinhwa-default';
    const TEMPLATE_NAME = 'pinhwa-template-test';

    public function __construct(protected ReportCardPrintService $reportCardPrintService)
    {
    }

    public function getData()
    {
        // get all semesters of the year from postingHeader
        $semester_setting = $this->resultsPostingHeader->semesterSetting;

        $semesters = SemesterSetting::where('semester_year_setting_id', $semester_setting->semester_year_setting_id)
            ->where('course_id', $semester_setting->course_id)
            ->get();

        // retrieve all results for the student across all semesters
        // since pinhwa is showing all semester results in 1 pdf file.
        $data = ResultsPostingLineItem::query()
            ->selectRaw('results_posting_headers.code AS results_posting_header_code, results_posting_headers.report_card_output_code,
            results_posting_line_items.report_card_output_component_code, results_posting_line_items.grade_id, results_posting_line_items.semester_class_id,
            subjects.code AS subject_code, subjects.sequence AS subject_sequence, subjects.name->>\'en\' AS subject_name_en, subjects.name->>\'zh\' AS subject_name_zh, results_posting_line_items.total,
            results_posting_line_items.total_grade, results_posting_line_items.label, results_posting_line_items.weightage_multiplier, results_posting_line_items.weightage_total,
            results_posting_line_items.grade_percentile_rank, results_posting_line_items.grade_rank, results_posting_line_items.grade_population,
            results_posting_line_items.class_rank, results_posting_line_items.class_population, results_posting_line_items.output_type')
            ->join('results_posting_headers', 'results_posting_headers.id', '=', 'results_posting_line_items.header_id')
            ->leftJoin('subjects', 'subjects.id', '=', 'results_posting_line_items.subject_id')
            ->where('student_id', $this->student->id)
            ->whereIn('semester_setting_id', $semesters->pluck('id')->toArray())
            ->whereIn('status', [ResultsPostingHeader::STATUS_COMPLETED, ResultsPostingHeader::STATUS_IN_PROGRESS])
            ->get();

        // group results by report_card_output_code (different exam results), and then by report_card_output_component_code (each line in each exam)
        $final = [];
        $subjects_list = [];

        foreach ($data as $d) {
            if (!isset($final[$d->report_card_output_code])) {
                $final[$d->report_card_output_code] = [];
            }
            if (!isset($final[$d->report_card_output_code][$d->report_card_output_component_code])) {
                $final[$d->report_card_output_code][$d->report_card_output_component_code] = [];
            }

            $final[$d->report_card_output_code][$d->report_card_output_component_code] = $d;
            if ($d->subject_code !== null && !isset($subjects_list[$d->subject_code])) {
                $subjects_list[$d->subject_code] = [
                    'sequence' => $d->subject_sequence,
                    'weightage_multiplier' => $d->weightage_multiplier ?? null,
                    'output_type' => $this->getOutputType($d->output_type),
                    'code' => $d->subject_code,
                    'en' => $d->subject_name_en,
                    'zh' => $d->subject_name_zh,
                ];
            }

        }
        return [
            'data' => $final,
            'subjects' => collect($subjects_list)->values()->sortBy([
                ['sequence', 'desc'],
                ['weightage_multiplier', 'desc'],
                ['code', 'asc'],
            ])->values(),
        ];

    }

    public function getFileName(): string
    {
        return 'report-card-' . $this->resultsPostingHeader->code . '-' . $this->student->student_number;
    }

    public function generate()
    {

        // todo: replace this with mat view to get final student class in each semester when ready
        $student_class = $this->student->getPrimaryClassOfSemester($this->resultsPostingHeader->semester_setting_id);

        if ($student_class === null) {
            throw new \Exception('Student class for semester ' . $this->resultsPostingHeader->semesterSetting->name . ' not found.');
        }
        if ($student_class->semesterClass === null || $student_class->semesterClass->classModel === null) {
            throw new \Exception('Unable to resolve student class');
        }

        $class = $student_class->semesterClass->classModel;
        $final = $this->getData();
        // student->student_grading_framework->report_card_output->report_card_output_components->output_type

        // generate PDF
        $adapter = new PdfExportAdapter();
        $adapter
            ->setReportViewName('pdf.report-cards.' . self::TEMPLATE_NAME)
            ->setReportData([
                'posting_header' => $this->resultsPostingHeader,
                'student' => $this->student,
                'class' => $class,
                'data' => $final['data'],
                'subjects' => $final['subjects'],
                //  'output_type' => static::LINE_ITEM_OUTPUT_TYPE,
            ]);

        $this->reportCardPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate();

        $url = $this->reportCardPrintService
            ->upload()
            ->getFileUrl();

        $this->expireExistingReportCards();

        return app()->make(StudentReportCardRepository::class)->create([
            'student_id' => $this->student->id,
            'results_posting_header_id' => $this->resultsPostingHeader->id,
            'grade_id' => $this->resultsPostingHeader->grade_id,
            'semester_setting_id' => $this->resultsPostingHeader->semester_setting_id,
            'semester_class_id' => $student_class->semester_class_id,
            'file_url' => $url,
            'file_generated_at' => now(),
            'is_visible_to_student' => $this->resultsPostingHeader->publish_date == Carbon::now()->tz(config('school.timezone'))->toDateString(),
            'is_active' => true,
        ]);

    }

    public function getStudent(): Student
    {
        return $this->student;
    }

    public function setStudent(Student $student): self
    {
        $this->student = $student;
        return $this;
    }

    public function getResultsPostingHeader(): ResultsPostingHeader
    {
        return $this->resultsPostingHeader;
    }

    public function setResultsPostingHeader(ResultsPostingHeader $resultsPostingHeader): self
    {
        $this->resultsPostingHeader = $resultsPostingHeader;
        return $this;
    }

    public function getOutputType(string $output_type)
    {
        switch ($output_type) {
            case ReportCardOutputComponent::OUTPUT_TYPE_SCORE:
                return 'total';
            case ReportCardOutputComponent::OUTPUT_TYPE_GRADE:
                return 'label';
        }
    }

    public function expireExistingReportCards()
    {
        StudentReportCard::where([
            'student_id' => $this->getStudent()->id,
            'is_active' => true
        ])
            ->whereRelation('resultsPostingHeader', 'report_card_output_code', $this->getResultsPostingHeader()->report_card_output_code)
            ->update(['is_active' => false]);
    }

}
