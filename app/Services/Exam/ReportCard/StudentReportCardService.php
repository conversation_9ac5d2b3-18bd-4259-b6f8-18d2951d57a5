<?php

namespace App\Services\Exam\ReportCard;

use App\Repositories\ResultsPostingHeaderRepository;
use App\Repositories\StudentReportCardRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class StudentReportCardService
{
    private array $data = [];

    public function __construct(
        protected StudentReportCardRepository $studentReportCardRepository,
        protected ResultsPostingHeaderRepository $resultsPostingHeaderRepository
    ) {
    }

    public function getAllReportCard($filters = []): Collection
    {
        return $this->studentReportCardRepository->getAll($filters);
    }

    public function getAllPaginatedReportCards($filters = []): LengthAwarePaginator
    {
        return $this->studentReportCardRepository->getAllPaginated($filters);
    }
}
