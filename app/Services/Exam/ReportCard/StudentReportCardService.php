<?php

namespace App\Services\Exam\ReportCard;

use App\Repositories\ResultsPostingHeaderRepository;
use App\Repositories\StudentReportCardRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;

class StudentReportCardService
{
    private array $data = [];

    public function __construct(
        protected StudentReportCardRepository $studentReportCardRepository,
        protected ResultsPostingHeaderRepository $resultsPostingHeaderRepository
    ) {
    }

    public function getAllReportCard($filters = []): Collection
    {
        return $this->studentReportCardRepository->getAll($filters);
    }

    public function getAllPaginatedReportCards($filters = []): LengthAwarePaginator
    {
        return $this->studentReportCardRepository->getAllPaginated($filters);
    }

    public function mergeReportCards()
    {

        $report_cards = $this->studentReportCardRepository->getAll([
            'grade_id' => 1,
            'results_posting_header' => 1
        ]);
       $merger = PDFMerger::init();

       $start = hrtime(true);
       $count = 0;
        foreach ($report_cards as $report_card){
            print_r(++$count."\n");
           // print_r($report_card->file_url);
           $merger->addString(file_get_contents($report_card->file_url) , 'all');
        }

        $merger->merge();
        $merger->save('test_merge.pdf');
        $end = hrtime(true); 

        echo "Process Time (seconds):".($end - $start) / 1000000000;
    }
}
