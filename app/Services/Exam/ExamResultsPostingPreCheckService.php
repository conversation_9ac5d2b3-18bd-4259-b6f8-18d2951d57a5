<?php

namespace App\Services\Exam;

use App\Exceptions\ExamResultsPostingException;
use App\Interfaces\IReportCardOutput;
use App\Models\ClassModel;
use App\Models\ExamPostingPreCheck;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Repositories\ReportCardOutputRepository;
use App\Repositories\ResultsPostingHeaderRepository;
use App\Repositories\ResultsPostingLineItemRepository;
use App\Repositories\StudentReportCardRepository;
use App\Repositories\StudentRepository;

class ExamResultsPostingPreCheckService
{

    protected int $semesterSettingId;
    protected int $drillDownLevel;
    protected ?string $resultSourceCode;
    protected ?int $classId;
    protected ?int $subjectId;
    protected ?int $studentId;

    public function __construct()
    {

    }

    public function getData() {
        switch ($this->drillDownLevel) {
            case 0:
                return $this->getDrillDownLevelZeroData();
            case 1:
                return $this->getDrillDownLevelOneData();
            case 2:
                return $this->getDrillDownLevelTwoData();
            default:
                throw new \Exception('Unsupported drill down level.');
        }
    }

    public function getDrillDownLevelZeroData() {

        $query = ExamPostingPreCheck::query();

        $query->where('semester_setting_id', $this->semesterSettingId);

        if ( isset($this->studentId) ) {
            $query->where('student_id', $this->studentId);
        }
        if ( isset($this->classId) ) {
            $query->where('class_id', $this->classId);
        }
        if ( isset($this->subjectId) ) {
            $query->where('subject_id', $this->subjectId);
        }
        if ( isset($this->resultSourceCode) ) {
            $query->where('result_source_code', $this->resultSourceCode);
        }

        $query->selectRaw('result_source_code, class_type, class_name, class_id, COUNT(*) FILTER (WHERE input_value_status = 1) AS completed_rows, COUNT(*) FILTER (WHERE input_value_status = 0) AS incomplete_rows, COUNT(*) AS total_rows, round((COUNT(*) FILTER (WHERE input_value_status = 1) / COUNT(*)::float * 100)::numeric, 2) AS completed_percentage')
            ->groupBy('result_source_code', 'class_type', 'class_name', 'class_id')
            ->orderBy('result_source_code', 'ASC')
            ->orderBy('class_type', 'ASC')
            ->orderBy(\DB::raw('class_name->>\'en\''), 'ASC');

        return $query->get();

    }


    public function getDrillDownLevelOneData() {

        $query = ExamPostingPreCheck::query();

        $query->where('semester_setting_id', $this->semesterSettingId);

        if ( isset($this->studentId) ) {
            $query->where('student_id', $this->studentId);
        }
        if ( isset($this->classId) ) {
            $query->where('class_id', $this->classId);
        }
        if ( isset($this->subjectId) ) {
            $query->where('subject_id', $this->subjectId);
        }
        if ( isset($this->resultSourceCode) ) {
            $query->where('result_source_code', $this->resultSourceCode);
        }

        $query->selectRaw('result_source_code, class_name, class_id, subject_name, subject_id, teacher_names, COUNT(*) FILTER (WHERE input_value_status = 1) AS completed_rows, COUNT(*) FILTER (WHERE input_value_status = 0) AS incomplete_rows, COUNT(*) AS total_rows, round((COUNT(*) FILTER (WHERE input_value_status = 1) / COUNT(*)::float * 100)::numeric, 2) AS completed_percentage')
            ->groupBy('result_source_code', 'class_name', 'class_id', 'subject_name', 'subject_id', 'teacher_names')
            ->orderBy('result_source_code', 'ASC')
            ->orderBy(\DB::raw('class_name->>\'en\''), 'ASC')
            ->orderBy(\DB::raw('subject_name->>\'en\''), 'ASC');

        return $query->get();

    }


    public function getDrillDownLevelTwoData() {

        $query = ExamPostingPreCheck::query();

        $query->where('semester_setting_id', $this->semesterSettingId);

        if ( isset($this->studentId) ) {
            $query->where('student_id', $this->studentId);
        }
        if ( isset($this->classId) ) {
            $query->where('class_id', $this->classId);
        }
        if ( isset($this->subjectId) ) {
            $query->where('subject_id', $this->subjectId);
        }
        if ( isset($this->resultSourceCode) ) {
            $query->where('result_source_code', $this->resultSourceCode);
        }

        $query->selectRaw('subject_name, subject_id, class_name, class_id, student_name, student_number, student_id, grading_type, result_source_subject_component_name, input_value, input_value_status')
            ->orderBy('student_number', 'ASC')
            ->orderBy(\DB::raw('result_source_subject_component_name->>\'en\''), 'ASC');

        return $query->get();

    }


    public function getDrillDownLevel(): int
    {
        return $this->drillDownLevel;
    }

    public function setDrillDownLevel(int $drillDownLevel): ExamResultsPostingPreCheckService
    {
        $this->drillDownLevel = $drillDownLevel;
        return $this;
    }

    public function getResultSourceCode(): string
    {
        return $this->resultSourceCode;
    }

    public function setResultSourceCode(?string $resultSourceCode): ExamResultsPostingPreCheckService
    {
        $this->resultSourceCode = $resultSourceCode;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getClassId()
    {
        return $this->classId;
    }

    /**
     * @param mixed $classId
     * @return ExamResultsPostingPreCheckService
     */
    public function setClassId($classId)
    {
        $this->classId = $classId;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSubjectId()
    {
        return $this->subjectId;
    }

    /**
     * @param mixed $subjectId
     * @return ExamResultsPostingPreCheckService
     */
    public function setSubjectId($subjectId)
    {
        $this->subjectId = $subjectId;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getStudentId()
    {
        return $this->studentId;
    }

    /**
     * @param mixed $studentId
     * @return ExamResultsPostingPreCheckService
     */
    public function setStudentId($studentId)
    {
        $this->studentId = $studentId;
        return $this;
    }

    public function getSemesterSettingId(): int
    {
        return $this->semesterSettingId;
    }

    public function setSemesterSettingId(int $semesterSettingId): ExamResultsPostingPreCheckService
    {
        $this->semesterSettingId = $semesterSettingId;
        return $this;
    }


}
