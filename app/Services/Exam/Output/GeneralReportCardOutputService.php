<?php

namespace App\Services\Exam\Output;

use App\Exceptions\BadExpressionException;
use App\Exceptions\ReportCardOutputException;
use App\Helpers\ExpressionHelper;
use App\Interfaces\IReportCardOutput;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\GradingScheme;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Services\GradingSchemeService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class GeneralReportCardOutputService implements IReportCardOutput
{
    const TARGET_KEYWORDS = [
        'SCORE' => 'actual_score',
        'GRADE' => 'actual_score_grade',
        'WEIGHT' => 'weightage_multiplier',
        'WEIGHTEDSCORE' => 'weighted_score',
    ];
    const OUTPUT_TARGET_KEYWORDS = [
        'TOTAL' => 'total',
        'LABEL' => 'label',
        'TOTAL_GRADE' => 'total_grade',
    ];
    const TARGET_RESOLVER = [
        'RESULTSOURCE.SUBJECT' => 'resolveResultSourceSubject',
        'RESULTSOURCE.SUBJECT.COMPONENT' => 'resolveResultSourceSubjectComponent',
        'OUTPUT.COMPONENT' => 'resolveReportCardOutputComponent',
    ];


    protected ExpressionHelper $evaluator;
    protected StudentGradingFramework $studentGradingFramework;
    protected ResultsPostingHeader $resultsPostingHeader;
    protected ReportCardOutputComponent $reportCardOutputComponent;
    protected GradingSchemeService $gradingSchemeService;
    protected bool $isValidation;
    protected Collection $valid_subjects;

    protected array $outputValues = [];

    public function __construct()
    {
        $this->evaluator = new ExpressionHelper();
        $this->evaluator->functions = [
            'sum' => ['ref' => self::class . '::sum', 'arc' => null],
            'min' => ['ref' => 'min', 'arc' => null],
            'max' => ['ref' => 'max', 'arc' => null],
            'round' => ['ref' => 'round', 'arc' => 2],
            'var' => ['ref' => [$this, 'resolveVariable'], 'arc' => 1],
            'randomnumber' => ['ref' => [$this, 'testGetRandomNumber'], 'arc' => 0],
            'customfunctionnoargs' => ['ref' => [$this, 'testCustomFunctionWithoutArgs'], 'arc' => 0],
            'customfunction' =>  ['ref' => [$this, 'testCustomFunction'], 'arc' => null],
            'customfunctiontwoargs' => ['ref' => [$this, 'testCustomFunctionTwoArgs'], 'arc' => 2],
            'customfunctionusemetadata' => ['ref' => [$this, 'testCustomFunctionUseMetadata'], 'arc' => 0],
            'zero' => ['ref' => [$this, 'zero'], 'arc' => 0]
        ];
        $this->evaluator->onFunction = [$this, 'customFunctionRouter'];
        $this->isValidation = false;
        $this->gradingSchemeService = app()->make(GradingSchemeService::class);
        $this->valid_subjects = new Collection([]);
    }


    /**
     * arc = argument count. Use 0 if no argument required, use null for unlimited arguments, Use any position number to indicate a fixed number of params.
     */
    public function customFunctionRouter($function_name, &$value) {
        switch($function_name) {
            case 'customfunction':
                $value = ['ref' => [$this, 'testCustomFunction'], 'arc' => null];
                break;
            case 'customfunctiontwoargs':
                $value = ['ref' => [$this, 'testCustomFunctionTwoArgs'], 'arc' => 2];
                break;
            case 'customfunctionusemetadata':
                $value = ['ref' => [$this, 'testCustomFunctionUseMetadata'], 'arc' => 0];
                break;
            case 'zero':
                $value = ['ref' => [$this, 'zero'], 'arc' => 0];
                break;
            default:
                throw new BadExpressionException("Unknown custom function '$function_name'");
        }
    }

    public function evaluate($expression) {
        $value = $this->evaluator->execute($expression);

        if ($value === null){
            return null;
        }

        if ( is_numeric($value) ) {
            return round($value, 4);
        }
        else{
            return (string) $value;
        }
    }

    public function validate($expression) {
        $this->setIsValidation(true);

        try{
            $this->evaluate($expression);
            return true;
        }catch(\Throwable $e) {
            return $e->getMessage();
        }
    }

    public function evaluateAllComponentsForOutput(ReportCardOutput $output, $priority = null) {

        // then we go through each output component 1 by 1 to evaluate, starting from lower priority first
        $components = $output->components->sortBy('resolve_priority');

        if ( $priority !== null ) {
            $components = $components->where('resolve_priority', $priority)->values();
        }

        foreach ( $components as $component ) {
            $this->outputValues[$component->reportCardOutput->code][$component->code] = $this->evaluateOutputComponent($component);
        }
        return $this;

    }

    /**
     * Not required anymore, since we compiling the result sources via output
     * @param ResultSource $result_source
     * @return $this
     */
    public function compileResultSourceScores(ResultSource $result_source) {

        $result_source->loadMissing(['subjects.components', 'subjects.subject']);

        foreach ( $result_source->subjects as $result_source_subject ) {

            if ( $result_source_subject->isGradeGradingType() ){
                continue;
            }

            $subject_score = 0;

            foreach ( $result_source_subject->components as $component ) {
                $component_score = bcmul($component->actual_score, ($component->weightage_percent / 100), 2);
                $subject_score = bcadd($subject_score, $component_score, 2);
            }

            $result_source_subject->actual_score = $subject_score;
            $result_source_subject->save();

        }

        return $this;

    }

    public function compileResultSourceScoresUsedInOutput(ReportCardOutput $output) {

        $output->loadMissing(['components']);
        $result_source_subjects_to_be_compiled = $this->getResultSourceSubjectsFromOutput($output);

        foreach ( $result_source_subjects_to_be_compiled as $result_source_code => $subject_codes ) {

            $result_source = $this->getResultSourceFromCode($result_source_code);

            foreach ( $subject_codes as $subject_code ) {
                $subject = $this->getSubjectFromCode($subject_code);
                $result_source_subject = ResultSourceSubject::where('result_source_id', $result_source->id)->where('subject_id', $subject->id)->first();

                if ( $result_source_subject === null ) {
                    throw new BadExpressionException('Unable to resolve ResultSourceSubject (' . $result_source_code . ', ' . $subject_code . ')');
                }

                if ( $result_source_subject->isGradeGradingType() ){
                    continue;
                }

                $subject_score = 0;

                foreach ( $result_source_subject->components as $component ) {
                    $component_score = bcmul($component->actual_score, ($component->weightage_percent / 100), 2);
                    $subject_score = bcadd($subject_score, $component_score, 2);
                }

                $result_source_subject->actual_score = $subject_score;
                $result_source_subject->save();

            }

        }

        return $this;

    }

    public function validateResultSourceBeforeCompilation(ResultSource $result_source) {

        $result_source->loadMissing(['subjects.components', 'subjects.subject']);
        $errors = [];

        foreach ( $result_source->subjects as $result_source_subject ) {
            $this->validateResultSourceSubjectDataEntered($result_source, $result_source_subject, $errors);
        }

        if ( !empty($errors) ) {
            throw new ReportCardOutputException(join(', ', $errors));
        }

        return $this;
    }


    public function validateResultSourcesInOutputBeforeCompilation(ReportCardOutput $output, $throw_error_as_exception = true) {

        $result_source_subjects_to_be_validated = $this->getResultSourceSubjectsFromOutput($output);
        $errors = [];

        foreach ( $result_source_subjects_to_be_validated as $result_source_code => $subject_codes ) {
            $result_source = $this->getResultSourceFromCode($result_source_code);
            foreach ( $subject_codes as $subject_code ) {
                $subject = $this->getSubjectFromCode($subject_code);
                $result_source_subject = ResultSourceSubject::where('result_source_id', $result_source->id)->where('subject_id', $subject->id)->first();
                if ( $result_source_subject === null ) {
                    throw new BadExpressionException('Unable to resolve ResultSourceSubject (' . $result_source_code . ', ' . $subject_code . ')');
                }

                $this->validateResultSourceSubjectDataEntered($result_source, $result_source_subject, $errors);

            }

        }

        if ( $throw_error_as_exception && !empty($errors) ) {
            throw new ReportCardOutputException(join(', ', $errors));
        }

        return $errors;
    }


    public function validateResultSourceSubjectDataEntered(ResultSource $result_source, ResultSourceSubject $result_source_subject, &$errors = []) {

        if ( $result_source_subject->isGradeGradingType() ) {
            if ( $result_source_subject->actual_score_grade === null ) {
                $errors[] = 'No grade entered for ' . $result_source->code . ' > ' . $result_source_subject->subject->name;
            }
        }
        else{
            foreach ( $result_source_subject->components as $component  ) {
                if ( $component->actual_score === null && !$result_source_subject->is_exempted ){
                    $errors[] = 'No score entered for ' . $result_source->code . ' > ' . $result_source_subject->subject->name . ' > ' . $component->code;
                }
            }
        }

        return $this;

    }

    public function getResultSourceSubjectsFromOutput(ReportCardOutput $output) {

        $result_source_subjects_to_be_validated = [];

        foreach ( $output->components as $output_component ) {
            // evaluate total_formula and label_formula
            $rs_matches = [];
            $rs_subject_matches = [];

            if ( preg_match("/RESULTSOURCE\[(.+)\].+/U", $output_component['total_formula'], $rs_matches) ) {
                if ( !isset($result_source_subjects_to_be_validated[$rs_matches[1]]) ) {
                    $result_source_subjects_to_be_validated[$rs_matches[1]] = [];
                }

                if ( preg_match("/\.SUBJECT\[(.+)\].+/U", $output_component['total_formula'], $rs_subject_matches) ) {
                    $result_source_subjects_to_be_validated[$rs_matches[1]][] = $rs_subject_matches[1];
                }
            }

            if ( preg_match("/RESULTSOURCE\[(.+)\].+/U", $output_component['label_formula'], $rs_matches) ) {
                if ( !isset($result_source_subjects_to_be_validated[$rs_matches[1]]) ) {
                    $result_source_subjects_to_be_validated[$rs_matches[1]] = [];
                }

                if ( preg_match("/\.SUBJECT\[(.+)\].+/U", $output_component['label_formula'], $rs_subject_matches) ) {
                    $result_source_subjects_to_be_validated[$rs_matches[1]][] = $rs_subject_matches[1];
                }
            }

        }
        // remove duplicates
        foreach ( $result_source_subjects_to_be_validated as $result_source_code => $list ) {
            $result_source_subjects_to_be_validated[$result_source_code] = collect($list)->unique()->sort()->values();
        }

        return $result_source_subjects_to_be_validated;

    }

    public function evaluateOutputComponent(ReportCardOutputComponent $component) {

        $this->setReportCardOutputComponent($component);

        $output = [
            'total' => null,
            'total_grade' => null,
            'label' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => null,       // only if available
            'grading_scheme_id' => null,    // only if available
            'output_type' => $component->output_type,
            'report_card_output_component' => $component,
            'calculate_rank' => $component->calculate_rank
        ];

        if ( $component->total_formula !== null ) {
            $output['total'] = $this->evaluate($component->total_formula);

            if ( $component->grading_scheme_id !== null ) {

                // apply grading scheme
                $output['total_grade'] = $this->gradingSchemeService
                    ->setGradingScheme($component->gradingScheme)
                    ->applyAndGetGrade($output['total']);

                $output['label'] = $output['total_grade']['display_as_name'];
            }
        }
        if ( $component->label_formula !== null ) {
            $output['label'] = $this->evaluate($component->label_formula);
        }
        if ( $component->subject_id !== null ) {
            $output['subject_id'] = $component->subject_id;
            $output['calculate_rank'] = $this->resolveCalculateRankForSubjects($component);
        }
        if ( $component->grading_scheme_id !== null ) {
            $output['grading_scheme_id'] = $component->grading_scheme_id;
        }
        if ( $component->weightage_multiplier !== null && $output['total'] > 0 ) {
            $output['weightage_multiplier'] = $this->resolveWeightMultiplier($component);
            $output['weightage_total'] = bcmul($output['total'], $output['weightage_multiplier'], 2);
        }
        return $output;

    }

    public static function sum(...$args) {
        return array_sum($args);
    }

    public function resolveVariable($expression) {

        $parts = explode('.', $expression);
        $components = [];
        $class_chain = [];
        $args_chain = [];

        $keywords_mapping = self::TARGET_KEYWORDS;

        if ( preg_match("/^OUTPUT/i", $expression) ) {
            $keywords_mapping = self::OUTPUT_TARGET_KEYWORDS;
        }

        foreach ( $parts as $part ) {

            $part = trim($part);

            if ( preg_match("/^([A-Z0-_]+)\[(.+)\]$/", $part, $matches) ) {

                $class = trim($matches[1]);
                $code = trim($matches[2]);

                $class_chain[] = $class;
                $class_raw = join('.', $class_chain);

                $args_chain[] = $code;

                $components[] = [
                    'raw' => $part,
                    'type' => 'lookup',
                    'class_raw' => $class_raw,
                    'resolver' => self::TARGET_RESOLVER[$class_raw] ?? null,
                    'args' => $args_chain,
                ];

            }else if ( isset($keywords_mapping[strtoupper($part)]) ) {

                $args_chain[] = $keywords_mapping[strtoupper($part)];

                $components[] = [
                    'raw' => $part,
                    'type' => 'target',
                    'resolver' => self::TARGET_RESOLVER[$class_raw] ?? null,
                    'args' => $args_chain,
                    'target' => $keywords_mapping[strtoupper($part)],
                ];

            }else{
                throw new BadExpressionException('Unable to parse expression "' . $expression . '" part "' . $part . '"');
            }

        }

        $target_component = collect($components)->where('type', 'target')->first();

        if ( $target_component === null ) {
            throw new BadExpressionException('Expression ' . $expression . ' does not contain Target element.');
        }

        return call_user_func_array([$this, $target_component['resolver']], $target_component['args']);

    }

    public function resolveResultSourceSubject($result_source_code, $subject_code, $target) {

        if ( $this->isValidation ) {
            return 1;
        }

        $result_source = $this->getResultSourceFromCode($result_source_code);

        $subject = $this->getSubjectFromCode($subject_code);

        $result_source_subject = ResultSourceSubject::where('result_source_id', $result_source->id)->where('subject_id', $subject->id)->first();

        if ( $result_source_subject === null ) {
            throw new BadExpressionException('Unable to resolve ResultSourceSubject (' . $result_source_code . ', ' . $subject_code . ', ' . $target . ')');
        }

        if ( $result_source_subject->$target === null ) {
            return null;
        }
        if ( is_numeric($result_source_subject->$target) ){
            return (float) $result_source_subject->$target;
        }
        else{
            return $result_source_subject->$target;
        }
    }

    public function resolveWeightMultiplier(ReportCardOutputComponent $component ){
        preg_match("/RESULTSOURCE\[(.+)\].+/U", $component->total_formula, $rs_matches);

        $result_source = $this->getResultSourceFromCode($rs_matches[1]);
        $subject = $this->getSubjectFromCode($component->code);
        $result_source_subject =  ResultSourceSubject::where('result_source_id', $result_source->id)
            ->where('subject_id', $subject->id)
            ->first();

        if (!is_null($result_source_subject) && $result_source_subject->is_exempted){
            return 0;
        } 
        else {
            return $component->weightage_multiplier;
        }
    }

    public function resolveCalculateRankForSubjects(ReportCardOutputComponent $component): bool{

        if (isset($component->total_formula)){
            preg_match("/RESULTSOURCE\[(.+)\].+/U", $component->total_formula, $rs_matches);
            $result_source = $this->getResultSourceFromCode($rs_matches[1]);
            $subject = $this->getSubjectFromCode($component->code);
            $result_source_subject =  ResultSourceSubject::where('result_source_id', $result_source->id)
                ->where('subject_id', $subject->id)
                ->first();
                
            if (!is_null($result_source_subject) && $result_source_subject->is_exempted){
                return false;
            }
        }

        return $component->calculate_rank;
    }

    public function resolveResultSourceSubjectComponent($result_source_code, $subject_code, $component_code, $target) {

        if ( $this->isValidation ) {
            return 1;
        }

        $result_source = $this->getResultSourceFromCode($result_source_code);

        $subject = $this->getSubjectFromCode($subject_code);

        $result_source_subject = Cache::remember('sgf-' . $this->studentGradingFramework->id . '-result-source-subject-' . $result_source_code . '-' . $subject_code, 60, function () use (&$result_source, &$subject) {
            return ResultSourceSubject::where('result_source_id', $result_source->id)->where('subject_id', $subject->id)->first();
        });

        $data = ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject->id)->where('code', $component_code)->first();

        if ( $data === null ) {
            throw new BadExpressionException('Unable to resolve ResultSourceSubjectComponent (' . $result_source_code . ', ' . $subject_code . ', ' . $component_code . ', ' . $target . ')');
        }

        if ( $data->$target === null ) {
            return null;
        }
        else if ( is_numeric($data->$target) ){
            return (float) $data->$target;
        }
        else{
            return $data->$target;
        }
    }

    public function resolveReportCardOutputComponent($output_code, $component_code, $target) {

        if ( $this->isValidation ) {
            return 1;
        }

        $value = null;

        // values determined at runtime will be kept in $this->outputValues during posting.
        if ( isset($this->outputValues[$output_code]) ) {
            if ( isset($this->outputValues[$output_code][$component_code]) ) {
                $value = $this->outputValues[$output_code][$component_code][$target] ?? null;
            }
        }

        if ( $value === null ) {

            $line_item = ResultsPostingLineItem::query()
                ->whereRelation('reportCardOutputComponent', 'code', $component_code,)
                ->whereRelation('reportCardOutputComponent.reportCardOutput', 'code', $output_code)
                ->whereRelation(
                    'reportCardOutputComponent.reportCardOutput', 'student_grading_framework_id', $this->studentGradingFramework->id)
                ->first();

            if ($line_item === null){
                return null;
            }

            $value = $line_item->getValue($target);
            if (is_numeric($value)){
                return (float) $value;
            }
            return $value;
        }
        else if ( is_numeric($value) ){
            return (float) $value;
        }
        else{
            return $value;
        }
    }

    public function getResultSourceFromCode($result_source_code) {
        return Cache::remember('sgf-' . $this->studentGradingFramework->id . '-result-source-' . $result_source_code, 60, function () use (&$result_source_code) {
            return ResultSource::where('student_grading_framework_id', $this->studentGradingFramework->id)->where('code', $result_source_code)->first();
        });
    }

    public function getSubjectFromCode($subject_code) {
        return Cache::remember('subject-' . $subject_code, 60, function () use (&$subject_code) {
            return Subject::where('code', $subject_code)->first();
        });
    }

    public function getStudentGradingFramework(): StudentGradingFramework
    {
        return $this->studentGradingFramework;
    }

    public function setStudentGradingFramework(StudentGradingFramework $studentGradingFramework): self
    {
        $this->studentGradingFramework = $studentGradingFramework;
        return $this;
    }

    public function testCustomFunction(...$args) {
        return 'custom-function-output-' . join('-', $args);
    }
    public function testCustomFunctionWithoutArgs() {
        return 'custom-function-output-no-args';
    }

    public function testCustomFunctionTwoArgs($a, $b) {
        return $a + $b;
    }

    public function testGetRandomNumber() {
        return rand(0, 4);
    }
    public function testCustomFunctionUseMetadata() {

        if ( !isset($this->resultsPostingHeader) ) {
            return 0;
        }

        return (float) $this->resultsPostingHeader->getMetadataByKey('test_number');

    }

    public function zero() {
        return 0;
    }

    public function isValidation(): bool
    {
        return $this->isValidation;
    }

    public function setIsValidation(bool $isValidation)
    {
        $this->isValidation = $isValidation;
        return $this;
    }

    public function getOutputValues(): array
    {
        return $this->outputValues;
    }

    public function setOutputValues(array $output_values): self
    {
        $this->outputValues = $output_values;
        return $this;
    }

    public function getResultsPostingHeader(): ResultsPostingHeader
    {
        return $this->resultsPostingHeader;
    }

    public function setResultsPostingHeader(ResultsPostingHeader $resultsPostingHeader): static
    {
        $this->resultsPostingHeader = $resultsPostingHeader;
        return $this;
    }

    public function setReportCardOutputComponent(ReportCardOutputComponent $report_card_output_component): static
    {
        $this->reportCardOutputComponent = $report_card_output_component;
        return $this;
    }

    public function getReportCardOutputComponent(): ReportCardOutputComponent
    {
        return $this->reportCardOutputComponent;
    }
}
