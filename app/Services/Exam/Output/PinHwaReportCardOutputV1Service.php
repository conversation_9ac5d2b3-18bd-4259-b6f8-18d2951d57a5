<?php

namespace App\Services\Exam\Output;

use App\Enums\ClassType;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\RewardPunishmentRecordStatus;
use App\Exceptions\BadExpressionException;
use App\Exceptions\ReportCardOutputException;
use App\Interfaces\IReportCardOutput;
use App\Models\CompetitionRecord;
use App\Models\LeadershipPositionRecord;
use App\Models\PeriodAttendance;
use App\Models\PromotionMark;
use App\Models\ReportCardOutput;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\RewardPunishmentRecord;
use App\Models\StudentClass;
use App\Services\ConductRecordService;
use DateTime;

class PinHwaReportCardOutputV1Service extends GeneralReportCardOutputService implements IReportCardOutput
{
    const array FORMULA = [
        'Gross Total' => [
            'value' => 'GETGROSSTOTAL',
            'args' => ["Result Source Code"]
        ],
        'Gross Weightage' => [
            'value' => 'GETGROSSWEIGHTAGE',
            'args' => ["Result Source Code"]
        ],
        'Marks Added' => [
            'value' => 'GETMARKSADDED',
            'args' => ["Semester Setting Code", "From Date", "To Date"]
        ],
        'Marks Subtracted' => [
            'value' => 'GETMARKSSUBTRACTED',
            'args' => ["From Date", "To Date"]
        ],
        'Conduct' => [
            'value' => 'GETCONDUCTSCORE',
            'args' => ["Semester Setting Code"]
        ],
        'Promotion Retention' => [
            'value' => 'GETPROMOTEDRETAINED',
            'args' => ["Semester Setting Code", "Output Code"]
        ],
        'Number of School Days' => [
            'value' => 'ROUND',
            'args' => ['Number of School Days']
        ],
        'Class Positions' => [
            'value' => 'GETCLASSLEADERSHIPPOSITIONS',
            'args' => ['Semester Setting Code']
        ],
        'Society' => [
            'value' => 'GETSOCIETY',
            'args' => ['Semester Setting Code']
        ],
        'Absence' => [
            'value' => 'GETABSENCE',
            'args' => ['From Date', 'To Date']
        ],
        'Merit' => [
            'value' => 'GETMERIT',
            'args' => ['From Date', 'To Date']
        ],
        'Exceptional Performance' => [
            'value' => 'GETEXCEPTIONALPERFORMANCE',
            'args' => ['From Date', 'To Date']
        ],
        '+' => ['value' => '+'],
        '-' => ['value' => '-'],
        '*' => ['value' => '*'],
        '/' => ['value' => '/']
    ];

    protected ConductRecordService $conductRecordService;

    const array PERIOD_ATTENDANCE_MARK_CONFIG = [
        PeriodAttendanceStatus::ABSENT->value => 0.04,
        PeriodAttendanceStatus::LATE->value => 0.02
    ];

    public function __construct()
    {
        parent::__construct();
        $this->conductRecordService = app()->make(ConductRecordService::class);
    }

    public function customFunctionRouter($function_name, &$value)
    {
        switch ($function_name) {
            case 'getgrosstotal':
                $value = ['ref' => [$this, 'getGrossTotal'], 'arc' => 1];
                break;
            case 'getgrossweightage':
                $value = ['ref' => [$this, 'getGrossWeightage'], 'arc' => 1];
                break;
            case 'getmarksadded':
                $value = ['ref' => [$this, 'getMarksAdded'], 'arc' => 3];
                break;
            case 'getmarkssubtracted':
                $value = ['ref' => [$this, 'getMarksSubtracted'], 'arc' => 2];
                break;
            case 'getconductscore':
                $value = ['ref' => [$this, 'getConductScore'], 'arc' => 3];
                break;
            case 'getpromotedretained':
                $value = ['ref' => [$this, 'getPromotedRetained'], 'arc' => 2];
                break;
            case 'getclassleadershippositions':
                $value = ['ref' => [$this, 'getClassLeadershipPositions'], 'arc' => 1];
                break;
            case 'getsociety':
                $value = ['ref' => [$this, 'getSociety'], 'arc' => 1];
                break;
            case 'getabsence':
                $value = ['ref' => [$this, 'getAbsence'], 'arc' => 2];
                break;
            case 'getmerit':
                $value = ['ref' => [$this, 'getMerit'], 'arc' => 2];
                break;
            case 'getexceptionalperformance':
                $value = ['ref' => [$this, 'getExceptionalPerformance'], 'arc' => 2];
                break;
            default:
                throw new BadExpressionException("Unknown custom function '$function_name'");
        }
    }

    public function getGrossTotal(string $result_source_code){
        if ($this->isValidation()) {
            return true;
        }

        $gross_total = ResultSourceSubject::where(['grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE])
                ->whereRelation('resultSource', 'code', $result_source_code)
                ->whereRelation('resultSource', 'student_grading_framework_id', $this->getStudentGradingFramework()->id)
                ->get()
                ->sum('weightedScore');

        return $gross_total;
    }

    public function getGrossWeightage(string $result_source_code){
        if ($this->isValidation()) {
            return true;
        }

        $gross_weightage = ResultSourceSubject::where([
                    'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE, 
                    'is_exempted' => false,
                ])
                ->whereRelation('resultSource', 'code', $result_source_code)
                ->whereRelation('resultSource', 'student_grading_framework_id', $this->getStudentGradingFramework()->id)
                ->get()
                ->sum('weightage_multiplier');
                
        return $gross_weightage;
    }

    public function getMarksAdded(string $semester_setting_code, string $date_from, string $date_to)
    {

        // total = competition_marks + conduct marks with scheme + avg_exam_marks (>0 only)
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $component = $this->getReportCardOutputComponent();

        // competition marks
        $competition_marks = CompetitionRecord::where('student_id', $this->studentGradingFramework->student_id)
            ->whereRelation('semesterClass.semesterSetting', 'code', $semester_setting_code)
            ->sum('mark');

        // conduct marks
        $conduct_marks = $this->conductRecordService->calculateConductMarks([
            'semester_setting_code' => $semester_setting_code,
            'student_id' => $this->studentGradingFramework->student_id,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

        $conduct_grade = $this->gradingSchemeService
            ->setGradingScheme($component->gradingScheme)
            ->applyAndGetGrade($conduct_marks);

        // avg_exam_marks
        $reward_punishment_avg_exam_marks = RewardPunishmentRecord::query()
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'status' => RewardPunishmentRecordStatus::POSTED,
            ])
            ->where('average_exam_marks', '>', 0)
            ->whereBetween('date', [$date_from, $date_to])
            ->sum('average_exam_marks');

        $total_marks = round($competition_marks + $conduct_grade['extra_marks'] + $reward_punishment_avg_exam_marks, 2);

        return $total_marks;
    }

    public function getMarksSubtracted(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        // total = attendance_marks + reward punishment avg_exam_marks (<0 only)
        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        // get marks deducted for attendance absent
        $period_attendances = PeriodAttendance::with('leaveApplication')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereIn('status', [PeriodAttendanceStatus::ABSENT, PeriodAttendanceStatus::LATE])
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        $attendance_marks = 0;

        $period_attendances->each(function ($period) use (&$attendance_marks) {
            $status = $period->status;
            $marks = self::PERIOD_ATTENDANCE_MARK_CONFIG[$status];

            if ($status === PeriodAttendanceStatus::ABSENT->value) {
                // if absent, we check whether got point deduction from leave application first, if none, then we deduct a hardcoded value 0.04
                $marks = $period->leaveApplication?->average_point_deduction ?? self::PERIOD_ATTENDANCE_MARK_CONFIG[$status];
            }

            $attendance_marks = $attendance_marks + $marks;
        });

        // reward punishment avg_exam_marks
        $reward_punishment_avg_exam_marks = RewardPunishmentRecord::query()
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'status' => RewardPunishmentRecordStatus::POSTED
            ])
            ->whereBetween('date', [$date_from, $date_to])
            ->where('average_exam_marks', '<', 0)
            ->sum('average_exam_marks');

        return round($attendance_marks + abs($reward_punishment_avg_exam_marks), 2);

    }

    public function getConductScore(string $semester_setting_code, string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        return $this->conductRecordService->calculateConductMarks([
            'student_id' => $this->studentGradingFramework->student_id,
            'semester_setting_code' => $semester_setting_code,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

    }

    public function getPromotedRetained(string $semester_setting_code, string $result_code)
    {
        if ($this->isValidation()) {
            return true;
        }
        // can remove result code if FINALEXAM is always going to be constant
        // must set priority to higher (later) than CONDUCT and SYS_NET_AVG
        $conduct_marks = $this->getOutputValues()[$result_code]['CONDUCT']['total'];
        $net_average = $this->getOutputValues()[$result_code]['SYS_NET_AVG']['total'];

        $promotion = PromotionMark::query()
            ->whereRelation('semesterClass.semesterSetting', 'code', $semester_setting_code)
            ->first();

        if ($promotion === null) {
            throw new ReportCardOutputException('Unable to get promotion settings for Semester ' . $semester_setting_code);
        }

        if (bccomp($conduct_marks, $promotion->conduct_mark_for_promotion, 2) >= 0
            && bccomp($net_average, $promotion->net_average_for_promotion, 2) >= 0) {
            return trans('exam.promote', [], 'zh');
        }

        return trans('exam.retained', [], 'zh');
    }

    public function getClassLeadershipPositions(string $semester_setting_code)
    {

        if ($this->isValidation()) {
            return true;
        }

        // if got multiple, always return the first one only
        $leadership_position_record = LeadershipPositionRecord::with('leadershipPosition')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereRelation('semesterSetting', 'code', $semester_setting_code)
            ->orderBy('created_at', 'desc')
            ->first();

        return $leadership_position_record?->leadershipPosition?->name;

    }

    public function getSociety(string $semester_setting_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        $student_class = StudentClass::with('semesterClass.classModel')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'class_type' => ClassType::SOCIETY
            ])
            ->whereRelation('semesterSetting', 'code', $semester_setting_code)
            ->get();

        return !$student_class->isEmpty() ? $student_class->implode('semesterClass.classModel.name', ', ') : null;
    }

    public function getAbsence(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        // Need attendance module
        $periods = PeriodAttendance::with('leaveApplication.leaveApplicationType')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'status' => PeriodAttendanceStatus::ABSENT
            ])
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        $periods = $periods->groupBy('leaveApplication.leaveApplicationType.name');

        $periods->transform(function ($period, $key) {
            $name = $key ?: trans('exam.absent', [], 'zh');
            $count = $period->count() . trans('exam.period', [], 'zh');
            return [
                'data' => $name . ":" . $count
            ];
        });

        return !$periods->isEmpty() ? $periods->implode('data', "; ") : null;
    }

    public function getMerit(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $reward_punishment_records = RewardPunishmentRecord::with('rewardPunishment.meritDemeritSettings')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'display_in_report_card' => true,
                'status' => RewardPunishmentRecordStatus::POSTED
            ])
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        if ($reward_punishment_records->isEmpty()) {
            return null;
        }

        $reward_punishment_records->transform(function ($record) {
            $data = [
                'line_item' =>
                    $record->rewardPunishment->meritDemeritSettings->implode('name', ', ') . " (" . $record->rewardPunishment->name . ")"
            ];
            return $data;
        });

        return $reward_punishment_records->implode(
            'line_item', '; ');
    }

    public function getExceptionalPerformance($date_from, $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $competition_records = CompetitionRecord::query()
            ->join('competitions', 'competitions.id', '=', 'competition_records.competition_id')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereBetween('competitions.date', [$date_from, $date_to])
            ->orderBy('competitions.date', 'DESC')
            ->get();

        if ($competition_records->isEmpty()) {
            return null;
        }

        $competition_records = $competition_records->filter(function ($item) {
            return strlen($item->competition->name) <= 50;
        })->slice(0, 3);

        // todo: Are we confirm to blindly drop competition name length > 50? even if student has some competition < 50 and some > 50?
        if ($competition_records->isEmpty()) {
            return trans('exam.reference', [], 'zh');
        }

        return $competition_records->implode('competition.name', '; ');
    }

    public function validateDate(string $date_string)
    {

        $date = DateTime::createFromFormat("Y-m-d", $date_string);
        if (!$date) {
            throw new BadExpressionException('Not in date format:' . $date);
        }
        return $date;
    }
}
