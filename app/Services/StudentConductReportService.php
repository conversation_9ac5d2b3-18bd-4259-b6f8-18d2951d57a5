<?php

namespace App\Services;

use App\Enums\ExamSemesterSettingCategory;
use App\Enums\RewardPunishmentRecordStatus;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\ConductRecord;
use App\Models\ExamSemesterSetting;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Repositories\StudentRepository;

class StudentConductReportService extends BaseReportService
{
    private StudentRepository $studentRepository;
    private GradingSchemeService $gradingSchemeService;
    protected ReportPrintService $reportPrintService;

    public function __construct(StudentRepository $student_repository,
        GradingSchemeService $grading_scheme_service,
        ReportPrintService $report_print_service )
    {
        $this->studentRepository = $student_repository;
        $this->gradingSchemeService = $grading_scheme_service;
        $this->reportPrintService = $report_print_service;
    }

    public function getConductReportData($filters = []){

        $exam_semester_settings = ExamSemesterSetting::where([
            'category' => ExamSemesterSettingCategory::MERIT_DEMERIT->value,
            'semester_setting_id' => $filters['semester_setting_id'],
            'grade_id' => $filters['grade_id']
        ])->first();

        $date_from = $exam_semester_settings->from_date;
        $date_to = $exam_semester_settings->to_date;

        $semester_class = SemesterClass::where('id', $filters['semester_class_id'])
            ->with('classModel.grade', 'semesterSetting')
            ->first();

        $student_filters = [
            'semester_class_id' => $filters['semester_class_id'],
            'is_latest_class_in_semester' => true
        ];

        $students = $this->studentRepository->getAll($student_filters);
        $student_ids = $students->pluck('id')->toArray();

        $reward_punishment_records = RewardPunishmentRecord::query()
            ->where('status', RewardPunishmentRecordStatus::POSTED)
            ->whereIn('student_id', $student_ids)
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        $student_conduct_records = ConductRecord::whereIn('student_id', $student_ids)
            ->with([
                'conductSetting.gradingScheme',
                'conductSettingTeacher.employee' => function ($query) {
                    $query->select('id', 'name');
                },
                'student' => function ($query){
                    $query->select('id', 'name', 'student_number');
                }
            ])
            ->get()
            ->groupBy('student_id');

        $homeroom_teacher_data = $student_conduct_records->first()
            ->where('conductSettingTeacher.is_homeroom_teacher', true)
            ->first();

        $homeroom_teacher = [
            'id' => $homeroom_teacher_data->conductSettingTeacher->employee->id,
            'name' => $homeroom_teacher_data->conductSettingTeacher->employee->name
        ];

        $other_teacher_data = $student_conduct_records->first()->where('conductSettingTeacher.is_homeroom_teacher', false);
        $other_teachers = $other_teacher_data->map(function ($data){
            return [
                'id' => $data->conductSettingTeacher->employee->id,
                'name' => $data->conductSettingTeacher->employee->name
            ];
        })->toArray();

        $student_conduct_records->transform(function ($conduct_record) use ($reward_punishment_records) {
            $student = $conduct_record[0]->student;

            $merit_score = $reward_punishment_records->where('student_id' , $student->id)
                ->where('conduct_marks', '>=', 0)
                ->sum('conduct_marks');

            $demerit_score = $reward_punishment_records->where('student_id' , $student->id)
                ->where('conduct_marks', '<', 0)
                ->sum('conduct_marks');

            $homeroom_teacher_marks = (float) $conduct_record->where('conductSettingTeacher.is_homeroom_teacher', true)->first()->marks;
            $other_teacher_marks = $conduct_record->where('conductSettingTeacher.is_homeroom_teacher', false)
                ->mapWithKeys(function ($record){
                    $data = [
                        'marks' => $record->marks,
                        'teacher_name' => $record->conductSettingTeacher->employee->name
                    ];
                    return [$record->conductSettingTeacher->employee->id => $data];
            });

            $other_teacher_average = $other_teacher_marks->avg('marks');
            $homeroom_teacher_percentage = 0.33 * $homeroom_teacher_marks;
            $other_teacher_percentage = 0.67 * $other_teacher_average;


            $total_conduct_marks = $homeroom_teacher_percentage + $other_teacher_percentage + $merit_score + $demerit_score;
            $conduct_grade = $this->gradingSchemeService
                ->setGradingScheme($conduct_record[0]->conductSetting->gradingScheme)
                ->applyAndGetGrade($total_conduct_marks);

            $data = [
                'name' => $student->getTranslation('name', 'zh')." ".$student->getTranslation('name', 'en'), // this sucks, need to standardize
                'student_number' => $student->student_number,
                'homeroom_teacher_marks' => $homeroom_teacher_marks,
                'other_teacher_average' => $other_teacher_average,
                'other_teacher_marks' => $other_teacher_marks->toArray(),
                'homeroom_teacher_percentage' => $homeroom_teacher_percentage,
                'other_teacher_percentage' => $other_teacher_percentage,
                'merit_score' => $merit_score != 0 ? abs($merit_score) : '',
                'demerit_score' => $demerit_score != 0 ? abs($demerit_score): '',
                'conduct_grade' => $conduct_grade['display_as_name'],
            ];
            return $data;
        });

        $student_conduct_records = $student_conduct_records->sortBy('name')->values()->toArray();

        $report_data = [
            'semester' => $semester_class->semesterSetting->name,
            'class' => $semester_class->classModel->code,
            'grade' => $semester_class->classModel->grade->name,
            'homeroom_teacher' => $homeroom_teacher,
            'other_teachers' => $other_teachers,
            'data' => $student_conduct_records
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return $url;

    }
}
