<?php

namespace App\Services\Timetable;

use App\Enums\LeaveApplicationStatus;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\Userable;
use App\Models\AttendancePeriodOverride;
use App\Models\AttendancePeriodOverrideLeaveApplication;
use App\Models\Employee;
use App\Models\Student;
use App\Repositories\AttendancePeriodOverrideRepository;
use App\Services\StudentService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;

class AttendancePeriodOverrideService
{
    protected Userable $userable;
    protected string $date;
    protected string $attendanceFromTime;
    protected string $attendanceToTime;
    protected Employee $updatedBy;
    protected array $studentIdsAndDates = [];
    protected Collection $leaveApplications;
    protected Collection $studentLeaveApplications;
    protected string $leaveApplicationNewStatus;
    protected array $individualOverrideToBeDeleted = [];
    protected array $individualOverrideToBeCreated = [];

    protected AttendancePeriodOverrideRepository $attendancePeriodOverrideRepository;
    protected StudentService $studentService;
    protected StudentTimetableService $studentTimetableService;

    public function __construct(
        AttendancePeriodOverrideRepository $attendancePeriodOverrideRepository,
        StudentService $studentService,
        StudentTimetableService $studentTimetableService,
    ) {
        $this->attendancePeriodOverrideRepository = $attendancePeriodOverrideRepository;
        $this->studentService = $studentService;
        $this->studentTimetableService = $studentTimetableService;
    }

    public function create()
    {

        $existed = $this->attendancePeriodOverrideRepository->exists([
            'attendance_recordable_type' => get_class($this->userable),
            'attendance_recordable_id' => $this->userable->id,
            'period' => $this->date,
        ]);

        if ($existed) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42002);
        }

        return $this->attendancePeriodOverrideRepository->create([
            'period' => $this->date,
            'attendance_recordable_type' => get_class($this->userable),
            'attendance_recordable_id' => $this->userable->id,
            'attendance_from' => $this->attendanceFromTime,
            'attendance_to' => $this->attendanceToTime,
            'updated_by_employee_id' => $this->updatedBy->id,
        ]);
    }

    public function update(AttendancePeriodOverride $attendancePeriodOverride, $data)
    {
        $data['updated_by_employee_id'] = $this->updatedBy->id;
        return $this->attendancePeriodOverrideRepository->update($attendancePeriodOverride, $data);
    }

    public function delete(AttendancePeriodOverride $attendancePeriodOverride)
    {
        $this->attendancePeriodOverrideRepository->delete($attendancePeriodOverride);
        return true;
    }

    public function getAllPaginated($filters = []): LengthAwarePaginator
    {
        return $this->attendancePeriodOverrideRepository
            ->getAllPaginated($filters);
    }


    public function getDate(): string
    {
        return $this->date;
    }

    public function setDate(string $date): AttendancePeriodOverrideService
    {
        $this->date = $date;
        return $this;
    }

    public function getUserable(): Userable
    {
        return $this->userable;
    }

    public function setUserable(Userable $userable): AttendancePeriodOverrideService
    {
        $this->userable = $userable;
        return $this;
    }


    public function getAttendanceFromTime(): string
    {
        return $this->attendanceFromTime;
    }

    public function setAttendanceFromTime(string $attendanceFromTime): AttendancePeriodOverrideService
    {
        $this->attendanceFromTime = $attendanceFromTime;
        return $this;
    }

    public function getAttendanceToTime(): string
    {
        return $this->attendanceToTime;
    }

    public function setAttendanceToTime(string $attendanceToTime): AttendancePeriodOverrideService
    {
        $this->attendanceToTime = $attendanceToTime;
        return $this;
    }

    public function getUpdatedBy(): Employee
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(Employee $updatedBy): AttendancePeriodOverrideService
    {
        $this->updatedBy = $updatedBy;
        return $this;
    }

    public function batchDelete(array $ids)
    {
        $this->attendancePeriodOverrideRepository->batchDeleteByIds($ids);
    }

    public function setLeaveApplications(Collection $leave_applications)
    {
        $this->leaveApplications = $leave_applications;
        return $this;
    }

    public function setEmployee(Employee $employee)
    {
        $this->employee = $employee;
        return $this;
    }

    public function setLeaveApplicationNewStatus($status)
    {
        $this->leaveApplicationNewStatus = $status;
        return $this;
    }

    public function updateIndividualOverrideByLeaveApplications()
    {
        $this->studentLeaveApplications = $this->leaveApplications->where('leave_applicable_type', Student::class);
        if (count($this->studentLeaveApplications) > 0) {
            if ($this->leaveApplicationNewStatus === LeaveApplicationStatus::APPROVED->value) { // pending to approved
                $this->batchCreateIndividualOverride();
            } else {
                $this->batchDeleteIndividualOverride();
            }
            $this->repostBackdatedLeave();
        }
    }

    private function mapDataForIndividualOverrideCreation()
    {
        $individual_overrides = $this->attendancePeriodOverrideRepository->getAll([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $this->studentLeaveApplications->pluck('leave_applicable_id')->unique()->toArray(),
            'period' => array_unique($this->studentLeaveApplications->pluck('leaveApplicationPeriods')->flatten()->pluck('date')->toArray()),
            'has_leave_application' => true,
            'includes' => ['leaveApplications.leaveApplicationPeriods'],
        ]);

        foreach ($this->studentLeaveApplications as $leave_application) {
            $leave_application_periods_group_by_date = $leave_application->leaveApplicationPeriods->groupBy('date');
            foreach ($leave_application_periods_group_by_date as $date => $leave_application_periods) {
                // can only be individual override created via leave application, only full day approved leave don't have individual override
                $existing_individual_override = $individual_overrides
                    ->where('attendance_recordable_id', $leave_application->leave_applicable_id)
                    ->where('attendance_recordable_type', $leave_application->leave_applicable_type)
                    ->where('period', $date)
                    ->first();
                $student_id = $leave_application->leave_applicable_id;
                $day = strtoupper(Carbon::parse($date)->format('l'));

                // init data with leave_application_id and periods_to_be_excluded from existing individual override
                if (!isset($this->individualOverrideToBeCreated[$date][$student_id])) {
                    if ($existing_individual_override) {
                        $individual_override_periods = $existing_individual_override->leaveApplications->pluck('leaveApplicationPeriods')->flatten()->where('date', $date)->pluck('period')->toArray();
                        $individual_override_leave_application_ids = $existing_individual_override->leaveApplications->pluck('id')->toArray();
                    } else {
                        $individual_override_periods = [];
                        $individual_override_leave_application_ids = [];
                    }

                    $this->individualOverrideToBeCreated[$date][$student_id] = [
                        'student' => $leave_application->leaveApplicable,
                        'student_id' => $student_id,
                        'date' => $date,
                        'day' => $day,
                        'leave_application_id' => $individual_override_leave_application_ids,
                        'periods_to_be_excluded' => $individual_override_periods,
                        'existing_individual_override' => $existing_individual_override ?? null,
                    ];
                }
                // append newly approved leave application id
                $this->individualOverrideToBeCreated[$date][$student_id]['leave_application_id'] = array_unique(array_merge(
                    $this->individualOverrideToBeCreated[$date][$student_id]['leave_application_id'],
                    [$leave_application->id]
                ));
                $this->individualOverrideToBeCreated[$date][$student_id]['periods_to_be_excluded'] = array_unique(array_merge(
                    $this->individualOverrideToBeCreated[$date][$student_id]['periods_to_be_excluded'],
                    $leave_application_periods->pluck('period')->toArray()
                ));

                // if is backdated leave, re-post each date (period + student)
                // today date is considered backdated, user might approve student leave application after 3 posting each day
                if (!$this->studentService->isFutureDate($date)) { // today's date and past dates
                    $this->studentIdsAndDates[] = [
                        'student_id' => $leave_application->leave_applicable_id,
                        'date' => $date,
                    ];
                }
            }
        }

        if (count($this->individualOverrideToBeCreated) > 0) {
            $this->individualOverrideToBeCreated = collect($this->individualOverrideToBeCreated)
                ->map(fn($students) => array_values($students)) // remove student_id key
                ->flatten(1) // remove date key
                ->toArray();
        }
    }

    private function batchCreateIndividualOverride()
    {
        $this->mapDataForIndividualOverrideCreation();

        if (count($this->individualOverrideToBeCreated) == 0) {
            return $this;
        }

        $student_ids = array_unique(array_column($this->individualOverrideToBeCreated, 'student_id'));
        $day = array_unique(array_column($this->individualOverrideToBeCreated, 'day'));
        $dates = array_unique(array_column($this->individualOverrideToBeCreated, 'date'));

        // student timetables
        $student_timetable_list_by_date_and_student = collect($this->studentTimetableService
            ->setDaysFilter($day)
            ->setStudentIds($student_ids)
            ->getTimetableWithTimeslotOverrideByDate($dates));

        $attendance_period_override_leave_application_to_be_created = [];
        foreach ($this->individualOverrideToBeCreated as $data) {
            $student_timetable = collect($student_timetable_list_by_date_and_student[$data['date']][$data['student_id']] ?? []);
            $student_timetable_excluded_leave_application_periods = $student_timetable->whereNotIn('period', $data['periods_to_be_excluded'])->where('is_empty', false)->where('has_mark_deduction', true);

            // no active period, skip
            if (count($student_timetable_excluded_leave_application_periods) == 0) {
                continue;
            }

            $student_attendance_period = $this->studentTimetableService->getAttendancePeriodForStudentAndDay($student_timetable_excluded_leave_application_periods);

            if ($data['existing_individual_override']) {
                $data['existing_individual_override']->leaveApplications()->detach();
                $data['existing_individual_override']->delete();
            }

            // check whether student already has existing attendance period override, if yes, throw exception
            if ($this->studentTimetableService->hasIndividualOverrideOn($data['student'], $data['date'])) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50004, ['date' => $data['date'], 'student' => $data['student']->getUserNumber()]);
            }

            $attendance_period_override = AttendancePeriodOverride::create([
                'attendance_recordable_type' => Student::class,
                'attendance_recordable_id' => $data['student_id'],
                'period' => $data['date'],
                'attendance_from' => $student_attendance_period['from'],
                'attendance_to' => $student_attendance_period['to'],
                'updated_by_employee_id' => $this->employee->id,
            ]);

            foreach ($data['leave_application_id'] as $leave_application_id) {
                $attendance_period_override_leave_application_to_be_created[] = [
                    'attendance_period_override_id' => $attendance_period_override->id,
                    'leave_application_id' => $leave_application_id,
                ];
            }
        }

        AttendancePeriodOverrideLeaveApplication::insert($attendance_period_override_leave_application_to_be_created);
        return $this;
    }

    private function mapDataForIndividualOverrideDelete()
    {
        $individual_overrides = $this->attendancePeriodOverrideRepository->getAll([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $this->studentLeaveApplications->pluck('leave_applicable_id')->unique()->toArray(),
            'period' => array_unique($this->studentLeaveApplications->pluck('leaveApplicationPeriods')->flatten()->pluck('date')->toArray()),
            'has_leave_application' => true,
            'includes' => ['leaveApplications.leaveApplicationPeriods'],
        ]);

        foreach ($this->studentLeaveApplications as $leave_application) {
            $leave_application_periods_group_by_date = $leave_application->leaveApplicationPeriods->groupBy('date');
            foreach ($leave_application_periods_group_by_date as $date => $leave_application_periods) {
                // can only be individual override created via leave application, only full day approved leave don't have individual override
                $existing_individual_override = $individual_overrides
                    ->where('attendance_recordable_id', $leave_application->leave_applicable_id)
                    ->where('attendance_recordable_type', $leave_application->leave_applicable_type)
                    ->where('period', $date)
                    ->first();
                $student_id = $leave_application->leave_applicable_id;
                $day = strtoupper(Carbon::parse($date)->format('l'));

                // init data with periods_to_be_excluded from existing individual override
                if ($existing_individual_override) {
                    if (!isset($this->individualOverrideToBeDeleted[$existing_individual_override->id])) {
                        $individual_override_periods = $existing_individual_override->leaveApplications
                            ->pluck('leaveApplicationPeriods')
                            ->flatten()
                            ->where('date', $date)
                            ->pluck('period')
                            ->toArray();

                        $this->individualOverrideToBeDeleted[$existing_individual_override->id] = [
                            'student_id' => $student_id,
                            'date' => $date,
                            'day' => $day,
                            'unapproved_leave_application_id' => [],
                            'periods_to_be_excluded' => $individual_override_periods,
                            'existing_individual_override' => $existing_individual_override,
                        ];
                    }
                    // append rejected leave application id
                    $this->individualOverrideToBeDeleted[$existing_individual_override->id]['unapproved_leave_application_id'] = array_unique(array_merge(
                        $this->individualOverrideToBeDeleted[$existing_individual_override->id]['unapproved_leave_application_id'],
                        [$leave_application->id]
                    ));
                    // remove leave application periods from existing individual override
                    $this->individualOverrideToBeDeleted[$existing_individual_override->id]['periods_to_be_excluded'] = array_values(array_diff(
                        $this->individualOverrideToBeDeleted[$existing_individual_override->id]['periods_to_be_excluded'],
                        $leave_application_periods->pluck('period')->toArray()
                    ));
                }

                // if is backdated leave, re-post each date (period + student)
                // today date is considered backdated, user might approve student leave application after 3 posting each day
                if (!$this->studentService->isFutureDate($date)) { // today's date and past dates
                    $this->studentIdsAndDates[] = [
                        'student_id' => $leave_application->leave_applicable_id,
                        'date' => $date,
                    ];
                }
            }
        }
    }

    private function batchDeleteIndividualOverride()
    {
        $this->mapDataForIndividualOverrideDelete();

        if (count($this->individualOverrideToBeDeleted) == 0) {
            return $this;
        }

        $student_ids = array_unique(array_column($this->individualOverrideToBeDeleted, 'student_id'));
        $day = array_unique(array_column($this->individualOverrideToBeDeleted, 'day'));
        $dates = array_unique(array_column($this->individualOverrideToBeDeleted, 'date'));

        // student timetables
        $student_timetable_list_by_date_and_student = collect($this->studentTimetableService
            ->setDaysFilter($day)
            ->setStudentIds($student_ids)
            ->getTimetableWithTimeslotOverrideByDate($dates));

        foreach ($this->individualOverrideToBeDeleted as $data) {
            $student_timetable = collect($student_timetable_list_by_date_and_student[$data['date']][$data['student_id']] ?? []);
            $student_timetable_excluded_leave_application_periods = $student_timetable->whereNotIn('period', $data['periods_to_be_excluded'])->where('is_empty', false)->where('has_mark_deduction', true);

            if (count($student_timetable_excluded_leave_application_periods) == 0) {
                continue;
            }

            $attendance_period_override = $data['existing_individual_override'];
            if (empty($data['periods_to_be_excluded'])) {
                $attendance_period_override->leaveApplications()->detach();
                $attendance_period_override->delete();
            } else {
                $student_attendance_period = $this->studentTimetableService->getAttendancePeriodForStudentAndDay($student_timetable_excluded_leave_application_periods);
                $attendance_period_override->leaveApplications()->detach($data['unapproved_leave_application_id']);
                $attendance_period_override->update([
                    'attendance_from' => $student_attendance_period['from'],
                    'attendance_to' => $student_attendance_period['to'],
                    'updated_by_employee_id' => $this->employee->id,
                ]);
            }
        }
        return $this;
    }

    private function repostBackdatedLeave()
    {
        $datum_group_by_date = collect($this->studentIdsAndDates)->groupBy('date');

        foreach ($datum_group_by_date as $date => $datum) {
            $student_ids = array_unique(array_column($datum->toArray(), 'student_id'));
            $student_ids_string = implode(',', $student_ids);

            Artisan::call('posting:student-attendance-input', ['--date' => $date, '--student_ids' => $student_ids_string]);
        }
    }
}
