<?php

namespace App\Services\Timetable;

use App\Helpers\ErrorCodeHelper;
use App\Models\EmployeeTimetable;
use App\Models\Period;
use App\Models\StudentTimetable;
use App\Models\Timetable;
use App\Repositories\PeriodGroupRepository;
use App\Repositories\PeriodLabelRepository;
use App\Repositories\TimeslotRepository;
use App\Repositories\TimeslotTeacherRepository;
use App\Repositories\TimetableRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class TimetableService
{
    private Timetable|Model $timetable;
    private array $timeslotsData;

    public function __construct(
        protected TimetableRepository $timetableRepository,
        protected TimeslotRepository $timeslotRepository,
        protected PeriodGroupRepository $periodGroupRepository,
        protected TimeslotTeacherRepository $timeslotTeacherRepository,
        protected PeriodLabelRepository $periodLabelRepository
    ) {
    }

    public function getAllPaginatedTimetables($filters = []): LengthAwarePaginator
    {
        return $this->timetableRepository->getAllPaginated($filters);
    }

    public function createTimetable($data): ?Model
    {
        return $this->timetableRepository->create($data);
    }

    public function updateTimetable($id, $data): ?Model
    {
        return $this->timetableRepository->update($id, $data);
    }

//    public function deleteTimetable($id): bool
//    {
//        return $this->timetableRepository->delete($id);
//    }

    public function createTimeSlotsViaPeriods(): void
    {
        $period_group = $this->periodGroupRepository->find($this->timetable->period_group_id);

        foreach ($period_group->periods as $period) {
            $this->timeslotRepository->create([
                'timetable_id' => $this->timetable->id,
                'period_id' => $period->id,
                'day' => $period->day,
                'attendance_from' => $period->from_time,
                'attendance_to' => $period->to_time,
            ]);
        }
        StudentTimetable::refreshViewTable();
    }

    public function setTimetable(Model|Timetable $timetable): static
    {
        $this->timetable = $timetable;

        return $this;
    }

    public function setTimeslotsData(array $timeslots_data): static
    {
        $this->timeslotsData = $timeslots_data;

        return $this;
    }

    public function checkAllTimeslotsIsExist(): static
    {
        $input_timeslot_ids = Arr::pluck($this->timeslotsData, 'id');

        $filters = [
            'timetable_id' => $this->timetable->id
        ];

        $timeslot_ids = $this->timeslotRepository->getAll($filters)->pluck('id')->toArray();
        sort($input_timeslot_ids);
        sort($timeslot_ids);

        if ($input_timeslot_ids !== $timeslot_ids) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::TIMETABLE_ERROR, 40006);
        }

        return $this;
    }

    public function updateTimeSlots(): void
    {
        foreach ($this->timeslotsData as $timeslot) {
            $this->timeslotRepository->updateById($timeslot['id'], [
                'class_subject_id' => $timeslot['class_subject_id'],
                'placeholder' => $timeslot['placeholder'],
                'attendance_from' => $timeslot['attendance_from'],
                'attendance_to' => $timeslot['attendance_to'],
                'default_init_status' => $timeslot['default_init_status'],
                'has_mark_deduction' => $timeslot['has_mark_deduction'],
            ]);

            $this->timeslotTeacherRepository->deleteByTimeslotId($timeslot['id']);

            foreach ($timeslot['teachers'] as $teacher) {
                $this->timeslotTeacherRepository->create([
                    'timeslot_id' => $timeslot['id'],
                    'employee_id' => $teacher['employee_id'],
                    'type' => $teacher['type'],
                ]);
            }
        }

        StudentTimetable::refreshViewTable();
        EmployeeTimetable::refreshViewTable();
    }

    public function getFormatTimetable(): array
    {
        $period_ids = $this->timetable->timeslots->pluck('period_id')->toArray();

        $periods = Period::query()->whereIn('id', $period_ids)->get();

        $period_labels = $this->periodLabelRepository->getAll([
            'period_group_id' => $this->timetable->period_group_id,
        ])->keyBy('period');

        $display_groups = [];

        $group_periods = $periods->groupBy('display_group')->all();

        foreach ($group_periods as $display_group_id => $periods) {
            $data = [
                'id' => $display_group_id,
                'periods' => [],
                'days' => []
            ];

            foreach ($periods as $period) {
                $timeslot = $this->timetable->timeslots->where('period_id', $period->id)->first();

                $data['periods'][$period->period] = [
                    'period_label_name' => $period_labels[$period->period]->name,
                    'period_label_name_translations' => $period_labels[$period->period]->getTranslations('name'),
                    'from_time' => $period->from_time,
                    'to_time' => $period->to_time,
                ];

                $data['days'][$period->day->value][$period->period] = [
                    'timeslot_id' => $timeslot->id,
                    'timeslot_class_subject_id' => $timeslot->class_subject_id,
                    'timeslot_placeholder' => $timeslot->placeholder,
                    'timeslot_attendance_from' => $timeslot->attendance_from,
                    'timeslot_attendance_to' => $timeslot->attendance_to,
                    'timeslot_default_init_status' => $timeslot->default_init_status,
                    'timeslot_has_mark_deduction' => $timeslot->has_mark_deduction,
                    'timeslot_teachers' => [],
                ];

                foreach ($timeslot->teachers as $teacher) {
                    $data['days'][$period->day->value][$period->period]['timeslot_teachers'][] = [
                        'employee_id' => $teacher->id,
                        'type' => $teacher->pivot->type,
                        'name' => $teacher->name,
                    ];
                }
            }

            //set periods to be unique
            $unique_periods = [];

            foreach ($data['periods'] as $period) {
                // Create a unique key based on the combination of from_time and to_time
                $key = $period['from_time'] . '-' . $period['to_time'];
                // Add to the unique array if it doesn't exist
                if (!isset($unique_periods[$key])) {
                    $unique_periods[$key] = $period;
                }
            }

            // Sort the unique periods by from_time and to_time
            usort($unique_periods, function ($a, $b) {
                // First compare by from_time
                $from_time_comparison = strcmp($a['from_time'], $b['from_time']);
                if ($from_time_comparison === 0) {
                    // If from_time is the same, compare by to_time
                    return strcmp($a['to_time'], $b['to_time']);
                }
                return $from_time_comparison;
            });

            $data['periods'] = array_values($unique_periods);

            $display_groups[$display_group_id] = $data;
        }

        return [
            'id' => $this->timetable->id,
            'name' => $this->timetable->name,
            'is_active' => $this->timetable->is_active,
            'semester_class_id' => $this->timetable->semester_class_id,
            'display_groups' => array_values($display_groups),
        ];
    }
}
