<?php

namespace App\Services;

use App\Enums\ConductRecordStatus;
use App\Enums\DeadlineType;
use App\Enums\RewardPunishmentRecordStatus;
use App\Helpers\ErrorCodeHelper;
use App\Models\ConductRecord;
use App\Models\ConductSetting;
use App\Models\ConductSettingTeacher;
use App\Models\RewardPunishmentRecord;
use App\Repositories\ConductRecordRepository;
use App\Repositories\DeadlineRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ConductRecordService
{
    private ConductRecordRepository $conductRecordRepository;
    private ConductSettingTeacher $conductSettingTeacher;

    public function __construct(ConductRecordRepository $conduct_record_repository)
    {
        $this->conductRecordRepository = $conduct_record_repository;
    }

    public function setConductSettingTeacher(ConductSettingTeacher $conduct_setting_teacher): self
    {
        $this->conductSettingTeacher = $conduct_setting_teacher;
        return $this;
    }

    public function getConductSettingTeacher(): ConductSettingTeacher
    {
        return $this->conductSettingTeacher;
    }

    public function getAllPaginatedConductRecords($filters = []): LengthAwarePaginator
    {
        return $this->conductRecordRepository->getAllPaginated($filters);
    }

    public function getAllConductRecords($filters = []): Collection
    {
        return $this->conductRecordRepository->getAll($filters);
    }

    public function createOrUpdateConductRecords($data): void
    {
        $conduct_setting_teacher = $this->getConductSettingTeacher();
        $conduct_setting = $conduct_setting_teacher->conductSetting;

        $conduct_setting_id = $conduct_setting->id;
        $conduct_setting_teacher_id = $conduct_setting_teacher->id;

        $this->validateCreateOrUpdateConductRecords($conduct_setting);

        $student_ids = collect($data['conduct_records'])->pluck('student_id')->toArray();

        $existing_records = ConductRecord::where('conduct_setting_id', $conduct_setting_id)
            ->where('conduct_setting_teacher_id', $conduct_setting_teacher_id)
            ->whereIn('student_id', $student_ids)
            ->get()
            ->keyBy('student_id');

        DB::transaction(function () use ($data, $conduct_setting_id, $conduct_setting_teacher_id, $existing_records) {
            $to_be_inserted = [];
            $now = now();

            foreach ($data['conduct_records'] as $payload) {
                $payload['conduct_setting_id'] = $conduct_setting_id;
                $payload['conduct_setting_teacher_id'] = $conduct_setting_teacher_id;

                $student_id = $payload['student_id'];

                if (isset($existing_records[$student_id])) {
                    $existing_records[$student_id]->update($payload);
                } else {
                    $to_be_inserted[] = [
                        'conduct_setting_id' => $conduct_setting_id,
                        'conduct_setting_teacher_id' => $conduct_setting_teacher_id,
                        'student_id' => $student_id,
                        'marks' => $payload['marks'],
                        'status' => ConductRecordStatus::DRAFT,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
            }

            ConductRecord::insert($to_be_inserted);
        });
    }

    public function validateCreateOrUpdateConductRecords(ConductSetting $conduct_setting): void
    {
        $semester_class = $conduct_setting->semesterClass;

        // get deadline for the class
        $deadline = (new DeadlineRepository)->first([
            'type' => DeadlineType::CONDUCT,
            'semester_setting_id' => $conduct_setting->semester_setting_id,
            'class_id' => $semester_class->class_id,
        ], false);

        if ($deadline) {
            // check if deadline has passed
            if (Carbon::now(config('school.timezone'))->gt($deadline->to)) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::DEADLINE_ERROR, 53001);
            }
        }
    }

    public function deleteConductRecord($id): bool
    {
        return $this->conductRecordRepository->delete($id);
    }

    public function calculateConductMarks(array $filters): float
    {
        $conduct_marks = ConductRecord::with('conductSettingTeacher')
            ->where([
                'student_id' => $filters['student_id'],
                'status' => ConductRecordStatus::POSTED,
            ])
            ->when(isset($filters['semester_setting_code']), function ($query) use ($filters) {
                return $query->whereRelation('conductSetting.semesterSetting', 'code', $filters['semester_setting_code']);
            })
            ->get();

        $reward_punishment_records = RewardPunishmentRecord::query()
            ->where([
                'student_id' => $filters['student_id'],
                'status' => RewardPunishmentRecordStatus::POSTED
            ])
            ->whereBetween('date', [$filters['date_from'], $filters['date_to']])
            ->get();

        $rp_conduct_marks = $reward_punishment_records->sum('conduct_marks');

        $teacher_marks = $conduct_marks->where('conductSettingTeacher.is_homeroom_teacher', false)->avg('marks');
        $homeroom_marks = $conduct_marks->where('conductSettingTeacher.is_homeroom_teacher', true)->first()?->marks;

        // conduct_record_marks = (0.33 * homeroomteacher) + (0.67 * avg(all_other_teachers))
        $conduct_record_marks = (0.33 * $homeroom_marks) + (0.67 * $teacher_marks);

        return (float) round($conduct_record_marks + $rp_conduct_marks, 2);
    }
}
