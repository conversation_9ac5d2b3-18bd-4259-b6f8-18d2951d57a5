<?php

namespace App\Services;

use App\Enums\EnrollmentAction;
use App\Enums\EnrollmentStatus;
use App\Enums\PaymentProvider;
use App\Enums\PaymentType;
use App\Exceptions\RepositoryException;
use App\Helpers\SystemHelper;
use App\Models\BillingDocument;
use App\Models\Config;
use App\Models\Enrollment;
use App\Models\GlAccount;
use App\Models\Guardian;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Repositories\EnrollmentRepository;
use App\Repositories\GuardianStudentRepository;
use App\Services\Billing\BillingDocumentLineItemService;
use App\Services\Billing\BillingDocumentService;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\MediaRepository;

class EnrollmentService
{
    const DEFAULT_DESCRIPTION_ENROLLMENT_PAYMENT = 'Enrollment Payment';
    private EnrollmentRepository $enrollmentRepository;
    private ConfigService $configService;
    private MediaRepository $mediaRepository;
    private PaymentGatewayService $paymentGatewayService;
    private GuardianStudentRepository $guardianStudentRepository;
    private GuardianService $guardianService;

    public function __construct(
        EnrollmentRepository      $enrollment_repository,
        GuardianStudentRepository $guardian_student_repository,
        ConfigService             $config_service,
        MediaRepository           $media_repository,
        PaymentGatewayService     $payment_gateway_service,
        GuardianService           $guardian_service
    )
    {
        $this->enrollmentRepository = $enrollment_repository;
        $this->guardianStudentRepository = $guardian_student_repository;
        $this->configService = $config_service;
        $this->mediaRepository = $media_repository;
        $this->paymentGatewayService = $payment_gateway_service;
        $this->guardianService = $guardian_service;
    }

    public function getAllPaginatedEnrollments($filters = []): LengthAwarePaginator
    {
        return $this->enrollmentRepository->getAllPaginated($filters);
    }

    public function getAllEnrollments($filters = []): Collection
    {
        return $this->enrollmentRepository->getAll($filters);
    }

    public function createEnrollment($data): ?Model
    {
        return $this->enrollmentRepository->create($data);
    }

    public function updateEnrollment($id, $data): ?Model
    {
        $this->setEnrollmentStatusAndStep($data);

        return DB::transaction(function () use ($id, $data) {
            // Step 1: Update student information
            $enrollment = $this->enrollmentRepository->update($id, $data);

            // Step 2: Update guardian
            if (isset($data['guardians'])) {
                $this->removeLinkGuardians($enrollment);

                foreach ($data['guardians'] as $guardian_data) {
                    $guardian_id = Arr::get($guardian_data, 'id');

                    if ($guardian_id) {
                        $guardian = $this->guardianService->findGuardianById($guardian_id);
                        $guardian = $this->guardianService->updateGuardian($guardian, $guardian_data);
                    } else {
                        $guardian = $this->guardianService->createGuardian($guardian_data);
                    }

                    $this->linkGuardian($enrollment, $guardian, $guardian_data['type']);
                }
            }

            // Step 3: Update documents
            if (isset($data['documents'])) {
                $this->attachDocumentToEnrollment($enrollment, $data['documents']);
            }

            return $enrollment;
        });
    }

    private function setEnrollmentStatusAndStep(array &$data): void
    {
        if ($data['step'] == Enrollment::STEP_TERMS_AND_CONDITIONS) {
            $data['status'] = $this->hasEnrollmentFees() ? EnrollmentStatus::PENDING_PAYMENT : EnrollmentStatus::SUBMITTED;
        }

        if ($data['action'] == EnrollmentAction::NEXT->value && $data['step'] < Enrollment::FINAL_STEP) {
            $data['step']++;
        }
    }

    private function hasEnrollmentFees(): bool
    {
        $config = $this->configService->getConfig(Config::ENROLLMENT_FEES);

        return (float)$config['value'] > 0;
    }

    protected function removeLinkGuardians(Enrollment|Model $enrollment): void
    {
        $this->guardianStudentRepository->deleteByStudenable($enrollment);
    }

    public function linkGuardian(Enrollment|Model $enrollment, Guardian|Model $guardian, string $type): void
    {
        $this->guardianStudentRepository->create([
            'guardian_id' => $guardian->id,
            'type' => $type,
            'studenable_type' => Enrollment::class,
            'studenable_id' => $enrollment->id
        ]);
    }

    /**
     * @throws Exception
     */
    private function attachDocumentToEnrollment(Enrollment|Model $enrollment, array $documents): void
    {
        $document_names = array_column($documents, 'name', 'id');

        $document_ids = array_filter(Arr::pluck($documents, 'id'));

        $medias = $this->mediaRepository->getByIds($document_ids);

        foreach ($medias as $media) {
            $media->tagToModel($enrollment, $document_names[$media->id]);
            if (!$media->save()) {
                throw new Exception('Unable to save media to enrollment');
            }
        }
    }

    /**
     * @throws RepositoryException
     */
    public function updateEnrollmentStatus($id, $data): ?Model
    {
        return $this->enrollmentRepository->update($id, [
            'status' => $data
        ]);
    }

    public function upload(Enrollment $enrollment, $data): ?Model
    {
        $media = null;

        DB::transaction(function () use ($enrollment, $data, &$media) {
            $media = $enrollment
                ->addMedia($data['file'])
                ->toMediaCollection($data['type']);
        });

        return $media;
    }

    public function getUploadableFilesOptions(): array
    {
        $config_enrollment_document_types = $this->configService->getConfigValueAndLabel(Config::ENROLLMENT_DOCUMENT_TYPE);

        return array_merge(Enrollment::TYPE_FILES, $config_enrollment_document_types);
    }

    /**
     * @throws RepositoryException
     * @throws Exception
     */
    public function generatePaymentUrl(User $user, Enrollment $enrollment, $input): Model
    {
        $enrollment_fees = $this->configService->getConfigValue(Config::ENROLLMENT_FEES);

        $line_item = app()->make(BillingDocumentLineItemService::class)
            ->setGlAccountCode(GlAccount::CODE_ENROLLMENT_ADMISSION)        // todo: reconfirm what kind of payment is this
            ->setBillableItem($enrollment)
            ->setDescription('Enrollment Entrance Exam Fees')
            ->setAmountBeforeTax($enrollment_fees)
            ->setProduct(SystemHelper::getEnrollmentExamProduct())
            ->setCurrency(config('school.currency_code'))
            ->make();

        $invoice_service = app()->make(BillingDocumentService::class);
        $billing_document = $invoice_service->init()
            ->setType(BillingDocument::TYPE_INVOICE)
            ->setSubType(BillingDocument::SUB_TYPE_ENROLLEMENT_EXAM_FEES)
            ->setDefaultValuesForEnrollmentExamFee()
            ->setCurrency(config('school.currency_code'))
            ->setStatus(BillingDocument::STATUS_CONFIRMED)
            ->setBillToParty($enrollment)
            ->addLineItem($line_item)
            ->calculateAmountBeforeTax()
            ->applyTax(SystemHelper::getNotApplicableTax())
            ->calculatePaymentDueDate()
            ->generateReferenceNumber()
            ->create()
            ->getBillingDocument();

        return $this->paymentGatewayService
            ->setType(PaymentType::ENROLLMENT_PAYMENT)
            ->setDescription(self::DEFAULT_DESCRIPTION_ENROLLMENT_PAYMENT)
            ->setProvider(PaymentProvider::PAYEX) // TODO: To add in other provider in the future if any
            ->setUser($user)
            ->setAmount($enrollment_fees)
            ->setPaymentTypes([$input['payment_type']])
            ->setCustomerEmail($input['customer_email'])
            ->setCustomerName($input['customer_name'])
            ->setTransactionLoggable($enrollment)
            ->setBillingDocument($billing_document)
            ->setPaymentMethod(PaymentMethod::where('code', $input['payment_type'])->firstOrFail())
            ->createPaymentTransaction();
    }
}
