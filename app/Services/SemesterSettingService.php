<?php

namespace App\Services;

use App\Repositories\SemesterSettingRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class SemesterSettingService
{
    private SemesterSettingRepository $semesterSettingRepository;

    public function __construct(SemesterSettingRepository $semester_setting_repository)
    {
        $this->semesterSettingRepository = $semester_setting_repository;
    }

    public function getAllPaginatedSemesterSettings($filters = []): LengthAwarePaginator
    {
        return $this->semesterSettingRepository->getAllPaginated($filters);
    }

    public function getAllSemesterSettings($filters = []): Collection
    {
        return $this->semesterSettingRepository->getAll($filters);
    }
    
    public function createSemesterSetting($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            if ($data['is_current_semester']) {
                $this->deactivateCurrentSemesterSettings();
            }

            return $this->semesterSettingRepository->create($data);
        });
    }

    public function updateSemesterSetting($id, $data): ?Model
    {
        return DB::transaction(function () use ($id, $data) {
            if ($data['is_current_semester']) {
                $this->deactivateCurrentSemesterSettings();
            }

            return $this->semesterSettingRepository->update($id, $data);
        });
    }

    public function deleteSemesterSetting($id): bool
    {
        return $this->semesterSettingRepository->delete($id);
    }

    public function getCurrentSemesterSettings(): Collection
    {
        return $this->semesterSettingRepository->getAll([
            'current_date' => now()
        ]);
    }

    public function deactivateCurrentSemesterSettings(): void
    {
        $active_semester_setting = $this->semesterSettingRepository->first([
            'is_current_semester' => true
        ], false);

        if ($active_semester_setting) {
            $this->semesterSettingRepository->update($active_semester_setting->id, [
                'is_current_semester' => false
            ]);
        }
    }

    public function firstById(int $semester_setting_id)
    {
        return $this->semesterSettingRepository->find($semester_setting_id);
    }
}
