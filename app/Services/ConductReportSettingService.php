<?php

namespace App\Services;

use App\Repositories\ConductReportSettingRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ConductReportSettingService
{
    private ConductReportSettingRepository $conductReportSettingRepository;

    public function __construct(ConductReportSettingRepository $conduct_report_setting_repository)
    {
        $this->conductReportSettingRepository = $conduct_report_setting_repository;
    }

    public function getAllPaginatedConductReportSettings($filters = []): LengthAwarePaginator
    {
        return $this->conductReportSettingRepository->getAllPaginated($filters);
    }

    public function getAllConductReportSettings($filters = []): Collection
    {
        return $this->conductReportSettingRepository->getAll($filters);
    }

    public function bulkCreateOrUpdateConductReportSettings($data): ?Model
    {
        $semester_setting_id = $data['semester_setting_id'];
        $conduct_report_settings = $data['conduct_report_settings'];

        return DB::transaction(function () use ($semester_setting_id, $conduct_report_settings) {

            foreach ($conduct_report_settings as $conduct_report_setting){
                $this->conductReportSettingRepository->updateOrCreate([
                    'semester_setting_id' => $semester_setting_id,
                    'category' => $conduct_report_setting['category'],
                    'grade_id' => $conduct_report_setting['grade_id']
                ],  
                $conduct_report_setting);
            }
        });
    }
}
