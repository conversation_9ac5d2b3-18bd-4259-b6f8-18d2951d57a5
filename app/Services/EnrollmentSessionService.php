<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\EnrollmentSession;
use App\Repositories\CountryRepository;
use App\Repositories\EnrollmentSessionRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class EnrollmentSessionService
{
    public function __construct(
        protected EnrollmentSessionRepository $enrollmentSessionRepository,
    ) {
    }

    public function getAllPaginatedEnrollmentSessions($filters = []): LengthAwarePaginator
    {
        return $this->enrollmentSessionRepository->getAllPaginated($filters);
    }

    public function getAllEnrollmentSessions($filters = []): Collection
    {
        return $this->enrollmentSessionRepository->getAll($filters);
    }

    public function createEnrollmentSession($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            $enrollment_session = $this->enrollmentSessionRepository->create([
                'name' => $data['name'],
                'from_date' => $data['from_date'],
                'to_date' => $data['to_date'],
                'code' => $data['code'],
                'is_active' => $data['is_active'],
                'course_id' => $data['course_id'],
                'fee_assignment_settings' => $data['fee_assignment_settings'] ?? null,
            ]);

            $data['subject_ids'] ??= [];

            $enrollment_session->examSubjects()->sync($data['subject_ids']);

            return $enrollment_session;
        });
    }

    public function updateEnrollmentSession(EnrollmentSession $enrollment_session, array $data): EnrollmentSession
    {
        return DB::transaction(function () use ($enrollment_session, $data) {
            $enrollment_session = $this->enrollmentSessionRepository->update($enrollment_session, [
                'name' => $data['name'],
                'from_date' => $data['from_date'],
                'to_date' => $data['to_date'],
                'code' => $data['code'],
                'is_active' => $data['is_active'],
                'course_id' => $data['course_id'],
                'fee_assignment_settings' => $data['fee_assignment_settings'] ?? null,
            ]);

            $data['subject_ids'] ??= [];

            $enrollment_session->examSubjects()->sync($data['subject_ids']);

            return $enrollment_session->refresh();
        });
    }

    public function deleteEnrollmentSession(EnrollmentSession $enrollment_session): bool
    {
        if (!$enrollment_session->canBeDeleted()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5002);
        }

        return $this->enrollmentSessionRepository->delete($enrollment_session);
    }

    public function getFeeSettingConditions(): array
    {
        return [
            [
                'value' => 'nationality_id',
                'label' => 'Nationality',
                'options' => $this->getNationalityOptions()
            ],
            [
                'value' => 'is_hostel',
                'label' => 'Staying in hostel',
                'options' => $this->getHostelOptions()
            ]
        ];
    }

    private function getNationalityOptions(): array
    {
        return (new CountryRepository())->getAll()
            ->map(function ($country) {
                return [
                    'value' => $country->id,
                    'label' => $country->name
                ];
            })
            ->toArray();
    }

    private function getHostelOptions(): array
    {
        return [
            [
                'value' => true,
                'label' => 'Yes'
            ],
            [
                'value' => false,
                'label' => 'No'
            ]
        ];
    }
}
