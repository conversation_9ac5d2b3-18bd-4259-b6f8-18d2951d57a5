<?php

namespace App\Services\Billing;

use App\Adapters\ExcelExportAdapter;
use App\Enums\PaymentStatus;
use App\Exports\GenericExcelExportViaCollection;
use App\Helpers\ErrorCodeHelper;
use App\Models\BillingDocument;
use App\Models\GlAccount;
use App\Repositories\BillingDocumentRepository;
use App\Services\BillingDocumentPostingPrintService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BillingDocumentPostingService
{
    protected ?string $paymentDateFrom;
    protected ?string $paymentDateTo;

    protected ?string $type;
    protected ?string $subType;
    protected ?string $status;
    protected ?string $referenceNo;

    protected ?Collection $dataset;
    protected ?Collection $transformedDataset;

    public function __construct(protected BillingDocumentRepository $billingDocumentRepository)
    {
    }

    public function init()
    {
        $this->paymentDateFrom = null;
        $this->paymentDateTo = null;
        $this->type = null;
        $this->subType = null;
        $this->status = null;
        $this->referenceNo = null;
        $this->dataset = null;

        return $this;
    }

    public function query()
    {

        if (!isset($this->paymentDateFrom) || !isset($this->paymentDateTo)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36012);
        }

        $payment_date_from_utc = Carbon::parse($this->paymentDateFrom, config('school.timezone'))->startOfDay()->tz('UTC')->toDateTimeString();
        $payment_date_to_utc = Carbon::parse($this->paymentDateTo, config('school.timezone'))->endOfDay()->tz('UTC')->toDateTimeString();

        $this->dataset = $this->billingDocumentRepository
            ->with(['lineItems.billableItem', 'billTo'])
            ->getAll([
                'paid_at_from' => $payment_date_from_utc,
                'paid_at_to' => $payment_date_to_utc,
                'type' => $this->type ?? null,
                'sub_type' => $this->subType ?? null,
                'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
                'status' => $this->status ?? null,
                'reference_no' => $this->referenceNo ?? null,
                'order_by' => [
                    'document_date' => 'ASC',
                ]
            ]);

        return $this;

    }

    public function transform()
    {

        $final = [];

        foreach ($this->dataset as $d) {

            $final[] = [
                'DocNo' => '<<New>>',
                'DocDate' => Carbon::parse($d->paid_at, 'UTC')->tz(config('school.timezone'))->format('d/m/Y'),
                'DebtorCode' => $this->generateDebtorCode($d),
                'Description' => $this->generateDescription($d),
                'CurrencyCode' => $d->currency_code,
                'ToHomeRate' => 1,
                'ToDebtorRate' => 1,
                'PaymentMethod' => 'CASH AT BANK-PBB',
                'ChequeNo' => 'FPX-' . Carbon::parse($d->paid_at, 'UTC')->tz(config('school.timezone'))->format('ymd'),
                'PaymentAmt' => $d->amount_after_tax,
                'BankCharge' => 1,
                'ToBankRate' => 1,
                'DocNo2' => $d->reference_no,
            ];

        }

        $this->transformedDataset = collect($final);
        return $this;

    }

    public function generateDescription(BillingDocument $billingDocument): string
    {

        $contents = [];

        $line_items = $billingDocument->lineItems->sortBy(function ($value) {
            if ($value->gl_account_code === GlAccount::CODE_SCHOOL_FEES) {
                return '0' . $value?->billableItem?->period;
            } else {
                if ($value->gl_account_code === GlAccount::CODE_HOSTEL_FEES) {
                    return '1' . $value?->billableItem?->period;
                } else {
                    return '2' . $value->gl_account_code . $value?->billableItem?->period;
                }
            }
        });

        foreach ($line_items as $lineItem) {

            // exclude less advance or discount line items
            if ( $lineItem->offset_billing_document_id != null || $lineItem->discount_id != null ) {
                continue;
            }

            $unpaid_item = $lineItem->billableItem;

            // for those lines that need unpaid item
            if ($unpaid_item !== null) {

                switch ($lineItem->gl_account_code) {
                    case GlAccount::CODE_SCHOOL_FEES:
                        $contents[] = 'FEES-' . Carbon::parse($unpaid_item->period)->format('Y/m');
                        break;
                    case GlAccount::CODE_HOSTEL_FEES:
                        $contents[] = 'HOSTEL-' . Carbon::parse($unpaid_item->period)->format('Y/m');
                        break;
                    case GlAccount::CODE_BOOK_FEES:
                        $contents[] = 'BOOK-' . Carbon::parse($unpaid_item->period)->format('Y');
                        break;
                    case GlAccount::CODE_EXAM_FEES:
                        $contents[] = 'EXAM-' . Carbon::parse($unpaid_item->period)->format('Y/m');
                        break;
                    case GlAccount::CODE_ENROLLMENT_REGISTRATION_CONTINUING:
                        $contents[] = 'FEES-REGISTRATION-EXISTING-' . Carbon::parse($unpaid_item->period)->format('Y');
                        break;
                }

            } else {
                $description = str_replace("\r\n", '', $lineItem->description);
                $description = str_replace("\n", '', $description);

                $contents[] = 'OTHERS-' . $description;
            }

        }

        return join(" & ", $contents);

    }

    public function generateDebtorCode(BillingDocument $billingDocument): ?string
    {

        if ($billingDocument->billTo === null) {
            return null;
        }

        return '300-0' . preg_replace("/[a-zA-Z]/", '', $billingDocument->billTo->getBillToReferenceNumber());

    }

    public function generateExcelAndGetFileUrl(): ?string
    {

        if (!isset($this->transformedDataset) || !isset($this->paymentDateFrom) || !isset($this->paymentDateTo)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36012);
        }

        if ($this->transformedDataset->count() === 0) {
            return null;
        }

        $filename = 'autocount-posting-' . Carbon::parse($this->paymentDateFrom)->format('Ymd') . '-' . Carbon::parse($this->paymentDateTo)->format('Ymd') . '-' . md5(auth()->user()->id . Carbon::now()->timestamp);

        $header = [
            'DocNo',
            'DocDate',
            'DebtorCode',
            'Description',
            'CurrencyCode',
            'ToHomeRate',
            'ToDebtorRate',
            'PaymentMethod',
            'ChequeNo',
            'PaymentAmt',
            'BankCharge',
            'ToBankRate',
            'DocNo2',
        ];

        $column_format = [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_DATE_DMYSLASH,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_NUMBER,
            'G' => NumberFormat::FORMAT_NUMBER,
            'H' => NumberFormat::FORMAT_TEXT,
            'I' => NumberFormat::FORMAT_TEXT,
            'J' => NumberFormat::FORMAT_NUMBER_00,
            'K' => NumberFormat::FORMAT_NUMBER,
            'L' => NumberFormat::FORMAT_NUMBER,
            'M' => NumberFormat::FORMAT_TEXT,
        ];

        $adapter = new ExcelExportAdapter();
        $adapter->setReportBuilder(new GenericExcelExportViaCollection(data: $this->transformedDataset, header_row: $header, columns_format: $column_format));

        $service = app()->make(BillingDocumentPostingPrintService::class);

        return $service->setExportFileAdapter($adapter)
            ->setFileName($filename)
            ->generate()
            ->upload()
            ->getFileUrl();

    }


    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): BillingDocumentPostingService
    {
        $this->type = $type;
        return $this;
    }

    public function getSubType(): string
    {
        return $this->subType;
    }

    public function setSubType(string $subType): BillingDocumentPostingService
    {
        $this->subType = $subType;
        return $this;
    }

    public function isPaid(): bool
    {
        return $this->isPaid;
    }

    public function setIsPaid(bool $isPaid): BillingDocumentPostingService
    {
        $this->isPaid = $isPaid;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): BillingDocumentPostingService
    {
        $this->status = $status;
        return $this;
    }

    public function getReferenceNo(): string
    {
        return $this->referenceNo;
    }

    public function setReferenceNo(string $referenceNo): BillingDocumentPostingService
    {
        $this->referenceNo = $referenceNo;
        return $this;
    }

    public function getDataset(): Collection
    {
        return $this->dataset;
    }

    public function setDataset(Collection $dataset): BillingDocumentPostingService
    {
        $this->dataset = $dataset;
        return $this;
    }

    public function getTransformedDataset(): ?Collection
    {
        return $this->transformedDataset;
    }

    public function setTransformedDataset(?Collection $transformedDataset): BillingDocumentPostingService
    {
        $this->transformedDataset = $transformedDataset;
        return $this;
    }

    public function getPaymentDateFrom(): ?string
    {
        return $this->paymentDateFrom;
    }

    public function setPaymentDateFrom(?string $paymentDateFrom): BillingDocumentPostingService
    {
        $this->paymentDateFrom = $paymentDateFrom;
        return $this;
    }

    public function getPaymentDateTo(): ?string
    {
        return $this->paymentDateTo;
    }

    public function setPaymentDateTo(?string $paymentDateTo): BillingDocumentPostingService
    {
        $this->paymentDateTo = $paymentDateTo;
        return $this;
    }


}
