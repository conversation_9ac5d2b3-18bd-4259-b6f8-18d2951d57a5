<?php

namespace App\Services\Billing;

use App\Interfaces\IDiscountSource;
use App\Interfaces\Userable;
use App\Models\BillingDocument;
use App\Models\DiscountSetting;
use App\Repositories\BillingDocumentRepository;
use App\Repositories\DiscountSettingRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DiscountSettingService
{

    protected Userable $userable;
    protected float $basisAmount;
    protected ?float $maxAmount;
    protected float $usedAmount;
    protected string $basis;
    protected string $effectiveFromDate;
    protected string $effectiveToDate;
    protected ?string $description;
    protected bool $isActive;
    protected Collection $limitToGlAccountCodes;
    protected ?IDiscountSource $discountSource;
    protected DiscountSetting $discount;

    protected DiscountSettingRepository $discountSettingRepository;

    public function __construct(DiscountSettingRepository $discountSettingRepository)
    {
        $this->discountSettingRepository = $discountSettingRepository;
        $this->init();
    }

    public function init()
    {
        $this->maxAmount = null;
        $this->usedAmount = 0;
        $this->basisAmount = 0;
        $this->limitToGlAccountCodes = collect([]);
        $this->discountSource = null;

        return $this;
    }

    public function getAllPaginatedDiscountSettings($filters = []): LengthAwarePaginator
    {
        return $this->discountSettingRepository->getAllPaginated($filters);
    }

    public function getAllDiscountSettings($filters = []): Collection
    {
        return $this->discountSettingRepository->getAll($filters);
    }

    public function create()
    {

        $this->discount = $this->discountSettingRepository->create([
            'basis' => $this->getBasis(),
            'basis_amount' => $this->getBasisAmount(),
            'max_amount' => $this->getMaxAmount(),
            'used_amount' => 0,
            'effective_from' => $this->getEffectiveFromDate(),
            'effective_to' => $this->getEffectiveToDate(),
            'gl_account_codes' => json_encode($this->getLimitToGlAccountCodes()->toArray()),
            'source_type' => $this->getDiscountSource()?->getClass(),
            'source_id' => $this->getDiscountSource()?->getId(),
            'userable_type' => get_class($this->getUserable()),
            'userable_id' => $this->getUserable()->id,
            'description' => $this->getDescription(),
            'is_active' => false, // by default is false, must be activated manually
        ]);

        return $this;

    }

    public function getPaidInvoicesEligibleForBackApply()
    {

        if (!isset($this->discount)) {
            throw new \Exception('Discount not set.');
        }

        return app()->make(BillingDocumentRepository::class)
            ->getPaidInvoicesEligibleForBackApply([
                'period_from' => $this->discount->effective_from,
                'period_to' => $this->discount->effective_to,
                'bill_to_type' => $this->discount->userable_type,
                'bill_to_id' => $this->discount->userable_id,
                'gl_account_codes' => $this->discount->getGlAccountCodes(),
            ]);

    }

    /**
     * This function is used to increase student's advance balance when discount is back-applied to already-paid invoices.
     * @return void
     */
    public function backApplyDiscountAsAdvancePayment()
    {

        if (!isset($this->discount)) {
            throw new \Exception('Discount not set.');
        }

        // foreach eligible invoices, determine which line item is eligible for discount.
        $eligible_invoices = $this->getPaidInvoicesEligibleForBackApply();

        DB::transaction(function () use (&$eligible_invoices) {

            foreach ($eligible_invoices as $eligible_invoice) {
                $eligible_discounts = app()->make(BillingDocumentService::class)
                    ->setBillingDocument($eligible_invoice)
                    ->calculateEligibleDiscounts($this->discount)
                    ->getEligibleDiscounts();

                if (count($eligible_discounts) > 0) {

                    $line_items = [];

                    foreach ($eligible_discounts as $eligible_discount) {

                        // determine discount amount
                        $amount_to_be_advance = $eligible_discount['amount_before_tax'];
                        $original_line_item = $eligible_discount['original_line_item'];

                        // create advance invoice for that amount, and save. Should have advance transactions recorded as well.
                        $line_items[] = app()->make(BillingDocumentLineItemService::class)
                            ->setGlAccountCode($original_line_item->gl_account_code)
                            ->setDescription('Discounted amount as advance payment from ' . $eligible_invoice->reference_no . ' - ' . $original_line_item->description)
                            ->setAmountBeforeTax($amount_to_be_advance)
                            ->setCurrency($eligible_invoice->currency_code)
                            ->make();
                    }

                    $invoice_service = app()->make(BillingDocumentService::class);
                    $invoice_service->init()
                        ->setStatus(BillingDocument::STATUS_CONFIRMED)
                        ->setType(\App\Models\BillingDocument::TYPE_ADVANCE_INVOICE)
                        ->setSubType(BillingDocument::SUB_TYPE_OTHERS)
                        ->setCurrency($eligible_invoice->currency_code)
                        ->setDocumentDate(now(config('school.timezone'))->startOfDay())
                        ->setLegalEntity($eligible_invoice->legalEntity)
                        ->setBillToParty($eligible_invoice->billTo)
                        ->setPaymentTerm($eligible_invoice->paymentTerm)
                        ->setRemitToBankAccount($eligible_invoice->remitTo)
                        ->setLineItems(collect($line_items))
                        ->calculateAmountBeforeTax()
                        ->applyTax($eligible_invoice->tax)
                        ->calculatePaymentDueDate()
                        ->generateReferenceNumber()
                        ->create();

                    $billing_document = $invoice_service->getBillingDocument();

                    app()->make(PaymentService::class)
                        ->setSystemInitiatedAsPaid($billing_document)
                        ->create();

                    $invoice_service
                        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, now(config('school.timezone')));

                }
            }

        });

        return $this;

    }

    public function getUserable(): Userable
    {
        return $this->userable;
    }

    public function setUserable(Userable $userable): DiscountSettingService
    {
        $this->userable = $userable;
        return $this;
    }


    public function getBasisAmount(): float
    {
        return $this->basisAmount;
    }

    public function setBasisAmount(float $basisAmount): DiscountSettingService
    {
        $this->basisAmount = $basisAmount;
        return $this;
    }

    public function getMaxAmount(): ?float
    {
        return $this->maxAmount;
    }

    public function setMaxAmount(?float $maxAmount): DiscountSettingService
    {
        $this->maxAmount = $maxAmount;
        return $this;
    }

    public function getBasis(): string
    {
        return $this->basis;
    }

    public function setBasis(string $basis): DiscountSettingService
    {
        $this->basis = $basis;
        return $this;
    }

    public function getEffectiveFromDate(): string
    {
        return $this->effectiveFromDate;
    }

    public function setEffectiveFromDate(string $effectiveFromDate): DiscountSettingService
    {
        $this->effectiveFromDate = $effectiveFromDate;
        return $this;
    }

    public function getEffectiveToDate(): string
    {
        return $this->effectiveToDate;
    }

    public function setEffectiveToDate(string $effectiveToDate): DiscountSettingService
    {
        $this->effectiveToDate = $effectiveToDate;
        return $this;
    }

    public function getLimitToGlAccountCodes(): Collection
    {
        return $this->limitToGlAccountCodes;
    }

    public function setLimitToGlAccountCodes(Collection $limitToGlAccountCodes): DiscountSettingService
    {
        $this->limitToGlAccountCodes = $limitToGlAccountCodes;
        return $this;
    }

    public function getDiscountSource(): ?IDiscountSource
    {
        return $this->discountSource;
    }

    public function setDiscountSource(IDiscountSource $discountSource): DiscountSettingService
    {
        $this->discountSource = $discountSource;

        if (!isset($this->description)) {
            $this->setDescription($this->discountSource->getDiscountDescription());
        }
        return $this;
    }

    public function expirePastDiscounts()
    {

        $discounts = $this->discountSettingRepository->getAll([
            'past_effective_date' => true,
        ]);

        if ($discounts->count() > 0) {
            $this->discountSettingRepository->expireDiscountSettingByIds($discounts->pluck('id'));
        }

        return true;

    }

    public function getAvailableDiscountsForUserable(Userable $userable, $apply_dates = [], $specific_discount_ids = [])
    {

        $apply_dates = collect($apply_dates)->unique()->values()->toArray();

        $discounts = $this->discountSettingRepository->getAll([
            'is_active' => true,
            'id' => $specific_discount_ids,
            'apply_dates' => $apply_dates,
            'userable_id' => $userable->id,
            'userable_type' => get_class($userable),
            'order_by' => [
                'id' => 'asc'
            ]
        ]);

        $grouped_discounts = [];

        foreach ($apply_dates as $apply_date) {

            $grouped_discounts[$apply_date] = [];

            // group discount by gl account & eliminate those that already hit limit
            foreach ($discounts as $discount) {

                if (!$discount->canBeUsedForDate($apply_date)) {
                    continue;
                }

                foreach ($discount->getGlAccountCodes() as $gl_account_code) {
                    if (!isset($grouped_discounts[$apply_date][$gl_account_code])) {
                        $grouped_discounts[$apply_date][$gl_account_code] = [];
                    }

                    $grouped_discounts[$apply_date][$gl_account_code][] = $discount;
                }
            }
        }

        return $grouped_discounts;

    }

    public function getAvailableDiscountsByGroup(Collection $discounts, $apply_dates)
    {

        $apply_dates = collect($apply_dates)->unique()->values()->toArray();

        $grouped_discounts = [];

        foreach ($apply_dates as $apply_date) {

            $grouped_discounts[$apply_date] = [];

            // group discount by gl account & eliminate those that already hit limit
            foreach ($discounts as $discount) {

                if (!$discount->canBeUsedForDate($apply_date)) {
                    continue;
                }

                foreach ($discount->getGlAccountCodes() as $gl_account_code) {
                    if (!isset($grouped_discounts[$apply_date][$gl_account_code])) {
                        $grouped_discounts[$apply_date][$gl_account_code] = [];
                    }

                    $grouped_discounts[$apply_date][$gl_account_code][] = $discount;
                }
            }
        }

        return $grouped_discounts;

    }

    public function getDiscount(): DiscountSetting
    {
        return $this->discount;
    }

    public function setDiscount(DiscountSetting $discount): DiscountSettingService
    {
        $this->discount = $discount;
        return $this;
    }

    public function getIsActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $is_active): DiscountSettingService
    {
        $this->isActive = $is_active;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): DiscountSettingService
    {
        $this->description = $description;
        return $this;
    }

    public function update()
    {
        if (!isset($this->discount)) {
            throw new \Exception('Discount not set.');
        }

        if (isset($this->effectiveFromDate)) {
            $payload['effective_from'] = $this->effectiveFromDate;
        }

        if (isset($this->effectiveToDate)) {
            $payload['effective_to'] = $this->effectiveToDate;
        }

        if (isset($this->isActive)) {
            $payload['is_active'] = $this->isActive;
        }

        if (isset($this->description)) {
            $payload['description'] = $this->description;
        }

        $this->discount = $this->discountSettingRepository->update($this->getDiscount(), $payload);

        return $this;
    }
}
