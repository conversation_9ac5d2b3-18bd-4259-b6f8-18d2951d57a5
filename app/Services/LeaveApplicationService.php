<?php

namespace App\Services;

use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\LeaveApplicable;
use App\Models\Employee;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationType;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\Student;
use App\Repositories\AttendancePeriodOverrideRepository;
use App\Repositories\LeaveApplicationPeriodRepository;
use App\Repositories\LeaveApplicationRepository;
use App\Repositories\PeriodAttendanceRepository;
use App\Repositories\PeriodLabelRepository;
use App\Services\Timetable\AttendancePeriodOverrideService;
use App\Services\Timetable\StudentTimetableService;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;

class LeaveApplicationService
{
    private LeaveApplicationRepository $leaveApplicationRepository;
    private LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository;
    private AttendancePeriodOverrideRepository $attendancePeriodOverrideRepository;
    private PeriodLabelRepository $periodLabelRepository;
    private StudentTimetableService $studentTimetableService;
    private StudentService $studentService;
    private PeriodAttendanceRepository $periodAttendanceRepository;
    private AttendancePeriodOverrideService $attendancePeriodOverrideService;
    private PeriodAttendanceService $periodAttendanceService;
    private ?LeaveApplication $firstLeaveApplicationWithProof;
    private LeaveApplication $leaveApplication;
    private ?UploadedFile $proof;
    private LeaveApplicable $leaveApplicable;
    private ?string $leaveApplicationStatus;
    private LeaveApplicationType $leaveApplicationType;
    private string $reason;
    private ?string $remarks;
    private bool $isPresent;
    private ?string $fromDate;
    private ?string $toDate;
    private array $periodLabelIds = [];
    private float $averagePointDeduction;
    private float $conductPointDeduction;
    private bool $isFullDay;
    private bool $updateProof;

    public function __construct(
        LeaveApplicationRepository $leaveApplicationRepository,
        LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository,
        AttendancePeriodOverrideRepository $attendancePeriodOverrideRepository,
        PeriodLabelRepository $periodLabelRepository,
        StudentTimetableService $studentTimetableService,
        StudentService $studentService,
        PeriodAttendanceRepository $periodAttendanceRepository,
        AttendancePeriodOverrideService $attendancePeriodOverrideService,
        PeriodAttendanceService $periodAttendanceService,
    ) {
        $this->leaveApplicationRepository = $leaveApplicationRepository;
        $this->leaveApplicationPeriodRepository = $leaveApplicationPeriodRepository;
        $this->attendancePeriodOverrideRepository = $attendancePeriodOverrideRepository;
        $this->periodLabelRepository = $periodLabelRepository;
        $this->studentTimetableService = $studentTimetableService;
        $this->studentService = $studentService;
        $this->periodAttendanceRepository = $periodAttendanceRepository;
        $this->attendancePeriodOverrideService = $attendancePeriodOverrideService;
        $this->periodAttendanceService = $periodAttendanceService;
        $this->firstLeaveApplicationWithProof = null;
        $this->proof = null;
        $this->leaveApplicationStatus = null;
    }

    public function getAllPaginatedLeaveApplications($filters = []): LengthAwarePaginator
    {
        return $this->leaveApplicationRepository->getAllPaginated($filters);
    }

    public function getAllLeaveApplications($filters = []): Collection
    {
        return $this->leaveApplicationRepository->getAll($filters);
    }

    public function getProof()
    {
        return $this->proof;
    }

    public function setProof(UploadedFile $proof): LeaveApplicationService
    {
        $this->proof = $proof;
        return $this;
    }

    public function setFirstLeaveApplicationWithProof(LeaveApplication $first_leave_application_with_proof): LeaveApplicationService
    {
        $this->firstLeaveApplicationWithProof = $first_leave_application_with_proof;
        return $this;
    }

    public function setLeaveApplicable(LeaveApplicable $leave_applicable): LeaveApplicationService
    {
        $this->leaveApplicable = $leave_applicable;
        return $this;
    }

    public function setLeaveApplicationStatus($leave_application_status): LeaveApplicationService
    {
        $this->leaveApplicationStatus = $leave_application_status;
        return $this;
    }

    public function setLeaveApplicationType(LeaveApplicationType $leave_application_type)
    {
        $this->leaveApplicationType = $leave_application_type;
        return $this;
    }

    public function setReason($reason)
    {
        $this->reason = $reason;
        return $this;
    }

    public function setRemarks($remarks)
    {
        $this->remarks = $remarks;
        return $this;
    }

    public function setFromDate($from_date)
    {
        $this->fromDate = $from_date;
        return $this;
    }

    public function setToDate($to_date)
    {
        $this->toDate = $to_date;
        return $this;
    }

    public function setIsPresent(bool $is_present)
    {
        $this->isPresent = $is_present;
        return $this;
    }

    public function setPeriodLabelIds(array $period_label_ids)
    {
        $this->periodLabelIds = $period_label_ids;
        return $this;
    }

    public function setIsFullDay(bool $is_full_day)
    {
        $this->isFullDay = $is_full_day;
        return $this;
    }

    public function setUpdateProof(bool $update_proof)
    {
        $this->updateProof = $update_proof;
        return $this;
    }

    public function createLeaveApplication(): ?LeaveApplication
    {
        if (!isset($this->fromDate) || !isset($this->toDate) || !isset($this->periodLabelIds)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50003);
        }

        $apply_leave_data = [
            'leave_applicable_type' => $this->leaveApplicable->getLeaveApplicableClass(),
            'leave_applicable_id' => $this->leaveApplicable->getLeaveApplicableId(),
            'status' => LeaveApplicationStatus::PENDING->value,
            'leave_application_type_id' => $this->leaveApplicationType->id,
            'reason' => $this->reason,
            'remarks' => $this->remarks,
            'is_present' => $this->isPresent,
            'average_point_deduction' => $this->averagePointDeduction,
            'conduct_point_deduction' => $this->conductPointDeduction,
            'is_full_day' => $this->isFullDay,
        ];

        DB::transaction(function () use ($apply_leave_data) {
            $this->leaveApplication = $this->leaveApplicationRepository->create($apply_leave_data);

            // for bulk creation, all leave applications will share the same proof
            if ($this->proof) {
                if (!$this->firstLeaveApplicationWithProof) {
                    $this->leaveApplication->replaceMedia(LeaveApplication::PROOF, $this->proof);
                    $this->setFirstLeaveApplicationWithProof($this->leaveApplication);
                } else {
                    $media = $this->firstLeaveApplicationWithProof->getFirstMedia(LeaveApplication::PROOF);
                    $media->copy($this->leaveApplication, LeaveApplication::PROOF);
                }
            }

            $this->syncLeaveApplicationPeriods();

        });

        return $this->leaveApplication;
    }

    public function updateLeaveApplication(): ?LeaveApplication
    {
        if (!isset($this->fromDate) || !isset($this->toDate) || !isset($this->periodLabelIds)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50003);
        }

        DB::transaction(function () use (&$leave_application) {

            $payload = [
                'leave_application_type_id' => $this->leaveApplicationType->id,
                'reason' => $this->reason,
                'remarks' => $this->remarks,
                'is_present' => $this->isPresent,
                'average_point_deduction' => $this->averagePointDeduction,
                'conduct_point_deduction' => $this->conductPointDeduction,
                'is_full_day' => $this->isFullDay,
            ];

            $this->leaveApplication = $this->leaveApplicationRepository->update($this->leaveApplication, $payload);

            $this->syncLeaveApplicationPeriods();

            if ($this->updateProof === true) {
                if ($this->proof && is_file($this->proof)) {
                    $this->leaveApplication->replaceMedia(LeaveApplication::PROOF, $this->proof);
                } else {
                    $this->leaveApplication->clearMediaCollection(LeaveApplication::PROOF);
                }
            }
        });
        return $this->leaveApplication;
    }

    public function updateStatus(array $leave_application_ids, $new_status)
    {
        // validate status change
        $validation = $this->validateStatusChange($leave_application_ids, $new_status);

        if (collect($validation)->pluck('status')->unique()->contains(false)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50007, ['errors' => collect($validation)->pluck('error_messages')->flatten()->join("\n")]);
        }

        $leave_applications = collect($validation)->pluck('leave_application');

        DB::transaction(function () use ($leave_applications, $new_status) {
            $this->leaveApplicationRepository->getQuery([
                'id' => $leave_applications->pluck('id')->toArray(),
            ])->update([
                'status' => $new_status,
            ]);

            $this->attendancePeriodOverrideService
                ->setLeaveApplications($leave_applications)
                ->setLeaveApplicationNewStatus($new_status)
                ->setEmployee($this->employee)
                ->updateIndividualOverrideByLeaveApplications();

            $this->periodAttendanceService
                ->setLeaveapplications($leave_applications)
                ->setLeaveApplicationNewStatus($new_status)
                ->updatePeriodAttendanceByLeaveApplications();
        });

        return $this;
    }

    public function deleteLeaveApplication(): void
    {
        // cannot delete leave application when it's being used in student attendance period override, attendances or period attendances
        if ($this->leaveApplication->attendancePeriodOverride()->exists() ||
            $this->leaveApplication->attendances()->exists() ||
            $this->leaveApplication->periodAttendance()->exists()
        ) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50005);
        }

        DB::transaction(function () {
            $this->leaveApplicationPeriodRepository->batchDeleteByLeaveApplicationId($this->leaveApplication->id);
            $this->leaveApplicationRepository->delete($this->leaveApplication);
        });
    }

    public function getLeaveApplication(): LeaveApplication
    {
        return $this->leaveApplication;
    }

    public function setLeaveApplication(LeaveApplication $leave_application): LeaveApplicationService
    {
        $this->leaveApplication = $leave_application;
        return $this;
    }

    public function syncLeaveApplicationPeriods()
    {
        if (count($this->periodLabelIds) == 0) {
            return;
        }
        $period_label_list = $this->periodLabelRepository->getAll([
            'id' => $this->periodLabelIds,
        ])
            ->keyBy('id')
            ->all();

        $this->leaveApplicationPeriodRepository->batchDeleteByLeaveApplicationId($this->leaveApplication->id);

        $leave_application_periods_to_be_created = [];

        $dates = CarbonPeriod::create($this->fromDate, $this->toDate);

        foreach ($dates as $date) {
            foreach (collect($this->periodLabelIds)->sort()->values() as $period_label_id) {
                $leave_application_periods_to_be_created[] = [
                    'date' => $date->toDateString(),
                    'leave_application_id' => $this->leaveApplication->id,
                    'period' => $period_label_list[$period_label_id]->period,
                ];
            }
        }

        $this->leaveApplicationPeriodRepository->insert($leave_application_periods_to_be_created);
    }

    public function validatePeriodLabelsByPeriodGroup(array $period_label_ids, PeriodGroup $period_group)
    {
        $periodLabelRepository = app()->make(PeriodLabelRepository::class);
        $period_labels = $periodLabelRepository->getAll([
            'period_group_id' => $period_group->id,
            'id' => $period_label_ids,
        ]);

        if (count($period_labels) !== count($period_label_ids)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50002, ['period_group_name' => $period_group->getFormattedTranslations('name')]);
        }
    }

    public function validateLeaveApplicationPeriodExistence($is_create)
    {
        $period = PeriodLabel::whereIn('id', $this->periodLabelIds)->pluck('period')->unique()->toArray();

        $filters = [
            'status_not_equal' => LeaveApplicationStatus::REJECTED->value,
            'leave_application_date_from' => $this->fromDate,
            'leave_application_date_to' => $this->toDate,
            'period' => $period,
            'leave_applicable_type' => $this->leaveApplicable->getLeaveApplicableClass(),
            'leave_applicable_id' => $this->leaveApplicable->getLeaveApplicableId(),
        ];
        if (!$is_create) {
            $filters['id_not_equal'] = $this->leaveApplication->id;
        }

        $leave_application_count = $this->leaveApplicationRepository->getCount($filters);

        if ($leave_application_count > 0) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50008, ['student' => $this->leaveApplicable->getUserNumber(), 'from' => $this->fromDate, 'to' => $this->toDate]);
        }

        return $this;
    }

    public function getConductPointDeduction(): float
    {
        return $this->conductPointDeduction;
    }

    public function setConductPointDeduction(float $conductPointDeduction): LeaveApplicationService
    {
        $this->conductPointDeduction = $conductPointDeduction;
        return $this;
    }

    public function getAveragePointDeduction(): float
    {
        return $this->averagePointDeduction;
    }

    public function setAveragePointDeduction(float $averagePointDeduction): LeaveApplicationService
    {
        $this->averagePointDeduction = $averagePointDeduction;
        return $this;
    }

    public function determineIsFullDay(PeriodGroup $periodGroup, array $period_label_ids)
    {
        return $periodGroup->number_of_periods == count($period_label_ids);
    }

    public function setEmployee(Employee $employee)
    {
        $this->employee = $employee;
        return $this;
    }

    public function validateStatusChange(array $leave_application_ids, $new_status)
    {
        $leave_applications = $this->leaveApplicationRepository->getAll([
            'id' => $leave_application_ids,
            'status_not_equal' => $new_status,
            'includes' => ['leaveApplicationPeriods', 'leaveApplicable', 'attendancePeriodOverride', 'periodAttendance'],
        ]);

        $attendance_period_overrides = $this->attendancePeriodOverrideRepository->getAll([
            'attendance_recordable_type' => array_unique($leave_applications->pluck('leave_applicable_type')->toArray()),
            'attendance_recordable_id' => array_unique($leave_applications->pluck('leave_applicable_id')->toArray()),
            'period' => array_unique($leave_applications->pluck('leaveApplicationPeriods')->flatten()->pluck('date')->toArray()),
            'has_leave_application' => false, // only retrieve individual overrides that were manually created, no leave application = created manually
        ])
            ->groupBy(['attendance_recordable_type', 'attendance_recordable_id', 'period'])
            ->all();

        $result = [];
        foreach ($leave_applications as $leave_application) {

            $status = true;
            $error_messages = [];
            $attendance_period_override = [];

            if ($leave_application->status->value === LeaveApplicationStatus::APPROVED->value && $new_status === LeaveApplicationStatus::REJECTED->value ||
                $leave_application->status->value === LeaveApplicationStatus::REJECTED->value && $new_status === LeaveApplicationStatus::APPROVED->value) {
                $error_messages[] = "Please change leave application status to PENDING first.";
            }

            $leave_application_periods_group_by_date = $leave_application->leaveApplicationPeriods->groupBy('date');
            foreach ($leave_application_periods_group_by_date as $date => $leave_application_periods) {
                // pending/rejected to approved + student
                if ($this->shouldCreateIndividualOverride($leave_application, $new_status)) {
                    $student_attendance_period_override = $attendance_period_overrides[$leave_application->leave_applicable_type][$leave_application->leave_applicable_id][$date] ?? null;

                    if (isset($student_attendance_period_override)) {
                        $error_messages[] = "Found existing attendance period override that was manually created on {$date}, please delete it first.";
                        $attendance_period_override[] = $student_attendance_period_override->first();
                    }
                }
            }

            if (count($error_messages) > 0) {
                $status = false;
            }

            $result[] = [
                'leave_application' => $leave_application,
                'status' => $status,
                'error_messages' => collect($error_messages),
                'attendance_period_overrides' => collect($attendance_period_override),
            ];
        }

        return $result;
    }

    public function shouldCreateIndividualOverride(LeaveApplication $leave_application, $new_status)
    {

        return $leave_application->status->value === LeaveApplicationStatus::PENDING->value &&
            $new_status === LeaveApplicationStatus::APPROVED->value &&
            $leave_application->leave_applicable_type == Student::class;
    }

    public function validateLeaveApplicationIsEditable(LeaveApplication $leave_application)
    {
        if ($leave_application->status == LeaveApplicationStatus::APPROVED) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50006);
        }
        return true;
    }

    public function getLeaveApplicationHistory($leave_applicable_type, $leave_applicable_id)
    {
        $filters = [
            'leave_applicable_type' => $leave_applicable_type,
            'leave_applicable_id' => $leave_applicable_id,
            'status' => $this->leaveApplicationStatus,
            'includes' => ['leaveApplicationType'],
            'has_periods' => true,
        ];

        if (isset($this->fromDate)) {
            $filters['leave_application_period_date_from'] = $this->fromDate;
        }
        if (isset($this->toDate)) {
            $filters['leave_application_period_date_to'] = $this->toDate;
        }

        $leave_applications = $this->leaveApplicationRepository->getAll($filters);

        $data = [];

        foreach ($leave_applications as $leave_application) {
            $leave_application_periods_group_by_date = $leave_application->leaveApplicationPeriods->groupBy('date');
            foreach ($leave_application_periods_group_by_date as $date => $leave_application_periods) {
                $data[] = [
                    "date" => $date,
                    "leave_application_type" => $leave_application->leaveApplicationType->getTranslations('name'),
                    "periods" => $leave_application_periods->pluck('period')->toArray(),
                    "leave_application_id" => $leave_application->id,
                    "reason" => $leave_application->reason,
                    "status" => $leave_application->status->value,
                ];
            }
        }

        if ($leave_applicable_type == Student::class) {
            $period_attendances = $this->periodAttendanceRepository->getAll([
                'student_id' => $leave_applicable_id,
                'status' => [PeriodAttendanceStatus::ABSENT->value, PeriodAttendanceStatus::LATE->value],
                'date_from' => $this->fromDate ?? null,
                'date_to' => $this->toDate ?? null,
                'without_leave_application' => true,
                'has_mark_deduction' => true,
            ]);

            $period_attendances_group_by_date_and_status = $period_attendances->groupBy(['date', 'status']);
            foreach ($period_attendances_group_by_date_and_status as $date => $period_attendances_group_by_status) {
                foreach ($period_attendances_group_by_status as $status => $period_attendances) {
                    // pluck period attendance's period
                    $period_attendances_periods = $period_attendances->pluck('period')->toArray();

                    $data[] = [
                        "date" => $date,
                        "periods" => $period_attendances_periods,
                        "leave_application_type" => PeriodAttendanceStatus::getPeriodAttendanceTranslation($status),
                        "leave_application_id" => null,
                        "reason" => null,
                        "status" => null,
                    ];
                }
            }
        }

        return collect($data)->sortByDesc('date')->values()->toArray();
    }
}
