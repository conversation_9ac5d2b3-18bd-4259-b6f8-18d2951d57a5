<?php

namespace App\Services;

use App\Enums\LibraryBookLoanPaymentStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Enums\LibraryPaymentTransactionPaymentStatus;
use App\Enums\LibraryPenaltyPaymentMethod;
use App\Enums\PushNotificationClickAction;
use App\Factories\PushNotificationFactory;
use App\Helpers\ErrorCodeHelper;
use App\Models\Book;
use App\Models\Config;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\WalletTransaction;
use App\Repositories\LibraryBookLoanRepository;
use App\Repositories\LibraryPaymentTransactionRepository;
use App\Services\PushNotification\PushNotificationService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class LibraryBookLoanService
{
    protected ConfigService $configService;
    protected LibraryBookLoanRepository $bookLoanRepository;
    protected BookService $bookService;

    protected LibraryBookLoan $bookLoan;
    protected bool $isLost;

    protected ?string $remarks;
    protected float $penaltyLostAmount;
    protected float $penaltyOverdueAmount;
    protected float $penaltyPaidAmount;
    protected WalletService $walletService;
    protected WalletTransactionService $walletTransactionService;
    protected string $paymentMethod;
    protected LibraryPaymentTransactionRepository $libraryPaymentTransactionRepository;
    protected float $penaltyTotalFineAmount;
    protected WalletTransaction $paymentWalletTransaction;
    protected Book $book;
    protected LibraryMember $member;
    protected string $dueDate;
    protected PushNotificationService $pushNotificationService;


    public function __construct(
        LibraryBookLoanRepository $book_loan_repository,
        ConfigService $config_service,
        BookService $book_service,
        WalletService $wallet_service,
        WalletTransactionService $wallet_transaction_service,
        LibraryPaymentTransactionRepository $library_payment_transaction_repository,
    ) {
        $this->bookLoanRepository = $book_loan_repository;
        $this->configService = $config_service;
        $this->bookService = $book_service;
        $this->walletService = $wallet_service;
        $this->walletTransactionService = $wallet_transaction_service;
        $this->libraryPaymentTransactionRepository = $library_payment_transaction_repository;

        $this->penaltyLostAmount = 0;
        $this->penaltyOverdueAmount = 0;
        $this->penaltyPaidAmount = 0;
        $this->penaltyTotalFineAmount = 0;
        $this->remarks = null;
    }

    public function getAllBookLoans($filters = []): Collection
    {
        return $this->bookLoanRepository->getAll($filters);
    }

    public function getAllPaginatedBookLoans($filters = []): LengthAwarePaginator
    {
        return $this->bookLoanRepository->getAllPaginated($filters);
    }

    public function createBookLoan(): LibraryBookLoan|Model
    {
        return $this->bookLoanRepository->create([
            'member_id' => $this->member->id,
            'book_id' => $this->book->id,
            'loan_date' => now(),
            'due_date' => $this->dueDate,
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ]);
    }

    public function getOverdueBookLoans(): Collection
    {
        $filters = [
            'is_overdue' => 1,
            'loan_status' => LibraryBookLoanStatus::BORROWED
        ];

        return $this->bookLoanRepository->getAll($filters);
    }

    public function calculateOverdueAmount(LibraryBookLoan $book_loan): float
    {
        $fine_per_day = $this->getFinePerDay($book_loan->member);

        if ($fine_per_day <= 0) {
            return 0;
        }

        $late_days = now(config('school.timezone'))->startOfDay()
            ->diffInDays($book_loan->due_date);

        if ($late_days <= 0) {
            return 0;
        }

        $overdue_amount = round($fine_per_day * $late_days, 2);

//        if ($overdue_amount > $fine_config['library_max_fine_per_book']) {
//            return $fine_config['library_max_fine_per_book'];
//        }

        return $overdue_amount;
    }

    public function getFinePerDay(LibraryMember $member): float
    {
        $config_key = null;

        switch ($member->type) {
            case LibraryMemberType::STUDENT:
                $config_key = Config::LIBRARY_FINE_PER_DAY_STUDENT;

                if ($member->is_librarian) {
                    $config_key = Config::LIBRARY_FINE_PER_DAY_LIBRARIAN;
                }
                break;
            case LibraryMemberType::EMPLOYEE:
                $config_key = Config::LIBRARY_FINE_PER_DAY_EMPLOYEE;
                break;
            case LibraryMemberType::OTHERS:
                $config_key = Config::LIBRARY_FINE_PER_DAY_OTHER;
                break;
        }

        $config = $this->configService->getConfig($config_key);

        return $config['value'];
    }

    public function isMemberAllowToBorrowBooks(Model $member, int $total_current_book_loan): bool
    {
        $member_borrow_limit = $member->borrow_limit;

        if ($total_current_book_loan > $member_borrow_limit) {
            return false;
        }

        if ($this->configCanBorrowWithUnreturnedBooks($member)) {
            return true;
        }

        $total_active_book_loan = $member->activeBookLoans->count();
        $total_book_loan = bcadd($total_active_book_loan, $total_current_book_loan, 0);

        if (bccomp($member_borrow_limit, $total_book_loan, 0) >= 0) {
            return true;
        }

        return false;
    }

    public function configCanBorrowWithUnreturnedBooks(Model $member): bool
    {
        switch ($member->type) {
            case LibraryMemberType::STUDENT:
                $config_key = Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT;
                break;
            case LibraryMemberType::EMPLOYEE:
                $config_key = Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE;
                break;
            case LibraryMemberType::OTHERS:
                $config_key = Config::BORROW_WITH_UNRETURNED_BOOK_OTHER;
                break;
            default:
                return false;
        }

        if ($member->is_librarian) {
            $config_key = Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN;
        }

        $config = $this->configService->getConfig($config_key);

        return $config['value'];
    }

    public function extend($due_date): Model
    {
        $this->validateExtend($due_date);

        return $this->bookLoanRepository->update($this->bookLoan, [
            'due_date' => $due_date,
        ]);
    }

    public function validateExtend(string $due_date): bool
    {
        // new due date must be after old due date
        if (Carbon::parse($due_date)->isBefore(Carbon::parse($this->bookLoan->due_date))) {
            throw new \Exception("New due date {$due_date} must be after old due date {$this->bookLoan->due_date->format('Y-m-d')}.");
        }

        return true;
    }

    public function checkIsBookIsBorrowed(): static
    {
        if ($this->bookLoan->loan_status !== LibraryBookLoanStatus::BORROWED) {
            throw new \Exception("Book [No:" . $this->bookLoan->book->book_no . "] is not borrowed.");
        }
        return $this;
    }

    public function payPenalty(): static
    {
        if ($this->penaltyPaidAmount == 0) {
            $this->updatePayment(LibraryBookLoanPaymentStatus::PAID);
            return $this;
        }

        if ($this->paymentMethod === LibraryPenaltyPaymentMethod::WALLET->value) {
            $this->processWalletPayment();
            $this->sendPaymentNotification();
        }

        $this->updatePayment(LibraryBookLoanPaymentStatus::PAID);
        $this->createPaymentTransaction();

        return $this;
    }

    public function checkIsOverPaid(): void
    {
        if (bccomp($this->penaltyPaidAmount, $this->penaltyTotalFineAmount, 2) > 0) {
            throw new \Exception("Book [No:" . $this->bookLoan->book->book_no . "] fine is overpaid.");
        }
    }

    public function processWalletPayment(): static
    {
        if ($this->paymentMethod !== LibraryPenaltyPaymentMethod::WALLET->value) {
            return $this;
        }

        // penalty paid amount should be specified by user, not calculated.
        if ($this->penaltyPaidAmount == 0) {
            return $this;
        }

        $user = $this->bookLoan->member->userable->user;
        $userable = $this->bookLoan->member->userable;

        $wallet_transaction_service = $this->walletTransactionService
            ->setUser($user)
            ->setWalletByCurrencyCode(config('school.currency_code'));

        $this->paymentWalletTransaction = $wallet_transaction_service
            ->setWalletTransactable($this->bookLoan)
            ->setUserable($userable)
            ->chargeWalletTransaction([
                'user' => $user,
                'currency' => config('school.currency_code'),
                'amount_after_tax' => $this->penaltyPaidAmount,
                'amount_before_tax' => $this->penaltyPaidAmount,
                'merchant_name' => 'Library',
                'order_reference_no' => uniqid(),
                'remark' => $this->remarks,
                'description' => 'Library book loan penalty'
            ]);

        return $this;
    }

    public function createPaymentTransaction(): void
    {
        $this->libraryPaymentTransactionRepository->create([
            'book_loan_id' => $this->bookLoan->id,
            'member_id' => $this->bookLoan->member_id,
            'payment_method' => $this->paymentMethod,
            'payment_amount' => $this->penaltyPaidAmount,
            'penalty_payment_status' => LibraryPaymentTransactionPaymentStatus::PAID,
            'payment_date' => now(),
            'description' => $this->remarks,
            'wallet_transaction_id' => isset($this->paymentWalletTransaction) ? $this->paymentWalletTransaction->id : null,
        ]);
    }

    public function sendPaymentNotification(): void
    {
        $user = Arr::get($this->bookLoan, 'member.userable.user');

        if (!$user) {
            return;
        }

        if (!$user->canReceivePushNotification()) {
            return;
        }

        $this->pushNotificationService = PushNotificationFactory::getInstance($user->push_notification_platform);

        $this->pushNotificationService
            ->setTitle('Library Book Loan Penalty Paid')
            ->setBody('Total ' . config('school.currency_code') . ' ' . number_format($this->penaltyPaidAmount, 2) . ' is deducted from your wallet for library book loan penalty')
            ->setClickAction(PushNotificationClickAction::FLUTTER_NOTIFICATION_CLICK)
            ->setUser($user)
            ->setToken($user->push_notification_token)
            ->queuedSend();

    }

    /**
     * Must call this function at the end of book return processing to save the book loan model to DB
     * @return $this
     */
    public function processBookReturn(): static
    {
        if ($this->isLost) {
            $this->processBookLost();
        } else {
            $this->processBookReturned();
        }

        $this->bookLoan->remarks = $this->remarks ?? null;
        $this->bookLoan->penalty_total_fine_amount = $this->penaltyTotalFineAmount;

        return $this;

    }

    public function save(): LibraryBookLoan
    {
        $this->bookLoan->save();
        return $this->bookLoan;
    }

    public function setBookLoan(LibraryBookLoan|Model $bookLoan): LibraryBookLoanService
    {
        $this->bookLoan = $bookLoan;
//        $this->penaltyOverdueAmount = $this->bookLoan->penalty_overdue_amount;
        return $this;
    }

    public function setIsLost(bool $isLost): LibraryBookLoanService
    {
        $this->isLost = $isLost;
        return $this;
    }

    public function setRemarks(?string $remarks): LibraryBookLoanService
    {
        $this->remarks = $remarks;
        return $this;
    }

    public function setPenaltyLostAmount(float $penaltyLostAmount): LibraryBookLoanService
    {
        $this->penaltyLostAmount = $penaltyLostAmount;
        return $this;
    }

    public function setPaymentMethod($payment_method): static
    {
        $this->paymentMethod = $payment_method;

        return $this;
    }

    public function setPenaltyPaidAmount(float $penaltyPaidAmount): LibraryBookLoanService
    {
        $this->penaltyPaidAmount = $penaltyPaidAmount;

        return $this;
    }

    public function setPenaltyOverdueAmount(float $penaltyOverdueAmount): LibraryBookLoanService
    {
        $this->penaltyOverdueAmount = $penaltyOverdueAmount;
        return $this;
    }

    public function calcPenaltyTotalFineAmount(): static
    {
        $this->penaltyTotalFineAmount = bcadd($this->penaltyLostAmount, $this->penaltyOverdueAmount, 2);

        return $this;
    }

    public function checkWalletHasSufficientBalance(string $method, array $input_book_loans): bool
    {
        if ($method !== LibraryPenaltyPaymentMethod::WALLET->value) {
            return true;
        }

        $total_fine_amount = round(array_sum(array_column($input_book_loans, 'penalty_paid_amount')), 2);

        if (bccomp($total_fine_amount, 0, 2) <= 0) {
            return true;
        }

        $first_book_loan = $this->findBookLoan($input_book_loans[0]['id']);
        $member = $first_book_loan->member;

        if ($member->type == LibraryMemberType::OTHERS) {
            throw new \Exception("Member dont have any wallet.");
        }

        $enough_balance = $this->walletService
            ->setWalletByUserAndCurrencyCode($member->userable->user, config('school.currency_code'))
            ->hasEnoughBalance($total_fine_amount);

        if (!$enough_balance) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1002), 1002);
        }

        return true;
    }

    public function findBookLoan($id): ?Model
    {
        return $this->bookLoanRepository->find($id);
    }

    public function setBook(Book $book): LibraryBookLoanService
    {
        $this->book = $book;
        return $this;
    }

    public function getMember(): LibraryMember
    {
        return $this->member;
    }

    public function setMember(LibraryMember $member): LibraryBookLoanService
    {
        $this->member = $member;
        return $this;
    }

    public function setDueDate(string $dueDate): LibraryBookLoanService
    {
        $this->dueDate = $dueDate;
        return $this;
    }

    public function setPaymentWalletTransaction(WalletTransaction $paymentWalletTransaction): LibraryBookLoanService
    {
        $this->paymentWalletTransaction = $paymentWalletTransaction;
        return $this;
    }

    protected function updatePayment($payment_status)
    {
        $this->checkIsOverPaid();

        $this->bookLoan->penalty_paid_amount = $this->penaltyPaidAmount ?: 0;
        $this->bookLoan->penalty_payment_status = $payment_status;

        return $this;
    }

    protected function processBookLost(): static
    {
        $this->bookService->markBookAsLost($this->bookLoan->book);

        $this->bookLoan->lost_date = now();
        $this->bookLoan->loan_status = LibraryBookLoanStatus::LOST;
        $this->bookLoan->penalty_lost_amount = $this->penaltyLostAmount;
        $this->bookLoan->penalty_overdue_amount = $this->penaltyOverdueAmount;
        $this->bookLoan->penalty_total_fine_amount = $this->penaltyTotalFineAmount;

        return $this;
    }

    protected function processBookReturned(): static
    {
        $this->bookService->markBookAsAvailable($this->bookLoan->book);

        $this->bookLoan->loan_status = LibraryBookLoanStatus::RETURNED;
        $this->bookLoan->penalty_overdue_amount = $this->penaltyOverdueAmount;
        $this->bookLoan->penalty_total_fine_amount = $this->penaltyTotalFineAmount;
        $this->bookLoan->return_date = now();

        return $this;

    }
}
