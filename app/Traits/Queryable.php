<?php

namespace App\Traits;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;

/**
 * Trait Queryable
 * Place all common query methods in this trait
 */
trait Queryable
{
    protected array|string $with = [];

    protected array|string $withCount = [];

    protected array|string $orderBy = [];

    protected int $per_page = self::PAGINATION_PER_PAGE;

    public function findOrFail($id): ?Model
    {
        return $this->getQuery()->findOrFail($id);
    }

    protected function getQuery($filters = []): Builder
    {
        $model = resolve($this->getModelClass());

        if (!empty($filters['per_page'])) {
            $this->per_page = $filters['per_page'];
        }

        if (isset($filters['includes']) && !empty($filters['includes'])) {
            $this->with($filters['includes']);
        }
        if (isset($filters['includes_count']) && !empty($filters['includes_count'])) {
            $this->withCount($filters['includes_count']);
        }

        $query = $model->query()
            ->when(!empty($this->with), function (Builder $query) {
                $query->with($this->with);
            })
            ->when(!empty($this->withCount), function (Builder $query) {
                $query->withCount($this->withCount);
            })
            ->when(!empty($filters['order_by']), function (Builder $query) use ($filters, $model) {
                $order_by = $filters['order_by'];
                $this->setupOrderBy($query, $order_by, $this->getModelClass());
            });

        return $query;
    }

    public function with(array|string $with): self
    {
        $this->with = $with;

        return $this;
    }

    public function withCount(array|string $with_count): self
    {
        $this->withCount = $with_count;

        return $this;
    }

    protected function setupOrderBy(Builder $query, $order_by, $model, $prefix = ''): void
    {
        $model = resolve($model);

        if (!$prefix) {
            $prefix = $model->getTable() . '.';
        }

        $translatable_field = $model->translatable ?? [];

        if (!is_array($order_by)) {
            $order_by = [$order_by => 'asc'];
        }

        foreach ($order_by as $key => $value) {
            /**
             * To handle non-associative array of order_by
             * E.g. order_by = ['name', 'code']
             */
            if (is_int($key)) {
                if (!Schema::hasColumn($model->getTable(), $value)) {
                    continue;
                }
                if (in_array($value, $translatable_field)) {
                    $value = $this->getTranslatableKey($value);
                }
                $query->orderBy($prefix . $value);
            } else {
                /**
                 * To handle associative array of order_by
                 */

                /**
                 * To handle associative array with locale specified
                 * E.g.
                 * $order_by = [
                 *   'name' => [
                 *      'en' => 'asc',
                 *      'zh' => 'desc',
                 *   ];
                 */
                if (is_array($value)) {
                    foreach ($value as $locale => $sort_direction) {
                        if (!Schema::hasColumn($model->getTable(), $key)) {
                            continue;
                        }
                        if (in_array($key, $translatable_field)) {
                            $key = $this->getTranslatableKey($key, $locale);
                        }

                        $query->orderBy($prefix . $key, $sort_direction);
                    }
                } else {
                    /**
                     * To handle associative array without locale specified
                     * E.g.
                     * $order_by = [
                     *   'name' => 'asc',
                     * ]
                     */
                    if (!Schema::hasColumn($model->getTable(), $key)) {
                        continue;
                    }
                    if (in_array($key, $translatable_field)) {
                        $key = $this->getTranslatableKey($key);
                    }
                    $query->orderBy($prefix . $key, $value);
                }
            }
        }
    }

    public function getTranslatableKey($key, $locale = null): string
    {
        if ($locale) {
            return $key . '->' . $locale;
        }

        return $key . '->' . app()->getLocale();
    }

    public function orderBy($filters = []): self
    {
        $this->orderBy = $filters;

        return $this;
    }

    public function find($id): Model|Collection|null
    {
        return $this->getQuery()->find($id);
    }

    public function first($filters = [], $should_fail_if_not_found = true): ?Model
    {
        $query = $this->getQuery($filters);

        if ($should_fail_if_not_found) {
            return $query->firstOrFail();
        } else {
            return $query->first();
        }
    }

    abstract public function getAll(array $filters = []): Collection;

    abstract public function getAllPaginated(array $filters = []): LengthAwarePaginator;

    protected function getModelTableName(): string
    {
        return resolve($this->getModelClass())->getTable();
    }
}
