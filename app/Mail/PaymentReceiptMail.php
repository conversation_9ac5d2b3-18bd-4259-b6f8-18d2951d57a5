<?php

namespace App\Mail;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentReceiptMail extends Mailable
{
    use Queueable, SerializesModels;

    private Payment $payment;
    private string $receiptPdfUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(
        Payment $payment,
        string $receipt_pdf_url
    )
    {
        $this->payment = $payment;
        $this->receiptPdfUrl = $receipt_pdf_url;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: config('app.name') . ': Official Receipt',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            htmlString: 'Your payment of ' . number_format($this->payment->amount_received, 2) . ' is successful. Click here to download your receipt:<br /><a href="' . $this->receiptPdfUrl . '" target="_blank">' . $this->receiptPdfUrl . '</a>',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
