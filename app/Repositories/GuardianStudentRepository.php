<?php

namespace App\Repositories;

use App\Models\Enrollment;
use App\Models\GuardianStudent;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class GuardianStudentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return GuardianStudent::class;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['studenable_id']), function (Builder $query) use ($filters) {
                $query->where('studenable_id', $filters['studenable_id']);
            })
            ->when(isset($filters['studenable_type']), function (Builder $query) use ($filters) {
                $query->where('studenable_type', $filters['studenable_type']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteByStudenable(Student|Enrollment|Model $studenable, $non_direct_dependant_only = false): void
    {
        $query = GuardianStudent::query()->where('studenable_type', get_class($studenable))
            ->where('studenable_id', $studenable->id);

        if ($non_direct_dependant_only) {
            $query->where('is_direct_dependant', false);
        }

        $query->delete();
    }
}
