<?php

namespace App\Repositories;

use App\Models\ConductSettingTeacher;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ConductSettingTeacherRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ConductSettingTeacher::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['conduct_setting_id']), function (Builder $query) use ($filters) {
                $query->where('conduct_setting_id', $filters['conduct_setting_id']);
            })
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                $query->where('employee_id', $filters['employee_id']);
            })
            ->when(isset($filters['is_homeroom_teacher']), function (Builder $query) use ($filters) {
                $query->where('is_homeroom_teacher', $filters['is_homeroom_teacher']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
