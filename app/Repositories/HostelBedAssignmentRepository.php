<?php

namespace App\Repositories;

use App\Enums\HostelBlockType;
use App\Models\Employee;
use App\Models\HostelBedAssignment;
use App\Models\Student;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class HostelBedAssignmentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelBedAssignment::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getCheckoutRecord(array $filters = [])
    {
        $filters = optional($filters);

        $includes = [
            'bed.hostelRoom.hostelBlock',
            'assignable' => function ($query) use ($filters) {
                $query->select(['id', 'name', 'student_number', 'nric', 'phone_number']);
                $query->with([
                    'latestPrimaryClass' => function ($query) use ($filters) {
                        $query->with('semesterClass.classModel.grade');
                        $query->where('semester_setting_id', $filters['semester_setting_id']);
                    },
                    'guardians' => function ($query) {
                        $query->select(['id', 'name']);
                        $query->orderBy('id', 'asc');
                    },
                ]);
            },
        ];

        $data = $this->getQuery()
            ->with($includes)
            ->select(['id', 'assignable_id', 'assignable_type', 'start_date', 'end_date', 'hostel_room_bed_id', 'remarks'])
            ->where('assignable_type', Student::class)
            ->whereYear('start_date', $filters['year'])
            ->whereYear('end_date', $filters['year'])
            ->get();

        return $data
            ->transform(function ($item) {
                $assignable = $item->assignable;
                $bed = $item->bed;

                $item['class_name'] = $assignable?->latestPrimaryClass?->semesterClass?->classModel->getTranslations('name');
                $item['grade_name'] = $assignable?->latestPrimaryClass?->semesterClass?->classModel?->grade->getTranslations('name');
                $item['guardian'] = $assignable?->guardians?->first()?->getTranslations('name');
                $item['student_name'] = $assignable?->getTranslations('name');
                $item['phone_number'] = $assignable?->phone_number;
                $item['student_number'] = $assignable?->student_number;
                $item['nric'] = $assignable?->nric;

                $item['block_name'] = $bed?->hostelRoom?->hostelBlock->getTranslation('name', app()->getLocale());
                $item['bed_name'] = $bed?->name;
                $item['room_name'] = $bed?->hostelRoom?->name;

                unset($item->assignable, $item->assignable_type, $item->assignable_id, $item->bed);

                return $item;
            })
            ->sortBy(function ($item) {
                return [
                    $item['block_name'],
                    $item['room_name'],
                    $item['bed_name'],
                ];
            })
            ->values();
    }

    public function getChangeRoomRecord(array $filters = [])
    {
        $filters = optional($filters);

        $includes = [
            'bed.hostelRoom',
            'previousBed.hostelRoom',
            'assignable' => function ($query) use ($filters) {
                $query->select(['id', 'name', 'student_number']);
                $query->with([
                    'latestPrimaryClass' => function ($query) use ($filters) {
                        $query->with('semesterClass.classModel.grade');
                        $query->where('semester_setting_id', $filters['semester_setting_id']);
                    },
                ]);
            },
        ];

        $data = $this->getQuery()
            ->with($includes)
            ->select(['id', 'assignable_id', 'assignable_type', 'start_date', 'end_date', 'hostel_room_bed_id', 'previous_hostel_room_bed_id'])
            ->where('assignable_type', Student::class)
            ->whereYear('start_date', $filters['year'])
            ->whereNotNull('previous_hostel_room_bed_id')
            ->orderBy('start_date', 'asc')
            ->get();

        $data->transform(function ($item) {
            $class_name = $item->assignable?->latestPrimaryClass?->semesterClass?->classModel->getTranslations('name');
            $grade_name = $item->assignable?->latestPrimaryClass?->semesterClass?->classModel?->grade->getTranslations('name');

            $student_name = $item->assignable->getTranslations('name');
            $student_number = $item->assignable->student_number;

            $room_name = $item->bed?->hostelRoom?->name;
            $bed_name = $item->bed?->name;

            $previous_room_name = $item->previousBed?->hostelRoom?->name;
            $previous_bed_name = $item->previousBed?->name;

            $check_in_date = isset($item->start_date) ? Carbon::parse($item->start_date)->toDateString() : $item->start_date;
            $check_out_date = isset($item->end_date) ? Carbon::parse($item->end_date)->toDateString() : $item->end_date;

            return [
                'previous_room_name' => $previous_room_name,
                'previous_bed_name' => $previous_bed_name,
                'student_number' => $student_number,
                'student_name' => $student_name,
                'room_name' => $room_name,
                'bed_name' => $bed_name,
                'class_name' => $class_name,
                'grade_name' => $grade_name,
                'check_in_date' => $check_in_date,
                'check_out_date' => $check_out_date,
            ];
        });

        return $data;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['hostel_room_bed_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['hostel_room_bed_id'])) {
                    $query->whereIn('hostel_room_bed_id', $filters['hostel_room_bed_id']);
                } else {
                    $query->where('hostel_room_bed_id', $filters['hostel_room_bed_id']);
                }
            })
            ->when(Arr::has($filters, 'end_date'), function (Builder $query) use ($filters) {
                if (is_null($filters['end_date'])) {
                    $query->whereNull('end_date');
                } else {
                    $query->where('end_date', $filters['end_date']);
                }

            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                if ($filters['type'] == HostelBlockType::EMPLOYEE) {
                    $query->whereHasMorph('assignable', [Employee::class]);
                } else {
                    $query->whereHasMorph('assignable', [Student::class]);
                }
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->whereHasMorph('assignable', [Student::class], function (Builder $morph_query) use ($filters) {
                    $morph_query->where('id', $filters['student_id']);
                });
            })
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                $query->whereHasMorph('assignable', [Employee::class], function (Builder $morph_query) use ($filters) {
                    $morph_query->where('id', $filters['employee_id']);
                });
            })
            ->when(isset($filters['assigned_by']), function (Builder $query) use ($filters) {
                $query->where('assigned_by', $filters['assigned_by']);
            });
    }
}
