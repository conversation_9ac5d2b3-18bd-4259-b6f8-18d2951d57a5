<?php

namespace App\Repositories;

use App\Models\BillingDocument;
use App\Models\BillingDocumentAdvanceTransaction;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AdvanceTransactionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return BillingDocumentAdvanceTransaction::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['billable_type']) && isset($filters['billable_id']), function (Builder $query) use ($filters) {
                $query->where('billable_type', $filters['billable_type'])
                    ->where('billable_id', $filters['billable_id']);
            })
            ->when(isset($filters['currency_code']), function (Builder $query) use ($filters) {
                $query->where('currency_code', $filters['currency_code']);
            })
            ->when(isset($filters['advance_invoice_id']), function (Builder $query) use ($filters) {
                $query->where('advance_invoice_id', $filters['advance_invoice_id']);
            })
            ->when(isset($filters['used_in_invoice_id']), function (Builder $query) use ($filters) {
                $query->where('used_in_invoice_id', $filters['used_in_invoice_id']);
            })
            ->when(isset($filters['empty_used_in_invoice_id']), function (Builder $query) {
                $query->whereNull('used_in_invoice_id');
            });

    }

    public function getAdvanceBalances(array $filters = []): Collection
    {
        return $this
            ->getQuery($filters)
            ->selectRaw('gl_account_code, advance_invoice_id, SUM(amount_before_tax) as balance_before_tax')
            ->groupBy('gl_account_code', 'advance_invoice_id')
            ->get();
    }

    public function isAdvanceInvoicedUsed(BillingDocument $advance_invoice)
    {
        return BillingDocumentAdvanceTransaction::where('advance_invoice_id', $advance_invoice->id)->whereNotNull('used_in_invoice_id')->exists();
    }

}
