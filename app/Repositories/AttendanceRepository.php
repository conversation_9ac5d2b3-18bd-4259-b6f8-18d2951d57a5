<?php

namespace App\Repositories;

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\Contractor;
use App\Models\PeriodAttendance;
use App\Models\PeriodAttendanceAndLeaveApplication;
use App\Models\Student;
use App\Models\Timeslot;
use App\Models\Timetable;
use App\Models\UserableView;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class AttendanceRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Attendance::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->where('date', $filters['date']);
            })
            ->when(isset($filters['date_from']), function (Builder $query) use ($filters) {
                $query->where('date', '>=', $filters['date_from']);
            })
            ->when(isset($filters['date_to']), function (Builder $query) use ($filters) {
                $query->where('date', '<=', $filters['date_to']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['check_in_status']), function (Builder $query) use ($filters) {
                if (is_array($filters['check_in_status'])) {
                    $query->whereIn('check_in_status', $filters['check_in_status']);
                } else {
                    $query->where('check_in_status', $filters['check_in_status']);
                }
            })
            ->when(isset($filters['check_out_status']), function (Builder $query) use ($filters) {
                if (is_array($filters['check_out_status'])) {
                    $query->whereIn('check_out_status', $filters['check_out_status']);
                } else {
                    $query->where('check_out_status', $filters['check_out_status']);
                }
            })
            // attendance_recordable_type without attendance_recordable_id
            ->when(!isset($filters['attendance_recordable_id']) && isset($filters['attendance_recordable_type']), function (Builder $query) use ($filters) {
                if (is_array($filters['attendance_recordable_type'])) {
                    $query->whereIn('attendance_recordable_type', $filters['attendance_recordable_type']);
                } else {
                    $query->where('attendance_recordable_type', $filters['attendance_recordable_type']);
                }
            })
            ->when(isset($filters['attendance_recordable_id']) && isset($filters['attendance_recordable_type']), function (Builder $query) use ($filters) {
                $query->where('attendance_recordable_type', $filters['attendance_recordable_type']);
                if (is_array($filters['attendance_recordable_id'])) {
                    $query->whereIn('attendance_recordable_id', $filters['attendance_recordable_id']);
                } else {
                    $query->where('attendance_recordable_id', $filters['attendance_recordable_id']);
                }
            })
            ->when(isset($filters['semester_setting_id']) || isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->whereHasMorph('attendanceRecordable', [Student::class], function (Builder $query) use ($filters) {
                    $query->whereHas('classes', function (Builder $query) use ($filters) {
                        if (isset($filters['semester_setting_id'])) {
                            $query->where('semester_setting_id', $filters['semester_setting_id']);
                        }
                        if (isset($filters['semester_class_id'])) {
                            $query->where('semester_class_id', $filters['semester_class_id']);
                        }

                        $query->where('is_active', true);
                    });
                });
            })
            ->when(isset($filters['latest_semester_setting_id']) || isset($filters['latest_semester_class_id']) || isset($filters['grade_id']),
                function (Builder $query) use ($filters) {
                    $query->whereHasMorph('attendanceRecordable', [Student::class], function (Builder $query) use ($filters) {
                        $query->whereHas('classes', function (Builder $query) use ($filters) {
                            $query->where('is_latest_class_in_semester', true);

                            if (isset($filters['latest_semester_setting_id'])) {
                                $query->where('semester_setting_id', $filters['latest_semester_setting_id']);
                            }
                            if (isset($filters['latest_semester_class_id'])) {
                                $query->where('semester_class_id', $filters['latest_semester_class_id']);
                            }
                            if (isset($filters['grade_id'])) {
                                $query->whereRelation('semesterClass.classModel', 'grade_id', $filters['grade_id']);
                            }
                            if (isset($filters['class_type'])) {
                                $query->where('class_type', $filters['class_type']);
                            }
                        });
                    });
                })
            ->when(isset($order_by['name']) || isset($order_by['number']), function (Builder $query) use ($order_by) {
                $query
                    ->join('userable_views', function (JoinClause $join) {
                        $join->on('userable_views.userable_id', '=', 'attendances.attendance_recordable_id')
                            ->whereColumn('userable_views.userable_type', '=', 'attendances.attendance_recordable_type');
                    })
                    ->when(isset($order_by['name']), function (Builder $query,) use ($order_by) {
                        $this->setupOrderBy($query, ['name' => $order_by['name']], UserableView::class, 'userable_views.');
                    })
                    ->when(isset($order_by['number']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, ['number' => $order_by['number']], UserableView::class, 'userable_views.');
                    });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function batchDeleteByIds(array $ids = []): bool
    {
        if (count($ids) === 0) {
            return true;
        }

        return $this->getQuery(['id' => $ids])->delete();
    }

    public function getAttendanceSummaryData(array $filters): mixed
    {
        $semester_class_ids = $filters['semester_class_ids'];

        $student_relationship = [
            'latestPrimaryClassBySemesterSettings' => function ($query) use ($semester_class_ids) {
                $query->select('semester_class_id', 'student_id', 'semester_setting_id');
                $query->whereIn('semester_class_id', $semester_class_ids);
                $query->with([
                    'semesterClass' => function ($query) {
                        $query->select('id', 'class_id', 'semester_setting_id');
                        $query->with([
                            'classModel' => function ($query) {
                                $query->select('id', 'name');
                            },
                        ]);
                    },
                ]);
            },
        ];

        // Find all students under each semester class
        $students_grouped_by_primary_classes = Student::with($student_relationship)
            ->select('id', 'name')
            ->whereHas('latestPrimaryClassBySemesterSettings', function ($query) use ($semester_class_ids) {
                $query->whereIn('semester_class_id', $semester_class_ids);
            })
            ->get()
            ->groupBy('latestPrimaryClassBySemesterSettings.0.semesterClass.classModel.name')
            ->sortKeys(); // sort by class name

        $result = [];

        $student_ids = $students_grouped_by_primary_classes->flatten()->pluck('id')->unique()->values();

        // find all attendance records between the date_from and date_to
        $attendances_all = Attendance::select('id', 'attendance_recordable_id', 'attendance_recordable_type', 'date', 'check_in_status', 'status')
            ->whereHasMorph(
                'attendanceRecordable',
                [Student::class],
            )
            ->whereIn('attendance_recordable_id', $student_ids->toArray())
            ->whereBetween('date', [$filters['date_from'], $filters['date_to']])
            ->orderBy('date', 'asc') // order by date
            ->get();

        foreach ($students_grouped_by_primary_classes as $students) {

            // find all attendance records between the date_from and date_to
            $attendances = $attendances_all
                ->whereIn('attendance_recordable_id', $students->pluck('id')->toArray())
                ->values();

            $current_class = $students->first()->latestPrimaryClassBySemesterSettings->first()->semesterClass->classModel;

            foreach ($attendances->groupBy('date') as $date => $attendances) {
                $late_count = $attendances->where('check_in_status', AttendanceCheckInStatus::LATE->value)->count();
                $present_count = $attendances->where('status', AttendanceStatus::PRESENT)->count() - $late_count;
                $absent_count = $attendances->where('status', AttendanceStatus::ABSENT)->count();

                $current_response = [
                    'date' => $date,
                    'id' => $current_class->id,
                    'name' => $current_class->getTranslations('name'),
                    'records' => [
                        'LATE' => $late_count,
                        'PRESENT' => $present_count,
                        'ABSENT' => $absent_count,
                    ],
                ];

                if (!isset($result[$date])) {
                    $result[$date] = [];
                }

                $result[$date][] = $current_response;
            }
        }

        return $result;
    }

    public function getClassAttendanceTakingData(array $filters)
    {
        $date = $filters['date'];
        $semester_class_id = $filters['semester_class_id'];

        // get the english word for $date
        $day = Carbon::parse($date)->locale('en')->translatedFormat('l');
        $day = strtoupper($day);

        $time_table_relationship = [
            'semesterClass.classModel',
            'periodGroup' => function ($query) use ($day) {
                $query->select('id');
                $query->with([
                    'periodLabels' => function ($query) {
                        $query->where('is_attendance_required', true);
                        $query->orderBy('period');
                    },
                    'periods' => function ($query) use ($day) {
                        $query->where('day', $day);
                    },
                ]);
            },
        ];

        // get time table for this semester_class
        $time_table = Timetable::with($time_table_relationship)
            ->where('semester_class_id', $semester_class_id)
            ->where('is_active', true)
            ->first();

        if (!$time_table) {
            return [
                'class' => null,
                'headers' => [],
                'attendance_data' => [],
            ];
        }


        $period_labels_key_by_period = $time_table->periodGroup->periodLabels->keyBy('period');

        $periods_key_by_period = $time_table->periodGroup->periods->keyBy('period');

        $headers = $period_labels_key_by_period
            ->intersectByKeys($periods_key_by_period)
            ->map(function ($label, $key) use ($periods_key_by_period) {
                $period = $periods_key_by_period[$key];

                return [
                    'from_time' => $period->from_time,
                    'to_time' => $period->to_time,
                    'period' => $period->period,
                    'name' => $label->getTranslations('name'),
                ];
            });

        $period_attendances = PeriodAttendance::query()
            ->leftJoin('timeslots', function (JoinClause $join) {
                $join->on('timeslots.id', '=', 'period_attendances.timeslot_id')
                    ->where('period_attendances.timeslot_type', Timeslot::class);
            })
            // TODO: Need to implement for timeslot override
//            ->leftJoin('timeslot_override', function (JoinClause $join) {
//                $join->on('timeslot_override.id', '=', 'period_attendances.timeslot_id')
//                    ->where('period_attendances.timeslot_type', TimeslotOverride::class);
//            })
            // TODO: This will not work for timeslot_override
            ->join('periods', 'periods.id', '=', 'timeslots.period_id')
            ->join('students', 'students.id', '=', 'period_attendances.student_id')
            // TODO: This will not work for timeslot_override
            ->join('timetables', 'timetables.id', '=', 'timeslots.timetable_id')
            ->leftJoin('attendances', function ($join) use ($date) {
                $join->on('attendances.attendance_recordable_id', '=', 'students.id')
                    ->on('attendances.date', '=', 'period_attendances.date')
                    ->where('attendances.attendance_recordable_type', '=', Student::class);
            })
            ->where('timeslots.day', $day)
            ->where('timetables.semester_class_id', $semester_class_id)
            ->where('period_attendances.date', $date)
            ->orderBy('students.name', 'asc')
            ->orderBy('periods.period', 'asc')
            ->select([
                'attendances.status AS school_attendance_status',
                'period_attendances.*',
                'periods.period',
                DB::raw('timeslots.day AS day'),
                'students.name as student_name',
                'students.student_number',
                'periods.from_time',
                'periods.to_time',
            ])
            ->get()
            ->groupBy('student_id');

        $attendance_data = [];

        foreach ($period_attendances as $grouped_period_attendance) {
            $first_attendance = $grouped_period_attendance->first();

            $result = [];

            $result['student_id'] = $first_attendance->student_id;
            $result['student_name'] = json_decode($first_attendance->student_name, true);
            $result['student_number'] = $first_attendance->student_number;
            $result['school_attendance_status'] = $first_attendance->school_attendance_status;

            $attendances_key_by_period = $grouped_period_attendance->keyBy('period');

            $period_attendance = [];

            foreach ($headers as $period => $attendance) {
                if (isset($attendances_key_by_period[$period])) {
                    $period_attendance[$period] = $attendances_key_by_period[$period]->status;
                } else {
                    $period_attendance[$period] = null;
                }
            }

            $result['period_attendances'] = $period_attendance;

            $attendance_data[] = $result;
        }

        return [
            'class' => $time_table->semesterClass->classModel,
            'headers' => $headers->values(),
            'attendance_data' => $attendance_data,
        ];
    }

    public function getAllForContractorsDailyAttendanceReport($filters)
    {

        $locale = app()->getLocale();

        $semester_setting_id = $filters['semester_setting_id'];
        unset($filters['semester_setting_id']);     // don't pass this to getQuery as getQuery uses semester_setting_id to filter student related stuff

        if ($filters['type'] === 'attend_only') {
            $query = $this->getQuery($filters);

            $query
                ->selectRaw('card_id, contractors.contractor_number, contractors.name->>\'' . $locale . '\' AS contractor_name, classes.name->>\'' . $locale . '\' AS class_name, check_in_datetime, check_out_datetime, COALESCE(attendances.status, \'ABSENT\') AS attendance_status')
                ->join('contractors', function ($join) {
                    $join->on('contractors.id', '=', 'attendances.attendance_recordable_id')
                        ->where('attendance_recordable_type', Contractor::class);
                });
        } else {
            $query = Contractor::query();

            $query
                ->selectRaw('card_id, contractors.contractor_number, contractors.name->>\'' . $locale . '\' AS contractor_name, classes.name->>\'' . $locale . '\' AS class_name, check_in_datetime, check_out_datetime, COALESCE(attendances.status, \'ABSENT\') AS attendance_status')
                ->leftJoin('attendances', function ($join) use ($filters) {
                    $join->on('attendances.attendance_recordable_id', '=', 'contractors.id')
                        ->where('attendance_recordable_type', Contractor::class)
                        ->where('date', $filters['date']);
                });
        }

        $query->join('class_subject_contractor', 'class_subject_contractor.contractor_id', '=', 'contractors.id')
            ->join('class_subjects', 'class_subjects.id', '=', 'class_subject_contractor.class_subject_id')
            ->join('semester_classes', function ($join) use ($semester_setting_id) {
                $join->on('semester_classes.id', '=', 'class_subjects.semester_class_id')
                    ->where('semester_classes.semester_setting_id', $semester_setting_id);
            })
            ->join('classes', function ($join) {
                $join->on('classes.id', '=', 'semester_classes.class_id')
                    ->where('classes.type', ClassType::SOCIETY->value);
            });

        if (app()->getLocale() === 'zh') {
            $query->orderByRaw('convert_to(contractors.name->>\'' . $locale . '\', \'GBK\') ASC');
        } else {
            $query->orderBy('contractor_name', 'ASC');
        }

        return $query->get();

    }

    public function getAttendanceRecordableIDByAbsentCount($filters): Collection
    {
        return $this->getQuery($filters)
            ->select('attendance_recordable_type', 'attendance_recordable_id', DB::raw('COUNT(*) as count'))
            ->groupBy(['attendance_recordable_type', 'attendance_recordable_id'])
            ->having(DB::raw('COUNT(*)'), '>=', $filters['absent_count'])
            ->get()
            ->groupBy('attendance_recordable_type');
    }

    public function getStudentAttendanceMarkDeductionData(array $filters)
    {
        $semester_class_ids = $filters['semester_class_ids'];
        $date_from = $filters['date_from'];
        $date_to = $filters['date_to'];

        // Get all students in the selected semester classes
        $students = Student::query()
            ->join('latest_primary_class_by_semester_setting_views', 'latest_primary_class_by_semester_setting_views.student_id', '=', 'students.id')
            ->join('semester_classes', 'semester_classes.id', '=', 'latest_primary_class_by_semester_setting_views.semester_class_id')
            ->join('classes', 'classes.id', '=', 'semester_classes.class_id')
            ->whereIn('latest_primary_class_by_semester_setting_views.semester_class_id', $semester_class_ids)
            ->select([
                'students.id as student_id',
                'students.name as student_name',
                'students.student_number',
                'latest_primary_class_by_semester_setting_views.semester_class_id',
                'classes.id as class_id',
                'classes.name as class_name',
            ])
            ->get();

        $raw_attendances = PeriodAttendanceAndLeaveApplication::query()
            ->where('leave_applicable_type', Student::class)
            ->whereIn('leave_applicable_id', $students->pluck('student_id')->toArray())
            ->whereBetween('date', [$date_from, $date_to])
            ->orderBy('date', 'asc')
            ->orderBy('period', 'asc')
            ->get();

        $students = $students->keyBy('student_id');

        $locale = app()->getLocale();

        $attendances = $raw_attendances
            ->map(function ($attendance) use ($students) { // combine each attendance with class_detail
                $student_class_detail = $students[$attendance->leave_applicable_id];

                return (object) array_merge(
                    $attendance->toArray(),
                    $student_class_detail->toArray()
                );
            })
            ->groupBy(['semester_class_id', 'student_id', 'date']);

        $response = $attendances->map(function ($students_in_class) use ($locale) {

            // get class info
            $first_record_for_class = $students_in_class->first()->first()->first();
            $class_name_json = json_decode($first_record_for_class->class_name, true);
            $locale_class_name = $class_name_json[$locale] ?? $class_name_json['en'] ?? 'Unknown Class';

            $student_results = $students_in_class
                ->map(function ($dates_for_student) use ($locale, $class_name_json) {
                    // get student info
                    $first_record_for_student = $dates_for_student->first()->first();
                    $student_name_json = json_decode($first_record_for_student->student_name, true);
                    $student_number = $first_record_for_student->student_number;

                    $attendances_by_date = $dates_for_student->map(function ($daily_attendances, $current_date) use ($locale) {
                        // get counts for each type of attendance
                        $daily_summary = $daily_attendances->reduce(function ($summary, $attendance) use ($locale) {
                            $key = $attendance->leave_name[$locale] ?? $attendance->leave_name['en'] ?? 'Unknown Leave';
                            $reason = $attendance->reason;
                            $deduction = $attendance->average_mark_deduction ?? 0;

                            if (!isset($summary[$key])) {
                                $summary[$key] = [
                                    'count' => 1, // Start count at 1
                                    'reason' => $reason,
                                    'average_point_deduction' => $deduction,
                                    'status' => $attendance->leave_application_type_id ? $key : PeriodAttendanceStatus::getTranslatedPeriodAttendance($attendance->status),
                                ];
                            } else {
                                $summary[$key]['count']++;
                            }

                            return $summary;
                        }, []);

                        $formatted_daily_summary = [];

                        foreach ($daily_summary as $type => $data) {
                            $formatted_daily_summary[] = [
                                'date' => $current_date,
                                'type' => $data['status'],
                                'base_deduct_average_point' => $data['average_point_deduction'],
                                'periods' => $data['count'],
                                'total_deduct_average_point' => (float) $data['average_point_deduction'] * $data['count'],
                                'reason' => $data['reason'],
                            ];
                        }
                        return $formatted_daily_summary;
                    })->flatten(1); // flatten because grouped by date

                    return [
                        'student_name' => $student_name_json,
                        'student_number' => $student_number,
                        'class' => $class_name_json,
                        'attendances' => $attendances_by_date->all(),
                    ];
                })
                ->sortBy(function ($item) use ($locale) {
                    return $item['student_name'][$locale] ?? array_values($item['student_name'])[0] ?? '';
                })
                ->values()
                ->all();

            return [
                'class_name' => $locale_class_name,
                'students' => $student_results,
            ];
        })
            ->sortBy('class_name')
            ->values();

        return $response;
    }
}
