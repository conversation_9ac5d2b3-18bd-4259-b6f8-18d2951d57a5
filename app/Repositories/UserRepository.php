<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\UserableView;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class UserRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return User::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $order_by = optional(optional($filters)['order_by']);

        return parent::getQuery($filters)
            ->select('users.*')
            ->when(isset($filters['exact_email']), function (Builder $query) use ($filters) {
                $query->where('email', $filters['exact_email']);
            })
            ->when(isset($filters['exact_phone_number']), function (Builder $query) use ($filters) {
                $query->where('phone_number', $filters['exact_phone_number']);
            })
            ->when(isset($filters['email']), function (Builder $query) use ($filters) {
                $query->where('email', 'ILIKE', "%" . $filters['email'] . "%");
            })
            ->when(isset($filters['phone_number']), function (Builder $query) use ($filters) {
                $query->where('phone_number', 'ILIKE', "%" . $filters['phone_number'] . "%");
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['role_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('roles', 'id', $filters['role_id']);
            })
            ->when(isset($filters['user_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('userables', 'userable_type', $filters['user_type']);
            })
            ->when(isset($filters['user_number']), function (Builder $query) use ($filters) {
                $query->whereRelation('userables', 'number', 'ILIKE', "%" . $filters['user_number'] . "%");
            })
            ->when(isset($filters['user_name']), function (Builder $query) use ($filters) {
                $query->whereHas('userables', function ($userable_query) use ($filters) {
                    $userable_query->where(function ($query) use ($filters) {
                        $query->whereTranslations('name', $filters['user_name'], 'ILIKE', true);
                    });
                });
            })
            // SORTING
            ->when($order_by['userable'], function (Builder $query) use ($order_by) {
                $query
                    ->join('userable_views', 'userable_views.user_id', '=', 'users.id')
                    ->when($order_by['userable'], function (Builder $query, $order) {
                        $this->setupOrderBy($query, $order, UserableView::class, 'userable_views.');
                    });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    // Not in use after userable implementation
//    public function getAllPaginatedByMultiFieldSearch(array $filters = []): LengthAwarePaginator
//    {
//        return $this
//            ->with('userable')
//            ->getQuery($filters)
//            ->when(isset($filters['search']), function(Builder $query) use ($filters) {
//                $query//->where($this->getTranslatableKey('name'), 'ILIKE', "%{$filters['search']}%")
//                    ->where('email', 'ILIKE', "%{$filters['search']}%")
//                    ->orWhereHasMorph('userable', [Student::class, Employee::class], function (Builder $query, string $type) use ($filters) {
//                        $column_name = resolve($type)->getUserNumberColumnName();
//
//                        $query->when($column_name, function ($query) use ($column_name, $filters) {
//                            $query->where($column_name, 'ILIKE', "%{$filters['search']}%");
//                        });
//                    });
//            })
//            ->paginate($this->per_page);
//    }

    public function firstByFilter(array $filters)
    {
        $filters = optional($filters);

        return $this->getQuery()
            ->when($filters['email'], function (Builder $query, $email) {
                $query->where('email', 'ILIKE', $email);
            })
            ->when($filters['phone_number'], function (Builder $query, $phone_number) {
                $query->where('phone_number', 'ILIKE', $phone_number);
            })
            ->first();
    }

    public function removePushNotificationSettingsByToken(string $token): void
    {
        $this->getQuery()
            ->where('push_notification_token', $token)
            ->update([
                'push_notification_token' => null,
                'push_notification_platform' => null,
            ]);
    }
}
