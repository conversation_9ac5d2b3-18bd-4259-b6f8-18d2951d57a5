<?php

namespace App\Repositories;

use App\Models\Guardian;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class GuardianRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Guardian::class;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['nric']), function ($query) use ($filters) {
                $query->where('nric', $filters['nric']);
            })
            ->when(isset($filters['name']), function ($query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['email']), function ($query) use ($filters) {
                $query->where('email', 'ILIKE', "%{$filters['email']}%");
            })
            ->when(isset($filters['phone_number']), function ($query) use ($filters) {
                $query->where('phone_number', 'ILIKE', "%{$filters['phone_number']}%");
            });

    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getGuardianIdsByDirectDependant(array $student_ids): array
    {
        return $this->getQuery()
            ->whereHas('directDependants', function ($query) use ($student_ids) {
                $query->whereIn('students.id', $student_ids);
            })
            ->pluck('id')
            ->unique()
            ->values()
            ->all();
    }
}
