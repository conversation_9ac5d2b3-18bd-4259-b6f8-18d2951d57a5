<?php

namespace App\Repositories;

use App\Models\Role;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class RoleRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Role::class;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', "ILIKE", "%". $filters['name'] ."%");
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getAllRolesByDefaultModels(array $default_models): Collection
    {
        return $this->getQuery()
            ->whereHas('models', function ($query) use ($default_models) {
                $query->whereIn('model', $default_models);
            })
            ->get();
    }
}
