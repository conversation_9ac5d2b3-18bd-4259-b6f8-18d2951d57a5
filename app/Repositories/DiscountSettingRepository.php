<?php

namespace App\Repositories;

use App\Models\DiscountSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class DiscountSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return DiscountSetting::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query->when(isset($filters['past_effective_date']), function (Builder $query) {
            $query->where('effective_to', '<', now(config('school.timezone'))->toDateString());
        })
            ->when(isset($filters['apply_date']), function (Builder $query) use ($filters) {
                $query->where(function ($q) use ($filters) {
                    $q->where('effective_to', '>=', $filters['apply_date'])
                        ->where('effective_from', '<=', $filters['apply_date']);
                });
            })
            ->when(isset($filters['apply_dates']), function (Builder $query) use ($filters) {
                $query->where(function ($q) use ($filters) {
                    foreach ($filters['apply_dates'] as $apply_date) {
                        $q->orWhere(function ($q2) use (&$apply_date) {
                            $q2->where('effective_to', '>=', $apply_date)
                                ->where('effective_from', '<=', $apply_date);
                        });
                    }
                });
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['userable_type']), function (Builder $query) use ($filters) {
                $query->where('userable_type', $filters['userable_type']);
            })
            ->when(isset($filters['userable_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['userable_id'])) {
                    $query->whereIn('userable_id', $filters['userable_id']);
                } else {
                    $query->where('userable_id', $filters['userable_id']);
                }
            })
            ->when(isset($filters['basis']), function (Builder $query) use ($filters) {
                $query->where('basis', $filters['basis']);
            })
            ->when(isset($filters['effective_from']), function (Builder $query) use ($filters) {
                $query->where('effective_from', '>=', $filters['effective_from']);
            })
            ->when(isset($filters['effective_to']), function (Builder $query) use ($filters) {
                $query->where('effective_to', '<=', $filters['effective_to']);
            })
            ->when(isset($filters['gl_account_code']), function (Builder $query) use ($filters) {
                $gl_account_code = $filters['gl_account_code'];
                $query->whereRaw(
                    'EXISTS (SELECT 1 FROM jsonb_array_elements_text(gl_account_codes) AS gl_account_code WHERE gl_account_code::text LIKE ?)',
                    ["%$gl_account_code%"],
                );
            })
            ->when(isset($filters['source_type']), function (Builder $query) use ($filters) {
                $query->where('source_type', $filters['source_type']);
            })
            ->when(isset($filters['still_available']), function (Builder $query) {
                $query->where(function ($q) {
                    $q->whereColumn('max_amount', '!=', 'used_amount')
                        ->orWhereNull('max_amount');
                });
            })
            ->when(isset($filters['source_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['source_id'])) {
                    $query->whereIn('source_id', $filters['source_id']);
                } else {
                    $query->where('source_id', $filters['source_id']);
                }
            })
            ->when(!empty($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            });


        if (!(isset($filters['order_by']['id']) || (isset($filters['order_by']) && is_array($filters['order_by']) && in_array('id', $filters['order_by'])))) {
            // Always do this to prevent pagination issue
            $query->orderByDesc('id');
        }

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this
            ->getQuery($filters)
            ->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function expireDiscountSettingByIds($ids)
    {

        DiscountSetting::whereIn('id', $ids)
            ->update([
                'is_active' => false,
                'updated_at' => now(),
            ]);

    }

}
