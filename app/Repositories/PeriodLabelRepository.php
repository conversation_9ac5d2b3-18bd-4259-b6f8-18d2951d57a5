<?php

namespace App\Repositories;

use App\Models\PeriodLabel;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PeriodLabelRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PeriodLabel::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters) 
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if(is_array($filters['id'])){
                    $query->whereIn('id', $filters['id']);
                }else{
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['period_group_id']), function (Builder $query) use ($filters) {
                if(is_array($filters['period_group_id'])){
                    $query->whereIn('period_group_id', $filters['period_group_id']);
                }else{
                    $query->where('period_group_id', $filters['period_group_id']);
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)->paginate($this->per_page);
    }
}
