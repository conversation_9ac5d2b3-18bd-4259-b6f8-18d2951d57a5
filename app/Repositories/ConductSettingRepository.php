<?php

namespace App\Repositories;

use App\Models\ConductSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ConductSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ConductSetting::class;
    }

    protected function getQuery($filters = []): Builder
    {
        if (!in_array('conductSettingTeachers', $filters['includes'] ?? [])) {
            $filters['includes'][] = 'conductSettingTeachers';
        }

        return parent::getQuery($filters)
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['grading_scheme_id']), function (Builder $query) use ($filters) {
                $query->where('grading_scheme_id', $filters['grading_scheme_id']);
            })
            ->when(isset($filters['active_grading_scheme']), function (Builder $query) use ($filters) {
                $query->whereRelation('gradingScheme', 'is_active', $filters['active_grading_scheme']);
            })
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                $query->whereHas('conductSettingTeachers', function ($query) use ($filters) {
                    $query->where('employee_id', $filters['employee_id'])
                        ->where('is_active', true);
                });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getFirstBySemesterClassId(int $semester_class_id): ?ConductSetting
    {
        return $this
            ->getQuery([
                'semester_class_id' => $semester_class_id,
                'includes' => ["semesterSetting", "semesterClass", "gradingScheme"]
            ])->first();
    }
}
