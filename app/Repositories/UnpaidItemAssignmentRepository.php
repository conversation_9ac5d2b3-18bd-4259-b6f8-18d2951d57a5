<?php

namespace App\Repositories;

use App\Models\UnpaidItemAssignment;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class UnpaidItemAssignmentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return UnpaidItemAssignment::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $this->with($filters['includes'] ?? []);

        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['product_id']), function (Builder $query) use ($filters) {
                $query->where('product_id', $filters['product_id']);
            })
            ->when(isset($filters['apply_at_from']), function (Builder $query) use ($filters) {
                $query->where('apply_at', '>=', $filters['apply_at_from']);
            })
            ->when(isset($filters['apply_at_to']), function (Builder $query) use ($filters) {
                $query->where('apply_at', '<=', $filters['apply_at_to']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['can_apply']), function (Builder $query) use ($filters) {
                if ($filters['can_apply']) {
                    $query->where('apply_at', '<=', Carbon::now('UTC'));
                } else {
                    $query->where('apply_at', '>', Carbon::now('UTC'));
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

}
