<?php

namespace App\Repositories;

use App\Models\Card;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;

class CardRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Card::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {

        return parent::getQuery($filters)
            ->when(isset($filters['userable_id']) && isset($filters['userable_type']),
            function (Builder $query) use ($filters) {
                $query->where('userable_id', $filters['userable_id'])
                    ->where('userable_type', $filters['userable_type']);
            })
            ->when(isset($filters['card_number']) , function (Builder $query) use ($filters) {
                if (is_array($filters['card_number'])) {
                    $query->whereIn('card_number', $filters['card_number']);
                } else {
                    $query->where('card_number', $filters['card_number']);
                }
            })
            ->when(isset($filters['card_number2']), function (Builder $query) use ($filters) {
                $query->where('card_number2', $filters['card_number2']);
            })
            ->when(isset($filters['card_number3']), function (Builder $query) use ($filters) {
                $query->where('card_number3', $filters['card_number3']);
            })
            ->when(isset($filters['card_type']), function (Builder $query) use ($filters) {
                $query->where('card_type', $filters['card_type']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'LIKE', "%" . $filters['name'] . "%");
            });
    }

    public function getCardByCardNumber($card_number): Builder|Model|null
    {
        $card = $this->with('userable')->getQuery()->where('card_number', $card_number)->first();

        if (!$card) {
            throw new \Exception('Card not found');
        }

        if (!$card->userable) {
            throw new \Exception('Userable not found');
        }

        return $card;
    }

    public function getCardDataForReport(array $filters = []): Collection
    {
        $data = Card::query()
            ->with([
                'userable' => function (MorphTo $morph_to) {
                    $morph_to->morphWith([
                        Student::class => ['latestPrimaryClass.semesterClass.classModel'],
                    ]);
                }
            ])
            ->select(['cards.*'])
            ->when(isset($filters['userable_type']) , function (Builder $query) use ($filters) {
                $query->where('userable_type', $filters['userable_type']);
            })
            ->orderBy( // default order by name asc
                DB::table((new $filters['userable_type'])->getTable())
                    ->select('name')
                    ->whereColumn('id', 'cards.userable_id')
                    ->limit(1),
                'asc'
            )
            ->get();

        $data->transform(function ($card) use ($filters) {

            switch ($filters['userable_type']) {
                case Student::class:
                    $payload['student_number'] = $card?->userable?->student_number;
                    $payload['student_name'] = $card?->userable?->getTranslations('name');
                    $payload['class_name'] = $card?->userable?->latestPrimaryClass?->semesterClass?->classModel?->getTranslations('name');
                    break;

                case Employee::class:
                    $payload['employee_number'] = $card?->userable?->employee_number;
                    $payload['employee_name'] = $card?->userable?->getTranslations('name');
                    break;

                case Contractor::class:
                    $payload['contractor_number'] = $card?->userable?->contractor_number;
                    $payload['contractor_name'] = $card?->userable?->getTranslations('name');
                    break;
            }

            $payload['card_number'] = $card->card_number;
            $payload['card_number2'] = $card->card_number2;
            $payload['card_number3'] = $card->card_number3;

            return $payload;
        });

        return $data;
    }
}
