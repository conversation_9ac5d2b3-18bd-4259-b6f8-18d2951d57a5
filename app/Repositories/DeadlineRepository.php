<?php

namespace App\Repositories;

use App\Models\ClassModel;
use App\Models\Deadline;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class DeadlineRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Deadline::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
            $query->where('semester_setting_id', $filters['semester_setting_id']);
        });

        $query->when(isset($filters['type']), function (Builder $query) use ($filters) {
            $query->where('type', $filters['type']);
        });

        $query->when(isset($filters['class_id']), function (Builder $query) use ($filters) {
            $query->where('deadlinable_type', ClassModel::class);

            if (is_array($filters['class_id'])) {
                $query->whereIn('deadlinable_id', $filters['class_id']);
            } else {
                $query->where('deadlinable_id', $filters['class_id']);
            }
        });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteByQuery(array $filters): bool
    {
        return $this->getQuery($filters)->delete();
    }
}
