<?php

namespace App\Repositories;

use App\Models\HostelInOutRecord;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class HostelInOutRecordRepository extends BaseRepository
{
    const ARRIVED = 'ARRIVED';
    const NOT_ARRIVED = 'NOT_ARRIVED';

    public function getModelClass(): string
    {
        return HostelInOutRecord::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['guardian_id']), function (Builder $query) use ($filters) {
                $query->where('guardian_id', $filters['guardian_id']);
            })
            ->when(isset($filters['hostel_room_bed_id']), function (Builder $query) use ($filters) {
                $query->where('hostel_room_bed_id', $filters['hostel_room_bed_id']);
            })
            ->when(isset($filters['check_in_datetime']), function (Builder $query) use ($filters) {
                switch ($filters['check_in_datetime']) {
                    case self::ARRIVED:
                        $query->whereNotNull('check_in_datetime');
                        break;
                    case self::NOT_ARRIVED:
                        $query->whereNull('check_in_datetime');
                        break;
                    default:
                        $query->where('check_in_datetime', $filters['check_in_datetime']);
                        break;
                }
            })
            ->when(isset($filters['student_number']), function (Builder $query) use ($filters) {
                $query->whereRelation('student', 'student_number', $filters['student_number']);
            })
            ->when(isset($filters['student_name']), function (Builder $query) use ($filters) {
                $query->whereHas('student', function ($query) use ($filters) {
                    $query->whereTranslations('name', $filters['student_name'], 'ILIKE', true);
                });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }


    public function getHostelBoardersGoHomeOrOutData(array $filters = [])
    {
        $filters = optional($filters);

        $date_from = Carbon::parse($filters['date_from'])->startOfDay()->toDateTimeString();
        $date_to = Carbon::parse($filters['date_to'])->endOfDay()->toDateTimeString();

        $parent_filters = [
            'includes' => [
                'student' => function ($query) {
                    $query->select(['id', 'student_number', 'name']);
                    $query->with([
                        'firstActiveHostelBedAssignment' => function ($query) {
                            $query->select(['id', 'assignable_type', 'assignable_id', 'hostel_room_bed_id']);
                            $query->with([
                                'bed' => function ($query) {
                                    $query->select(['id', 'hostel_room_id', 'name']);
                                    $query->with([
                                        'hostelRoom' => function ($query) {
                                            $query->select(['id', 'name', 'hostel_block_id']);
                                            $query->with([
                                                'hostelBlock' => function ($query) {
                                                    $query->select(['id', 'name']);
                                                }
                                            ]);
                                        }
                                    ]);
                                }
                            ]);
                        },
                    ]);
                },
                'guardian' => function ($query) {
                    $query->select(['id', 'name', 'nric']);
                },
                'checkOutBy' => function ($query) {
                    $query->select(['id']);
                    $query->with([
                        'employee' => function ($query) {
                            $query->select(['id', 'user_id', 'name']);
                        },
                    ]);
                },
                'checkInBy' => function ($query) {
                    $query->select(['id']);
                    $query->with([
                        'employee' => function ($query) {
                            $query->select(['id', 'user_id', 'name']);
                        },
                    ]);
                },
            ],
        ];

        $data = $this->getQuery($parent_filters)
            ->select(['id', 'check_out_datetime', 'check_in_datetime', 'reason', 'student_id', 'guardian_id', 'check_in_by', 'check_out_by', 'card_no'])
            ->when($filters['yet_return'] == true, function (Builder $query) use ($date_from, $date_to) {
                $query->whereBetween('check_out_datetime', [$date_from, $date_to]);
                $query->whereNull('check_in_datetime');
            })
            ->when($filters['leave_school'] == true, function (Builder $query) use ($date_from, $date_to) {
                $query->whereBetween('check_out_datetime', [$date_from, $date_to]);
            })
            ->when($filters['returned'] == true, function (Builder $query) use ($date_from, $date_to) {
                $query->whereBetween('check_in_datetime', [$date_from, $date_to]);
            })
            ->when($filters['in_out_type'], function (Builder $query, $in_out_type) {
                $query->where('type', $in_out_type);
            })
            ->get();

        // TODO: Kimi (Not important for now) Data transformation can be done at service level
        $data->transform(function ($record) {

            $checkout_timestamp = isset($record->check_out_datetime) ? Carbon::parse($record->check_out_datetime)->setTimezone(config('school.timezone')) : null;
            $checkout_date = isset($checkout_timestamp) ? $checkout_timestamp->toDateString() : null;
            $checkout_time = isset($checkout_timestamp) ? $checkout_timestamp->format('h:i A') : null;

            $checkin_timestamp = isset($record->check_in_datetime) ? Carbon::parse($record->check_in_datetime)->setTimezone(config('school.timezone')) : null;
            $checkin_date = isset($checkin_timestamp) ? $checkin_timestamp->toDateString() : null;
            $checkin_time = isset($checkin_timestamp) ? $checkin_timestamp->format('h:i A') : null;

            return [
                'block_name' => $record?->student?->firstActiveHostelBedAssignment?->bed?->hostelRoom?->hostelBlock?->getTranslation('name', app()->getLocale()),
                'room_name' => $record?->student?->firstActiveHostelBedAssignment?->bed?->hostelRoom?->name,
                'bed_name' => $record?->student?->firstActiveHostelBedAssignment?->bed?->name,
                'card_no' => $record?->card_no,
                'student_number' => $record?->student?->student_number,
                'student_name' => $record?->student?->getTranslations('name'),
                'check_out_date' => $checkout_date,
                'check_out_time' => $checkout_time,
                'check_in_date' => $checkin_date,
                'check_in_time' => $checkin_time,
                'reason' => $record?->reason,
                'guardian_name' => $record?->guardian?->getTranslations('name'),
                'guardian_nric' => $record?->guardian?->nric,
                'check_out_by' => $record?->checkOutBy?->employee?->getTranslations('name'),
                'check_in_by' => $record?->checkInBy?->employee?->getTranslations('name'),
            ];
        });

        return $data->sortBy([
            ['block_name', 'asc'],
            ['room_name', 'asc'],
            ['bed_name', 'asc'],
        ])->values();
    }
}
