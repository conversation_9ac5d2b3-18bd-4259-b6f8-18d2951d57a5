<?php

namespace App\Repositories;

use App\Enums\BookLoanSettingType;
use App\Enums\LibraryMemberType;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookLoanSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class BookRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Book::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $includes = Arr::get($filters, 'includes', []);
        $includes = array_merge($includes, ['authors']);

        $filters['includes'] = $includes;
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['book_loan_exist']), function (Builder $query) {
                $query->has('bookLoans');
            })
            ->when(isset($filters['isbn']), function (Builder $query) use ($filters) {
                $query->where('isbn', $filters['isbn']);
            })
            ->when(isset($filters['book_no']), function (Builder $query) use ($filters) {
                $query->where('book_no', $filters['book_no']);
            })
            ->when(isset($filters['book_no_wildcard']), function (Builder $query) use ($filters) {
                $query->where('book_no', 'ILIKE', '%'.$filters['book_no_wildcard'].'%');
            })
            ->when(isset($filters['call_no']), function (Builder $query) use ($filters) {
                $query->where('call_no', $filters['call_no']);
            })
            ->when(isset($filters['call_no_wildcard']), function (Builder $query) use ($filters) {
                $query->where('call_no', 'ILIKE', '%'.$filters['call_no_wildcard'].'%');
            })
            ->when(isset($filters['book_sub_classification_id']), function (Builder $query) use ($filters) {
                $query->where('book_sub_classification_id', $filters['book_sub_classification_id']);
            })
            ->when(isset($filters['title']), function (Builder $query) use ($filters) {
                $query->where('title', 'ILIKE', "%" . $filters['title'] . "%");
            })
            ->when(isset($filters['book_language_id']), function (Builder $query) use ($filters) {
                $query->where('book_language_id', $filters['book_language_id']);
            })
            ->when(isset($filters['author_ids']), function (Builder $query) use ($filters) {
                $query->whereHas('authors', function ($query) use ($filters) {
                    $author_table = (new Author())->getTable();
                    $query->whereIn($author_table . '.id', $filters['author_ids']);
                });
            })
            ->when(isset($filters['period_loan_date_from']), function (Builder $query) use ($filters) {
                $query->whereHas('bookLoans', function (Builder $query) use ($filters) {
                    $query->where('loan_date', '>=', $filters['period_loan_date_from']);
                });
            })
            ->when(isset($filters['period_loan_date_to']), function (Builder $query) use ($filters) {
                $query->whereHas('bookLoans', function (Builder $query) use ($filters) {
                    $query->where('loan_date', '<=', $filters['period_loan_date_to']);
                });
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function orderByTotalBookLoan(array $filters = []): Collection|array
    {
        return $this
            ->getQuery($filters)
            ->withCount(['bookLoans' => function (Builder $query) use ($filters) {
                $query->when(isset($filters['period_loan_date_from']), function (Builder $query) use ($filters) {
                    $query->where('loan_date', '>=', $filters['period_loan_date_from']);
                })
                ->when(isset($filters['period_loan_date_to']), function (Builder $query) use ($filters) {
                    $query->where('loan_date', '<=', $filters['period_loan_date_to']);
                });
            }])
            ->orderBy('book_loans_count', 'desc')
            ->limit(10)
            ->get();
    }

    public function syncAuthors($id, $author_ids): void
    {
        $model = $this->resolveModel($id);
        $model->authors()->sync($author_ids);
    }

    public function updateLoanSettings($id, $settings): void
    {
        $model = $this->resolveModel($id);

        $loan_settings = $model->loanSettings;

        foreach ($settings as $type => $value) {
            $loan_setting = $loan_settings->where('type', BookLoanSettingType::from($type))->first();
            if ($loan_setting) {
                $loan_setting->fill($value);
                $loan_setting->save();
            } else {
                $value['type'] = $type;
                $loan_setting = new BookLoanSetting();
                $loan_setting->fill($value);
                $model->loanSettings()->save($loan_setting);
            }
        }
    }

    public function findLoanSettingVia(int $id, LibraryMemberType $type): Model|Builder|null
    {
        return BookLoanSetting::query()
            ->where('book_id', $id)
            ->where('type', $type)
            ->first();
    }
}
