<?php

namespace App\Repositories;

use App\Models\Grade;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\ResultsPostingLineItemHistory;
use App\Models\SemesterSetting;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ResultsPostingLineItemRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ResultsPostingLineItem::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query
            ->when(isset($filters['header_id']), function (Builder $query) use ($filters) {
                $query->where('header_id', $filters['header_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['subject_id']), function (Builder $query) use ($filters) {
                $query->where('subject_id', $filters['subject_id']);
            });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function archivePreviousData($student_id, array $output_component_ids) {

        $data = ResultsPostingLineItem::where('student_id', $student_id)
            ->whereIn('report_card_output_component_id', $output_component_ids)
            ->get();

        \DB::transaction(function() use (&$data){

            foreach ( $data as $d ) {
                $history_row = $d->replicate();
                $history_row->setTable('results_posting_line_items_history');
                $history_row->save();

                $d->delete();
            }

        });

        return true;
    }

    public function archivePreviousDataByHeader(ResultsPostingHeader $header) {

        $data = ResultsPostingLineItem::where('header_id', $header->id)->get();

        \DB::transaction(function() use (&$data){

            foreach ( $data as $d ) {
                $history_row = $d->replicate();
                $history_row->setTable('results_posting_line_items_history');
                $history_row->save();

                $d->delete();
            }

        });

        return $this;
    }



    public function restoreLineItemsByHeader(ResultsPostingHeader $header) {

        $data = ResultsPostingLineItemHistory::where('header_id', $header->id)->get();

        \DB::transaction(function() use (&$data){

            foreach ( $data as $d ) {
                $row = $d->replicate();
                $row->setTable('results_posting_line_items');
                $row->save();

                $d->delete();
            }

        });

        return $this;
    }

}
