<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\EmployeeJobTitle;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;

class EmployeeRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Employee::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->select($this->getModelTableName() . '.*') // Used for joined sorting
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['email']), function (Builder $query) use ($filters) {
                $query->where('employees.email', $filters['email']);
            })
            ->when(isset($filters['phone_number']), function (Builder $query) use ($filters) {
                $query->where('employees.phone_number', $filters['phone_number']);
            })
            ->when(isset($filters['job_title_id']), function (Builder $query) use ($filters) {
                $query->where('job_title_id', $filters['job_title_id']);
            })
            ->when(isset($filters['gender']), function (Builder $query) use ($filters) {
                $query->where('gender', $filters['gender']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['religion_id']), function (Builder $query) use ($filters) {
                $query->where('religion_id', $filters['religion_id']);
            })
            ->when(isset($filters['race_id']), function (Builder $query) use ($filters) {
                $query->where('race_id', $filters['race_id']);
            })
            ->when(isset($filters['state_id']), function (Builder $query) use ($filters) {
                $query->where('state_id', $filters['state_id']);
            })
            ->when(isset($filters['country_id']), function (Builder $query) use ($filters) {
                $query->where('country_id', $filters['country_id']);
            })
            ->when(isset($filters['is_hostel']), function (Builder $query) use ($filters) {
                $query->where('is_hostel', $filters['is_hostel']);
            })
            ->when(isset($filters['is_teacher']), function (Builder $query) use ($filters) {
                $query->whereRelation('employeeCategory', 'is_teacher', $filters['is_teacher']);
            })
            ->when(isset($filters['employee_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['employee_number'])) {
                    $query->whereIn('employee_number', $filters['employee_number']);
                } else {
                    $query->where('employee_number', $filters['employee_number']);
                }
            })
            ->when(isset($filters['badge_no']), function (Builder $query) use ($filters) {
                $query->where('badge_no', $filters['badge_no']);
            })
            ->when(isset($filters['nric']), function (Builder $query) use ($filters) {
                $query->where('nric', $filters['nric']);
            })
            ->when(isset($filters['has_active_bed']), function (Builder $query) use ($filters) {
                if ($filters['has_active_bed']) {
                    $query->whereHas('activeHostelBedAssignments');
                } else {
                    $query->whereDoesntHave('activeHostelBedAssignments');
                }
            })
            ->when(isset($filters['block_id']), function (Builder $query) use ($filters) {
                $query->whereHas('activeHostelBedAssignments.bed.hostelRoom', function ($query) use ($filters) {
                    $query->where('hostel_block_id', $filters['block_id']);
                });
            })
            ->when(isset($filters['room_id']), function (Builder $query) use ($filters) {
                $query->whereHas('activeHostelBedAssignments.bed', function ($query) use ($filters) {
                    $query->where('hostel_room_id', $filters['room_id']);
                });
            })
            ->when(isset($filters['bed_id']), function (Builder $query) use ($filters) {
                $query->whereHas('activeHostelBedAssignments', function ($query) use ($filters) {
                    $query->where('hostel_room_bed_id', $filters['bed_id']);
                });
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($order_by['name']) || isset($order_by['email']) || isset($order_by['phone_number']),
                function (Builder $query) use ($filters) {
                    $query = $query->join('users as u', function (JoinClause $join) {
                        $join->on('u.id', '=', 'employees.user_id');
                    });

                    $order_by = Arr::only($filters['order_by'], ['name', 'email', 'phone_number']);

                    $this->setupOrderBy($query, $order_by, User::class, 'u.');
                })
            ->when(isset($order_by['job_title']), function (Builder $query) use ($order_by) {
                $query = $query->join('master_employee_job_titles as ejt', 'ejt.id', '=', 'employees.job_title_id');

                $order_by = [
                    'name' => $order_by['job_title']
                ];

                $this->setupOrderBy($query, $order_by, EmployeeJobTitle::class, 'ejt.');
            });
    }

    public function getByIds(array $ids, mixed $with): Collection
    {
        return $this->getQuery(['with' => $with])
            ->whereIn('id', $ids)
            ->get();
    }

    public function getEmployeeLodging(array $filters = []): Collection
    {
        $parent_filters = [
            'includes' => [
                'firstActiveHostelBedAssignment' => function ($query) {
                    $query->select(['id', 'assignable_type', 'assignable_id', 'hostel_room_bed_id']);
                    $query->with([
                        'bed' => function ($query) {
                            $query->select(['id', 'hostel_room_id', 'name']);
                            $query->with([
                                'hostelRoom' => function ($query) {
                                    $query->select(['id', 'name', 'hostel_block_id']);
                                    $query->with([
                                        'hostelBlock' => function ($query) {
                                            $query->select(['id', 'name']);
                                        }
                                    ]);
                                }
                            ]);
                        }
                    ]);
                },
            ],
            'order_by' => ['name' => 'ASC'],
        ];

        $data = $this->getQuery($parent_filters)
            ->select(['employees.id', 'employees.name', 'gender', 'employees.phone_number', 'address', 'employment_start_date'])
            ->where('is_hostel', true)
            ->whereHas('firstActiveHostelBedAssignment')
            ->get();

        // TODO: Kimi (Not important for now) Data transformation can be done at service level
        $data->transform(function ($employee) {
            return [
                'block_name' => $employee->firstActiveHostelBedAssignment?->bed?->hostelRoom?->hostelBlock?->getTranslations('name'),
                'room_name' => $employee->firstActiveHostelBedAssignment?->bed?->hostelRoom?->name,
                'employee_name' => $employee->getTranslations('name'),
                'gender' => $employee->gender->value,
                'phone_number' => $employee->phone_number,
                'address' => $employee->address,
                'report_date' => $employee->employment_start_date,
            ];
        });

        $locale = app()->getLocale();

        return $data->sortBy(function ($item) use ($locale) {
            return [
                $item['block_name'][$locale] ?? '',
                $item['room_name'],
            ];
        })->values();
    }
}
