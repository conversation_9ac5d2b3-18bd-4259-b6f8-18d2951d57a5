<?php

namespace App\Repositories;

use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SemesterClassRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SemesterClass::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->orderBy('id')->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = optional(optional($filters)['order_by']);

        $query = parent::getQuery($filters);
        $query->select('semester_classes.*')
            ->when(isset($filters['semester_setting_id']), function ($query) use ($filters) {
                if (is_array($filters['semester_setting_id'])) {
                    $query->whereIn($this->getModelTableName() . '.semester_setting_id', $filters['semester_setting_id']);
                } else {
                    $query->where($this->getModelTableName() . '.semester_setting_id', $filters['semester_setting_id']);
                }
            })->when(isset($filters['class_id']), function ($query) use ($filters) {
                if (is_array($filters['class_id'])) {
                    $query->whereIn($this->getModelTableName() . '.class_id', $filters['class_id']);
                } else {
                    $query->where($this->getModelTableName() . '.class_id', $filters['class_id']);
                }
            })->when(isset($filters['id']), function ($query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn($this->getModelTableName() . '.id', $filters['id']);
                } else {
                    $query->where($this->getModelTableName() . '.id', $filters['id']);
                }
            })->when(isset($filters['homeroom_teacher_id']), function ($query) use ($filters) {
                $query->where($this->getModelTableName() . '.homeroom_teacher_id', $filters['homeroom_teacher_id']);
            })->when(isset($filters['is_active']), function ($query) use ($filters) {
                $query->where($this->getModelTableName() . '.is_active', $filters['is_active']);
            })->when(isset($filters['homeroom_teacher_name']), function (Builder $query) use ($filters) {
                $query->whereHas('homeroomTeacher', function ($query) use ($filters) {
                    $query->whereTranslations('name', $filters['homeroom_teacher_name'], 'ILIKE', true);
                });
            })->when(isset($filters['class_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('classModel', 'type', $filters['class_type']);
            })->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('classModel', 'grade_id', $filters['grade_id']);
            });

        // SORTING
        $query->when($order_by['class'] || $order_by['grade'], function (Builder $query) use ($order_by) {

            $query
                ->join('classes', 'classes.id', '=', 'semester_classes.class_id')
                ->leftJoin('master_grades', 'master_grades.id', '=', 'classes.grade_id')
                ->when($order_by['class'], function (Builder $query, $order) {
                    $this->setupOrderBy($query, $order, ClassModel::class, 'classes.');
                })
                ->when($order_by['grade'], function (Builder $query, $order) {
                    $this->setupOrderBy($query, $order, Grade::class, 'master_grades.');
                });
        });

        $query->when($order_by['semester_setting'], function (Builder $query) use ($order_by) {
            $query
                ->join('master_semester_settings', 'master_semester_settings.id', '=', 'semester_classes.semester_setting_id')
                ->when($order_by['semester_setting'], function (Builder $query, $order) {
                    $this->setupOrderBy($query, $order, SemesterSetting::class, 'master_semester_settings.');
                });
        });

        $query->when($order_by['homeroom_teacher'], function (Builder $query) use ($order_by) {
            $query
                ->join('employees', 'employees.id', '=', 'semester_classes.homeroom_teacher_id')
                ->when($order_by['homeroom_teacher'], function (Builder $query, $order) {
                    $this->setupOrderBy($query, $order, Employee::class, 'employees.');
                });
        });

        return $query;
    }
}
