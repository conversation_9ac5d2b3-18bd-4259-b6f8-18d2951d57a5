<?php

namespace App\Repositories;

use App\Models\SemesterYearSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SemesterYearSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SemesterYearSetting::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query->when(isset($filters['year']), function (Builder $query) use ($filters) {
            $query->where('year', $filters['year']);
        });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
