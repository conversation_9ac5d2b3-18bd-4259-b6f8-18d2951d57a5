<?php

namespace App\Repositories;

use App\Models\Config;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class ConfigRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Config::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getConfigByKey($key): ?Model
    {
        return $this
            ->getQuery(['key' => $key])
            ->first();
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['key']), function (Builder $query) use ($filters) {
                $query->where('key', $filters['key']);
            })
            ->when(isset($filters['keys']), function (Builder $query) use ($filters) {
                $query->whereIn('key', $filters['keys']);
            });
    }
}
