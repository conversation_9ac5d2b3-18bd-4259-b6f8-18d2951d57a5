<?php

namespace App\Repositories;

use App\Models\EnrollmentUser;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EnrollmentUserRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EnrollmentUser::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $filters = optional($filters);

        return $query
            ->when($filters['nric'], function (Builder $query, $nric) {
                $query->where('nric', $nric);
            })
            ->when($filters['email'], function (Builder $query, $email) {
                $query->where('email', $email);
            })
            ->when($filters['phone_number'], function ($query, $phone_number) {
                $query->where('phone_number', $phone_number);
            })
            ->when($filters['name'], function ($query, $name) {
                $query->whereTranslations('name', $name, 'ILIKE', true);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
