<?php

namespace App\Repositories;

use App\Models\CalendarTarget;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;

class CalendarTargetRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return CalendarTarget::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $filters = optional($filters);

        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['calendar_id']), function (Builder $query) use ($filters) {
                $query->where('calendar_id', $filters['calendar_id']);
            })
            ->when(isset($filters['priority']) , function (Builder $query) use ($filters){
                $query->where('priority', $filters['priority']);
            })
            ->when(isset($filters['calendar_targetable_id']) , function (Builder $query) use ($filters) {
                $query->where('calendar_targetable_id', $filters['calendar_targetable_id']);
            })
            ->when(isset($filters['calendar_targetable_type']), function (Builder $query) use ($filters) {
                $query->where('calendar_targetable_type', $filters['calendar_targetable_type']);
            });
    }

    public function deleteByCalendarId(int $calendar_id): void
    {
        CalendarTarget::query()->where('calendar_id', $calendar_id)->delete();
    }

    public function getStudentsSchoolDayOrNonSchoolDayByDate($date, array $student_ids = [])
    {
        $date = Carbon::parse($date);
        $year = $date->year;

        $calendar_targets_group_by_calendar_targetable_id = CalendarTarget::with([
            'calendar' => function ($query) use ($date) {
                $query->select('id');
                $query->with('settings', function ($query) use ($date) {
                    $query->where('date', $date);
                });
            },
        ])
            ->whereHas('calendar', function ($query) use ($year) {
                $query->where('year', $year)
                    ->where('is_active', true);
            })
            ->whereHas('calendar.settings', function ($query) use ($date) {
                $query->where('date', $date);
            })
            ->when(!empty($student_ids), function ($query) use ($student_ids) {
                $query->whereIn('calendar_targets.calendar_targetable_id', $student_ids);
            })
            ->where('calendar_targetable_type', Student::class)
            ->get()
            ->groupBy('calendar_targetable_id');

        $data = [];
        // true = school day
        // false = non school day
        foreach ($calendar_targets_group_by_calendar_targetable_id as $calendar_targetable_id => $calendar_targets) {
            $data[$calendar_targetable_id] = $calendar_targets->sortByDesc('priority')->first()->calendar->settings->first()->is_attendance_required;
        }
        return $data;
    }
}
