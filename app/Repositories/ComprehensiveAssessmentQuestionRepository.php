<?php

namespace App\Repositories;

use App\Models\ComprehensiveAssessmentCategory;
use App\Models\ComprehensiveAssessmentQuestion;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ComprehensiveAssessmentQuestionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ComprehensiveAssessmentQuestion::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->select('comprehensive_assessment_questions.*')
            ->when(isset($filters['question']), function (Builder $query) use ($filters) {
                $query->whereTranslations('question', $filters['question'], 'ILIKE', true);
            })
            ->when(isset($filters['comprehensive_assessment_category_id']), function (Builder $query) use ($filters) {
                $query->where('comprehensive_assessment_category_id', $filters['comprehensive_assessment_category_id']);
            })
            ->when(isset($order_by['comprehensive_assessment_category_name']), function (Builder $query) use ($order_by) {
                $query->join('comprehensive_assessment_categories', 'comprehensive_assessment_categories.id', '=', 'comprehensive_assessment_questions.comprehensive_assessment_category_id');

                $order_by = [
                    'name' => $order_by['comprehensive_assessment_category_name']
                ];

                $this->setupOrderBy($query, $order_by, ComprehensiveAssessmentCategory::class, 'comprehensive_assessment_categories.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
