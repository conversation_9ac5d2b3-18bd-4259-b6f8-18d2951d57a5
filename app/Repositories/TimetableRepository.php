<?php

namespace App\Repositories;

use App\Models\Timetable;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TimetableRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Timetable::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['period_group_id']), function (Builder $query) use ($filters) {
                $query->where('period_group_id', $filters['period_group_id']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['class_name']), function (Builder $query) use ($filters) {
                $query->whereHas('semesterClass.classModel', function (Builder $q) use ($filters) {
                    $q->whereTranslations('name', $filters['class_name'], 'ILIKE', true);
                });
            })
            ->when(isset($filters['semester_name']), function (Builder $query) use ($filters) {
                $query->whereHas('semesterClass.semesterSetting', function (Builder $q) use ($filters) {
                    $q->where('name', 'ILIKE', $filters['semester_name'] . '%');
                });
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['semester_class_id'])) {
                    $query->whereIn('semester_class_id', $filters['semester_class_id']);
                } else {
                    $query->where('semester_class_id', $filters['semester_class_id']);
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
