<?php

namespace App\Repositories;

use App\Models\Exam;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ExamRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Exam::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);
        $query
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['results_entry_period_open']) && $filters['results_entry_period_open'], function (Builder $query) {
                $query->where('results_entry_period_from', '<=', now('UTC')->toDateTimeString())
                    ->where('results_entry_period_to', '>=', now('UTC')->toDateTimeString());
            });
        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
