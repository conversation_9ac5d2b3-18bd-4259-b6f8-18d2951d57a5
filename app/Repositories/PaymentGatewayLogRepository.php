<?php

namespace App\Repositories;

use App\Models\PaymentGatewayLog;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PaymentGatewayLogRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PaymentGatewayLog::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->when(isset($filters['provider']), function (Builder $query) use ($filters) {
                $query->where('provider', $filters['provider']);
            })
            ->when(isset($filters['order_id']), function (Builder $query) use ($filters) {
                $query->where('order_id', $filters['order_id']);
            })
            ->when(isset($filters['description']), function (Builder $query) use ($filters) {
                $query->where('description', 'LIKE', "%" . $filters['description'] . "%");
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->paginate($this->per_page);
    }
}
