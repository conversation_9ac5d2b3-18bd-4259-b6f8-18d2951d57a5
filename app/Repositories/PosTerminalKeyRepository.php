<?php

namespace App\Repositories;

use App\Models\PosTerminalKey;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PosTerminalKeyRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PosTerminalKey::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query->when(isset($filters['name']), function (Builder $query) use ($filters) {
            $query->where('name', "ILIKE", "%". $filters['name'] ."%");
        })->when(isset($filters['terminal_id']), function (Builder $query) use ($filters) {
            $query->where('terminal_id', $filters['terminal_id']);
        });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
