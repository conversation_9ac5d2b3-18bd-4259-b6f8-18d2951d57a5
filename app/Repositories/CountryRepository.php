<?php

namespace App\Repositories;

use App\Models\Country;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CountryRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Country::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query->when(isset($filters['name']), function (Builder $query) use ($filters) {
            $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
        });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getByName($name): ?Model
    {
        return $this->getQuery()->where($this->getTranslatableKey('name'), 'LIKE', "%" . $name . "%")->first();
    }
}
