<?php

namespace App\Repositories;

use App\Models\Product;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ProductRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Product::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['code']), function (Builder $query) use ($filters){
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['category']), function (Builder $query) use ($filters){
                $query->where('category', $filters['category']);
            })
            ->when(isset($filters['sub_category_1']), function (Builder $query) use ($filters){
                $query->where('sub_category_1', $filters['sub_category_1']);
            })
            ->when(isset($filters['sub_category_2']), function (Builder $query) use ($filters){
                $query->where('sub_category_2', $filters['sub_category_2']);
            })
            ->when(isset($filters['uom_code']), function (Builder $query) use ($filters){
                $query->where('uom_code', $filters['uom_code']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
        ;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
