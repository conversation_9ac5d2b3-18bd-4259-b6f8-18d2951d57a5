<?php

namespace App\Repositories;

use App\Models\EcommerceOrderItem;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EcommerceOrderItemRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceOrderItem::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)
            ->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function groupByProductId(array $filters = []): Collection
    {
        return $this
            ->getQuery($filters)
            ->selectRaw('
                product_id,
                product_name,
                product_unit_price,
                currency_code,
                SUM(quantity) as total_quantity,
                SUM(amount_before_tax) + SUM(tax_amount) as total
            ')
            ->groupBy('product_id', 'product_name', 'product_unit_price', 'currency_code')
            ->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['merchant_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('order', 'merchant_type', $filters['merchant_type']);
            })
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('merchant_id', $filters['merchant_id']);
            })
            ->when(isset($filters['product_id']), function (Builder $query) use ($filters) {
                $query->where('product_id', $filters['product_id']);
            })
            ->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->whereHas('order.recipientStudentClass.semesterClass.classModel', function (Builder $query) use ($filters) {
                    $query->where('grade_id', $filters['grade_id']);
                });
            })
            ->whereHas('order', function ($query) use ($filters) {
                $query
                    ->when(isset($filters['start_date']), function (Builder $query) use ($filters) {
                        $query->where('created_at', '>=', $filters['start_date']);
                    })
                    ->when(isset($filters['end_date']), function (Builder $query) use ($filters) {
                        $query->where('created_at', '<=', $filters['end_date']);
                    })
                    ->when(isset($filters['payment_status']), function (Builder $query) use ($filters) {
                        $query->where('payment_status', $filters['payment_status']);
                    })
                    ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                        $query->where('status', $filters['status']);
                    })
                    ->when(isset($filters['has_student_class']), function (Builder $query) use ($filters) {
                        if ($filters['has_student_class'] === true) {
                            $query->whereNotNull('recipient_student_class_id');
                        }

                        if ($filters['has_student_class'] === false) {
                            $query->whereNull('recipient_student_class_id');
                        }
                    });
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->whereHas('order.recipientStudentClass', function ($query) use ($filters) {
                    $query->where('semester_setting_id', $filters['semester_setting_id']);
                });
            })
            ->when(isset($filters['start_product_delivery_date']), function (Builder $query) use ($filters) {
                $query->where('product_delivery_date', '>=', $filters['start_product_delivery_date']);
            })
            ->when(isset($filters['end_product_delivery_date']), function (Builder $query) use ($filters) {
                $query->where('product_delivery_date', '<=', $filters['end_product_delivery_date']);
            })
            ->when(isset($filters['semester_class_ids']) || isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                if (isset($filters['semester_class_id'])) {
                    $filters['semester_class_ids'] = [$filters['semester_class_id']];
                }

                $query->whereHas('order.recipientStudentClass.semesterClass', function ($query) use ($filters) {
                    $query->whereIn('id', $filters['semester_class_ids']);
                });
            })
            ->has('order');

    }
}
