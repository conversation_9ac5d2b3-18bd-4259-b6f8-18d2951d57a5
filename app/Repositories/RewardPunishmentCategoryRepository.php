<?php

namespace App\Repositories;

use App\Models\RewardPunishmentCategory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class RewardPunishmentCategoryRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return RewardPunishmentCategory::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
