<?php

namespace App\Repositories;

use App\Models\EcommerceProductTag;
use App\Models\EcommerceProductTagTarget;
use App\Models\Employee;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class EcommerceProductTagTargetRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceProductTagTarget::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['tag_id']), function (Builder $query) use ($filters) {
                if(is_array($filters['tag_id'])){
                    $query->whereIn('tag_id', $filters['tag_id']);
                }else{
                    $query->where('tag_id', $filters['tag_id']);
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteByProductTag(EcommerceProductTag|Model $productTag): void
    {
        EcommerceProductTagTarget::query()
            ->where('tag_id', $productTag->id)
            ->delete();
    }

    public function deleteByUserable(Student|Employee|Model $userable): void
    {
        EcommerceProductTagTarget::query()
            ->where('userable_type', get_class($userable))
            ->where('userable_id', $userable->id)
            ->delete();
    }
}
