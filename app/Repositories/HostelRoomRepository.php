<?php

namespace App\Repositories;

use App\Models\HostelBlock;
use App\Models\HostelRoom;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class HostelRoomRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelRoom::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->select('hostel_rooms.*') // To avoid column ambiguity
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                if (is_array($filters['name'])) {
                    $query->whereIn('name', $filters['name']);
                } else {
                    $query->where('name', 'ILIKE', "%{$filters['name']}%");
                }
            })
            ->when(isset($filters['hostel_block_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['hostel_block_id'])) {
                    $query->whereIn('hostel_block_id', $filters['hostel_block_id']);
                } else {
                    $query->where('hostel_block_id', $filters['hostel_block_id']);
                }
            })
            ->when(isset($filters['hostel_block_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('hostelBlock', 'type', $filters['hostel_block_type']);
            })
            ->when(isset($filters['gender']), function (Builder $query) use ($filters) {
                $query->where('gender', $filters['gender']);
            })
            ->when(Arr::has($filters, 'is_active') && $filters['is_active'] !== null, function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($order_by['hostel_block']), function (Builder $query) use ($order_by) {
                $query->join('hostel_blocks', 'hostel_rooms.hostel_block_id', '=', 'hostel_blocks.id');

                $this->setupOrderBy($query, $order_by['hostel_block'], HostelBlock::class, 'hostel_blocks.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function hostelRoomHasBeds(HostelRoom|int $hostel_room): bool
    {
        if ($hostel_room instanceof HostelRoom) {
            return $hostel_room->hasBeds();
        } else {
            return $this->findOrFail($hostel_room)->hasBeds();
        }
    }
}
