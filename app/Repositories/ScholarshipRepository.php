<?php

namespace App\Repositories;

use App\Models\Scholarship;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ScholarshipRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Scholarship::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['is_internal']), function (Builder $query) use ($filters) {
                $query->where('is_internal', $filters['is_internal']);
            })
            ->when(isset($filters['application_open_at']) && isset($filters['application_close_at']), function (Builder $query) use ($filters) {
                $query->where('application_open_at', '>=', $filters['application_open_at'])
                    ->where('application_close_at', '<=', $filters['application_close_at']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
