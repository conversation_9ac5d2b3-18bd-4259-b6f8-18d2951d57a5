<?php

namespace App\Repositories;

use App\Models\Period;
use App\Models\PeriodGroup;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PeriodRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Period::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];
        
        return parent::getQuery($filters)
            ->select('periods.*')
            ->when(isset($filters['period_group_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['period_group_id'])) {
                    $query->whereIn('period_group_id', $filters['period_group_id']);
                } else {
                    $query->where('period_group_id', $filters['period_group_id']);
                }
            })
            ->when(isset($filters['day']), function (Builder $query) use ($filters) {
                $query->where('day', $filters['day']);
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($order_by['period_group_name']), function (Builder $query) use ($order_by) {
                $query->join('period_groups', 'periods.period_group_id', '=', 'period_groups.id');
                $order_by = [
                    'name' => $order_by['period_group_name']
                ];

                $this->setupOrderBy($query, $order_by, PeriodGroup::class, 'period_groups.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)->paginate($this->per_page);
    }

    public function removePeriodsByPeriodGroupId(int $period_group_id): void
    {
        $this->getQuery()
            ->where('period_group_id', $period_group_id)
            ->delete();
    }
}
