<?php

namespace App\Repositories;

use App\Models\Announcement;
use App\Models\AnnouncementTarget;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AnnouncementRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Announcement::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['title']), function (Builder $query) use ($filters) {
                $query->where('title', 'ILIKE', "%" . $filters['title'] . "%");
            })
            ->when(isset($filters['message']), function (Builder $query)  use ($filters) {
                $query->where('message', 'ILIKE', "%" . $filters['message'] . "%");
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['scheduled_time']), function (Builder $query) use ($filters) {
                $query->whereDate('scheduled_time', $filters['scheduled_time']);
            })
            ->when(isset($filters['sent_at']), function (Builder $query) use ($filters) {
                $query->whereDate('sent_at', $filters['sent_at']);
            })
            ->when(isset($filters['created_by']), function (Builder $query) use ($filters) {
                $query->where('created_by', $filters['created_by']);
            })
            ->when(isset($filters['approved_by']), function (Builder $query) use ($filters) {
                $query->where('approved_by', $filters['approved_by']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function syncStudents(Announcement $announcement, array $student_ids): void
    {
        $announcement->students()->sync($student_ids);
    }

    public function syncEmployees(Announcement $announcement, array $employee_ids): void
    {
        $announcement->employees()->sync($employee_ids);
    }

    public function syncGuardians(Announcement $announcement, array $guardian_ids): void
    {
        $announcement->guardians()->sync($guardian_ids);
    }

    public function getRecipientUserIdsByAnnouncementId(int $announcement_id): array
    {
        return AnnouncementTarget::with('userable:id,user_id')
            ->where('announcement_id', $announcement_id)
            ->get()
            ->pluck('userable.user_id')
            ->unique()
            ->filter()
            ->all();
    }
}
