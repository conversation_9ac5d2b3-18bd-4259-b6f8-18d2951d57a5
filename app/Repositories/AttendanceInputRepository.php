<?php

namespace App\Repositories;

use App\Models\AttendanceInput;
use App\Models\UserableView;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;

class AttendanceInputRepository extends BaseRepository
{
    public function getModelClass(): string {
        return AttendanceInput::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if(is_array($filters['id'])){
                    $query->whereIn('id', $filters['id']);
                }else{
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->whereDate('date', $filters['date']);
            })
            // attendance_recordable_type without attendance_recordable_id
            ->when(!isset($filters['attendance_recordable_id']) && isset($filters['attendance_recordable_type']), function (Builder $query) use ($filters) {
                if ( is_array($filters['attendance_recordable_type'])) {
                    $query->whereIn('attendance_recordable_type', $filters['attendance_recordable_type']);
                }else{
                    $query->where('attendance_recordable_type', $filters['attendance_recordable_type']);
                }
                })
            ->when(isset($filters['attendance_recordable_id']) && isset($filters['attendance_recordable_type']),function (Builder $query) use ($filters) {
                $query->where('attendance_recordable_id', $filters['attendance_recordable_id'])
                    ->where('attendance_recordable_type', $filters['attendance_recordable_type']);
            })
            ->when(isset($filters['terminal_id']), function (Builder $query) use ($filters) {
                if(is_array($filters['terminal_id'])){
                    $query->whereIn('terminal_id', $filters['terminal_id']);
                }else{
                    $query->where('terminal_id', $filters['terminal_id']);
                }
            })
            ->when(isset($order_by['name']) || isset($order_by['number']), function (Builder $query) use ($order_by) {
                $query
                    ->join('userable_views', function (JoinClause $join) {
                            $join->on('userable_views.userable_id', '=', 'attendance_input.attendance_recordable_id')
                                ->whereColumn('userable_views.userable_type', '=', 'attendance_input.attendance_recordable_type');
                    })
                    ->when(isset($order_by['name']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, ['name' => $order_by['name']], UserableView::class, 'userable_views.');
                    })
                    ->when(isset($order_by['number']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, ['number' => $order_by['number']], UserableView::class, 'userable_views.');
                    });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
