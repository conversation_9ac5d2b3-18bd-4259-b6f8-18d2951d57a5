<?php

namespace App\Repositories;

use App\Models\ModelHasAnnouncementGroup;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ModelHasAnnouncementGroupRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ModelHasAnnouncementGroup::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['announcement_group_id']), function (Builder $query) use ($filters) {
                if(is_array($filters['announcement_group_id'])){
                    $query->whereIn('announcement_group_id', $filters['announcement_group_id']);
                }else{
                    $query->where('announcement_group_id', $filters['announcement_group_id']);
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
