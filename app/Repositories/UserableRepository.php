<?php

namespace App\Repositories;

use App\Models\UserableView;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class UserableRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return UserableView::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getAllPaginatedByMultiFieldSearch(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->whereTranslations('name', $filters['search'], 'ILIKE', true)
                    ->orWhere('email', 'ILIKE', "%" . $filters['search'] . "%")
                    ->orWhere('phone_number', 'ILIKE', "%" . $filters['search'] . "%")
                    ->orWhere('number', 'ILIKE', "%" . $filters['search'] . "%");
            })
            ->paginate($this->per_page);
    }

    public function getExistingUserableIds($type, $ids): array
    {
        return $this->getQuery()
            ->select('userable_id')
            ->where('userable_type', $type)
            ->whereIn('userable_id', $ids)
            ->get()
            ->pluck('userable_id')
            ->toArray();
    }
}
