<?php

namespace App\Repositories;

use App\Models\PromotionMark;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PromotionMarkRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PromotionMark::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
            $query->where('semester_class_id', $filters['semester_class_id']);
        })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('semesterClass', 'semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('semesterClass.classModel', 'grade_id', $filters['grade_id']);
            });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
