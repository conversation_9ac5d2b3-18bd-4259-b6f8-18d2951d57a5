<?php

namespace App\Repositories;

use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Subject;
use App\Models\SubstituteRecord;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SubstituteRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SubstituteRecord::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $order_by = optional(optional($filters)['order_by']);

        $query = parent::getQuery($filters);

        return $query
            ->select('substitute_records.*')
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['timeslot_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['timeslot_id'])) {
                    $query->whereIn('timeslot_id', $filters['timeslot_id']);
                } else {
                    $query->where('timeslot_id', $filters['timeslot_id']);
                }
            })
            ->when(isset($filters['requestor_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['requestor_id'])) {
                    $query->whereIn('requestor_id', $filters['requestor_id']);
                } else {
                    $query->where('requestor_id', $filters['requestor_id']);
                }
            })
            ->when(isset($filters['semester_class_id']) || isset($filters['subject_id']), function (Builder $query) use ($filters) {
                $query->whereHas('classSubject', function (Builder $query) use ($filters) {
                    $query->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                        $query->where('semester_class_id', $filters['semester_class_id']);
                    });
                    $query->when(isset($filters['subject_id']), function (Builder $query) use ($filters) {
                        $query->where('subject_id', $filters['subject_id']);
                    });
                });
            })
            ->when(isset($filters['class_subject_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['class_subject_id'])) {
                    $query->whereIn('class_subject_id', $filters['class_subject_id']);
                } else {
                    $query->where('class_subject_id', $filters['class_subject_id']);
                }
            })
            ->when(isset($filters['substitute_teacher_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['substitute_teacher_id'])) {
                    $query->whereIn('substitute_teacher_id', $filters['substitute_teacher_id']);
                } else {
                    $query->where('substitute_teacher_id', $filters['substitute_teacher_id']);
                }
            })
            ->when(isset($filters['substitute_or_requestor_teacher_id']), function (Builder $query) use ($filters) {
                $query->where(function (Builder $query) use ($filters) {
                    $query->where('substitute_teacher_id', $filters['substitute_or_requestor_teacher_id'])
                        ->orWhere('requestor_id', $filters['substitute_or_requestor_teacher_id']);
                });
            })
            ->when(isset($filters['substitute_date_from']), function (Builder $query) use ($filters) {
                $query->where('substitute_date', '>=', $filters['substitute_date_from']);
            })
            ->when(isset($filters['substitute_date_to']), function (Builder $query) use ($filters) {
                $query->where('substitute_date', '<=', $filters['substitute_date_to']);
            })
            ->when(isset($filters['substitute_date']), function (Builder $query) use ($filters) {
                $query->where('substitute_date', $filters['substitute_date']);
            })
            ->when($order_by['requestor_name'], function (Builder $query) use ($order_by) {
                $query->join('employees', 'employees.id', '=', 'substitute_records.requestor_id');
                $order_by = [
                    'name' => $order_by['requestor_name']
                ];
                $this->setupOrderBy($query, $order_by, Employee::class, 'employees.');
            })
            ->when($order_by['substitute_teacher_name'], function (Builder $query) use ($order_by) {
                $query->join('employees', 'employees.id', '=', 'substitute_records.substitute_teacher_id');
                $order_by = [
                    'name' => $order_by['substitute_teacher_name']
                ];
                $this->setupOrderBy($query, $order_by, Employee::class, 'employees.');
            })
            ->when($order_by['subject_name'] || $order_by['class_name'], function (Builder $query) use ($order_by) {
                $query->join('class_subjects', 'class_subjects.id', '=', 'substitute_records.class_subject_id')
                    ->join('subjects', 'subjects.id', '=', 'class_subjects.subject_id')
                    ->join('semester_classes', 'semester_classes.id', '=', 'class_subjects.semester_class_id')
                    ->join('classes', 'classes.id', '=', 'semester_classes.class_id');

                if (isset($order_by['subject_name'])) {
                    $order_by = [
                        'name' => $order_by['subject_name']
                    ];
                    $this->setupOrderBy($query, $order_by, Subject::class, 'subjects.');
                }

                if (isset($order_by['class_name'])) {
                    $order_by = [
                        'name' => $order_by['class_name']
                    ];
                    $this->setupOrderBy($query, $order_by, ClassModel::class, 'classes.');
                }
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getFirstSubstituteRecord($filters = []): ?SubstituteRecord
    {
        return $this->getQuery($filters)->first();
    }
}
