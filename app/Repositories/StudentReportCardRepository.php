<?php

namespace App\Repositories;

use App\Models\ResultsPostingHeader;
use App\Models\Student;
use App\Models\StudentReportCard;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class StudentReportCardRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return StudentReportCard::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['results_posting_header_id']), function (Builder $query) use ($filters) {
                $query->where('results_posting_header_id', $filters['results_posting_header_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->where('grade_id', $filters['grade_id']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['publish_date']), function (Builder $query) use ($filters) {
                $query->whereRelation('resultsPostingHeader', 'publish_date', $filters['publish_date']);
            })
            ->when(isset($filters['result_posting_header_status']), function (Builder $query) use ($filters) {
                $query->whereRelation('resultsPostingHeader', 'status', $filters['result_posting_header_status']);
            });
    }

    public function changeReportCardVisibilityByHeader(ResultsPostingHeader $header, bool $is_visible)
    {

        $this->getQuery([
            'results_posting_header_id' => $header->id,
            'is_active' => true,
        ])->update([
            'is_visible_to_student' => $is_visible
        ]);

    }

    public function changeReportCardVisibilityByPublishDate($publish_date, $is_visible)
    {
        $this->getQuery([
            'is_active' => true,
            'result_posting_header_status' => ResultsPostingHeader::STATUS_COMPLETED,
            'publish_date' => $publish_date
        ])->update([
            'is_visible_to_student' => $is_visible
        ]);
    }

    public function changeReportCardVisibilityByStudent(ResultsPostingHeader $header, Student $student, $is_visible)
    {

        $this->getQuery([
            'results_posting_header_id' => $header->id,
            'student_id' => $student->id,
        ])->update([
            'is_visible_to_student' => $is_visible
        ]);

    }

}
