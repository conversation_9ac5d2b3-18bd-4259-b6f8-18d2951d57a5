<?php

namespace App\Repositories;

use App\Models\Enrollment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EnrollmentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Enrollment::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['admission_year']), function (Builder $query) use ($filters) {
                $query->where('admission_year', $filters['admission_year']);
            })
            ->when(isset($filters['admission_grade_id']), function (Builder $query)  use ($filters) {
                $query->where('admission_grade_id', $filters['admission_grade_id']);
            })
            ->when(isset($filters['student_name']), function (Builder $query)  use ($filters) {
                $query->where($this->getTranslatableKey('student_name'), 'ILIKE', "%{$filters['student_name']}%");
            })
            ->when(isset($filters['nric_no']), function (Builder $query) use ($filters) {
                $query->where('nric_no', $filters['nric_no']);
            })
            ->when(isset($filters['passport_no']), function (Builder $query) use ($filters) {
                $query->where('passport_no', $filters['passport_no']);
            })
            ->when(isset($filters['birthplace_id']), function (Builder $query) use ($filters) {
                $query->where('birthplace_id', $filters['birthplace_id']);
            })
            ->when(isset($filters['nationality_id']), function (Builder $query) use ($filters) {
                $query->where('nationality_id', $filters['nationality_id']);
            })
            ->when(isset($filters['gender']), function (Builder $query) use ($filters) {
                $query->where('gender', $filters['gender']);
            })
            ->when(isset($filters['birth_cert_no']), function (Builder $query) use ($filters) {
                $query->where('birth_cert_no', $filters['birth_cert_no']);
            })
            ->when(isset($filters['race_id']), function (Builder $query) use ($filters) {
                $query->where('race_id', $filters['race_id']);
            })
            ->when(isset($filters['religion_id']), function (Builder $query) use ($filters) {
                $query->where('religion_id', $filters['religion_id']);
            })
            ->when(isset($filters['state_id']), function (Builder $query) use ($filters) {
                $query->where('state_id', $filters['state_id']);
            })
            ->when(isset($filters['country_id']), function (Builder $query) use ($filters) {
                $query->where('country_id', $filters['country_id']);
            })
            ->when(isset($filters['created_by']), function (Builder $query) use ($filters) {
                $query->where('created_by', $filters['created_by']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
