<?php

namespace App\Repositories;

use App\Models\Club;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ClubRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Club::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);
        $query
        ->when(isset($filters['club_category_id']), function (Builder $query) use ($filters) {
            $query->where('club_category_id', $filters['club_category_id']);
        })
        ->when(isset($filters['code']) , function (Builder $query) use ($filters) {
            $query->where('code', $filters['code']);
        })
        ->when(isset($filters['name']) , function (Builder $query) use ($filters) {
            $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
        });

        return $query;

    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
