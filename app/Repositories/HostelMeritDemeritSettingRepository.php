<?php

namespace App\Repositories;

use App\Models\HostelMeritDemeritSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class HostelMeritDemeritSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelMeritDemeritSetting::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%" . $filters['name'] . "%");
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
