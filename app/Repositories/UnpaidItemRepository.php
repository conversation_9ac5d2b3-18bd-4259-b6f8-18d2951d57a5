<?php

namespace App\Repositories;

use App\Enums\ClassType;
use App\Models\BillingDocumentLineItem;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Services\Billing\BillingDocumentService;
use App\Services\Billing\DiscountSettingService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class UnpaidItemRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return UnpaidItem::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['bill_to_type']) && isset($filters['bill_to_id']), function (Builder $query) use ($filters) {
                $query->where('bill_to_type', $filters['bill_to_type'])
                    ->where('bill_to_id', $filters['bill_to_id']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['period_from']), function (Builder $query) use ($filters) {
                $query->where('period', '>=', $filters['period_from']);
            })
            ->when(isset($filters['period_to']), function (Builder $query) use ($filters) {
                $query->where('period', '<=', $filters['period_to']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getUnpaidItemsByIds(array $unpaid_item_ids): Collection
    {
        if (!count($unpaid_item_ids)) {
            return new Collection;
        }

        return $this->getAll([
            'includes' => [
                'billTo',
                'product',
            ],
            'id' => $unpaid_item_ids,
            'order_by' => ['period' => 'asc'],
        ]);
    }

    public function getStudentOutstandingBalanceReportByClass(array $semester_class_ids, $date_to)
    {

        $semester_classes = SemesterClass::with([
            'homeroomTeacher' => function ($query) {
                $query->select('id', 'name');
            },
            'classModel' => function ($query) {
                $query->select('id', 'name');
            },
        ])
            ->whereIn('id', $semester_class_ids)
            ->get();

        $related_student_ids = [];

        $primary_class_students_group = LatestPrimaryClassBySemesterSettingView::with([
            'student' => function ($query) use ($date_to) {
                $query->select('id', 'student_number', 'name');
                $query->withWhereHas('unpaidItems', function ($query) use ($date_to) {
                    $query->with(['product'])->select('id', 'bill_to_type', 'bill_to_id', 'status', 'description', 'period', 'amount_before_tax', 'gl_account_code', 'product_id')
                        ->where('status', UnpaidItem::STATUS_UNPAID)
                        ->where('period', '<=', $date_to);
                });
            },
        ])
            ->whereHas('student', function ($query) {
                $query->where('is_active', true);
            })
            ->whereHas('student.unpaidItems', function ($query) use ($date_to) {
                $query->where('status', UnpaidItem::STATUS_UNPAID)
                    ->where('period', '<=', $date_to);
            })
            ->whereIn('semester_class_id', $semester_class_ids)
            ->get()
            ->groupBy('semester_class_id');

        $non_primary_class_student_group = StudentClass::with([
            'student' => function ($query) use ($date_to) {
                $query->select('id', 'student_number', 'name');
                $query->withWhereHas('unpaidItems', function ($query) use ($date_to) {
                    $query->with(['product'])->select('id', 'bill_to_type', 'bill_to_id', 'status', 'description', 'period', 'amount_before_tax', 'gl_account_code', 'product_id')
                        ->where('status', UnpaidItem::STATUS_UNPAID)
                        ->where('period', '<=', $date_to);
                });
            },
        ])
            ->whereHas('student', function ($query) {
                $query->where('is_active', true);
            })
            ->where('class_type', '!=', ClassType::PRIMARY)
            ->whereIn('semester_class_id', $semester_class_ids)
            ->where('is_active', true)
            ->get()
            ->groupBy('semester_class_id');

        $students_group_by_semester_class_id = $primary_class_students_group
            ->union($non_primary_class_student_group)
            ->mapWithKeys(function ($semester_class, $semester_class_id) use (&$related_student_ids) {
                $student_ids = $semester_class->pluck('student_id')->unique()->toArray();
                $related_student_ids = array_unique(array_merge($related_student_ids, $student_ids));

                return [
                    // semester class id as array key, pluck student with unpaid items
                    $semester_class_id => $semester_class->pluck('student')->sortBy('student_number')->values()
                ];
            })
            ->all();


        // merge both array, use array_replace to preserve array key
        $students_group_by_semester_class_id = $this->calculateDiscount($related_student_ids, $students_group_by_semester_class_id);

        $locale = app()->getLocale();
        $students_group_by_semester_class = [];
        $debtors_and_arrears_group_by_year_month = [];
        $debtors_and_arrears_group_by_consecutive_year_month = [];
        foreach ($semester_classes as $semester_class) {
            $students = $students_group_by_semester_class_id[$semester_class->id] ?? collect();
            $no = 1;
            $class_total_amount = 0;
            // students with debt
            foreach ($students as $student) {

                $description = [];
                $months_group_by_year = [];
                $student_total_amount = 0;
                $month_concat_string_array_key = null;

                foreach ($student->unpaidItems->sortBy('period') as $unpaid_item) {
                    $date = Carbon::parse($unpaid_item->period)->locale($locale);
                    $year = $date->year;
                    $month = $date->month;
                    $formatted_month = $date->translatedFormat('M');

                    $description[] = $unpaid_item->product->getTranslation('name', $locale) . ' (' . $formatted_month . ')';

                    $months_group_by_year[$year][$month] = $formatted_month;
                    $student_total_amount = bcadd($student_total_amount, $unpaid_item->amount_before_tax, 2);

                    // array key e.g. 2025(Jan)
                    $year_with_month_array_key = "{$year}({$month})";
                    // if student has 2 unpaid items in same year and month, should be considered as 1 debtor
                    $debtors_and_arrears_group_by_year_month[$year_with_month_array_key][$student->id][$unpaid_item->id] = $unpaid_item->amount_before_tax;
                }

                // e.g. 2024(Dec),2025(Jan,Feb) and 2025(Feb)
                $month_concat_string_array_key = $this->convertMonthsGroupByYearToString($months_group_by_year);
                // if student has 2 semester classes, e.g. 1 primary 1 society, should be considered as 1 debtor
                $debtors_and_arrears_group_by_consecutive_year_month[$month_concat_string_array_key][$student->id] = $student_total_amount;

                $class_total_amount = bcadd($class_total_amount, $student_total_amount, 2);

                $students_group_by_semester_class[$semester_class->id]['students'][] = [
                    'no' => $no,
                    'student_no' => $student->student_number,
                    'name' => $student->getTranslations('name'),
                    'description' => implode(', ', $description),
                    'months_group_by_year' => $months_group_by_year,
                    'month_concat_string' => $month_concat_string_array_key,
                    'student_total_amount' => number_format($student_total_amount, 2),
                ];

                $no++;
            }
            $students_group_by_semester_class[$semester_class->id]['class_details'] = [
                'class_name' => $semester_class->classModel->getTranslation('name', $locale),
                'homeroom_teacher' => $semester_class->homeroomTeacher ? $semester_class->homeroomTeacher->getTranslation('name', $locale) : "-",
                'class_total_amount' => number_format($class_total_amount, 2),
            ];
        }

        $all_classes_total_debtors = 0;
        $all_classes_total_arrears = 0;
        $total_debtors_and_arrears_group_by_consecutive_year_month = [];
        foreach ($debtors_and_arrears_group_by_consecutive_year_month as $consecutive_year_month => $debtors) {
            $debtors_count = 0;
            $sum = 0;
            foreach ($debtors as $arrear) {
                $sum = bcadd($sum, $arrear, 2);
                $debtors_count++;
            }
            $total_debtors_and_arrears_group_by_consecutive_year_month[$consecutive_year_month]['total_debtors'] = number_format($debtors_count);
            $total_debtors_and_arrears_group_by_consecutive_year_month[$consecutive_year_month]['total_arrears'] = number_format($sum, 2);

            $all_classes_total_arrears = bcadd($all_classes_total_arrears, $sum, 2);
            $all_classes_total_debtors += $debtors_count;
        }

        $total_debtors_and_arrears_group_by_year_month = [];
        foreach ($debtors_and_arrears_group_by_year_month as $year_month => $debtors) {
            $sum = 0;
            $debtors_count = count($debtors);
            foreach ($debtors as $arrears) {
                foreach ($arrears as $arrear) {
                    $sum = bcadd($sum, $arrear, 2);
                }
            }
            $total_debtors_and_arrears_group_by_year_month[$year_month]['total_debtors'] = number_format($debtors_count);
            $total_debtors_and_arrears_group_by_year_month[$year_month]['total_arrears'] = number_format($sum, 2);
        }

        ksort($total_debtors_and_arrears_group_by_year_month, SORT_NATURAL);
        ksort($total_debtors_and_arrears_group_by_consecutive_year_month, SORT_NATURAL);

        return [
            'students_group_by_semester_class' => $students_group_by_semester_class,
            'total_debtors_and_arrears_group_by_year_month' => $total_debtors_and_arrears_group_by_year_month,
            'total_debtors_and_arrears_group_by_consecutive_year_month' => $total_debtors_and_arrears_group_by_consecutive_year_month,
            'all_classes_total_arrears' => number_format($all_classes_total_arrears, 2),
            'all_classes_total_debtors' => $all_classes_total_debtors,
        ];
    }

    private function convertMonthsGroupByYearToString(array $months_group_by_year): string
    {
        $month_concat_string = null;
        foreach ($months_group_by_year as $year => $months) {
            if ($month_concat_string !== null) {
                $month_concat_string .= ', ';
            }
            $month_concat_string .= $year . '(' . implode(', ', $months) . ')';
        }
        return $month_concat_string;
    }

    /**
     * Function to calculate discount for each student
     * Copied directly from BillingDocumentService
     */
    private function calculateDiscount(array $related_student_ids, array $students_group_by_semester_class_id)
    {
        $discounts_in_DB = (new DiscountSettingRepository())->getAll([
            'is_active' => true,
            'userable_id' => $related_student_ids,
            'userable_type' => Student::class,
            'still_available' => true,
            'order_by' => [
                'id' => 'asc'
            ]
        ])->groupBy('userable_id');

        foreach ($students_group_by_semester_class_id as $students) {
            foreach ($students as $student) {
                $bill_to_party = $student;

                $unpaid_items = $student->unpaidItems;

                $discounts = collect([]);

                if (count($unpaid_items) > 0) {
                    /** @var BillingDocumentService $billing_document_service */
                    $billing_document_service = resolve(BillingDocumentService::class);

                    $billing_document_service
                        ->setBillToParty($bill_to_party)
                        ->setDocumentDate(now()->startOfDay());

                    foreach ($unpaid_items as $d) {
                        $line_item = new BillingDocumentLineItem([
                            'is_discount' => false,
                            'amount_before_tax' => $d->amount_before_tax,
                            'billable_item_id' => $d->id,
                            'billable_item_type' => get_class($d),
                            'gl_account_code' => $d->gl_account_code,
                        ]);

                        $line_item->setRelation('billableItem', $d);

                        $billing_document_service->addLineItem($line_item);
                    }


                    $apply_dates = $billing_document_service->determineApplyDates();

                    $current_student_discounts = $discounts_in_DB[$bill_to_party->id] ?? collect();

                    $discounts = resolve(DiscountSettingService::class)->getAvailableDiscountsByGroup($current_student_discounts, $apply_dates);


                    // foreach line item, determine can apply discount or not
                    // if can, add a new line item to minus the discounted value
                    $default_date = $billing_document_service->getDocumentDate()->toDateString();

                    $eligible_discounts = [];

                    foreach ($billing_document_service->getToBePaidLineItems() as $line_item) {

                        $apply_date = $line_item->getApplyDateWithDefaultValue($default_date);
                        $gl_account_code = $line_item->gl_account_code;
                        $balance_amount_to_be_deducted = $line_item->balance_discountable_amount_before_tax;

                        if (isset($discounts[$apply_date][$gl_account_code])) {

                            foreach ($discounts[$apply_date][$gl_account_code] as $discount_setting) {

                                $discount_amount = $discount_setting->calculateDiscountAmount($line_item->amount_before_tax);
                                $final_discount_amount = 0;

                                if (bccomp($discount_amount, $balance_amount_to_be_deducted, 2) === 1) {
                                    $final_discount_amount = $balance_amount_to_be_deducted;
                                    $balance_amount_to_be_deducted = 0;
                                } else {
                                    $final_discount_amount = $discount_amount;
                                    $balance_amount_to_be_deducted = bcsub($balance_amount_to_be_deducted, $discount_amount, 2);
                                }

                                if (bccomp($final_discount_amount, 0, 2) === 1) {
                                    $eligible_discounts[] = [
                                        'original_line_item' => $line_item,
                                        'discount_setting' => $discount_setting,
                                        'amount_before_tax' => $final_discount_amount,
                                    ];
                                }

                            }
                        }
                    }

                    // there can be multiple discounts for a billable item
                    $discounts = collect($eligible_discounts)->groupBy('original_line_item.billable_item_id');
                }

                foreach ($unpaid_items as &$unpaid_item) {
                    if (isset($discounts[$unpaid_item->id])) {
                        // sum all discounts for this billable item
                        $total_discounts = $discounts[$unpaid_item->id]->sum('amount_before_tax');

                        $unpaid_item->amount_before_tax = bcsub($unpaid_item->amount_before_tax, $total_discounts, 2);
                    }
                }
            }
        }

        return $students_group_by_semester_class_id;
    }
}
