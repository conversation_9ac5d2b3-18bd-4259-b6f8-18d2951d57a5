<?php

namespace App\Repositories;

use App\Models\Timeslot;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TimeslotRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Timeslot::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['timetable_id']), function (Builder $query) use ($filters) {
                $query->where('timetable_id', $filters['timetable_id']);
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['day']), function (Builder $query) use ($filters) {
                $query->where('day', $filters['day']);
            })
            ->when(isset($filters['active_timetable']), function (Builder $query) use ($filters) {
                $query->whereHas('timetable', function (Builder $query) use ($filters) {
                    if ($filters['active_timetable']) {
                        $query->where('is_active', true);
                    }
                });
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
