<?php

namespace App\Repositories;

use App\Models\Student;
use App\Models\StudentGradingFramework;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class StudentGradingFrameworkRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return StudentGradingFramework::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['grading_framework_id']), function (Builder $query) use ($filters) {
                $query->where('grading_framework_id', $filters['grading_framework_id']);
            })
            ->when(isset($filters['academic_year']), function (Builder $query) use ($filters){
                $query->where('academic_year', $filters['academic_year']);
            });
    }

    public function expireAllForStudent(Student $student)
    {

        StudentGradingFramework::where('student_id', $student->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);


        // clear any cache
        \Cache::forget('active-student-grading-framework-' . $student->id);

    }

    public function expireStudentGradingFrameworkByDate($expiry_date){
        StudentGradingFramework::where([
            'effective_to' => $expiry_date,
            'is_active' => true
        ])
        ->update(['is_active' => false]);
    }

}
