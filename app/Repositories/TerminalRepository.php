<?php

namespace App\Repositories;

use App\Models\Terminal;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TerminalRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Terminal::class;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['ids']), function (Builder $query) use ($filters) {
                $query->whereIn('id', $filters['ids']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->whereIn('merchant_id', $filters['merchant_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
