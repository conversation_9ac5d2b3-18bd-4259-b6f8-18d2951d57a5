<?php

namespace App\Repositories;

use App\Models\AnnouncementGroup;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AnnouncementGroupRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return AnnouncementGroup::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                return $query->where('name', 'LIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                return $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function syncStudents(AnnouncementGroup $announcement_group, array $student_ids): void
    {
        $announcement_group->students()->sync($student_ids);
    }

    public function syncEmployees(AnnouncementGroup $announcement_group, array $employee_ids): void
    {
        $announcement_group->employees()->sync($employee_ids);
    }

    public function getStudentIdsByAnnouncementGroupIds(array $announcement_group_ids): array
    {
        return $this->with('students')
            ->find($announcement_group_ids)
            ->pluck('students.*.id')
            ->flatten()
            ->unique()
            ->values()
            ->all();
    }

    public function getEmployeeIdsByAnnouncementGroupIds(array $announcement_group_ids): array
    {
        return $this->with('employees')
            ->find($announcement_group_ids)
            ->pluck('employees.*.id')
            ->flatten()
            ->unique()
            ->values()
            ->all();
    }
}
