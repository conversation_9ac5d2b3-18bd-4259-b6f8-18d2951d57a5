<?php

namespace App\Repositories;

use App\Models\TimeslotOverride;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TimeslotOverrideRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return TimeslotOverride::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['student_id'])) {
                    $query->whereIn('student_id', $filters['student_id']);
                } else {
                    $query->where('student_id', $filters['student_id']);
                }
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                if (is_array($filters['date'])) {
                    $query->whereIn('date', $filters['date']);
                } else {
                    $query->where('date', $filters['date']);
                }
            })
            ->when(isset($filters['date_from']), function (Builder $query) use ($filters) {
                $query->where('date', '>=', $filters['date_from']);
            })
            ->when(isset($filters['date_to']), function (Builder $query) use ($filters) {
                $query->where('date', '<=', $filters['date_to']);
            })
            ->when(isset($filters['period']), function (Builder $query) use ($filters) {
                if (is_array($filters['period'])) {
                    $query->whereIn('period', $filters['period']);
                } else {
                    $query->where('period', $filters['period']);
                }
            })
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['employee_id'])) {
                    $query->whereIn('employee_id', $filters['employee_id']);
                } else {
                    $query->where('employee_id', $filters['employee_id']);
                }
            })
            ->when(isset($filters['inherit_from_school_attendance']), function (Builder $query) use ($filters) {
                $query->where('inherit_from_school_attendance', $filters['inherit_from_school_attendance']);
            })
            ->when(isset($filters['class_attendance_required']), function (Builder $query) use ($filters) {
                $query->where('class_attendance_required', $filters['class_attendance_required']);
            })
            ->when(isset($filters['is_empty']), function (Builder $query) use ($filters) {
                $query->where('is_empty', $filters['is_empty']);
            });

        if (!(isset($filters['order_by']['id']) || (isset($filters['order_by']) && is_array($filters['order_by']) && in_array('id', $filters['order_by'])))) {
            // Always do this to prevent pagination issue
            $query->orderByDesc('id');
        }

        return $query;
    }

    // pls use TimeslotOverrideService's batchDeleteTimeslotOverride()
    public function batchDeleteByIds(array $id): bool
    {
        if (count($id) === 0) {
            return true;
        }

        return $this->getQuery(['id' => $id])->delete();
    }
}
