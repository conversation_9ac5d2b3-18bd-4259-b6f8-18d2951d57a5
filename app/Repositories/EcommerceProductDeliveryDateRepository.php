<?php

namespace App\Repositories;

use App\Models\EcommerceProduct;
use App\Models\EcommerceProductDeliveryDate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class EcommerceProductDeliveryDateRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceProductDeliveryDate::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function deleteByProduct(EcommerceProduct|Model $product): void
    {
        $product->deliveryDates()->delete();
    }

    public function deleteByDeliveryDates(array $delivery_dates): void
    {
        if ( count($delivery_dates) === 0 ){
            return;
        }
        EcommerceProductDeliveryDate::query()
            ->whereIn('delivery_date', $delivery_dates)
            ->delete();
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['delivery_date_from']), function (Builder $query) use ($filters) {
                $query->where('delivery_date', '>=', $filters['delivery_date_from']);
            })
            ->when(isset($filters['delivery_date_to']), function (Builder $query) use ($filters) {
                $query->where('delivery_date', '<=', $filters['delivery_date_to']);
            });
    }
}
