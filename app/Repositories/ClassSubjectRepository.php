<?php

namespace App\Repositories;

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Subject;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ClassSubjectRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ClassSubject::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        $query = parent::getQuery($filters);

        return $query
            ->select('class_subjects.*')
            ->when(isset($filters['semester_class_id']) , function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['subject_id']) , function (Builder $query) use ($filters) {
                $query->where('subject_id', $filters['subject_id']);
            })
            ->when(isset($filters['class_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('semesterClass.classModel', 'type', $filters['class_type']);
            })
            ->when(isset($filters['semester_setting_id']) , function (Builder $query) use ($filters) {
                $query->whereRelation('semesterClass', 'semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($order_by['class_name']) , function (Builder $query) use ($order_by) {
                $query->join('semester_classes', 'semester_classes.id', '=', 'class_subjects.semester_class_id')
                    ->join('classes', 'classes.id', '=', 'semester_classes.class_id');

                $order_by = [
                    'name' => $order_by['class_name']
                ];

                $this->setupOrderBy($query, $order_by, ClassModel::class, 'classes.');
            })
            ->when(isset($order_by['subject_name']), function (Builder $query) use ($order_by) {
                $query->join('subjects', 'subjects.id', '=', 'class_subjects.subject_id');

                $order_by = [
                    'name' => $order_by['subject_name']
                ];

                $this->setupOrderBy($query, $order_by, Subject::class, 'subjects.');
            })
            ->orderByDesc('class_subjects.id');
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    /**
     * Get largest number of students under $semester_class_id
     */
    public function getBySemesterIds($semester_class_id)
    {
        return $this
            ->getQuery()
            ->with('students')
            ->withCount('students')
            ->orderByDesc('students_count')
            ->where('semester_class_id', $semester_class_id)
            ->first();
    }
}
