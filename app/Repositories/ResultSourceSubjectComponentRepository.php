<?php

namespace App\Repositories;

use App\Models\ResultSourceSubjectComponent;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ResultSourceSubjectComponentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ResultSourceSubjectComponent::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['result_source_subject_id']), function (Builder $query) use ($filters) {
                $query->where('result_source_subject_id', $filters['result_source_subject_id']);
            });
    }


}
