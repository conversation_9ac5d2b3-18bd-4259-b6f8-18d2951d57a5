<?php

namespace App\Repositories;

use App\Models\EcommerceProductSubCategory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EcommerceProductSubCategoryRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceProductSubCategory::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['ids']), function (Builder $query) use ($filters) {
                $query->whereIn('id', $filters['ids']);
            })
            ->when(isset($filters['product_category_id']), function (Builder $query) use ($filters) {
                $query->where('product_category_id', $filters['product_category_id']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', '%'.$filters['name'].'%');
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->whereRelation('category', 'type', $filters['type']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)
            ->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
