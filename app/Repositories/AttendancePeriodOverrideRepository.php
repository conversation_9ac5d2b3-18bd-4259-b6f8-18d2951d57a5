<?php

namespace App\Repositories;

use App\Models\AttendancePeriodOverride;
use App\Models\UserableView;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;

class AttendancePeriodOverrideRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return AttendancePeriodOverride::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        return parent::getQuery($filters)
            ->when(isset($filters['period']), function (Builder $query) use ($filters) {
                if(is_array($filters['period'])){
                    $query->whereIn('period', $filters['period']);
                }else{
                    $query->where('period', $filters['period']);
                }
            })
            ->when(isset($filters['attendance_recordable_type']) && isset($filters['attendance_recordable_id']), function (Builder $query) use (&$filters) {
                if(is_array($filters['attendance_recordable_type'])){
                    $query->whereIn('attendance_recordable_type', $filters['attendance_recordable_type']);
                }else{
                    $query->where('attendance_recordable_type', $filters['attendance_recordable_type']);
                }

                if(is_array($filters['attendance_recordable_id'])){
                    $query->whereIn('attendance_recordable_id', $filters['attendance_recordable_id']);
                }else{
                    $query->where('attendance_recordable_id', $filters['attendance_recordable_id']);
                }
            })
            ->when(isset($filters['period_from']) && isset($filters['period_to']), function (Builder $query) use (&$filters) {
                $query->where('period', '>=', $filters['period_from'])
                    ->where('period', '<=', $filters['period_to']);
            })
            ->when(isset($filters['created_by_employee_id']), function (Builder $query) use ($filters) {
                $query->where('created_by_employee_id', $filters['created_by_employee_id']);
            })
            ->when(isset($filters['leave_application_id']), function (Builder $query) use ($filters) {
                if(is_array($filters['leave_application_id'])){
                    $query->whereIn('leave_application_id', $filters['leave_application_id']);
                }else{
                    $query->where('leave_application_id', $filters['leave_application_id']);
                }
            })
            ->when(isset($filters['id']) , function (Builder $query) use ($filters) {
                if(is_array($filters['id'])){
                    $query->whereIn('id', $filters['id']);
                }else{
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($order_by['name']) || isset($order_by['number']), function (Builder $query) use ($order_by) {
                $query
                    ->join('userable_views', function (JoinClause $join) {
                            $join->on('userable_views.userable_id', '=', 'attendance_period_override.attendance_recordable_id')
                                ->whereColumn('userable_views.userable_type', '=', 'attendance_period_override.attendance_recordable_type');
                    })
                    ->when(isset($order_by['name']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, ['name' => $order_by['name']], UserableView::class, 'userable_views.');
                    })
                    ->when(isset($order_by['number']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, ['number' => $order_by['number']], UserableView::class, 'userable_views.');
                    });
            })
            ->when(isset($filters['has_leave_application']), function (Builder $query) use ($filters) {
                if ($filters['has_leave_application'] == true) {
                    $query->whereHas('leaveApplications');
                } else {
                    $query->whereDoesntHave('leaveApplications');
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function batchDeleteByIds(array $ids): bool
    {
        if ( count($ids) === 0 ) {
            return true;
        }

        return $this->getQuery(['id' => $ids])->delete();
    }
}
