<?php

namespace App\Repositories;

use App\Models\PendingStudentEmployeeStatusChange;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PendingStudentEmployeeStatusChangeRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PendingStudentEmployeeStatusChange::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query->when(isset($filters['type']), function (Builder $query) use ($filters) {
            $query->where('type', $filters['type']);
        })->when(isset($filters['execution_date']), function (Builder $query) use ($filters) {
            $query->where('execution_date', $filters['execution_date']);
        })->when(isset($filters['status']), function (Builder $query) use ($filters) {
            $query->where('status', $filters['status']);
        });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getByIds(array $ids, mixed $with): Collection
    {
        return $this->getQuery(['with' => $with])
            ->whereIn('id', $ids)
            ->get();
    }

    public function batchUpdateByIds(array $ids, array $data)
    {
        $model = resolve($this->getModelClass());
        $model = $model->whereIn('id', $ids)->update($data);
    }
}
