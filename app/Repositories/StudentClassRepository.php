<?php

namespace App\Repositories;

use App\Models\StudentClass;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;

class StudentClassRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return StudentClass::class;
    }

    public function bulkUpdate(array $ids, array $data): void
    {
        if (empty($ids)) {
            return;
        }
        $filters = ['id' => $ids];

        $this->getQuery($filters)->update($data);
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function bulkUpdateStudentClassSeats(array $seat_updates): int
    {
        if (empty($seat_updates)) {
            return 0;
        }

        $cases = [];
        $ids = [];

        foreach ($seat_updates as $update) {
            $cases[] = sprintf(
                "WHEN id = %d THEN %s",
                $update['id'],
                $update['seat_no'] === null ? 'NULL' : $update['seat_no']
            );

            $ids[] = $update['id'];
        }

        if (empty($cases)) {
            return 0;
        }

        return StudentClass::whereIn('id', $ids)
            ->update([
                'seat_no' => DB::raw(sprintf(
                    'CASE %s ELSE seat_no END',
                    implode(' ', $cases)
                ))
            ]) ?? 0;
    }

    public function getActiveStudentsBySemesterClassIds(array $semester_class_ids): SupportCollection
    {
        return DB::table('student_classes')
            ->join('students', 'students.id', '=', 'student_classes.student_id')
            ->whereIn('student_classes.semester_class_id', $semester_class_ids)
            ->where('student_classes.is_active', true)
            ->select('student_classes.id', 'student_classes.semester_class_id', 'students.name as student_name')
            ->get();
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['semester_setting_ids']), function (Builder $query) use ($filters) {
                $query->whereIn('semester_setting_id', $filters['semester_setting_ids']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['semester_class_id'])) {
                    $query->whereIn('semester_class_id', $filters['semester_class_id']);
                } else {
                    $query->where('semester_class_id', $filters['semester_class_id']);
                }
            })
            ->when(isset($filters['class_type']), function (Builder $query) use ($filters) {
                $query->where('class_type', $filters['class_type']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['student_id'])) {
                    $query->whereIn('student_id', $filters['student_id']);
                } else {
                    $query->where('student_id', $filters['student_id']);
                }
            })
            ->when(isset($filters['class_enter_date']), function (Builder $query) use ($filters) {
                $query->whereDate('class_enter_date', $filters['class_enter_date']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['for_seats']), function (Builder $query) use ($filters) {
                $query->whereHas('student'); // only get student_classes with student

                $query->where(function ($query) {
                    $query->where('is_active', false)->whereNotNull('seat_no')
                        ->orWhere('is_active', true);
                });

                //  default order for seating
                $query->orderBy('seat_no');
            });
    }
}
