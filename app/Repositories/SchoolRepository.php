<?php

namespace App\Repositories;

use App\Models\School;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SchoolRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return School::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['level']), function (Builder $query) use ($filters) {
                $query->where('level', $filters['level']);
            })
            ->when(isset($filters['state_id']), function (Builder $query) use ($filters) {
                $query->where('state_id', $filters['state_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
