<?php

namespace App\Repositories;

use App\Models\Bank;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class BankRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Bank::class;
    }
    
    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['swift_code']), function (Builder $query) use ($filters) {
                $query->where('swift_code', $filters['swift_code']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
