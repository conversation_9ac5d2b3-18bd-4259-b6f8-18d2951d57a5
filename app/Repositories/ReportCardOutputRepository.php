<?php

namespace App\Repositories;

use App\Models\ReportCardOutput;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ReportCardOutputRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ReportCardOutput::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['student_grading_framework_id']), function (Builder $query) use ($filters) {
                if ( is_array($filters['student_grading_framework_id']) ) {
                    $query->whereIn('student_grading_framework_id', $filters['student_grading_framework_id']);
                }else{
                    $query->where('student_grading_framework_id', $filters['student_grading_framework_id']);
                }
            });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

}
