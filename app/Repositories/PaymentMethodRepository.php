<?php

namespace App\Repositories;

use App\Models\PaymentMethod;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PaymentMethodRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PaymentMethod::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                if (is_array($filters['code'])) {
                    $query->whereIn('code', $filters['code']);
                } else {
                    $query->where('code', $filters['code']); // Code should be easy to remember, no need to use ILIKE
                }
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%". $filters['name'] ."%");
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
