<?php

namespace App\Repositories;

use App\Models\LibraryBookLoan;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class LibraryBookLoanRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return LibraryBookLoan::class;
    }

    public function hasBookLoanByMemberId($member_id)
    {
        return $this->first(['member_id' => $member_id], false) !== null;
    }

    public function hasBookLoanByBookId($book_id)
    {
        return $this->first(['book_id' => $book_id], false) !== null;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this
            ->getQuery($filters)
            ->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $includes = Arr::get($filters, 'includes', []);
        $includes = array_merge($includes, ['book', 'member']);

        $filters['includes'] = $includes;
        $query = parent::getQuery($filters);

        $query->when(isset($filters['ids']), function (Builder $query) use ($filters) {
            $query->whereIn('id', $filters['ids']);
        })
            ->when(isset($filters['is_overdue']), function (Builder $query) use ($filters) {
                if ($filters['is_overdue']) {
                    $query->where('due_date', '<', now(config('school.timezone'))->toDateString());
                } else {
                    $query->where('due_date', '>=', now(config('school.timezone'))->toDateString());
                }
            })
            ->when(isset($filters['loan_status']), function (Builder $query) use ($filters) {
                $query->where('loan_status', $filters['loan_status']);
            })
            ->when(isset($filters['loan_statuses']), function (Builder $query) use ($filters) {
                $query->whereIn('loan_status', $filters['loan_statuses']);
            })
            ->when(isset($filters['empty_return_date']), function (Builder $query) use ($filters) {
                $query->whereNull('return_date');
            })
            ->when(isset($filters['book_id']), function (Builder $query) use ($filters) {
                $query->where('book_id', $filters['book_id']);
            })
            ->when(isset($filters['member_id']), function (Builder $query) use ($filters) {
                $query->where('member_id', $filters['member_id']);
            })
            ->when(isset($filters['penalty_payment_status']), function (Builder $query) use ($filters) {
                $query->where('penalty_payment_status', $filters['penalty_payment_status']);
            })
            ->when(isset($filters['period_loan_date_from']), function (Builder $query) use ($filters) {
                $query->where('loan_date', '>=', $filters['period_loan_date_from']);
            })
            ->when(isset($filters['period_loan_date_to']), function (Builder $query) use ($filters) {
                $query->where('loan_date', '<=', $filters['period_loan_date_to']);
            })
            ->when(isset($filters['period_due_date_from']), function (Builder $query) use ($filters) {
                $query->where('due_date', '>=', $filters['period_due_date_from']);
            })
            ->when(isset($filters['period_due_date_to']), function (Builder $query) use ($filters) {
                $query->where('due_date', '<=', $filters['period_due_date_to']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->whereHas('member', function (Builder $query) use ($filters) {
                    $query->whereHasMorph(
                        'userable',
                        [Student::class],
                        function (Builder $query) use ($filters) {
                            $query->whereHas('classes', function ($query) use ($filters) {
                                $query->where('semester_setting_id', $filters['semester_setting_id'])
                                    ->where('is_active', true);
                            });
                        }
                    );
                });
            })
            ->when(isset($filters['book_language_id']), function (Builder $query) use ($filters) {
                $query->whereHas('book', function (Builder $query) use ($filters) {
                    $query->where('book_language_id', $filters['book_language_id']);
                });
            })
            ->when(isset($filters['member_type']), function (Builder $query) use ($filters) {
                $query->whereHas('member', function (Builder $query) use ($filters) {
                    $query->where('type', $filters['member_type']);
                });
            })
            ->when(isset($filters['book_category_id']), function (Builder $query) use ($filters) {
                $query->whereHas('book.bookCategory', function (Builder $query) use ($filters) {
                    $query->where('master_book_categories.id', $filters['book_category_id']);
                });
            });

        return $query;
    }
}
