<?php

namespace App\Repositories;

use App\Models\CounsellingCaseRecord;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CounsellingCaseRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return CounsellingCaseRecord::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['created_by_employee_id']), function (Builder $query) use ($filters) {
                $query->where('created_by_employee_id', $filters['created_by_employee_id']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['visit_date']), function (Builder $query) use ($filters) {
                $query->whereDate('visit_datetime', $filters['visit_date']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
