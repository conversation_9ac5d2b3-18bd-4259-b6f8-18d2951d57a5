<?php

namespace App\Repositories;

use App\Models\SchoolAttendancePeriodOverride;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SchoolAttendancePeriodOverrideRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SchoolAttendancePeriodOverride::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['from']), function (Builder $query) use ($filters) {
                $query->where('from', '>=', $filters['from']);
            })
            ->when(isset($filters['to']), function (Builder $query) use ($filters) {
                $query->where('to', '<=', $filters['to']);
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->where('from', '<=', $filters['date'])
                    ->where('to', '>=', $filters['date']);
            })
            ->when(isset($filters['dates']), function (Builder $query) use ($filters) {

                $query->where(function ($q) use ($filters) {
                    foreach ($filters['dates'] as $date) {
                        $q->orWhere(function ($q2) use ($date) {
                            $q2->where('from', '<=', $date)
                                ->where('to', '>=', $date);
                        });
                    }
                });

            })
            ->when(isset($filters['remarks']), function (Builder $query) use ($filters) {
                $query->where('remarks', 'ILIKE', "%" . $filters['remarks'] . "%");
            });
    }

    public function getFirstPrioritySchoolAttendancePeriodOverride($date)
    {
        $filters = [
            'date' => $date,
            'order_by' => ['priority' => 'asc'],
        ];

        return $this->getQuery($filters)->first(); // Order by priority in ascending order
    }
}
