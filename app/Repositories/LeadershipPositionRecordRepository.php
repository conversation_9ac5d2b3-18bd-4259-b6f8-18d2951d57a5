<?php

namespace App\Repositories;

use App\Models\LeadershipPosition;
use App\Models\LeadershipPositionRecord;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class LeadershipPositionRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return LeadershipPositionRecord::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->select('leadership_position_records.*')
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['leadership_position_id']), function (Builder $query) use ($filters) {
                $query->where('leadership_position_id', $filters['leadership_position_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($order_by['student']), function (Builder $query) use ($order_by) {
                $query->join('students as s', 's.id', '=', 'leadership_position_records.student_id');

                $this->setupOrderBy($query, $order_by['student'], Student::class, 's.');
            })
            ->when(isset($order_by['leadershipPosition']), function (Builder $query) use ($order_by) {
                $query->join('master_leadership_positions', 'master_leadership_positions.id', '=', 'leadership_position_records.leadership_position_id');

                $this->setupOrderBy($query, $order_by['leadershipPosition'], LeadershipPosition::class, 'master_leadership_positions.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteRecordsBySemesterClassId(int $semester_class_id): void
    {
        LeadershipPositionRecord::query()
            ->where('semester_class_id', $semester_class_id)
            ->delete();
    }
}
