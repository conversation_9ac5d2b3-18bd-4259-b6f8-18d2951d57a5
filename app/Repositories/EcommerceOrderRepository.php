<?php

namespace App\Repositories;

use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Models\EcommerceOrder;
use App\Models\EcommerceOrderItem;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class EcommerceOrderRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceOrder::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getOrderListByStudent(array $filters)
    {
        return EcommerceOrder::query()
            ->select([
                'billing_documents.reference_no AS receipt_number',
                'ecommerce_order_items.product_delivery_date AS meal_date',
                'ecommerce_order_items.product_name AS items',
                'ecommerce_order_items.quantity as items_quantity',
            ])
            ->join('ecommerce_order_items', function (JoinClause $join) {
                $join->on('ecommerce_order_items.order_id', '=', 'ecommerce_orders.id');
            })
            ->leftJoin('billing_document_line_items', function (JoinClause $join) {
                $join->on('billing_document_line_items.billable_item_id', '=', 'ecommerce_order_items.id')
                    ->where('billing_document_line_items.billable_item_type', EcommerceOrderItem::class);
            })
            ->leftJoin('billing_documents', 'billing_documents.id', '=', 'billing_document_line_items.billing_document_id')
            ->whereBetween('ecommerce_order_items.product_delivery_date', [$filters['from_date'], $filters['to_date']])
            ->where('ecommerce_orders.status', EcommerceOrderStatus::COMPLETED->value)
            ->where('ecommerce_orders.payment_status', EcommerceOrderPaymentStatus::PAID->value)
            ->where('ecommerce_orders.merchant_type', $filters['merchant_type'])
            ->where('ecommerce_orders.buyer_userable_id', $filters['student_id'])
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('ecommerce_order_items.merchant_id', $filters['merchant_id']);
            })
            ->orderBy('billing_documents.reference_no', 'desc')
            ->orderBy('ecommerce_order_items.product_name', 'asc')
            ->get();
    }

    public function getMerchantSalesData(array $filters)
    {
        $from_date = Carbon::parse($filters['from_date']);
        $to_date = Carbon::parse($filters['to_date']);

        return EcommerceOrder::query()
            ->select([
                'ecommerce_order_items.product_delivery_date AS meal_date',
                'merchants.id as merchant_id',
                DB::raw('SUM(ecommerce_order_items.amount_before_tax + ecommerce_order_items.tax_amount) as total_sales'),
                DB::raw('SUM(ecommerce_order_items.quantity) as total_item_sold'),
                DB::raw('COUNT(DISTINCT ecommerce_orders.id) as total_receipt'),
                DB::raw('CASE
                    WHEN SUM(ecommerce_order_items.quantity) > 0
                    THEN ROUND(SUM(ecommerce_order_items.amount_before_tax + ecommerce_order_items.tax_amount) / SUM(ecommerce_order_items.quantity), 2)
                    ELSE 0
                    END as avg_per_item'),
                DB::raw('CASE
                    WHEN COUNT(DISTINCT ecommerce_orders.id) > 0
                    THEN ROUND(SUM(ecommerce_order_items.amount_before_tax + ecommerce_order_items.tax_amount) / COUNT(DISTINCT ecommerce_orders.id), 2)
                    ELSE 0
                    END as avg_per_receipt')
            ])
            ->join('ecommerce_order_items', 'ecommerce_order_items.order_id', '=', 'ecommerce_orders.id')
            ->join('merchants', 'merchants.id', '=', 'ecommerce_order_items.merchant_id')
            ->whereBetween('ecommerce_order_items.product_delivery_date', [$from_date, $to_date])
            ->where('ecommerce_orders.status', EcommerceOrderStatus::COMPLETED->value)
            ->where('ecommerce_orders.payment_status', EcommerceOrderPaymentStatus::PAID->value)
            ->where('ecommerce_orders.merchant_type', $filters['merchant_type'])
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('merchants.is_active', $filters['is_active']);
            })
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('ecommerce_order_items.merchant_id', $filters['merchant_id']);
            })
            ->groupBy('ecommerce_order_items.product_delivery_date', 'merchants.id')
            ->orderBy('meal_date', 'desc')
            ->get();
    }

    public function getOrderByDailyCollection(array $filters)
    {
        return EcommerceOrder::query()
            ->selectRaw(
                "
                ecommerce_order_items.product_id AS product_id,
                ecommerce_order_items.product_name AS product_name,
                ecommerce_order_items.product_unit_price AS unit_price,
                SUM(ecommerce_order_items.quantity) AS quantity,
                SUM(ecommerce_order_items.quantity * ecommerce_order_items.product_unit_price) AS total_price,
                merchants.name AS merchant_name
                "
            )
            ->join('ecommerce_order_items', function (JoinClause $join) {
                $join->on('ecommerce_order_items.order_id', '=', 'ecommerce_orders.id');
            })
            ->join('merchants', function (JoinClause $join) {
                $join->on('merchants.id', '=', 'ecommerce_order_items.merchant_id');
            })
            ->whereDate('ecommerce_order_items.product_delivery_date', $filters['date'])
            ->where('ecommerce_orders.status', EcommerceOrderStatus::COMPLETED->value)
            ->where('ecommerce_orders.payment_status', EcommerceOrderPaymentStatus::PAID->value)
            ->where('ecommerce_orders.merchant_type', $filters['merchant_type'])
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('ecommerce_order_items.merchant_id', $filters['merchant_id']);
            })
            ->where('merchants.is_active', true)
            ->orderBy('product_name', 'asc')
            ->groupBy('product_id', 'product_name', 'unit_price', 'merchant_name')
            ->get()
            ->map(function ($order) {
                $order->merchant_name = implode(' - ', json_decode($order->merchant_name, true)) ?? '-';

                return $order;
            });
    }

    public function getOrderTransactionData(array $filters): Collection
    {
        return EcommerceOrder::query()
            ->with('items')
            ->select([
                'ecommerce_orders.id',
                'billing_documents.reference_no',
                'ecommerce_orders.order_reference_number',
                'userable_views.name AS buyer_name',
                'userable_views.number AS buyer_number',
                'classes.name AS class_name',
                'billing_documents.amount_after_tax AS total_amount',
                'ecommerce_orders.created_at',
            ])
            ->join('userable_views', function ($join) {
                $join->on('userable_views.userable_id', '=', 'ecommerce_orders.buyer_userable_id')
                    ->whereColumn('userable_views.userable_type', '=', 'ecommerce_orders.buyer_userable_type');
            })
            ->leftJoin('student_classes', 'student_classes.id', '=', 'ecommerce_orders.recipient_student_class_id')
            ->leftJoin('semester_classes', 'semester_classes.id', '=', 'student_classes.semester_class_id')
            ->leftJoin('classes', 'classes.id', '=', 'semester_classes.class_id')
            ->join('ecommerce_order_items', function (JoinClause $join) use ($filters) {
                $join->on('ecommerce_order_items.order_id', '=', 'ecommerce_orders.id')
                    ->where('ecommerce_order_items.id', '=', function ($query) use ($filters) {
                        $query->select('ecommerce_order_items.id')
                            ->from('ecommerce_order_items')
                            ->whereColumn('order_id', 'ecommerce_orders.id')
                            ->orderBy('id', 'desc')
                            ->limit(1);
                    });
            })
            ->leftJoin('billing_document_line_items', function (JoinClause $join) {
                $join->on('billing_document_line_items.billable_item_id', '=', 'ecommerce_order_items.id')
                    ->where('billing_document_line_items.billable_item_type', EcommerceOrderItem::class);
            })
            ->leftJoin('billing_documents', 'billing_documents.id', '=', 'billing_document_line_items.billing_document_id')
            ->whereBetween('ecommerce_orders.created_at', [$filters['from_date'], $filters['to_date']])
            ->where('ecommerce_orders.merchant_type', $filters['merchant_type'])
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->whereHas('items', function ($query) use ($filters) {
                    $query->where('merchant_id', $filters['merchant_id']);
                });
            })
            ->orderBy('ecommerce_orders.created_at', 'desc')->get();
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->whereBetween('created_at', [
                    Carbon::parse($filters['date'], config('school.timezone'))->startOfDay()->tz(config('app.timezone')),
                    Carbon::parse($filters['date'], config('school.timezone'))->endOfDay()->tz(config('app.timezone'))
                ]);
            })
            ->when(isset($filters['start_date']), function (Builder $query) use ($filters) {
                $query->where('created_at', '>=', $filters['start_date']);
            })
            ->when(isset($filters['end_date']), function (Builder $query) use ($filters) {
                $query->where('created_at', '<=', $filters['end_date']);
            })
            ->when(isset($filters['from_cancel_before_datetime']), function (Builder $query) use ($filters) {
                $query->where('cancel_before_datetime', '>=', $filters['from_cancel_before_datetime']);
            })
            ->when(isset($filters['to_cancel_before_datetime']), function (Builder $query) use ($filters) {
                $query->where('cancel_before_datetime', '<=', $filters['to_cancel_before_datetime']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->whereHas('recipientStudentClass', function ($query) use ($filters) {
                    $query->where('semester_class_id', $filters['semester_class_id']);
                });
            })
            ->when(isset($filters['has_student_class']), function (Builder $query) {
                $query->whereNotNull('recipient_student_class_id');
            })
            ->when(isset($filters['student_ids']) || isset($filters['student_id']), function (Builder $query) use ($filters) {
                if (isset($filters['student_id'])) {
                    $filters['student_ids'] = [$filters['student_id']];
                }

                $query->whereHas('recipientStudentClass', function ($query) use ($filters) {
                    $query->whereIn('student_id', $filters['student_ids']);
                });
            })
            ->when(isset($filters['merchant_type']), function (Builder $query) use ($filters) {
                $query->where('merchant_type', $filters['merchant_type']);
            })
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->whereHas('items', function ($query) use ($filters) {
                    $query->where('merchant_id', $filters['merchant_id']);
                });
            })
            ->when(isset($filters['ids']), function (Builder $query) use ($filters) {
                $query->whereIn('id', $filters['ids']);
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['order_reference_number']), function (Builder $query) use ($filters) {
                $query->where('order_reference_number', 'ILIKE', '%' . $filters['order_reference_number'] . '%');
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('ecommerce_orders.status', $filters['status']);
            })
            ->when(isset($filters['payment_status']), function (Builder $query) use ($filters) {
                $query->where('ecommerce_orders.payment_status', $filters['payment_status']);
            })
            ->when(isset($filters['userable_type']) && isset($filters['userable_id']), function (Builder $query) use ($filters) {
                $query->where('buyer_userable_type', $filters['userable_type'])
                    ->where('buyer_userable_id', $filters['userable_id']);
            });

    }
}
