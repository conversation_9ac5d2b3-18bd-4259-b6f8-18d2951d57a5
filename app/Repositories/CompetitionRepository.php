<?php

namespace App\Repositories;

use App\Models\Competition;
use App\Models\Department;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompetitionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Competition::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        return parent::getQuery($filters)
            ->select('competitions.*')
            ->when(isset($filters['name']) , function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%". $filters['name']."%");
            })
            ->when(isset($filters['department_id']) , function (Builder $query) use ($filters) {
                $query->where('department_id', $filters['department_id']);
            })
            ->when(isset($filters['date']) , function (Builder $query) use ($filters) {
                $query->where('date', $filters['date']);
            })
            ->when(isset($filters['student_name']) || isset($filters['student_number']) , function (Builder $query) use ($filters) {
                $query->whereHas('records', function (Builder $query) use ($filters) {
                    $query->whereHas('student', function (Builder $query) use ($filters) {
                        if(isset($filters['student_number'])){
                            $query->where('student_number', $filters['student_number']);
                        }
                        if(isset($filters['student_name'])){
                            $query->whereTranslations('name', $filters['student_name'], 'ILIKE', true);
                        }
                    });
                });
            })
            ->when(isset($order_by['department']), function (Builder $query) use ($order_by) {
                $query->join('master_departments', 'master_departments.id', '=', 'competitions.department_id');
                $this->setupOrderBy($query, $order_by['department'], Department::class, 'master_departments.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
