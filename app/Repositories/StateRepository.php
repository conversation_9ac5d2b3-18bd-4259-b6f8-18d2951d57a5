<?php

namespace App\Repositories;

use App\Models\State;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class StateRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return State::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['country_id']), function (Builder $query) use ($filters) {
                $query->where('country_id', $filters['country_id']);
            });
    }


    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
