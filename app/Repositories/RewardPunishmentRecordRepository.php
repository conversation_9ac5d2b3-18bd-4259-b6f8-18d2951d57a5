<?php

namespace App\Repositories;

use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class RewardPunishmentRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return RewardPunishmentRecord::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];
        
        return parent::getQuery($filters)
            ->select(resolve($this->getModelClass())->getTable() . '.*')
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->where('date', $filters['date']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['reward_punishment_id']), function (Builder $query) use ($filters) {
                $query->where('reward_punishment_id', $filters['reward_punishment_id']);
            })
            ->when(isset($filters['display_in_report_card']), function (Builder $query) use ($filters) {
                $query->where('display_in_report_card', $filters['display_in_report_card']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($order_by['student_name']) || isset($order_by['student_number']), function (Builder $query) use ($order_by) {
                $query->join('students', 'reward_punishment_records.student_id', '=', 'students.id');

                $filter_order_by = [];
                if (isset($order_by['student_name'])) {
                    $filter_order_by['name'] = $order_by['student_name'];
                }

                if (isset($order_by['student_number'])) {
                    $filter_order_by['student_number'] = $order_by['student_number'];
                }
                $this->setupOrderBy($query, $filter_order_by, Student::class, 'students.');
            })
            ->when(isset($order_by['reward_punishment_name']), function (Builder $query) use ($order_by) {
                $query->join('reward_punishments', 'reward_punishment_records.reward_punishment_id', '=', 'reward_punishments.id');

                $this->setupOrderBy($query, [
                    'name' => $order_by['reward_punishment_name'],
                ], RewardPunishment::class, 'reward_punishments.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
