<?php

namespace App\Repositories;

use App\Models\RewardPunishment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class RewardPunishmentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return RewardPunishment::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['category_id']), function (Builder $query) use ($filters) {
                $query->where('category_id', $filters['category_id']);
            })
            ->when(isset($filters['sub_category_id']), function (Builder $query) use ($filters) {
                $query->where('sub_category_id', $filters['sub_category_id']);
            })
            ->when(isset($filters['display_in_report_card']), function (Builder $query) use ($filters) {
                $query->where('display_in_report_card', $filters['display_in_report_card']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
