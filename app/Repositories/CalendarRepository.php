<?php

namespace App\Repositories;

use App\Models\Calendar;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CalendarRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Calendar::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->select(['calendars.*'])
            ->selectSub(function ($query) {
                $query->from('calendar_targets')
                  ->select('priority')
                  ->whereColumn('calendar_targets.calendar_id', 'calendars.id')
                  ->limit(1);
            }, 'priority')
            ->withCount('targets')
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['year']), function (Builder $query) use ($filters) {
                $query->where('year', $filters['year']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['is_default']), function (Builder $query) use ($filters) {
                $query->where('is_default', $filters['is_default']);
            });
    }

    public function getDefaultByYear(int $year, Calendar $calendar = null): Calendar|Model|null
    {
        return Calendar::query()
            ->where('year', $year)
            ->where('is_default', 1)
            ->where('is_active', 1)
            ->when(isset($calendar), function (Builder $query) use ($calendar) {
                $query->where('id', '!=', $calendar->id);
            })
            ->first();
    }
}
