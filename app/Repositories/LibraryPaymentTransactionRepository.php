<?php

namespace App\Repositories;

use App\Models\LibraryPaymentTransaction;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class LibraryPaymentTransactionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return LibraryPaymentTransaction::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query
            ->when(isset($filters['member_id']), function (Builder $query) use ($filters) {
                $query->where('member_id', $filters['member_id']);
            })
            ->when(isset($filters['payment_method']), function (Builder $query) use ($filters) {
                $query->where('payment_method', $filters['payment_method']);
            })
            ->when(isset($filters['book_loan_id']), function (Builder $query) use ($filters) {
                $query->where('book_loan_id', $filters['book_loan_id']);
            })
            ->when(isset($filters['period_payment_date']), function (Builder $query) use ($filters) {
                $query->whereBetween('payment_date', $filters['period_payment_date']);
            });

        return $query;

    }

    public function getAll(array $filters = []): Collection
    {
        return $this
            ->getQuery($filters)
            ->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->with(['bookLoan', 'member'])
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
