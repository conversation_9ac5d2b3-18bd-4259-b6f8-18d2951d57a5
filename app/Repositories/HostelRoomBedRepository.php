<?php

namespace App\Repositories;

use App\Enums\HostelBlockType;
use App\Enums\HostelRoomBedStatus;
use App\Models\HostelBlock;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;

class HostelRoomBedRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelRoomBed::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['block_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('hostelRoom.hostelBlock', 'type', $filters['block_type']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                if (is_array($filters['name'])) {
                    $query->whereIn('name', $filters['name']);
                } else {
                    $query->where('name', 'ILIKE', "%{$filters['name']}%");
                }
            })
            ->when(isset($filters['hostel_room_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['hostel_room_id'])) {
                    $query->whereIn('hostel_room_id', $filters['hostel_room_id']);
                } else {
                    $query->where('hostel_room_id', $filters['hostel_room_id']);
                }
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['hostel_block_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('hostelRoom.hostelBlock', 'id', $filters['hostel_block_id']);
            })
            ->when(isset($filters['hostel_block_type']), function (Builder $query) use ($filters) {
                $query->whereRelation('hostelRoom.hostelBlock', 'type', $filters['hostel_block_type']);
            })
            ->when(isset($order_by['hostel_room']) || isset($order_by['hostel_block']), function (Builder $query) use ($order_by) {
                $query->join('hostel_rooms', 'hostel_rooms.id', '=', 'hostel_room_beds.hostel_room_id')
                    ->join('hostel_blocks', 'hostel_blocks.id', '=', 'hostel_rooms.hostel_block_id')
                    ->when(isset($order_by['hostel_room']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, $order_by['hostel_room'], HostelRoom::class, 'hostel_rooms.');
                    })
                    ->when(isset($order_by['hostel_block']), function (Builder $query) use ($order_by) {
                        $this->setupOrderBy($query, $order_by['hostel_block'], HostelBlock::class, 'hostel_blocks.');
                    });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)
            ->select($this->getModelTableName().'.*') // Used for joined sorting
            ->paginate($this->per_page);
    }

    public function hostelRoomBedHasAssignments(HostelRoomBed|int $hostel_room_bed): bool
    {
        if ($hostel_room_bed instanceof HostelRoomBed) {
            return $hostel_room_bed->hasHostelBedAssignments();
        } else {
            return $this->findOrFail($hostel_room_bed)->hasHostelBedAssignments();
        }
    }

    public function getAvailableBedByIdsAndBlockType(array $ids, HostelBlockType $block_type): Collection
    {
        return $this->getQuery()
            ->where('status', HostelRoomBedStatus::AVAILABLE)
            ->whereIn('id', $ids)
            ->whereRelation('hostelRoom.hostelBlock', 'type', $block_type)
            ->get();
    }

    public function getAvailableBedReportData(array $filters = []): Collection
    {
        // Currently only have report for student block
        $filters['block_type'] = HostelBlockType::STUDENT->value;

        $filters = optional($filters);

        $locale = app()->getLocale();

        $data = $this->getQuery()
            ->selectRaw(
                "
                hostel_rooms.id,
                hostel_rooms.name,
                COUNT(hostel_room_beds.id) as capacity,
                SUM(CASE WHEN hostel_room_beds.status = 'OCCUPIED' THEN 1 ELSE 0 END) as occupied_beds,
                SUM(CASE WHEN hostel_room_beds.status = 'AVAILABLE' THEN 1 ELSE 0 END) as available_beds
                "
            )
            ->join('hostel_rooms', function (JoinClause $join) {
                $join->on('hostel_rooms.id', '=', 'hostel_room_beds.hostel_room_id');
            })
            ->join('hostel_blocks', function (JoinClause $join) {
                $join->on('hostel_blocks.id', '=', 'hostel_rooms.hostel_block_id');
            })
            ->when($filters['block_type'], function (Builder $query, $block_type) {
                $query->whereRelation('hostelRoom.hostelBlock', 'type', $block_type);
            })
            ->where('hostel_rooms.is_active', true)
            ->where('hostel_room_beds.is_active', true)
            ->groupBy('hostel_rooms.id', 'hostel_rooms.name', 'hostel_rooms.capacity', 'hostel_blocks.name')
            ->orderByRaw("hostel_blocks.name->>'{$locale}' ASC")
            ->orderBy('hostel_rooms.name', 'ASC')
            ->get();

        return $data;
    }
}
