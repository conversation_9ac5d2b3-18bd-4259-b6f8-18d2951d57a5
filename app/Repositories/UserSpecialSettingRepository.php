<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\UserSpecialSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class UserSpecialSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return UserSpecialSetting::class;
    }

    public function getQuery($filters = []): Builder
    {
        $this->with($filters['includes'] ?? []);

        $order_by = optional(optional($filters)['order_by']);

        return parent::getQuery($filters)
            ->select('user_special_settings.*')
            ->when(isset($filters['module']), function (Builder $query) use ($filters) {
                $query->where('module', $filters['module']);
            })
            ->when(isset($filters['submodule']), function (Builder $query) use ($filters) {
                $query->where('submodule', $filters['submodule']);
            })
            ->when(isset($filters['user_id']), function (Builder $query) use ($filters) {
                $query->where('user_id', $filters['user_id']);
            })
            ->when($order_by['employee'], function (Builder $query) use ($order_by) {
                $query
                    ->join('users', 'users.id', '=', 'user_special_settings.user_id')
                    ->join('employees', 'employees.user_id', '=', 'users.id')
                    ->when($order_by['employee'], function (Builder $query, $order) {
                        $this->setupOrderBy($query, $order, Employee::class, 'employees.');
                    });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
