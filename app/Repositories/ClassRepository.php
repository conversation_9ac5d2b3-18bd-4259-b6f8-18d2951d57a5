<?php

namespace App\Repositories;

use App\Models\ClassModel;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class ClassRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ClassModel::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function findBySemesterClassId($semester_class_id): ClassModel|Model
    {
        return $this->getQuery()->whereRelation('semesterClasses', 'id', $semester_class_id)->first();
    }

    public function getQuery($filters = []): Builder
    {
        $includes = Arr::get($filters, 'includes', []);
        $includes = array_merge($includes, ['semesterClasses', 'grade']);

        $filters['includes'] = $includes;

        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['stream']) , function (Builder $query) use ($filters) {
                $query->where('stream', $filters['stream']);
            })
            ->when(isset($filters['type']) , function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['code']) , function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['grade_id']) , function (Builder $query) use ($filters) {
                $query->where('grade_id', $filters['grade_id']);
            })
            ->when(isset($filters['semester_setting_id']) , function (Builder $query) use ($filters) {
                $query->whereRelation('semesterClasses', 'semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }
}
