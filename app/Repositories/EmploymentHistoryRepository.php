<?php

namespace App\Repositories;

use App\Models\EmploymentHistory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EmploymentHistoryRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EmploymentHistory::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query->when(isset($filters['job_title_id']), function (Builder $query) use ($filters) {
            $query->where('job_title_id', $filters['job_title_id']);
        })->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
            $query->where('employee_id', $filters['employee_id']);
        });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getByIds(array $ids, mixed $with): Collection
    {
        return $this->getQuery(['with' => $with])
            ->whereIn('id', $ids)
            ->get();
    }
}
