<?php

namespace App\Repositories;

use App\Models\LibraryMember;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class LibraryMemberRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return LibraryMember::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['member_or_card_number']), function (Builder $query) use ($filters) {
                $query->where('member_number', $filters['member_or_card_number'])
                    ->orWhere('card_number', $filters['member_or_card_number']);
            })
            ->when(isset($filters['member_number']), function (Builder $query) use ($filters) {
                $query->where('member_number', $filters['member_number']);
            })
            ->when(isset($filters['nric']), function (Builder $query) use ($filters) {
                $query->where('nric', $filters['nric']);
            })
            ->when(isset($filters['phone_number']), function (Builder $query) use ($filters) {
                $query->where('phone_number', $filters['phone_number']);
            })
            ->when(isset($filters['email']), function (Builder $query) use ($filters) {
                $query->where('email', $filters['email']);
            })
            ->when(isset($filters['card_number']), function (Builder $query) use ($filters) {
                $query->where('card_number', $filters['card_number']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function findVia(string $userable_type, int $userable_id): Model|LibraryMember|null
    {
        return LibraryMember::query()->where('userable_type', $userable_type)
            ->where('userable_id', $userable_id)
            ->first();
    }

    public function bulkUpdateMemberCardNumber(string $userable_type, array $payload)
    {
        return LibraryMember::where('userable_type', $userable_type)
            ->whereIn('userable_id', array_column($payload, 'userable_id'))
            ->update([
                'card_number' => DB::raw('CASE
                    ' . collect($payload)->map(function ($item) {
                        return "WHEN userable_type = '{$item['userable_type']}'
                            AND userable_id = {$item['userable_id']}
                            THEN '{$item['card_number']}'";
                    })->implode(' ') . '
                    ELSE card_number END')
            ]);
    }
}
