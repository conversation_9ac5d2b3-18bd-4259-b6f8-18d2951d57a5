<?php

namespace App\Repositories;

use App\Models\EcommerceProduct;
use App\Models\EcommerceProductTagAssignment;
use App\Models\EcommerceProductTagTarget;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EcommerceProductRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceProduct::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)
            ->get();
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['ids']), function (Builder $query) use ($filters) {
                $query->whereIn('id', $filters['ids']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', $filters['name'] . "%");
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('merchant_id', $filters['merchant_id']);
            })
            ->when(isset($filters['merchant_type']), function (Builder $query) use ($filters) {
                $query->whereHas('merchant', function (Builder $query) use ($filters) {
                    $query->where('type', $filters['merchant_type']);
                });
            })
            ->when(isset($filters['delivery_date']), function (Builder $query) use ($filters) {
                $query->whereHas('deliveryDates', function (Builder $query) use ($filters) {
                    $query->where('delivery_date', $filters['delivery_date']);
                });
            })
            ->when(isset($filters['delivery_date_from']) || isset($filters['delivery_date_to']), function (Builder $query) use ($filters) {
                $query->whereHas('deliveryDates', function (Builder $query) use ($filters) {
                    if (isset($filters['delivery_date_from'])) {
                        $query->where('delivery_date', '>=', $filters['delivery_date_from']);
                    }

                    if (isset($filters['delivery_date_to'])) {
                        $query->where('delivery_date', '<=', $filters['delivery_date_to']);
                    }
                });
            })
            ->when(isset($filters['category_id']), function (Builder $query) use ($filters) {
                $query->whereHas('subCategories', function (Builder $query) use ($filters) {
                    $query->where('product_category_id', $filters['category_id']);
                });
            })
            ->when(isset($filters['sub_category_id']), function (Builder $query) use ($filters) {
                $query->whereHas('subCategories', function (Builder $query) use ($filters) {
                    $query->where('ecommerce_product_sub_categories.id', $filters['sub_category_id']);
                });
            })
            ->when(isset($filters['group_id']), function (Builder $query) use ($filters) {
                $query->whereHas('groups', function (Builder $query) use ($filters) {
                    $query->where('ecommerce_product_groups.id', $filters['group_id']);
                });
            })
            ->when(isset($filters['tag_id']), function (Builder $query) use ($filters) {
                $query->whereHas('tags', function (Builder $query) use ($filters) {
                    $query->where('ecommerce_product_tags.id', $filters['tag_id']);
                });
            })
            ->when(isset($filters['available_date']), function (Builder $query) use ($filters) {
                $query->whereHas('availableDates', function (Builder $query) use ($filters) {
                    $query->where('available_date', $filters['available_date']);
                });
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['userable_type']), function (Builder $query) use ($filters) {
//                if ($filters['userable_type'] !== Employee::class) {
                $product_ids_without_tag = EcommerceProduct::whereDoesntHave('tags')
                    ->get()
                    ->pluck('id')
                    ->toArray();

                $tag_ids = EcommerceProductTagTarget::where('userable_type', $filters['userable_type'])
                    ->where('userable_id', $filters['userable_id'])
                    ->get()
                    ->pluck('tag_id');

                $product_ids_that_tag_user = EcommerceProductTagAssignment::whereIn('tag_id', $tag_ids)
                    ->get()
                    ->pluck('product_id')
                    ->toArray();

                $product_ids = array_merge($product_ids_without_tag, $product_ids_that_tag_user);

                $query->whereIn('id', $product_ids);
//                }
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
