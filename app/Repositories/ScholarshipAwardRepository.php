<?php

namespace App\Repositories;

use App\Models\ScholarshipAward;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ScholarshipAwardRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ScholarshipAward::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['scholarship_id']), function (Builder $query) use ($filters) {
                $query->where('scholarship_id', $filters['scholarship_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['effective_from']), function (Builder $query) use ($filters) {
                $query->where('effective_from', '>=', $filters['effective_from']);
            })
            ->when(isset($filters['effective_to']), function (Builder $query) use ($filters) {
                $query->where('effective_to', '<=', $filters['effective_to']);
            });
    }

    public function deleteByScholarshipAndAwardIds(int $scholarship_id, array $award_ids): void
    {
        ScholarshipAward::where('scholarship_id', $scholarship_id)
            ->whereIn('id', $award_ids)
            ->delete();
    }
}
