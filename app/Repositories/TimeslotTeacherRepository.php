<?php

namespace App\Repositories;

use App\Models\TimeslotTeacher;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TimeslotTeacherRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return TimeslotTeacher::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                $query->where('employee_id', $filters['employee_id']);
            })
            ->when(isset($filters['timeslot_with_class_subject']), function (Builder $query) use ($filters) {
                $query->whereHas('timeslot.classSubject');
            })
            ->when(isset($filters['active_timetable']), function (Builder $query) use ($filters) {
                $query->whereHas('timeslot.timetable', function (Builder $query) use ($filters) {
                    $query->where('is_active', $filters['active_timetable']);
                });
            })
            ->when(isset($filters['not_timeslot_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['not_timeslot_id'])) {
                    $query->whereNotIn('timeslot_id', $filters['not_timeslot_id']);
                } else {
                    $query->where('timeslot_id', '!=', $filters['not_timeslot_id']);
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteByTimeslotId(int $timeslot_id): void
    {
        $this->getQuery()->where('timeslot_id', $timeslot_id)->delete();
    }
}
