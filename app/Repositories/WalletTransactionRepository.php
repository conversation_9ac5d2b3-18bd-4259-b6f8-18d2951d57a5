<?php

namespace App\Repositories;

use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Models\Card;
use App\Models\Student;
use App\Models\Terminal;
use App\Models\WalletTransaction;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;

class WalletTransactionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return WalletTransaction::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $order_by = optional(optional($filters)['order_by']);

        return $this
            ->getQuery($filters)
            ->select('wallet_transactions.*')
            // SORTING
            ->when($order_by['card'], function (Builder $query) use ($order_by) {
                $query
                    ->join('cards', 'cards.id', '=', 'wallet_transactions.card_id')
                    ->when($order_by['card'], function (Builder $query, $order) {
                        $this->setupOrderBy($query, $order, Card::class, 'cards.');
                    });
            })
            ->paginate($this->per_page);
    }

    public function getWalletTransactionByReferenceNo($reference_no): ?Model
    {
        return $this->getQuery()->where(\DB::raw('UPPER(reference_no)'), strtoupper($reference_no))->first();
    }

    public function getDailyWalletTransactionsFromTerminal(array $filters): Collection
    {
        $filters = optional($filters);

        $timezone = config('school.timezone');

        $start_date = Carbon::parse($filters['start_date'], $timezone)->startOfDay()->tz('UTC')->toDateTimeString();
        $end_date = Carbon::parse($filters['end_date'], $timezone)->endOfDay()->tz('UTC')->toDateTimeString();

        $data = WalletTransaction::selectRaw(
            "
                DATE(wallet_transactions.created_at AT TIME ZONE 'UTC' AT TIME ZONE ?) as transaction_date,
                SUM(wallet_transactions.total_amount) as total_amount
                ", [$timezone]
        )
            ->join('terminals', function (JoinClause $join) {
                $join->on('terminals.id', '=', 'wallet_transactions.wallet_transactable_id')
                    ->where('wallet_transactions.wallet_transactable_type', Terminal::class);
            })
            ->where('wallet_transactions.type', WalletTransactionType::TRANSACTION->value)
            ->where('wallet_transactions.status', WalletTransactionStatus::SUCCESS->value)
            ->whereBetween('wallet_transactions.created_at', [$start_date, $end_date])
            ->when($filters['type'], function (Builder $query, $type) {
                $query->where('terminals.type', $type);
            })
            ->when($filters['terminal_ids'], function (Builder $query, $terminal_ids) {
                $query->whereIn('terminals.id', $terminal_ids);
            })
            ->groupBy('transaction_date')
            ->orderBy('transaction_date', 'desc')
            ->get();

        return $data;
    }

    public function getQuery($filters = []): Builder
    {
        $includes = $filters['includes'] ?? [];
        $with = $filters['with'] ?? [];
        $filters['includes'] = array_merge($includes, $with);

        return parent::getQuery($filters)
            ->when(isset($filters['user_name']) || isset($filters['user_email']) || isset($filters['user_phone_number']), function (Builder $query) use ($filters) {
                $query->whereHasMorph('userable', '*', function (Builder $query, string $type) use ($filters) {
                    if (isset($filters['user_name'])) {
                        $query->whereTranslations('name', $filters['user_name'], 'ILIKE', true);

                        if ($type === Student::class) {
                            $query->orWhereHas('directGuardians', function (Builder $query) use ($filters) {
                                $query->whereTranslations('name', $filters['user_name'], 'ILIKE', true);
                            });
                        }
                    }

                    if (isset($filters['user_email'])) {
                        $query->where('email', 'ILIKE', "%" . $filters['user_email'] . "%");

                        if ($type === Student::class) {
                            $query->orWhereRelation('directGuardians', 'email', 'ILIKE', "%" . $filters['user_email'] . "%");
                        }
                    }

                    if (isset($filters['user_phone_number'])) {
                        $query->where('phone_number', 'ILIKE', "%" . $filters['user_phone_number'] . "%");

                        if ($type === Student::class) {
                            $query->orWhereRelation('directGuardians', 'phone_number', 'ILIKE', "%" . $filters['user_phone_number'] . "%");
                        }
                    }
                });
            })
            ->when(isset($filters['wallet_id']), function ($query) use ($filters) {
                $query->where('wallet_id', $filters['wallet_id']);
            })
            ->when(isset($filters['reference_no']), function ($query) use ($filters) {
                $query->where('reference_no', $filters['reference_no']);
            })
            ->when(isset($filters['card_number']), function ($query) use ($filters) {
                $query->whereRelation('card', 'card_number', $filters['card_number']);
            })
            ->when(isset($filters['type']), function ($query) use ($filters) {
                if (is_array($filters['type'])) {
                    $query->whereIn('type', $filters['type']);
                } else {
                    $query->where('type', $filters['type']);
                }
            })
            ->when(isset($filters['wallet_transactable_type']) && isset($filters['wallet_transactable_id']), function ($query) use ($filters) {
                $query->where('wallet_transactable_type', $filters['wallet_transactable_type'])
                    ->where('wallet_transactable_id', $filters['wallet_transactable_id']);
            })
            ->when(isset($filters['transaction_from']), function ($query) use ($filters) {
                $query->where('created_at', '>=', Carbon::parse($filters['transaction_from'], config('school.timezone'))->startOfDay()->tz('UTC'));
            })
            ->when(isset($filters['transaction_to']), function ($query) use ($filters) {
                $query->where('created_at', '<=', Carbon::parse($filters['transaction_to'], config('school.timezone'))->endOfDay()->tz('UTC'));
            })
            ->when(isset($filters['status']), function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            });

    }
}
