<?php

namespace App\Repositories;

use App\Models\EmployeeSessionSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EmployeeSessionSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EmployeeSessionSetting::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['employee_session_id']), function (Builder $query) use ($filters) {
                $query->where('employee_session_id', $filters['employee_session_id']);
            })
            ->when(isset($filters['day']), function (Builder $query) use ($filters) {
                $query->where('day', $filters['day']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function deleteByEmployeeSessionId(int $employee_session_id): bool
    {
        return $this->getQuery()->where('employee_session_id', $employee_session_id)->delete();
    }
}
