<?php

namespace App\Repositories;

use App\Models\LeaveApplication;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class LeaveApplicationRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return LeaveApplication::class;
    }

    public function getQuery($filters = []): Builder
    {
        if (isset($filters['leave_application_period_date_from']) || isset($filters['leave_application_period_date_to'])) {
            $includes['leaveApplicationPeriods'] = function ($query) use ($filters) {
                if (!empty($filters['leave_application_period_date_from'])) {
                    $query->where('date', '>=', $filters['leave_application_period_date_from']);
                }
                if (!empty($filters['leave_application_period_date_to'])) {
                    $query->where('date', '<=', $filters['leave_application_period_date_to']);
                }
            };

            $filters['includes'] = array_merge($filters['includes'] ?? [], $includes);
        }

        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['id_not_equal']), function (Builder $query) use ($filters) {
                if (is_array($filters['id_not_equal'])) {
                    $query->whereNotIn('id', $filters['id_not_equal']);
                } else {
                    $query->where('id', '!=', $filters['id_not_equal']);
                }
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                if (is_array($filters['status'])) {
                    $query->whereIn('status', $filters['status']);
                } else {
                    $query->where('status', $filters['status']);
                }
            })
            ->when(isset($filters['status_not_equal']), function (Builder $query) use ($filters) {
                $query->where('status', '!=', $filters['status_not_equal']);
            })
            ->when(isset($filters['leave_application_type_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['leave_application_type_id'])) {
                    $query->whereIn('leave_application_type_id', $filters['leave_application_type_id']);
                } else {
                    $query->where('leave_application_type_id', $filters['leave_application_type_id']);
                }
            })
            // leave_applicable_type without leave_applicable_id
            ->when(!isset($filters['leave_applicable_id']) && isset($filters['leave_applicable_type']), function (Builder $query) use ($filters) {
                if (is_array($filters['leave_applicable_type'])) {
                    $query->whereIn('leave_applicable_type', $filters['leave_applicable_type']);
                } else {
                    $query->where('leave_applicable_type', $filters['leave_applicable_type']);
                }
            })
            ->when(isset($filters['leave_applicable_id']) && isset($filters['leave_applicable_type']), function (Builder $query) use ($filters) {
                if (is_array($filters['leave_applicable_id'])) {
                    $query->whereIn('leave_applicable_id', $filters['leave_applicable_id']);
                } else {
                    $query->where('leave_applicable_id', $filters['leave_applicable_id']);
                }

                $query->where('leave_applicable_type', $filters['leave_applicable_type']);
            })
            ->when(
                isset($filters['leave_application_date_from']) || isset($filters['leave_application_date_to']) || isset($filters['leave_application_date']) || isset($filters['period']),
                function (Builder $query) use ($filters) {
                    $query->whereRelation('leaveApplicationPeriods', function (Builder $leave_application_dates_query) use ($filters) {
                        if (isset($filters['leave_application_date_from'])) {
                            $leave_application_dates_query->where('date', '>=', $filters['leave_application_date_from']);
                        }
                        if (isset($filters['leave_application_date_to'])) {
                            $leave_application_dates_query->where('date', '<=', $filters['leave_application_date_to']);
                        }
                        if (isset($filters['leave_application_date'])) {
                            $leave_application_dates_query->where('date', $filters['leave_application_date']);
                        }
                        if (isset($filters['period'])) {
                            if (is_array($filters['period'])) {
                                $leave_application_dates_query->whereIn('period', $filters['period']);
                            } else {
                                $leave_application_dates_query->where('period', $filters['period']);
                            }
                        }
                    });
                }
            )->when(isset($filters['is_present']), function (Builder $query) use ($filters) {
                $query->where('is_present', $filters['is_present']);
            })
            ->when(isset($filters['is_full_day']), function (Builder $query) use ($filters) {
                $query->where('is_full_day', $filters['is_full_day']);
            })->when(isset($filters['has_periods']), function (Builder $query) use ($filters) {
                $query->whereHas('leaveApplicationPeriods', function ($query) use ($filters) {
                    if (!empty($filters['leave_application_period_date_from'])) {
                        $query->where('date', '>=', $filters['leave_application_period_date_from']);
                    }
                    if (!empty($filters['leave_application_period_date_to'])) {
                        $query->where('date', '<=', $filters['leave_application_period_date_to']);
                    }
                });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getCount(array $filters = []): int
    {
        return $this->getQuery($filters)->count();
    }
}
