<?php

namespace App\Repositories;

use App\Models\SchoolProfile;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SchoolProfileRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SchoolProfile::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['short_name']), function (Builder $query) use ($filters) {
                $query->where('short_name', $filters['short_name']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where($this->getTranslatableKey('name'), 'LIKE', "%" . $filters['name'] . "%");
            })
            ->paginate($this->per_page);
    }
}
