<?php

namespace App\Repositories;

use App\Models\Calendar;
use App\Models\CalendarSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CalendarSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return CalendarSetting::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
        ->when(isset($filters['calendar_id']) , function (Builder $query) use ($filters) {
            $query->where('calendar_id', $filters['calendar_id']);
        })
        ->when(isset($filters['date']), function (Builder $query) use ($filters) {
            if (is_array($filters['date'])) {
                $query->whereIn('date', $filters['date']);
            } else {
                $query->where('date', $filters['date']);
            }
        });
    }

    public function deleteByCalendarAndDate($calendar_id, array $dates = []): void
    {
        $this->getQuery([
            'calendar_id' => $calendar_id,
            'date' => !empty($dates) ? $dates : null
        ])->delete();
    }
}
