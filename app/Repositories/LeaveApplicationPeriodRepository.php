<?php

namespace App\Repositories;

use App\Models\LeaveApplicationPeriod;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class LeaveApplicationPeriodRepository extends BaseRepository
{
    public function getModelClass(): string {
        return LeaveApplicationPeriod::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if ( is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                }else{
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['leave_application_id']), function (Builder $query)  use ($filters) {
                if ( is_array($filters['leave_application_id'])) {
                    $query->whereIn('leave_application_id', $filters['leave_application_id']);
                }else{
                    $query->where('leave_application_id', $filters['leave_application_id']);
                }
            })
            ->when(isset($filters['leave_application_date_from']), function (Builder $query)  use ($filters) {
                $query->where('date', '>=', $filters['leave_application_date_from']);
            })
            ->when(isset($filters['leave_application_date_to']), function (Builder $query)  use ($filters) {
                $query->where('date', '<=', $filters['leave_application_date_to']);
            })
            ->when(isset($filters['leave_application_date']), function (Builder $query) use ($filters) {
                $query->where('date', $filters['leave_application_date']);
            })
            ->when(isset($filters['period']), function (Builder $query) use ($filters) {
                $query->where('period', $filters['period']);
            })
            ->when(isset($filters['leave_application_status']) || isset($filters['leave_applicable_type']) || isset($filters['leave_applicable_id']), function (Builder $query) use ($filters) {
                $query->whereHas('leaveApplication', function ($query) use ($filters) {
                    if ($filters['leave_application_status']) {
                        $query->where('status', $filters['leave_application_status']);
                    }
                    if ($filters['leave_applicable_type']) {
                        $query->where('leave_applicable_type', $filters['leave_applicable_type']);
                    }
                    if ($filters['leave_applicable_id']) {
                        if ( is_array($filters['leave_applicable_id'])) {
                            $query->whereIn('leave_applicable_id', $filters['leave_applicable_id']);
                        }else{
                            $query->where('leave_applicable_id', $filters['leave_applicable_id']);
                        }
                    }
                });
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function batchDeleteByLeaveApplicationId($leave_application_id): bool
    {
        if ( $leave_application_id === null ){
            return true;
        }

        return $this->getQuery(['leave_application_id' => $leave_application_id])->delete();
    }
}
