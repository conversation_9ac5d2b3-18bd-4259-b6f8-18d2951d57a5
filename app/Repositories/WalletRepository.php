<?php

namespace App\Repositories;

use App\Models\Guardian;
use App\Models\Student;
use App\Models\User;
use App\Models\UserableView;
use App\Models\Wallet;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class WalletRepository extends BaseRepository
{
    protected CurrencyRepository $currencyRepository;
    protected StudentRepository $studentRepository;

    public function __construct(
        CurrencyRepository $currency_repository,
        StudentRepository $student_repository
    ) {
        $this->currencyRepository = $currency_repository;
        $this->studentRepository = $student_repository;
    }

    public function getModelClass(): string
    {
        return Wallet::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $order_by = optional(optional($filters)['order_by']);

        return $this
            ->getQuery($filters)
            ->select('wallets.*')
            ->when(isset($filters['user_name']) || isset($filters['user_email']) || isset($filters['user_phone_number']), function (Builder $query) use ($filters) {
                $query->whereHas('userables', function (Builder $query) use ($filters) {
                    $query->whereHasMorph('userable', '*', function (Builder $query, string $type) use ($filters) {

                        if (isset($filters['user_name'])) {
                            $query->whereTranslations('name', $filters['user_name'], 'ILIKE', true);

                            if ($type === Student::class) {
                                $query->orWhereHas('directGuardians', function (Builder $query) use ($filters) {
                                    $query->whereTranslations('name', $filters['user_name'], 'ILIKE', true);
                                });
                            }
                        }

                        if (isset($filters['user_email'])) {
                            $query->where('email', 'ILIKE', "%" . $filters['user_email'] . "%");

                            if ($type === Student::class) {
                                $query->orWhereRelation('directGuardians', 'email', 'ILIKE', "%" . $filters['user_email'] . "%");
                            }
                        }

                        if (isset($filters['user_phone_number'])) {
                            $query->where('phone_number', 'ILIKE', "%" . $filters['user_phone_number'] . "%");

                            if ($type === Student::class) {
                                $query->orWhereRelation('directGuardians', 'phone_number', 'ILIKE', "%" . $filters['user_phone_number'] . "%");
                            }
                        }
                    });
                });
            })
            ->when(isset($filters['user_type']), function (Builder $query) use ($filters) {
                $query->whereHas('userables', function ($userable_query) use ($filters) {
                    $userable_query->where(function ($query) use ($filters) {
                        $query->where('userable_type', $filters['user_type']);
                    });
                });
            })
            // filter by employee/student number
            ->when(isset($filters['user_number']), function (Builder $query) use ($filters) {
                $query->whereRelation('userables', 'number', 'ILIKE', "%" . $filters['user_number'] . "%");
            })

            // SORTING
            ->when($order_by['userable'], function (Builder $query) use ($order_by) {
                $query
                    ->join('userable_views', 'userable_views.user_id', '=', 'wallets.user_id')
                    ->when($order_by['userable'], function (Builder $query, $order) {
                        $this->setupOrderBy($query, $order, UserableView::class, 'userable_views.');
                    });
            })
            ->paginate($this->per_page);
    }

    public function addBalance(Wallet $wallet, $balance): void
    {
        $wallet->increment('balance', $balance);
    }

    public function getWalletByUserAndCurrencyCode(User $user, string $currency): ?Model
    {
        $currency = $this->currencyRepository->getCurrencyByCode($currency);

        $user_wallet = $user->wallets()->where('currency_id', $currency->id)->first();

        if ($user_wallet) {
            return $user_wallet;
        }

        throw new \Exception('Wallet not found');
    }

    public function getWalletByIds(array $wallet_ids): Collection
    {
        return $this->getQuery()->whereIn('id', $wallet_ids)->get();
    }

    public function getAllPaginatedByMultiFieldSearchAndCurrency(
        User $user,
        array $filters,
        $exclude_own_wallet = true
    ): LengthAwarePaginator {
        $filters = optional($filters);

        $filters['includes'] = [
            'userables' => function ($query) use ($filters) {
                if ($filters['userable_type'] == Student::class) {
                    $query->where('userable_type', Guardian::class);
                }
            },
            'currency'
        ];
        return $this
            ->getQuery($filters)
            ->when($filters['search'], function ($query, $search) use ($user) {
                $query->whereHas('userables', function ($userable_query) use ($user, $search) {
                    $userable_query->where(function ($query) use ($search) {
                        $query->whereTranslations('name', $search, 'ILIKE', true)
                            ->orWhere('number', 'ILIKE', "%$search%")
                            ->orWhere('email', 'ILIKE', "%$search%")
                            ->orWhere('phone_number', 'ILIKE', "%$search%");
                    });
                });
            })
            ->when($filters['userable_type'] && $filters['userable_id'],
                function (Builder $query) use ($filters, $user) {
                    if ($filters['userable_type'] == Guardian::class) {
                        $transferable_student_user_ids = $user->getAllUserables()
                            ->where('userable_type', Student::class)
                            ->pluck('user_id')
                            ->all();

                        $query->whereIn('user_id', $transferable_student_user_ids);
                    }

                    if ($filters['userable_type'] == Student::class) {
                        $student = $this->studentRepository->find($filters['userable_id']);

                        $query->whereIn('user_id', $student->directGuardians()->pluck('user_id')->all());
                    }
                })
            ->when($filters['currency_code'], function ($query, $currency_code) {
                $query->whereHas('currency', function ($currency_query) use ($currency_code) {
                    $currency_query->where('code', $currency_code);
                });
            })
            ->when($exclude_own_wallet, function ($query) use ($filters, $user) {
                // $query->where('user_id', '!=', $user->id);
                if ($filters['userable_type'] && $filters['userable_id']) {
                    $query->whereDoesntHave('userables', function ($userable_query) use ($filters) {
                        $userable_query->where('user_id', '=', $filters['userable_id'])
                            ->where('userable_type', $filters['userable_type']);
                    });
                } else {
                    $query->where('user_id', '!=', $user->id);
                }
            })
            ->paginate($this->per_page);
    }

    /** function to get wallets owned by the user */
    public function getPaginatedUserWallets(User $user, array $filters): LengthAwarePaginator
    {
        $filters = optional($filters);
        $filters['includes'] = ['userables', 'currency', 'user'];
        return $this
            ->getQuery($filters)
            ->where('user_id', $user->id)
            ->when($filters['currency_code'], function ($query, $currency_code) {
                $query->whereHas('currency', function ($currency_query) use ($currency_code) {
                    $currency_query->where('code', 'ILIKE', "%$currency_code%");
                });
            })
            ->paginate($this->per_page);
    }
}
