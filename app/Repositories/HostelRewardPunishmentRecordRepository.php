<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\HostelRewardPunishmentRecord;
use App\Models\HostelRewardPunishmentSetting;
use App\Models\Student;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class HostelRewardPunishmentRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelRewardPunishmentRecord::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = $filters['order_by'] ?? [];

        return parent::getQuery($filters)
            ->select(resolve($this->getModelClass())->getTable() . '.*') // To avoid column ambiguity
            ->when(isset($filters['person_in_charge_id']), function (Builder $query) use ($filters) {
                $query->where('person_in_charge_id', $filters['person_in_charge_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                $query->where('date', $filters['date']);
            })
            ->when(isset($filters['hostel_reward_punishment_setting_id']), function (Builder $query) use ($filters) {
                $query->where('hostel_reward_punishment_setting_id', $filters['hostel_reward_punishment_setting_id']);
            })
            ->when(isset($filters['created_by']), function (Builder $query) use ($filters) {
                $query->where('created_by', $filters['created_by']);
            })
            ->when(isset($order_by['person_in_charge_name']), function (Builder $query) use ($order_by) {
                $query->join('employees', 'hostel_reward_punishment_records.person_in_charge_id', '=', 'employees.id');

                $this->setupOrderBy($query, [
                    'name' => $order_by['person_in_charge_name'],
                ], Employee::class, 'employees.');
            })
            ->when(isset($order_by['student_name']) || isset($order_by['student_number']), function (Builder $query) use ($order_by) {
                $query->join('students', 'hostel_reward_punishment_records.student_id', '=', 'students.id');

                $filter_order_by = [];
                if (isset($order_by['student_name'])) {
                    $filter_order_by['name'] = $order_by['student_name'];
                }

                if (isset($order_by['student_number'])) {
                    $filter_order_by['student_number'] = $order_by['student_number'];
                }
                $this->setupOrderBy($query, $filter_order_by, Student::class, 'students.');
            })
            ->when(isset($order_by['hostel_reward_punishment_setting_name']) || isset($order_by['hostel_reward_punishment_setting_points']), function (Builder $query) use ($order_by) {
                $query->join(
                    'hostel_reward_punishment_settings',
                    'hostel_reward_punishment_records.hostel_reward_punishment_setting_id', '=', 'hostel_reward_punishment_settings.id'
                );

                $filter_order_by = [];
                if (isset($order_by['hostel_reward_punishment_setting_name'])) {
                    $filter_order_by['name'] = $order_by['hostel_reward_punishment_setting_name'];
                }

                if (isset($order_by['hostel_reward_punishment_setting_points'])) {
                    $filter_order_by['points'] = $order_by['hostel_reward_punishment_setting_points'];
                }

                $this->setupOrderBy($query, $filter_order_by, HostelRewardPunishmentSetting::class, 'hostel_reward_punishment_settings.');
            });
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getHostelRewardPunishmentReportDataByYear(array $filters = []): Collection
    {
        $year = date("Y", strtotime($filters['date_from']));

        $parent_filters = [
            'includes' => [
                'personInCharge' => function ($query) {
                    $query->select(['id', 'name']);
                },
                'student' => function ($query) use ($year) {
                    $query->select(['id', 'name', 'student_number']);
                    $query->with([
                        'hostelBedByYear' => function ($query) use ($year) {
                            $query->select(['year', 'assignable_id', 'assignable_type', 'hostel_room_bed_id'])->where('year', $year);
                            $query->with([
                                'bed' => function ($query) {
                                    $query->select(['id', 'hostel_room_id', 'name']);
                                    $query->with([
                                        'hostelRoom' => function ($query) {
                                            $query->select(['id', 'name', 'hostel_block_id']);
                                            $query->with([
                                                'hostelBlock' => function ($query) {
                                                    $query->select(['id', 'code']);
                                                }
                                            ]);
                                        }
                                    ]);
                                }
                            ]);
                        }
                    ]);
                },
                'hostelRewardPunishmentSetting' => function ($query) {
                    $query->select(['id', 'hostel_merit_demerit_setting_id', 'name', 'points']);
                    $query->with([
                        'hostelMeritDemeritSetting' => function ($query) {
                            $query->select(['id', 'name']);
                        }
                    ]);
                }
            ]
        ];

        $data = $this->getQuery($parent_filters)
            ->select(['id', 'person_in_charge_id', 'student_id', 'date', 'hostel_reward_punishment_setting_id', 'remark'])
            ->whereBetween('date', [$filters['date_from'], $filters['date_to']])
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['room_id']), function (Builder $query) use ($filters) {
                $query->whereHas('student.hostelBedByYear.bed', function ($query) use ($filters) {
                    $query->where('hostel_room_id', $filters['room_id']);
                });
            })
            ->when(isset($filters['block_id']), function (Builder $query) use ($filters) {
                $query->whereHas('student.hostelBedByYear.bed.hostelRoom', function ($query) use ($filters) {
                    $query->where('hostel_block_id', $filters['block_id']);
                });
            })
            ->has('student.hostelBedByYear')
            ->get();

        return $data;
    }
}
