<?php

namespace App\Repositories;

use App\Models\EcommerceProduct;
use App\Models\EcommerceProductAvailableDate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class EcommerceProductAvailableDateRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EcommerceProductAvailableDate::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function deleteByProduct(EcommerceProduct|Model $product): void
    {
        $product->availableDates()->delete();
    }

    public function deleteByAvailableDates(array $available_dates): void
    {
        if ( count($available_dates) === 0 ){
            return;
        }

        EcommerceProductAvailableDate::query()->whereIn('available_date', $available_dates)->delete();
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['available_date_from']), function (Builder $query) use ($filters) {
                $query->where('available_date', '>=', $filters['available_date_from']);
            })
            ->when(isset($filters['available_date_to']), function (Builder $query) use ($filters) {
                $query->where('available_date', '<=', $filters['available_date_to']);
            });
    }
}
