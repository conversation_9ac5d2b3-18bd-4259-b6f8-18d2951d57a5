<?php

namespace App\Repositories;

use App\Models\BookSubClassification;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class BookSubClassificationRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return BookSubClassification::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['book_classification_id']), function (Builder $query) use ($filters) {
                $query->where('book_classification_id', $filters['book_classification_id']);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', 'LIKE', "%". $filters['code']. "%");
            });
    }
}
