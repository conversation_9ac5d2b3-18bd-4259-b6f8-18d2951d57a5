<?php

namespace App\Repositories;

use App\Models\UserInbox;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;

class UserInboxRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return UserInbox::class;
    }

    public function deleteByUserInboxIds(array $user_inbox_ids): bool
    {
        if (count($user_inbox_ids) === 0) {
            return true;
        }

        return $this->getQuery(['ids' => $user_inbox_ids])->delete();
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['ids']), function (Builder $query) use ($filters) {
                $query->whereIn('id', $filters['ids']);
            })
            ->when(isset($filters['announcement_id']), function (Builder $query) use ($filters) {
                $query->where('announcement_id', $filters['announcement_id']);
            })
            ->when(isset($filters['user_id']), function (Builder $query) use ($filters) {
                $query->where('user_id', $filters['user_id']);
            })
            ->when(isset($filters['title']), function (Builder $query) use ($filters) {
                $query->where('title', 'ILIKE', "%" . $filters['title'] . "%");
            })
            ->when(isset($filters['message']), function (Builder $query) use ($filters) {
                $query->where('message', 'ILIKE', "%" . $filters['message'] . "%");
            })
            ->when(isset($filters['is_read']), function (Builder $query) use ($filters) {
                if ($filters['is_read']) {
                    $query->whereNotNull('read_at');
                } else {
                    $query->whereNull('read_at');
                }
            })
            ->when(isset($filters['created_at']), function (Builder $query) use ($filters) {
                $query->whereBetween('created_at', [
                    Carbon::parse($filters['created_at'], config('school.timezone'))->startOfDay()->tz(config('app.timezone')),
                    Carbon::parse($filters['created_at'], config('school.timezone'))->endOfDay()->tz(config('app.timezone'))
                ]);
            })
            ->when(isset($filters['announcement_id']), function (Builder $query) use ($filters) {
                $query->where('announcement_id', $filters['announcement_id']);
            });
    }

    public function getAllQuery(array $filters = []): Builder
    {
        return $this->getQuery($filters);
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
