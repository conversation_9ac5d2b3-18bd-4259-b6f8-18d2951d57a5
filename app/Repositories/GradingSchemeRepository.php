<?php

namespace App\Repositories;

use App\Models\GradingScheme;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class GradingSchemeRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return GradingScheme::class;
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%{$filters['name']}%");
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }
}
