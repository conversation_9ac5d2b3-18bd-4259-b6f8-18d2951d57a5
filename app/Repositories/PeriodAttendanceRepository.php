<?php

namespace App\Repositories;

use App\Models\PeriodAttendance;
use App\Models\Timeslot;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PeriodAttendanceRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return PeriodAttendance::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['date']), function (Builder $query) use ($filters) {
                if (is_array($filters['date'])) {
                    $query->whereIn('date', $filters['date']);
                } else {
                    $query->where('date', $filters['date']);
                }
            })
            ->when(isset($filters['date_from']), function (Builder $query) use ($filters) {
                $query->where('date', '>=', $filters['date_from']);
            })
            ->when(isset($filters['date_to']), function (Builder $query) use ($filters) {
                $query->where('date', '<=', $filters['date_to']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                if (is_array($filters['status'])) {
                    $query->whereIn('status', $filters['status']);
                } else {
                    $query->where('status', $filters['status']);
                }
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['student_id'])) {
                    $query->whereIn('student_id', $filters['student_id']);
                } else {
                    $query->where('student_id', $filters['student_id']);
                }
            })
            ->when(isset($filters['timeslot_type']), function (Builder $query) use ($filters) {
                $query->where('timeslot_type', $filters['timeslot_type']);
            })
            ->when(isset($filters['timeslot_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['timeslot_id'])) {
                    $query->whereIn('timeslot_id', $filters['timeslot_id']);
                } else {
                    $query->where('timeslot_id', $filters['timeslot_id']);
                }
            })
            ->when(isset($filters['without_leave_application']), function (Builder $query) use ($filters) {
                if ($filters['without_leave_application'] == true) {
                    $query->whereNull('leave_application_id');
                }
            })
            ->when(isset($filters['period']), function (Builder $query) use ($filters) {
                if (is_array($filters['period'])) {
                    $query->whereIn('period', $filters['period']);
                } else {
                    $query->where('period', $filters['period']);
                }
            })
            ->when(isset($filters['is_attendance_required']), function (Builder $query) use ($filters) {
                $query->whereHasMorph('timeslot',
                    [Timeslot::class],
                    function (Builder $query) use ($filters) {
                        $query->whereRelation('timetable.periodGroup.periodLabels', 'is_attendance_required', $filters['is_attendance_required']);
                    });
            })
            ->when(isset($filters['employee_id']), function (Builder $query) use ($filters) {
                $query->where('updated_by_employee_id', $filters['employee_id']);
            })
            ->when(isset($filters['has_mark_deduction']), function (Builder $query) use ($filters) {
                $query->where('has_mark_deduction', $filters['has_mark_deduction']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function batchDeleteByIds(array $ids = []): bool
    {
        if ( count($ids) === 0 ){
            return true;
        }

        return $this->getQuery([
            'id' => $ids
        ])->delete();
    }
}
