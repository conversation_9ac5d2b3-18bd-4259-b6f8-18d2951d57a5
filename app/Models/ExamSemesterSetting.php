<?php

namespace App\Models;

use App\Enums\ExamSemesterSettingCategory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamSemesterSetting extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'category' => ExamSemesterSettingCategory::class
    ];

    public function grade(): BelongsTo
    {
        return $this->belongsTo(Grade::class, 'grade_id');
    }

    public function semesterSetting(): BelongsTo
    {
        return $this->belongsTo(SemesterSetting::class, 'semester_setting_id');
    }

}
