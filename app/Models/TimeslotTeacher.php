<?php

namespace App\Models;

use App\Enums\TimeslotTeacherType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class TimeslotTeacher extends Pivot
{
    use HasFactory;

    public $timestamps = false;

    protected $casts = [
        'type' => TimeslotTeacherType::class
    ];

    public function employee(): Belong<PERSON>To
    {
        return $this->belongsTo(Employee::class);
    }

    public function timeslot(): BelongsTo
    {
        return $this->belongsTo(Timeslot::class);
    }
}
