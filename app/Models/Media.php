<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media as BaseMedia;

class Media extends BaseMedia
{
    use HasFactory;

    public function getTemporaryUrl(DateTimeInterface $expiration, string $conversionName = '', array $options = []): string
    {
        // For testing, use local drive
        if (env('APP_ENV') == 'testing' || env('MEDIA_DISK') == 'public') {
            return $this->getFullUrl($conversionName);
        }

        $urlGenerator = $this->getUrlGenerator($conversionName);

        return $urlGenerator->getTemporaryUrl($expiration, $options);
    }

    public function getUrl(string $conversionName = ''): string
    {
        // For testing, use local drive
        if (env('APP_ENV') == 'testing' || env('MEDIA_DISK') == 'public') {
            return $this->getFullUrl($conversionName);
        }

        return parent::getUrl($conversionName);
    }

    public function getFullUrl(string $conversionName = ''): string
    {
        return url(parent::getUrl($conversionName));
    }

    public function tagToModel(HasMedia $model, $collection_name, $replace = true): self
    {
        if ($replace) {
            $model->clearMediaCollection($collection_name);
        }

        $this->model_type = get_class($model);
        $this->model_id = $model->getKey();
        $this->collection_name = $collection_name;
        $this->save();

        return $this;
    }
}
