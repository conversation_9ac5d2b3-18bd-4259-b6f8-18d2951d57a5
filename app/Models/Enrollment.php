<?php

namespace App\Models;

use App\Enums\CardStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Interfaces\Billable;
use App\Interfaces\BillableItem;
use App\Interfaces\TransactionLoggable;
use App\Interfaces\Userable;
use App\Traits\Models\HasMediaInteractions;
use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\MediaLibrary\HasMedia;

class Enrollment extends Model implements HasMedia, TransactionLoggable, Userable, Billable, BillableItem
{
    use HasFactory, HasTranslations, HasMediaInteractions;

    const FINAL_STEP = 5;
    const STEP_STUDENT_PROFILE = 1;
    const STEP_GUARDIAN_PROFILE = 2;
    const STEP_DOCUMENT_UPLOAD = 3;
    const STEP_TERMS_AND_CONDITIONS = 4;
    const STEP_PAYMENT = 5;

    const TYPE_FILE_NRIC = 'file_nric';
    const TYPE_FILE_PASSPORT = 'file_passport';

    const TYPE_FILES = [
        ['value' => self::TYPE_FILE_NRIC, 'label' => 'NRIC'],
        ['value' => self::TYPE_FILE_PASSPORT, 'label' => 'Passport'],
    ];

    public $translatable = ['student_name'];

    public $guarded = ['id'];

    protected $casts = [
        'gender' => Gender::class,
        'status' => EnrollmentStatus::class,
    ];

    /**
     * Get the enrollment's grade.
     */
    public function admissionGrade(): BelongsTo
    {
        return $this->belongsTo(Grade::class, 'admission_grade_id');
    }

    /**
     * Get the enrollment's birthplace.
     */
    public function birthplace(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'birthplace_id');
    }

    /**
     * Get the enrollment's nationality.
     */
    public function nationality(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    /**
     * Get the enrollment's race.
     */
    public function race(): BelongsTo
    {
        return $this->belongsTo(Race::class, 'race_id');
    }

    /**
     * Get the enrollment's religion.
     */
    public function religion(): BelongsTo
    {
        return $this->belongsTo(Religion::class, 'religion_id');
    }

    /**
     * Get the enrollment's state.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Get the enrollment's country.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function guardians(): MorphToMany
    {
        return $this->morphToMany(Guardian::class, 'studenable', GuardianStudent::class)
            ->withPivot('type');
    }

    public function user(): ?BelongsTo
    {
        return null;
    }

    /**
     * Check if enrollment owned by user passed in
     */
    public function isOwnedBy(User $user): bool
    {
        return ($user->id === $this->created_by);
    }

    public function canBeEditedBy(User $user): bool
    {
        // TODO: To add this in after having admin module
//        if($user->userable instanceof Admin) {}

        return $this->created_by == $user->id || $user->isEmployee();
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the enrollment's nricFiles.
     */
    public function nricFiles(): Attribute
    {
        return Attribute::make(
            get: function () {
                $medias = $this->getMedia(Enrollment::TYPE_FILE_NRIC);

                if ($medias) {
                    $nric_links = [];

                    foreach ($medias as $media) {
                        $nric_links[] = [
                            'id' => $media->id,
                            'uuid' => $media->uuid,
                            'url' => $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds'))),
                        ];
                    }

                    return $nric_links;
                }

                return null;
            }
        );
    }

    /**
     * Get the enrollment's passportFiles.
     */
    public function passportFiles(): Attribute
    {
        return Attribute::make(
            get: function () {
                $medias = $this->getMedia(Enrollment::TYPE_FILE_PASSPORT);

                if ($medias) {
                    $nric_links = [];

                    foreach ($medias as $media) {
                        $nric_links[] = [
                            'id' => $media->id,
                            'uuid' => $media->uuid,
                            'url' => $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds'))),
                        ];
                    }

                    return $nric_links;
                }

                return null;
            }
        );
    }

    public function getCurrency(): Currency
    {
        // TODO: Set the currency into config
        return Currency::where('code', 'MYR')->firstOrFail();
    }

    public function canMakePayment(): bool
    {
        return in_array($this->status, [EnrollmentStatus::PENDING_PAYMENT, EnrollmentStatus::PAYMENT_FAILED]);
    }

    public function getUserNumberColumnName(): ?string
    {
        return null;
    }

    public function getUserNumber(): ?string
    {
        return 'ENROLLMENT-' . $this->id;
    }

    public function getUserTypeDescription(): string
    {
        return Userable::USER_LABELS[Enrollment::class];
    }

    public function getProfilePicture(): ?string
    {
        return null;
    }

    public function getBillToType(): string
    {
        return get_class($this);
    }

    public function getBillToId(): int
    {
        return $this->id;
    }

    public function getBillToName(): string
    {
        return $this->getUserName() ?? '';
    }

    public function getUserName(): ?string
    {
        return $this->getTranslation('student_name', 'en');
    }

    public function firstActiveCard(): MorphOne
    {
        return $this->morphOne(Card::class, 'userable')->latest('id')->where('status', CardStatus::ACTIVE->value);
    }

    public function getBillToAddress(): string
    {
        return $this->address ?? '';
    }

    public function getBillToEmail(): ?string
    {
        return $this->email;
    }

    public function getBillToReferenceNumber(): ?string
    {
        return null;
    }

    public function markAsPaid($payments)
    {
        if (in_array($this->status, [EnrollmentStatus::PENDING_PAYMENT, EnrollmentStatus::PAYMENT_FAILED])) {
            $this->status = EnrollmentStatus::SUBMITTED;
            $this->save();
        }
    }

    public function billingDocumentVoidedActionCallback()
    {
        if (in_array($this->status, [EnrollmentStatus::PENDING_PAYMENT, EnrollmentStatus::PAYMENT_FAILED])) {
            $this->status = EnrollmentStatus::PENDING_PAYMENT;
            $this->save();
        }
    }

    public function getDiscountApplicableDate()
    {
        return null;
    }
}
