<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ConductSetting extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function conductSettingTeachers(): HasMany
    {
        return $this->hasMany(ConductSettingTeacher::class, 'conduct_setting_id');
    }

    public function semesterSetting(): BelongsTo
    {
        return $this->belongsTo(SemesterSetting::class, 'semester_setting_id');
    }

    public function semesterClass(): BelongsTo
    {
        return $this->belongsTo(SemesterClass::class, 'semester_class_id');
    }

    public function gradingScheme(): BelongsTo
    {
        return $this->belongsTo(GradingScheme::class, 'grading_scheme_id');
    }
}
