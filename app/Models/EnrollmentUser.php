<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use OwenIt\Auditing\Contracts\Auditable;

class EnrollmentUser extends Authenticatable implements Auditable
{
    use  HasApiTokens, HasFactory, HasTranslations, \OwenIt\Auditing\Auditable;

    public $translatable = ['name'];
    protected $guarded = ['id'];

    public function otps(): HasMany
    {
        return $this->hasMany(EnrollmentLoginOtp::class);
    }

    public function enrollments(): hasMany
    {
        return $this->hasMany(Enrollment::class);
    }
}
