<?php

namespace App\Models;

use App\Interfaces\MaterializedViewModel;
use App\Jobs\RefreshMaterializedViewJob;
use Illuminate\Database\Eloquent\Model;

class ExamPostingPreCheck extends Model implements MaterializedViewModel
{
    // This is a view table
    protected $table = 'exam_posting_pre_checks';

    protected $casts = [
        'class_name' => 'array',
        'teacher_names' => 'array',
        'subject_name' => 'array',
        'student_name' => 'array',
        'result_source_subject_component_name' => 'array',
    ];

    public function class()
    {
        return $this->belongsTo(ClassModel::class);
    }

    public static function refreshViewTable($queue = true): void
    {
        if ($queue) {
            RefreshMaterializedViewJob::dispatch('exam_posting_pre_checks', true);
        } else {
            RefreshMaterializedViewJob::dispatchSync('exam_posting_pre_checks', true);
        }
    }
}
