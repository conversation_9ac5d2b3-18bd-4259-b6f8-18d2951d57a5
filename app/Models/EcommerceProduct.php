<?php

namespace App\Models;

use App\Traits\Models\HasMediaInteractions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;

class EcommerceProduct extends Model implements HasMedia, Auditable
{
    use HasFactory, HasMediaInteractions, \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'merchant_id',
        'code',
        'name',
        'description',
        'price_before_tax',
        'tax_id',
        'currency_code',
        'is_active',
        'sequence'
    ];

    public function deliveryDates(): HasMany
    {
        return $this->hasMany(EcommerceProductDeliveryDate::class, 'product_id');
    }

    public function availableDates(): HasMany
    {
        return $this->hasMany(EcommerceProductAvailableDate::class, 'product_id');
    }

    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class, 'merchant_id');
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(EcommerceProductGroup::class, EcommerceProductGroupAssignment::class, 'product_id', 'product_group_id');
    }

    public function subCategories(): BelongsToMany
    {
        return $this->belongsToMany(EcommerceProductSubCategory::class, EcommerceProductCategoryAssignment::class, 'product_id', 'product_sub_category_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(EcommerceProductTag::class, EcommerceProductTagAssignment::class, 'product_id', 'tag_id');
    }

    public function tax()
    {
        return $this->belongsTo(Tax::class);
    }

    public function getPhotoAttribute()
    {
        $media = $this->getMedia('photo')->first();

        if ($media) {
            return $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds')));
        }

        return null;
    }
}
