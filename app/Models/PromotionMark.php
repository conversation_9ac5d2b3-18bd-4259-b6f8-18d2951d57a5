<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class PromotionMark extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $table = 'promotion_marks';

    protected $guarded = ['id'];

    public function semesterClass(): BelongsTo
    {
        return $this->belongsTo(SemesterClass::class, 'semester_class_id');
    }

}
