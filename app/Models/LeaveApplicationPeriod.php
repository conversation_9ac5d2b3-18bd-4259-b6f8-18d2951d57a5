<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class LeaveApplicationPeriod extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $table = 'leave_application_periods';

    protected $guarded = ['id'];

    public function leaveApplication()
    {
        return $this->belongsTo(LeaveApplication::class);
    }
}
