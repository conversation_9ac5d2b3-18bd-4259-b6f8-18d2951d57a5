<?php

namespace App\Models;

use App\Interfaces\Deletable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class EnrollmentSession extends Model implements Deletable
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'fee_assignment_settings' => 'json',
    ];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function examSubjects(): BelongsToMany
    {
        return $this->belongsToMany(
            Subject::class,
            'enrollment_session_exam_subject',
            'enrollment_session_id',
            'subject_id',
        );
    }

    public function canBeDeleted(): bool
    {
        if ($this->examSubjects()->count()) {
            return false;
        }

        return true;
    }
}
