<?php

namespace App\Jobs;

use App\Models\GradingFramework;
use App\Models\GradingFrameworkHeader;
use App\Models\Student;
use App\Models\StudentGradingFramework;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateGradingFrameworkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public StudentGradingFramework $studentGradingFramework;
    public GradingFramework $gradingFramework;

    /**
     * Create a new job instance.
     */
    public function __construct(StudentGradingFramework $student_grading_framework, GradingFramework $grading_framework)
    {
        //$this->student = $student;
        $this->gradingFramework = $grading_framework;
        $this->studentGradingFramework = $student_grading_framework;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $service = app()->make(StudentGradingFrameworkService::class);

            $effective_from = Carbon::parse($this->studentGradingFramework->effective_from);
            $effective_to = Carbon::parse($this->studentGradingFramework->effective_to);

            $service->setStudent($this->studentGradingFramework->student)
                ->setGradingFramework($this->gradingFramework)
                ->setStudentGradingFramework($this->studentGradingFramework)
                ->updateGradingFramework($effective_from, $effective_to);
        }
        catch (\Exception $e){
            \Log::error($e->getMessage());
            \Log::error($e->getTraceAsString());
        }
    }
}
