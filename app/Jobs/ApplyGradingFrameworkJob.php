<?php

namespace App\Jobs;

use App\Models\GradingFramework;
use App\Models\GradingFrameworkHeader;
use App\Models\Student;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ApplyGradingFrameworkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public Student $student;
    public GradingFramework $gradingFramework;
    public Carbon $effectiveFrom;
    public Carbon $effectiveTo;
    public array $input;

    /**
     * Create a new job instance.
     */
    public function __construct(Student $student, GradingFramework $grading_framework, array $input)
    {
        $this->student = $student;
        $this->gradingFramework = $grading_framework;
        $this->input = $input;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $service = app()->make(StudentGradingFrameworkService::class);

            $effective_from = Carbon::parse($this->input['effective_from']);
            $effective_to = Carbon::parse($this->input['effective_to']);
    
            $service->setStudent($this->student)
                ->setGradingFramework($this->gradingFramework)
                ->applyGradingFrameworkForPeriod($effective_from, $effective_to, $this->input['academic_year']);

        }
        catch (\Exception $e){
            \Log::error($e->getMessage());
            \Log::error($e->getTraceAsString());
        }
    }
}
