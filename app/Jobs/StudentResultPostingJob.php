<?php

namespace App\Jobs;

use App\Models\ResultsPostingHeader;
use App\Models\Student;
use App\Services\Exam\ExamResultsPostingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StudentResultPostingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public ResultsPostingHeader $postingHeader;
    public Student $student;
    public int $studentId;

    public function __construct(ResultsPostingHeader $posting_header, $student_id)
    {
        $this->postingHeader = $posting_header;
        $this->studentId = $student_id;
        $this->student = Student::findOrFail($student_id);
    }

    public function handle(): void
    {
        $service = app()->make(ExamResultsPostingService::class);
        $student_grading_framework = $this->student->activeGradingFramework();

        $this->postingHeader->addLog("Running results posting job for student {$this->student->student_number}");

        if ( $student_grading_framework === null ) {
            $this->postingHeader->addError($this->studentId, 'Unable to get active student grading framework for student ' . $this->student->student_number);
            $this->reportProgress(false);
            return;
        }

        $report_card_output = $student_grading_framework->outputs()->where('code', $this->postingHeader->report_card_output_code)->first();

        // if cannot find output code within student's SGF, dont process but considered progress.
        if ( $report_card_output === null ) {
            $this->postingHeader->addError($this->studentId, 'Report Card Output Code ' . $this->postingHeader->report_card_output_code . ' not found for student ' . $this->student->student_number);
            $this->reportProgress(false);
            return;
        }

        try{

            $errors = $service
                ->setResultsPostingHeader($this->postingHeader)
                ->setReportCardOutput($report_card_output)
                ->setReportCardOutputService($report_card_output->service_class)
                ->runChecks(true);

            $this->postingHeader->addLog("[{$this->student->student_number}] Validating results");

            if ( count($errors) > 0 ) {
                $this->postingHeader->addError($this->studentId, $errors);
                $this->reportProgress(false);
                return;
            }

            $this->postingHeader->addLog("[{$this->student->student_number}] Validation OK");

            $service->runPosting()
                ->savePostingOutput();

            $this->postingHeader->addLog("[{$this->student->student_number}] Student results posting completed.");

            $this->reportProgress(true);

        }catch(\Exception $e){
            $this->postingHeader->addLog("[{$this->student->student_number}] Student results posting error.");
            $this->postingHeader->addError($this->studentId, $e->getMessage());

            \Log::error($e->getMessage());
            \Log::error($e->getTraceAsString());
            $this->reportProgress(false);
        }

    }

    public function reportProgress($is_success) {
        \Cache::increment($this->postingHeader->getProgressTrackingKey($is_success));
    }
}
