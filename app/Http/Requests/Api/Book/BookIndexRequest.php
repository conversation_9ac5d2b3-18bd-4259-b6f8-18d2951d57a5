<?php

namespace App\Http\Requests\Api\Book;

use App\Http\Requests\Api\CommonApiValidationRequest;

class BookIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authentication done on middleware side
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'book_no' => ['nullable', 'string'],
            'book_no_wildcard' => ['nullable', 'string'],
            'call_no_wildcard' => ['nullable', 'string'],
            'call_no' => ['nullable', 'string'],
            'isbn' => ['nullable', 'string'],
            'title' => ['nullable', 'string'],
            'author_ids' => ['nullable', 'array'],
            'author_ids.*' => ['integer'],
            'book_sub_classification_id' => ['nullable', 'integer'],
        ]);
    }
}
