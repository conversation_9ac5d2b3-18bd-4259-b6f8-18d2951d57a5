<?php

namespace App\Http\Requests\Api\GradingScheme;

use App\Enums\GradingSchemeType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GradingSchemeCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(GradingSchemeType::values())],
            'code' => ['required', 'string', 'max:32', Rule::unique('master_grading_schemes', 'code')],
            'name' => ['required', 'string'],
            'is_active' => ['required', 'boolean'],
            'grading_scheme_items' => ['required', 'array'],
            'grading_scheme_items.*.name' => ['required', 'string'],
            'grading_scheme_items.*.display_as_name' => ['required', 'string'],
            'grading_scheme_items.*.from' => ['required', 'numeric'],
            'grading_scheme_items.*.to' => ['required', 'numeric'],
            'grading_scheme_items.*.extra_marks' => ['required', 'numeric', 'min:0'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
