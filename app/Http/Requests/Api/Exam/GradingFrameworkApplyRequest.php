<?php

namespace App\Http\Requests\Api\Exam;

use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;


class GradingFrameworkApplyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'student_ids' => ['required', 'array'],
            'student_ids.*' => ['required', 'integer', 'exists:students,id'],
            'grading_framework_id' => ['required', 'int', 'exists:master_grading_frameworks,id'],
            'effective_from' => ['required', 'date_format:Y-m-d'],
            'effective_to' => ['required', 'date_format:Y-m-d', 'after_or_equal:effective_from'],
            'academic_year' => ['required', 'date_format:Y']
        ];
    }
}
