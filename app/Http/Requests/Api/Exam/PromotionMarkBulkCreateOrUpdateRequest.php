<?php

namespace App\Http\Requests\Api\Exam;

use Illuminate\Foundation\Http\FormRequest;


class PromotionMarkBulkCreateOrUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.semester_class_id' => ['required', 'integer', 'exists:semester_classes,id'],
            '*.net_average_for_promotion' => ['required', 'decimal:0,2'],
            '*.conduct_mark_for_promotion' => ['required', 'decimal:0,2']
        ];
    }
}
