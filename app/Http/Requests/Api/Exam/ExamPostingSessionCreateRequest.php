<?php

namespace App\Http\Requests\Api\Exam;

use App\Http\Requests\Api\CommonApiValidationRequest;

class ExamPostingSessionCreateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'grade_id' => 'required|integer|exists:master_grades,id',
            'semester_setting_id' => 'required|integer|exists:master_semester_settings,id',
            'code' => 'required|string|exists:report_card_outputs,code',
            'publish_date' => 'required|date_format:Y-m-d|after_or_equal:today'
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
