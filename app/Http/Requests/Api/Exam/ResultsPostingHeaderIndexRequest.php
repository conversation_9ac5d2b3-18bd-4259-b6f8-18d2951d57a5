<?php

namespace App\Http\Requests\Api\Exam;

use App\Http\Requests\Api\CommonApiValidationRequest;

class ResultsPostingHeaderIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'report_card_output_code' => ['nullable', 'string'],
            'grade_id' => ['nullable', 'integer'],
            'semester_setting_id' => ['nullable', 'integer'],
            'status' => ['nullable', 'string']
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'report_card_output_code' => isset($this->report_card_output_code) ? strtoupper($this->report_card_output_code) : null,
            'status' => isset($this->status) ? strtoupper($this->status) : null,
        ]);
    }
}
