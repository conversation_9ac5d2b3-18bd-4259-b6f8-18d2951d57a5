<?php

namespace App\Http\Requests\Api\Exam;

use App\Http\Requests\Api\CommonApiValidationRequest;

class StudentReportCardIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'results_posting_header_id' => ['nullable', 'integer'],
            'student_id' => ['nullable', 'integer'],
            'grade_id' => ['nullable', 'integer'],
            'semester_setting_id' => ['nullable', 'integer'],
            'semester_class_id' => ['nullable', 'integer'],
        ]);
    }
}
