<?php

namespace App\Http\Requests\Api\Timetable;

use App\Enums\PeriodAttendanceStatus;
use App\Enums\TimeslotTeacherType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TimetableUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'is_active' => ['required', 'boolean'],
            'timeslots' => ['required', 'array'],
            'timeslots.*.id' => ['required', 'exists:timeslots,id'],
            'timeslots.*.class_subject_id' => ['nullable', 'exists:class_subjects,id'],
            'timeslots.*.placeholder' => ['nullable', 'string'],
            'timeslots.*.attendance_from' => ['nullable', 'date_format:H:i:s'],
            'timeslots.*.attendance_to' => ['nullable', 'date_format:H:i:s'],
            'timeslots.*.default_init_status' => ['required', Rule::in(PeriodAttendanceStatus::values())],
            'timeslots.*.has_mark_deduction' => ['required', 'boolean'],
            'timeslots.*.teachers' => ['present', 'array'],
            'timeslots.*.teachers.*.employee_id' => ['nullable', 'exists:employees,id'],
            'timeslots.*.teachers.*.type' => ['nullable', Rule::in(TimeslotTeacherType::values())],
        ];
    }
}
