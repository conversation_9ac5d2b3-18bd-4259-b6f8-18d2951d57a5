<?php

namespace App\Http\Requests\Api\Reports\Attendance;

use App\Enums\ExportType;
use App\Models\Employee;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ClassAttendanceTakingStatusReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'date_from' => ['required', 'date_format:Y-m-d'],
            'date_to' => ['required', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'employee_id' => ['required', Rule::exists(Employee::class, 'id')],
            'export_type' => ['nullable', Rule::in([ExportType::PDF->value])],
        ];
    }
}
