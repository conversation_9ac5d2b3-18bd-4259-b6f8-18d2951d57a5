<?php

namespace App\Http\Requests\Api\EcommerceProduct;

use App\Enums\MerchantType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EcommerceProductCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'merchant_id' => ['required', Rule::exists('merchants', 'id')],
            'merchant_type' => ['required', Rule::in(MerchantType::values())],
            'code' => ['required', 'string', 'unique:ecommerce_products,code'],
            'name' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'price_before_tax' => ['required', 'numeric', 'min:0'],
            'currency_code' => ['required', Rule::exists('master_currencies', 'code')],
            'tax_id' => ['required', 'integer', Rule::exists('master_taxes', 'id')],
            'is_active' => ['required', 'boolean'],
            'photo' => ['nullable', 'image', 'max:2048'],
            'delivery_dates' => ['nullable', 'array'],
            'delivery_dates.*' => ['date'],
            'available_dates' => ['nullable', 'array'],
            'available_dates.*' => ['date'],
            'product_group_ids' => ['nullable', 'array'],
            'product_group_ids.*' => ['integer', 'exists:ecommerce_product_groups,id'],
            'product_sub_category_ids' => ['nullable', 'array'],
            'product_sub_category_ids.*' => ['nullable', Rule::exists('ecommerce_product_sub_categories', 'id')],
            'product_tag_ids' => ['nullable', 'array'],
            'product_tag_ids.*' => ['exists:ecommerce_product_tags,id'],
            'sequence' => ['required', 'integer'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
