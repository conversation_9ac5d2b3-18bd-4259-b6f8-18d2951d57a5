<?php

namespace App\Http\Requests\Api\ConductReportSetting;

use App\Enums\ConductReportSettingCategory;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class ConductReportSettingBulkCreateOrUpdateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'semester_setting_id' => ['required', 'integer', 'exists:master_semester_settings,id'],
            'conduct_report_settings' => ['required', 'array'],
            'conduct_report_settings.*.category'  => ['required', 'string', Rule::in(ConductReportSettingCategory::values())],
            'conduct_report_settings.*.from_date'  => ['required', 'date'],
            'conduct_report_settings.*.to_date'  => ['required', 'date', 'after_or_equal:from_date'],
            'conduct_report_settings.*.year' => ['required','date_format:Y'],
            'conduct_report_settings.*.grade_id' => ['nullable', 'integer', 'exists:master_grades,id'],
        ]);
    }
}
