<?php

namespace App\Http\Requests\Api\ConductReportSetting;

use App\Http\Requests\Api\CommonApiValidationRequest;

class ConductReportSettingIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'semester_setting_id' => ['nullable', 'integer'],
            'year' => ['nullable', 'date_format:Y'],
            'grade_id' => ['nullable', 'integer'],
            'category' => ['nullable', 'string'],
        ]);
    }
}
