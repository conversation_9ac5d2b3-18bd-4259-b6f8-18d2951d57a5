<?php

namespace App\Http\Requests\Api\WalletTransaction;

use App\Enums\CardStatus;
use App\Rules\WalletChargeHashCheck;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WalletChargeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'card_number' => ['required', Rule::exists('cards', 'card_number')->where('status', CardStatus::ACTIVE)],
            'pin' => ['nullable', 'string', 'max:6'],
            'amount_before_tax' => ['required', 'numeric', 'min:0'],
            'amount_after_tax' => ['required', 'numeric', 'min:0'],
            'tax_percent' => ['required', 'numeric', 'min:0'],
            'merchant_name' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'currency' => ['required', Rule::exists('master_currencies', 'code')],
            'transaction_date' => ['required', 'date'],
            'order_reference_no' => ['nullable', 'string', Rule::unique('wallet_transactions', 'reference_no')],
            'check' => ['required', 'string', new WalletChargeHashCheck()],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'order_reference_no' => isset($this->order_reference_no) ? strtoupper($this->order_reference_no) : null,
        ]);
    }
}
