<?php

namespace App\Http\Requests\Api\Club;

use App\Http\Requests\Api\CommonApiValidationRequest;

class ClubIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'club_category_id' => ['nullable', 'exists:club_categories,id'],
            'code' => ['nullable', 'string'],
            'name' => ['nullable'],
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
