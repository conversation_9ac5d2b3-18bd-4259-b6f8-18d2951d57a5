<?php

namespace App\Http\Requests\Api\ExamSemesterSetting;

use App\Enums\ExamSemesterSettingCategory;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class ExamSemesterSettingBulkCreateOrUpdateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'semester_setting_id' => ['required', 'integer', 'exists:master_semester_settings,id'],
            'exam_semester_settings' => ['required', 'array'],
            'exam_semester_settings.*.category'  => ['required', 'string', Rule::in(ExamSemesterSettingCategory::values())],
            'exam_semester_settings.*.from_date'  => ['required', 'date'],
            'exam_semester_settings.*.to_date'  => ['required', 'date', 'after_or_equal:from_date'],
            'exam_semester_settings.*.year' => ['required','date_format:Y'],
            'exam_semester_settings.*.grade_id' => ['nullable', 'integer', 'exists:master_grades,id'],
        ]);
    }
}
