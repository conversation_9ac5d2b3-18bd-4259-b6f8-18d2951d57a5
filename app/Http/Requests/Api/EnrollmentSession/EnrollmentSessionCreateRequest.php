<?php

namespace App\Http\Requests\Api\EnrollmentSession;

use App\Repositories\CountryRepository;
use App\Repositories\ProductRepository;
use App\Repositories\SubjectRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentSessionCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $fee_assignment_settings_payload = collect($this->input('fee_assignment_settings', []));

        // PRODUCT _ID
        $product_ids_payload = $fee_assignment_settings_payload->pluck('outcome.product_id')->filter()->unique()->toArray();
        $existing_product_ids = (new ProductRepository())->find($product_ids_payload)->pluck('id')->toArray();

        // COUNTRY ID
        $nationality_ids = $fee_assignment_settings_payload
            ->pluck('conditions')
            ->filter() // Filter out null or empty conditions
            ->flatten(1)
            ->filter(function ($item) {
                // Filter to only include items where 'field' is 'nationality_id'
                return isset($item['field']) && $item['field'] === 'nationality_id';
            })
            ->pluck('value') // this has country_id values
            ->unique() // Get only the unique nationality IDs
            ->values()
            ->toArray();

        $existing_country_ids = (new CountryRepository())->find($nationality_ids)->pluck('id')->toArray();

        return [
            'name' => ['required', 'string'],
            'from_date' => ['required', 'date', 'date_format:Y-m-d'],
            'to_date' => ['required', 'date', 'date_format:Y-m-d', 'after:from_date'],
            'code' => ['required', 'string', Rule::unique('enrollment_sessions', 'code')],
            'is_active' => ['required', 'boolean'],
            'course_id' => ['required', 'exists:master_courses,id'],
            'subject_ids' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    if (count($value) !== collect($value)->uniqueStrict()->count()) {
                        $fail(__('validation.distinct', ['attribute' => 'subject']));
                    }
                },
                new CheckExistFromRepositoryRule(SubjectRepository::class),
            ],
            'fee_assignment_settings' => [
                'nullable',
                'array',
                function ($attribute, $value, $fail) use ($existing_product_ids, $existing_country_ids) {
                    foreach ($value as $index => $setting) {
                        // Make sure [conditions, outcome] keys are present
                        foreach (['conditions', 'outcome'] as $field) {
                            if (!array_key_exists($field, $setting)) {
                                $fail(__('validation.required', ['attribute' => "fee_assignment_settings.{$index}.{$field}"]));
                                // e.g : The fee_assignment_settings.0.conditions field is required.
                            }
                        }

                        /**
                         * VALIDATE CONDITIONS STRUCTURE
                         */

                        if ($setting['conditions'] === null) {
                            // This is valid, no need to validate individual conditions
                        } else if (!is_array($setting['conditions'])) {
                            $fail(__('validation.array', ['attribute' => "fee_assignment_settings.{$index}.conditions"]));
                        } else {
                            foreach ($setting['conditions'] as $condition_index => $condition) {
                                // Check if condition is an array
                                if (!is_array($condition)) {
                                    $fail(__('validation.array', ['attribute' => "fee_assignment_settings.{$index}.conditions.{$condition_index}"]));
                                    continue;
                                }

                                // Make sure ['field', 'operator', 'value'] fields are present
                                foreach (['field', 'operator', 'value'] as $field) {

                                    if (!array_key_exists($field, $condition)) {
                                        $fail(__('validation.required', ['attribute' => "fee_assignment_settings.{$index}.conditions.{$condition_index}.{$field}"]));
                                        continue; // Skip to next field if it doesn't exist
                                    } else {
                                        if (!empty($condition['value'])) {
                                            // validate country_id field is in database
                                            if ($field === 'field' && $condition['field'] === 'nationality_id') {
                                                if (!in_array($condition['value'], $existing_country_ids)) {
                                                    $fail(__('validation.exists', ['attribute' => "fee_assignment_settings.{$index} nationality id : {$condition['value']}"]));
                                                }
                                            }
        
                                            // validate if is_hostel field is boolean
                                            if ($field === 'field' && $condition['field'] === 'is_hostel') {
                                                if (!is_bool($condition['value'])) {
                                                    $fail(__('validation.boolean', ['attribute' => "fee_assignment_settings.{$index} is hostel"]));
                                                }
                                            }
                                        }
                                    }
                                }

                                // Validate operator is one of the allowed values ['=', '!=']
                                if (isset($condition['operator']) && !in_array($condition['operator'], ['=', '!='])) {
                                    $fail(__('validation.in', ['attribute' => "fee_assignment_settings.{$index}.conditions.{$condition_index}.operator"]));
                                }
                            }
                        }


                        /**
                         * VALIDATE OUTCOME STRUCTURE
                         */

                        $outcome = $setting['outcome'];

                        // Check if outcome is an array
                        if (!is_array($outcome)) {
                            $fail(__('validation.array', ['attribute' => "fee_assignment_settings.{$index}.outcome"]));
                        } else {
                            // Make sure ['product_id', 'period', 'amount'] fields are present
                            foreach (['product_id', 'period', 'amount'] as $field) {
                                if (!array_key_exists($field, $outcome)) {
                                    $fail(__('validation.required', ['attribute' => "fee_assignment_settings.{$index}.outcome.{$field}"]));
                                }
                            }

                            // Validate product_id is valid
                            if (isset($outcome['product_id'])) {
                                if (!is_int($outcome['product_id'])) {
                                    $fail(__('validation.integer', ['attribute' => "fee_assignment_settings.{$index}.outcome.product_id"]));
                                } else {
                                    if (!in_array($outcome['product_id'], $existing_product_ids)) {
                                        $fail(__('validation.exists', ['attribute' => "fee_assignment_settings.{$index}.outcome.product_id"]));
                                    }
                                }
                            }

                            // Validate period is a date
                            if (isset($outcome['period']) && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $outcome['period'])) {
                                $fail(__('validation.date_format', [
                                    'attribute' => "fee_assignment_settings.{$index}.outcome.period",
                                    'format' => 'Y-m-d'
                                ]));
                            }

                            // Validate amount is numeric
                            if (isset($outcome['amount']) && !is_numeric($outcome['amount'])) {
                                $fail(__('validation.numeric', ['attribute' => "fee_assignment_settings.{$index}.outcome.amount"]));
                            }
                        }
                    }
                },
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
