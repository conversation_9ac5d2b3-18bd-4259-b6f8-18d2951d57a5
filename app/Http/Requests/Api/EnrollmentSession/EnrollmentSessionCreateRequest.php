<?php

namespace App\Http\Requests\Api\EnrollmentSession;

use App\Repositories\CountryRepository;
use App\Repositories\ProductRepository;
use App\Repositories\SubjectRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentSessionCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $fee_assignment_settings_payload = collect($this->input('fee_assignment_settings', []));

        // PRODUCT _ID
        $product_ids_payload = $fee_assignment_settings_payload->pluck('outcome.product_id')->filter()->unique()->toArray();
        $existing_product_ids = (new ProductRepository())->find($product_ids_payload)->pluck('id')->toArray();

        // COUNTRY ID
        $nationality_ids = $fee_assignment_settings_payload
            ->pluck('conditions')
            ->filter() // Filter out null or empty conditions
            ->flatten(1)
            ->filter(function ($item) {
                // Filter to only include items where 'field' is 'nationality_id'
                return isset($item['field']) && $item['field'] === 'nationality_id';
            })
            ->pluck('value') // this has country_id values
            ->unique() // Get only the unique nationality IDs
            ->values()
            ->toArray();

        $existing_country_ids = (new CountryRepository())->find($nationality_ids)->pluck('id')->toArray();

        return [
            'name' => ['required', 'string'],
            'from_date' => ['required', 'date', 'date_format:Y-m-d'],
            'to_date' => ['required', 'date', 'date_format:Y-m-d', 'after:from_date'],
            'code' => ['required', 'string', Rule::unique('enrollment_sessions', 'code')],
            'is_active' => ['required', 'boolean'],
            'course_id' => ['required', 'exists:master_courses,id'],
            'subject_ids' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    if (count($value) !== collect($value)->uniqueStrict()->count()) {
                        $fail(__('validation.distinct', ['attribute' => 'subject']));
                    }
                },
                new CheckExistFromRepositoryRule(SubjectRepository::class),
            ],
            // Fee assignment settings validation
            'fee_assignment_settings' => ['nullable', 'array'],
            'fee_assignment_settings.*.conditions' => ['nullable', 'array'],
            'fee_assignment_settings.*.outcome' => ['required', 'array'],

            // Conditions validation
            'fee_assignment_settings.*.conditions.*.field' => ['required_with:fee_assignment_settings.*.conditions.*', 'string'],
            'fee_assignment_settings.*.conditions.*.operator' => ['required_with:fee_assignment_settings.*.conditions.*', 'string', 'in:=,!='],
            'fee_assignment_settings.*.conditions.*.value' => ['required_with:fee_assignment_settings.*.conditions.*'],

            // Outcome validation
            'fee_assignment_settings.*.outcome.product_id' => ['required', 'integer', Rule::in($existing_product_ids)],
            'fee_assignment_settings.*.outcome.period' => ['required', 'date_format:Y-m-d'],
            'fee_assignment_settings.*.outcome.amount' => ['required', 'numeric', 'min:0'],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
