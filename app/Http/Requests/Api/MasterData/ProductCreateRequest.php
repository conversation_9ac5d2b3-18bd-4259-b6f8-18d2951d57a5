<?php

namespace App\Http\Requests\Api\MasterData;

use App\Enums\ProductCategory;
use App\Models\Product;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', Rule::unique(Product::class, 'code')],
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string'],
            'category' => ['required', 'string', Rule::in(ProductCategory::values())],
            'sub_category_1' => ['nullable', 'string'],
            'sub_category_2' => ['nullable', 'string'],
            'uom_code' => ['required', Rule::exists('master_uoms', 'code')],
            'unit_price' => ['required', 'numeric'],
            'gl_account_code' => ['required', Rule::exists('master_gl_accounts', 'code')],
            'description' => ['nullable'],
            'is_active' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
