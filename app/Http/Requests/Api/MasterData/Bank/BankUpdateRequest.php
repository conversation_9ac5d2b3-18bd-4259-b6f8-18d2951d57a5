<?php

namespace App\Http\Requests\Api\MasterData\Bank;

use App\Models\Bank;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BankUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'code' => ['required', 'string', 'max:16', Rule::unique(Bank::class, 'code')->ignore($this->bank->id)],
            'swift_code' => ['required', 'string', 'max:16', Rule::unique(Bank::class, 'swift_code')->ignore($this->bank->id)],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
