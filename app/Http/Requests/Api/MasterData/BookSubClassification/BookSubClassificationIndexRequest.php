<?php

namespace App\Http\Requests\Api\MasterData\BookSubClassification;

use App\Http\Requests\Api\CommonApiValidationRequest;

class BookSubClassificationIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authentication done on middleware side
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'book_classification_id' => ['nullable', 'integer'],
            'name' => ['nullable'],
            'code' => ['nullable', 'string'],
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
