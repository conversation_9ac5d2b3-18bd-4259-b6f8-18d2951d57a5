<?php

namespace App\Http\Requests\Api\MasterData;

use App\Enums\ProductCategory;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class ProductIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'code' => ['nullable'],
            'name' => ['nullable'],
            'category' => ['nullable', Rule::in(ProductCategory::values())],
            'sub_category_1' => ['nullable'],
            'sub_category_2' => ['nullable'],
            'uom_code' => ['nullable', Rule::exists('master_uoms', 'code')],
            'is_active' => ['nullable', 'boolean'],
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
