<?php

namespace App\Http\Requests\Api\MasterData;

use App\Rules\InternationalizationValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SchoolProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array'],
            'name.*' => ['nullable', 'string', 'max:100', new InternationalizationValidation()],
            'code' => ['required'],
            'short_name' => ['required', 'string'],
            'address' => ['required', 'string'],
            'country_id' => ['required', Rule::exists('master_countries', 'id')],
            'state_id' => ['required', Rule::exists('master_states', 'id')],
            'city' => ['required', 'string'],
            'postcode' => ['required', 'string'],
            'phone_1' => ['nullable', 'string'],
            'phone_2' => ['nullable', 'string'],
            'fax_1' => ['nullable', 'string'],
            'fax_2' => ['nullable', 'string'],
            'email' => ['nullable', 'email'],
            'url' => ['nullable', 'string'],
            'logo' => ['nullable', 'image', 'max:2048'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
