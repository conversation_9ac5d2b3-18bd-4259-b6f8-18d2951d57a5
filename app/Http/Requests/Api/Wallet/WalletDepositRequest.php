<?php

namespace App\Http\Requests\Api\Wallet;

use App\Helpers\ConfigHelper;
use App\Interfaces\Userable;
use App\Models\Config;
use App\Rules\AuthorizeUserableAccess;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WalletDepositRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'userable_id' => ['required', new AuthorizeUserableAccess],
            'userable_type' => ['required', Rule::in(Userable::USERABLE_TYPES)],
            'customer_name' => ['required', 'string'],
            'customer_email' => ['nullable', 'email'],
            'amount' => [
                'required',
                'numeric',
                'min:' . ConfigHelper::get(Config::WALLET_DEPOSIT_MIN_AMOUNT)
            ],
            'wallet_id' => [
                'required',
                Rule::exists('wallets', 'id'),
                function ($attribute, $value, $fail) {
                    if (!auth()->user()->canTopupWallet($value)) {
                        $fail(trans('validation.exists'));
                    }
                }
            ],
            'payment_type' => ['required', Rule::in(config('payments.accepted_payment_types'))],
            'return_url' => ['nullable', 'url:http,https']
        ];

        if (ConfigHelper::get(Config::WALLET_DEPOSIT_MAX_AMOUNT) > 0) {
            $rules['amount'][] = 'max:' . ConfigHelper::get(Config::WALLET_DEPOSIT_MAX_AMOUNT);
        }

        return $rules;
    }
}
