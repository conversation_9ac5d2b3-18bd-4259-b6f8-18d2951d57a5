<?php

namespace App\Http\Requests\Api\TimeslotOverride;

use App\Models\Student;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TimeslotOverrideBulkCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $student_ids = Student::select(['id'])
            ->whereIn('id', array_unique($this->input('student_ids.*', [])))
            ->pluck('id')
            ->toArray();

        return [
            'date' => ['required', 'date', 'date_format:Y-m-d'],
            'employee_id' => ['nullable', 'required_if:use_homeroom_teacher,false', 'exists:employees,id'],
            'use_homeroom_teacher' => ['required', 'boolean'],
            'student_ids' => ['required', 'array', 'min:1'],
            'student_ids.*' => ['required', 'integer', Rule::in($student_ids)],
            'periods' => ['required', 'array', 'min:1'],
            'periods.*.period' => ['required', 'integer'],
            'periods.*.placeholder' => ['nullable', 'required_if:periods.*.is_empty,false'],
            'periods.*.attendance_from' => ['nullable', 'required_if:periods.*.is_empty,false', 'date_format:H:i:s'],
            'periods.*.attendance_to' => ['nullable', 'required_if:periods.*.is_empty,false', 'date_format:H:i:s'],
            'periods.*.inherit_from_school_attendance' => ['required', 'boolean'],
            'periods.*.class_attendance_required' => ['required', 'boolean'],
            'periods.*.is_empty' => ['required', 'boolean'],
        ];
    }
}
