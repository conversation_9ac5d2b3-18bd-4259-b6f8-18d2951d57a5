<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Http\Requests\Api\CommonApiValidationRequest;

class EnrollmentIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'student_name' => ['nullable', 'string'],
            'created_by' => ['nullable', 'exists:users,id'],
            'admission_grade_id' => ['nullable'],
            'birthplace_id' => ['nullable', 'exists:master_countries,id'],
            'nationality_id' => ['nullable', 'exists:master_countries,id'],
            'race_id' => ['nullable', 'exists:master_races,id'],
            'religion_id' => ['nullable', 'exists:master_religions,id'],
            'birth_cert_no' => ['nullable'],
            'nric_no' => ['nullable'],
            'gender' => ['nullable'],
        ]);
    }
}
