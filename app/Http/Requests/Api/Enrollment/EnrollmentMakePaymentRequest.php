<?php

namespace App\Http\Requests\Api\Enrollment;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentMakePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && $this->enrollment->canBeEditedBy(auth()->user());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     * @throws \Exception
     */
    public function rules(): array
    {
        return [
            'customer_email' => ['required', 'email'],
            'customer_name' => ['required', 'string', 'max:100'],
            'payment_type' => ['required', Rule::in(config('payments.accepted_payment_types'))],
        ];
    }
}
