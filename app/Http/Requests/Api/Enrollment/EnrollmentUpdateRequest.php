<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Enums\EnrollmentAction;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\MarriedStatus;
use App\Models\Config;
use App\Models\Enrollment;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use App\Rules\TemporaryUploadedByUserValidation;
use App\Services\ConfigService;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

class EnrollmentUpdateRequest extends FormRequest
{
    const UNMODIFIABLE_STEPS = [
        Enrollment::STEP_STUDENT_PROFILE,
        Enrollment::STEP_GUARDIAN_PROFILE,
        Enrollment::STEP_DOCUMENT_UPLOAD,
        Enrollment::STEP_TERMS_AND_CONDITIONS,
    ];

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && $this->enrollment->canBeEditedBy(auth()->user());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     * @throws \Exception
     */
    public function rules(): array
    {
        $this->validateEnrollmentCanBeModified();

        // FINAL_STEP - 1 because final step(PAYMENT) uses another api
        $steps_allowed = implode(',', range(1, Enrollment::FINAL_STEP - 1));

        $rules['step'] = ['required', 'in:' . $steps_allowed];
        $rules['action'] = ['required', Rule::in(EnrollmentAction::values())];

        // If saving as draft, can make nullable for other fields,
        $is_required = !($this->action === EnrollmentAction::SAVE_AS_DRAFT->value);

        return array_merge($rules, $this->getValidations($is_required, (int)$this->step));
    }

    /**
     * @throws \Exception
     */
    private function validateEnrollmentCanBeModified(): void
    {
        $is_step_unmodifiable = in_array($this->step, self::UNMODIFIABLE_STEPS);
        $is_enrollment_not_draft = $this->enrollment->status !== EnrollmentStatus::DRAFT;

        if ($is_step_unmodifiable && $is_enrollment_not_draft) {
            throw new \Exception("Submitted enrollments cannot be modified.");
        }
    }

    private function getValidations(bool $is_required, int $step): array
    {
        $required_rule = $is_required ? 'required' : 'nullable';

        return match ($step) {
            Enrollment::STEP_STUDENT_PROFILE => [
                'student_name' => [$required_rule, 'array', new RequiredLocaleValidation()],
                'student_name.*' => [new InternationalizationValidation()],
                'date_of_birth' => [$required_rule, 'date'],
                'phone_no' => ['nullable', 'string'],
                'email' => [$required_rule, 'email'],
                'nationality_id' => [$required_rule, 'exists:master_countries,id'], // key to MasterCountry
                'nric_no' => [($is_required ? 'required_without:passport_no' : 'nullable'), 'string', 'digits:12'],
                'passport_no' => [($is_required ? 'required_without:nric_no' : 'nullable'), 'string'],
                'birth_cert_no' => [$required_rule, 'string'],
                'gender' => [$required_rule, Rule::in(Gender::values())],
                'birthplace_id' => [$required_rule, 'exists:master_countries,id'], // key to MasterCountry
                'religion_id' => [$required_rule, 'exists:master_religions,id'], // key to MasterReligion
                'race_id' => [$required_rule, 'exists:master_races,id'], // key to MasterRace
                'address' => [$required_rule, 'string'],
                'postal_code' => [$required_rule, 'string'],
                'city' => [$required_rule, 'string'],
                'state_id' => [$required_rule, 'exists:master_states,id'],
                'country_id' => [$required_rule, 'exists:master_countries,id'],
            ],
            Enrollment::STEP_GUARDIAN_PROFILE => $this->getGuardianValidations($is_required),
            Enrollment::STEP_DOCUMENT_UPLOAD => $this->getDocumentValidations($is_required),
            Enrollment::STEP_TERMS_AND_CONDITIONS => [
                'terms_and_conditions' => [$required_rule, 'accepted'],
            ],
        };
    }

    private function getGuardianValidations(bool $is_required): array
    {
        $required_guardians = [GuardianType::FATHER->value, GuardianType::MOTHER->value];

        // If is required, rule will be `required_if:guardians.*.type,FATHER,MOTHER`
        $required_rule = $is_required ? 'required_if:guardians.*.type,' . implode(',', $required_guardians) : 'nullable';

        return [
            "guardians" => ['required', 'array', 'size:3'], // Size set to 3, always need FATHER, MOTHER, GUARDIAN
            "guardians.*.type" => ['required', 'string', Rule::in(GuardianType::values()), 'distinct'],
            "guardians.*.name" => ['present', $required_rule, 'array'],
            "guardians.*.name.*" => [new InternationalizationValidation()],
            "guardians.*.nric" => [
                'present',
                function ($attribute, $value, $fail) use ($required_guardians) {
                    $this->nricPassportValidation($attribute, $value, 'passport_number', $fail, $required_guardians);
                }
            ],
            "guardians.*.passport_number" => [
                'present',
                function ($attribute, $value, $fail) use ($required_guardians) {
                    $this->nricPassportValidation($attribute, $value, 'nric', $fail, $required_guardians);
                }
            ],
            "guardians.*.phone_number" => ['nullable', $required_rule, 'string'],
            "guardians.*.email" => ['nullable', $required_rule, 'email'],
            "guardians.*.nationality_id" => ['nullable', $required_rule, 'exists:master_countries,id'],
            "guardians.*.race_id" => ['nullable', $required_rule, 'exists:master_races,id'],
            "guardians.*.religion_id" => ['nullable', $required_rule, 'exists:master_religions,id'],
            "guardians.*.education_id" => ['nullable', 'exists:master_education,id'],
            "guardians.*.married_status" => ['nullable', Rule::in(MarriedStatus::values()), 'distinct'],
            "guardians.*.occupation" => ['nullable', 'string'],
            "guardians.*.occupation_description" => ['nullable', 'string'],
        ];
    }

    /**
     * Get current attribute name, e.g. documents.0.nric, $attribute_key = nric
     * Get guardian type, it will replace current attribute name to type, e.g. documents.0.nric -> documents.0.type, then Arr::get() will get the value of documents.0.nric
     * Get the target attribute to match with, e.g. comparing with passport, then it will change documents.0.nric -> documents.0.passport, then it Arr::get() will set the target_attribute value
     * Check guardian type and both field, if guardian type is required (in this case FATHER and MOTHER), and both value is null, throw validation error
     */
    private function nricPassportValidation($attribute, $value, $check_against_attribute, $fail, $required_guardian_types): void
    {
        $attribute_key = explode('.', $attribute);
        $attribute_key = end($attribute_key);

        $guardian_type = Arr::get($this, str_replace($attribute_key, 'type', $attribute));

        $target_attribute_name = str_replace($attribute_key, $check_against_attribute, $attribute);
        $target_attribute_value = Arr::get($this, str_replace($attribute_key, $check_against_attribute, $attribute));

        if (in_array($guardian_type, $required_guardian_types) && empty($value) && empty($target_attribute_value)) {
            $display_attribute_name = Arr::get($this->attributes(), "guardian.*.".$check_against_attribute, $target_attribute_name);
            $fail(__('validation.required_without', ['values' => $display_attribute_name]));
        }

        if ($attribute_key === 'nric_no' && !empty($value)) {
            if (!is_numeric($value)) {
                $fail(__('validation.numeric', ['attribute' => $attribute_key]));
            }

            if (strlen($value) !== 12) {
                $fail(__('validation.digits', ['attribute' => $attribute_key, 'digits' => 12]));
            }
        }
    }

    public function attributes()
    {
        return [
            "guardian.*.type" => 'type',
            "guardian.*.name" => 'name',
            "guardian.*.nric" => 'nric',
            "guardian.*.passport_number" => 'passport number',
            "guardian.*.phone_number" => 'phone number',
            "guardian.*.email" => 'email',
            "guardian.*.nationality_id" => 'nationality',
            "guardian.*.race_id" => 'race',
            "guardian.*.religion_id" => 'religion',
            "guardian.*.education_id" => 'education',
            "guardian.*.married_status" => 'married status',
            "guardian.*.occupation" => 'occupation',
            "guardian.*.occupation_description" => 'occupation description',
            'documents.*.name' => 'document name',
            'documents.*.id' => 'document id'
        ];
    }

    private function getDocumentValidations(mixed $is_required): array
    {
        $is_required = $is_required == 'required';
        $config_service = app(ConfigService::class);
        $config_document_type = Arr::pluck(Enrollment::TYPE_FILES, 'value');
        $config_document_type = array_merge($config_document_type, $config_service->getConfigValue(Config::ENROLLMENT_DOCUMENT_TYPE));

        return [
            'documents' => ['array', 'size:' . count($config_document_type)],
            'documents.*' => 'required_array_keys:id,name',
            'documents.*.name' => ['distinct', Rule::in($config_document_type)],
            'documents.*.id' => ['present', new TemporaryUploadedByUserValidation(), function (string $attribute, mixed $value, Closure $fail) use ($is_required) {
                $file_collection_name = str_replace('id', 'name', $attribute);
                $file_collection_name = Arr::get($this, $file_collection_name);

                // Scenario 1: If is required, no input value or existing file will trigger validation error
                $this->validateDocumentRequirement($is_required, $value, $file_collection_name, $fail);

                // Scenario 2: If this value appear multiple times, throw error
                $this->validateDistinctDocumentId($value, $fail);
            }],
        ];
    }

    private function validateDocumentRequirement($is_required, $value, $file_collection_name, Closure $fail): void
    {
        if ($is_required && !($value || $this->enrollment->hasMedia($file_collection_name))) {
            $fail(__('validation.required'));
        }
    }

    private function validateDistinctDocumentId($value, Closure $fail): void
    {
        if ($value) {
            $document_ids = array_filter(Arr::pluck($this->documents, 'id'));
            $document_id_counters = array_count_values($document_ids);

            if ($document_id_counters[$value] > 1) {
                $fail(__('validation.distinct'));
            }
        }
    }
}
