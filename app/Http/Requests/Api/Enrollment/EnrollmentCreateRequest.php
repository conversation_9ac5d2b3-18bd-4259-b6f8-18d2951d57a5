<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;

class EnrollmentCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'admission_year' => ['required', 'integer', 'digits:4'],
            'admission_grade_id' => ['required', 'exists:master_grades,id'], // key to MasterGrade
            'student_name' => ['required', 'array', new RequiredLocaleValidation()],
            'student_name.*' => [new InternationalizationValidation()],
            'nric_no' => ['required_without:passport_no', 'string', 'digits:12'],
            'passport_no' => ['required_without:nric_no', 'string'],
        ];
    }
}
