<?php

namespace App\Http\Requests\Api\Hostel;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class HostelRoomBedCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'hostel_room_id' => ['required', 'exists:hostel_rooms,id'],
            'name' => [
                'required',
                'string',
                Rule::unique('hostel_room_beds', 'name')
                    ->where('hostel_room_id', $this->hostel_room_id),
            ],
            'is_active' => ['required', 'boolean'],
        ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => isset($this->name) ? strtoupper($this->name) : null,
        ]);
    }
}
