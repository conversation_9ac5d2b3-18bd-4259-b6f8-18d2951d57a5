<?php

namespace App\Http\Requests\Api\Hostel;

use App\Enums\HostelRoomGender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class HostelRoomUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'hostel_block_id' => ['required', 'exists:hostel_blocks,id'],
            'name' => [
                'required',
                'string',
                Rule::unique('hostel_rooms', 'name')
                    ->where('hostel_block_id', $this->hostel_block_id)
                    ->ignore($this->hostel_room->id),
            ],
            'gender' => ['required', Rule::in(HostelRoomGender::values())],
            'capacity' => ['nullable', 'integer'],
            'remarks' => ['nullable'],
            'is_active' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => isset($this->name) ? strtoupper($this->name) : null,
        ]);
    }
}
