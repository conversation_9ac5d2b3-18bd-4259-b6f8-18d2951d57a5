<?php

namespace App\Http\Requests\Api\Hostel;

use App\Enums\HostelInOutType;
use App\Rules\StudentHasIncompleteHostelInOutRecord;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class HostelInOutRecordCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'students' => ['required', 'array'],
            'students.*.type' => ['required', Rule::in(HostelInOutType::values())],
            'students.*.id' => ['bail', 'required', 'exists:students,id', new StudentHasIncompleteHostelInOutRecord()],
            'students.*.guardian_id' => ['nullable', 'exists:guardians,id'],
            'students.*.hostel_room_bed_id' => ['required', 'exists:hostel_room_beds,id'],
            'students.*.check_out_datetime' => ['required', 'date_format:Y-m-d H:i:s'],
            'students.*.card_no' => ['required_if:students.*.type,' . HostelInOutType::OUTING->value],
            'students.*.reason' => ['nullable'],
        ];
    }

    public function attributes(): array
    {
        return [
            'students.*.type' => 'type',
            'students.*.id' => 'student',
            'students.*.guardian_id' => 'guardian',
            'students.*.hostel_room_bed_id' => 'hostel room bed',
            'students.*.check_out_datetime' => 'check out datetime',
            'students.*.card_no' => 'card number',
        ];
    }
}
