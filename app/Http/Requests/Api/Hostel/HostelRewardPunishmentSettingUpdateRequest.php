<?php

namespace App\Http\Requests\Api\Hostel;

use Illuminate\Foundation\Http\FormRequest;

class HostelRewardPunishmentSettingUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'hostel_merit_demerit_setting_id' => ['required', 'exists:hostel_merit_demerit_settings,id'],
            'code' => ['required', 'string'],
            'name' => ['required', 'string'],
            'points' => ['required', 'numeric'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
