<?php

namespace App\Http\Requests\Api\EcommerceProductTag;

use App\Enums\MerchantType;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Student;
use App\Repositories\UserableRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EcommerceProductTagUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $tag_id = request()->input('tag')->id;

        $userable_repository = resolve(UserableRepository::class);

        $input_userables = $this->collect('userables');
        $student_ids = $input_userables->where('type', Student::class)->pluck('id')->toArray();
        $employee_ids = $input_userables->where('type', Employee::class)->pluck('id')->toArray();
        $guardian_ids = $input_userables->where('type', Guardian::class)->pluck('id')->toArray();

        $existing_userables = [
            Student::class => $userable_repository->getExistingUserableIds(Student::class, $student_ids),
            Employee::class => $userable_repository->getExistingUserableIds(Employee::class, $employee_ids),
            Guardian::class => $userable_repository->getExistingUserableIds(Guardian::class, $guardian_ids),
        ];

        return [
            'name' => ['required', 'string', Rule::unique('ecommerce_product_tags')->where('type', $this->type)->ignore($tag_id, 'id')],
            'type' => ['required', Rule::in(MerchantType::values())],
            'product_ids' => ['present', 'array'],
            'product_ids.*' => ['required', 'integer', 'exists:ecommerce_products,id'],
            'userables' => ['present', 'array'],
            'userables.*.type' => ['required', Rule::in([Student::class, Employee::class, Guardian::class])],
            'userables.*.id' => [
                'required',
                function ($attribute, $value, $fail) use ($existing_userables) {
                    $index = explode('.', $attribute)[1];
                    $type = $this->input("userables.$index.type");
                    if (!isset($existing_userables[$type]) || !in_array($value, $existing_userables[$type])) {
                        $fail(__('validation.in', ['attribute' => $attribute]));
                    }
                }
            ]
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(['tag' => $this->route('product_tag')]);
    }
}
