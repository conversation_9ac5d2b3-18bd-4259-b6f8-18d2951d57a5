<?php

namespace App\Http\Requests\Api\SubstituteRecord;

use App\Http\Requests\Api\CommonApiValidationRequest;

class SubstituteRecordBulkCreateOrUpdateSubmitRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'substitute_records' => ['required', 'array', 'min:1'],
            'substitute_records.*.timeslot_id' => ['required', 'exists:timeslots,id'],
            'substitute_records.*.substitute_teacher_id' => ['required', 'exists:employees,id'],
            'substitute_records.*.substitute_date' => ['required', 'date'],
            'substitute_records.*.remarks' => ['nullable', 'string'],
            'substitute_records.*.allowance' => ['required', 'numeric', 'min:0'],
        ]);
    }
}
