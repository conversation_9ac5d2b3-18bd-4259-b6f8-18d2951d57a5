<?php

namespace App\Http\Controllers\Api;

use App\Enums\EnrollmentStatus;
use App\Helpers\ErrorCodeHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Enrollment\EnrollmentCreateRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentIndexRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentMakePaymentRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\DepositRequestResource;
use App\Http\Resources\EnrollmentResource;
use App\Models\Enrollment;
use App\Models\User;
use App\Services\EnrollmentService;
use App\Traits\HandlesPagination;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class EnrollmentController extends Controller
{
    use HandlesPagination;

    protected EnrollmentService $enrollmentService;

    public function __construct(EnrollmentService $enrollment_service)
    {
        $this->middleware(function ($request, $next) {
            /** @var User $user */
            $user = auth()->user();

            // Only allow guardian or admin to view
            if ($user->isGuardian() || $user->isEmployee()) {
                return $next($request);
            }

            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4001);
        });

        $this->enrollmentService = $enrollment_service;
    }

    public function index(EnrollmentIndexRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $input = $request->validated();

        if ($user->isGuardian()) {
            $input['created_by'] = $user->id;
        }

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, 
            $this->enrollmentService, 
            'getAllEnrollments',
            'getAllPaginatedEnrollments');

        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EnrollmentResource::collection($data))->getResponse();
    }

    public function show(Enrollment $enrollment): JsonResponse
    {
        $enrollment->load(['admissionGrade', 'nationality', 'birthplace', 'race' ,'religion', 'state', 'country', 'createdBy', 'guardians.country', 'guardians.race', 'guardians.religion', 'guardians.education']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function create(EnrollmentCreateRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $input = $request->validated();
        $input['created_by'] = $user->id;
        $input['status'] = EnrollmentStatus::DRAFT;
        $input['step'] = Enrollment::STEP_STUDENT_PROFILE; // When save, it will default the enrollment form to first step

        $enrollment = $this->enrollmentService->createEnrollment($input);

        $enrollment->load(['admissionGrade', 'nationality', 'birthplace', 'race' ,'religion', 'state', 'country', 'createdBy', 'guardians.country', 'guardians.race', 'guardians.religion', 'guardians.education']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    /**
     * @throws Exception
     */
    public function update(Enrollment $enrollment, EnrollmentUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment = $this->enrollmentService->updateEnrollment($enrollment, $input);
        $enrollment->load(['admissionGrade', 'nationality', 'birthplace', 'race' ,'religion', 'state', 'country', 'createdBy', 'guardians.country', 'guardians.race', 'guardians.religion', 'guardians.education']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function makePayment(Enrollment $enrollment, EnrollmentMakePaymentRequest $request): JsonResponse
    {
        if (!$enrollment->canMakePayment()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5001);
        }

        $input = $request->validated();

        $user = auth()->user();

        $payment_url = $this->enrollmentService->generatePaymentUrl($user, $enrollment, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new DepositRequestResource($payment_url))
            ->getResponse();
    }

    public function getUploadableFileOptions()
    {
        $uploadable_file_options = $this->enrollmentService->getUploadableFilesOptions();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($uploadable_file_options)
            ->getResponse();
    }
}
