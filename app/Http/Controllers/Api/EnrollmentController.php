<?php

namespace App\Http\Controllers\Api;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Enrollment\EnrollmentBulkSaveImportedDataRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentDownloadTemplateRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentImportTemplateValidationRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EnrollmentResource;
use App\Models\Enrollment;
use App\Repositories\EnrollmentSessionRepository;
use App\Services\EnrollmentService;
use App\Services\ReportPrintService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EnrollmentController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected EnrollmentService $enrollmentService,
        protected ReportPrintService $reportPrintService,
        protected EnrollmentSessionRepository $enrollmentSessionRepository
    ) {
    }

    public function index(EnrollmentIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input,
            $this->enrollmentService,
            'getAllEnrollments',
            'getAllPaginatedEnrollments'
        );

        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EnrollmentResource::collection($data))->getResponse();
    }

    public function show(Enrollment $enrollment): JsonResponse
    {
        $enrollment->load(['admissionGrade', 'nationality', 'race', 'religion', 'state', 'country', 'guardians.country', 'guardians.race', 'guardians.religion', 'guardians.education']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function downloadPrePaymentTemplate(EnrollmentDownloadTemplateRequest $request)
    {
        $input = $request->validated();

        $report_data = $this->enrollmentService->getTemplateData($input['enrollment_session_id'], false);

        $report_view_name = 'templates.enrollment-template';
        $file_name = 'enrollment-template';

        $export_type = ExportType::EXCEL;
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function downloadPostPaymentTemplate(EnrollmentDownloadTemplateRequest $request)
    {
        $input = $request->validated();

        $report_data = $this->enrollmentService->getTemplateData($input['enrollment_session_id'], true);

        $report_view_name = 'templates.enrollment-template-post-payment';
        $file_name = 'enrollment-template';

        $export_type = ExportType::EXCEL;
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    /**
     * Validate the enrollment import template (FULL).
     */
    public function importTemplateValidation(EnrollmentImportTemplateValidationRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        $response = $this->enrollmentService
            ->setImportFile($input['file'])
            ->setEnrollmentSession($enrollment_session)
            ->transformExcelToCollection()
            ->validateBaseForImport()
            ->validatePrePaymentInfoForImport()
            ->validateReligionForImport()
            ->validateNRICForImport()
            ->validatePassportNumberForImport()
            ->validatePrimarySchoolForImport()
            ->validateHealthConcernForImport()
            ->validateSubjectForImport()
            ->validatePhoneNumberForImport()
            ->validatePhoneNumberAndEmailUniquenessForImport()
            ->getValidatedData();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    /**
     * save the enrollment import template (FULL).
     */
    public function bulkSaveImportedData(EnrollmentBulkSaveImportedDataRequest $request): JsonResponse
    {
        $input = $request->all();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        try {
            $response = $this->enrollmentService
                ->setData($input['enrollments'])
                ->setEnrollmentSession($enrollment_session)
                ->validateBaseForImport()
                ->validatePrePaymentInfoForImport()
                ->validateReligionForImport()
                ->validateNRICForImport()
                ->validatePassportNumberForImport()
                ->validatePrimarySchoolForImport()
                ->validateHealthConcernForImport()
                ->validatePhoneNumberForImport()
                ->validatePhoneNumberAndEmailUniquenessForImport()
                ->validateSubjectForImport();

            if ($response->getValidatedData()['error_count'] > 0) {
                return (new ApiResponse())
                    ->setMessage(__('api.common.error'))
                    ->setCode(422)
                    ->setData($response->getValidatedData())
                    ->getResponse();
            }

            $response->saveImportedData();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            Log::error('Enrollment bulkSaveImportedData function error: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return (new ApiResponse())
                ->setError($e->getMessage())
                ->setCode($e->getCode())
                ->getResponse();
        }
    }
}
