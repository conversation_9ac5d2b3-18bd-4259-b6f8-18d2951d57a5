<?php

namespace App\Http\Controllers\Api\Exam;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Exam\PromotionMarkBulkCreateOrUpdateRequest;
use App\Http\Requests\Api\Exam\PromotionMarkIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\PromotionMarkResource;
use App\Models\PromotionMark;
use App\Services\Exam\PromotionMarkService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class PromotionMarkController extends Controller
{
    use HandlesPagination;

    protected PromotionMarkService $promotionMarkService;

    public function __construct(PromotionMarkService $promotion_mark_service)
    {
        $this->promotionMarkService = $promotion_mark_service;
    }

    public function index(PromotionMarkIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input, 
            $this->promotionMarkService, 
            'getAllPromotionMarks', 
            'getAllPaginatedPromotionMarks');

        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(PromotionMarkResource::collection($data))
            ->getResponse();
    }

    public function bulkCreateOrUpdate(PromotionMarkBulkCreateOrUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->promotionMarkService->bulkCreateOrUpdatePromotionMark($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
