<?php

namespace App\Http\Controllers\Api\Exam;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Exam\ExamResultsDataEntryGetEligibleClassesRequest;
use App\Http\Requests\Api\Exam\ExamResultsDataEntryGetEligibleStudentsRequest;
use App\Http\Requests\Api\Exam\ReopenPostedEntryRequest;
use App\Http\Requests\Api\Exam\SaveExamResultsDataEntryRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ExamResource;
use App\Http\Resources\SimpleClassModelResource;
use App\Http\Resources\SimpleSubjectResource;
use App\Models\ClassModel;
use App\Models\Exam;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\Subject;
use App\Services\Exam\ExamResultsDataEntryService;
use App\Services\Exam\ExamResultsPostingService;

class ExamResultsDataEntryController extends Controller
{
    protected ExamResultsDataEntryService $examResultsDataEntryService;
    protected ExamResultsPostingService $examResultPostingService;

    public function __construct(
        ExamResultsDataEntryService $exam_result_data_entry_service,
        ExamResultsPostingService $exam_result_posting_service
    ) {
        $this->examResultsDataEntryService = $exam_result_data_entry_service;
        $this->examResultPostingService = $exam_result_posting_service;

    }

    public function save(SaveExamResultsDataEntryRequest $request)
    {

        $user = \Auth::user();
        $employee = $user->employee;
        $input = $request->validated();

        try {

            if ($employee === null) {
                throw new \Exception('User is not an employee.');
            }

            $result_source_subject = ResultSourceSubject::where('id', $input['result_source_subject_id'])->firstOrFail();

            $service = $this->examResultsDataEntryService
                ->setEmployee($employee)
                ->setResultSourceSubject($result_source_subject)
                ->setScore($input['score'] ?? null)
                ->setGrade($input['grade'] ?? null);

            if (isset($input['result_source_subject_component_id'])) {
                $result_source_subject_component = ResultSourceSubjectComponent::where('id', $input['result_source_subject_component_id'])
                    ->firstOrFail();

                if ($result_source_subject_component->result_source_subject_id !== $result_source_subject->id) {
                    throw new \Exception('Result source subject component does not belong to result source subject');
                }

                $service->setResultSourceSubjectComponent($result_source_subject_component);
            }

            $service->saveUsingResultSourceSubject();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();

        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(400)
                ->setCode((int) $e->getCode())
                ->getResponse();
        }
    }


    public function reopenPostedEntry(ReopenPostedEntryRequest $request)
    {

        $user = \Auth::user();
        $employee = $user->employee;
        $input = $request->validated();

        try {

            if ($employee === null) {
                throw new \Exception('User is not an employee.');
            }

            $result_source_subject = ResultSourceSubject::where('id', $input['result_source_subject_id'])->firstOrFail();

            $this->examResultsDataEntryService
                ->setEmployee($employee)
                ->setResultSourceSubject($result_source_subject)
                ->reopenPostedEntriesByResultSourceSubject();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();

        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(400)
                ->setCode((int) $e->getCode())
                ->getResponse();
        }


    }

    public function getEligibleSubjects()
    {

        $user = \Auth::user();
        $employee = $user->employee;

        try {

            if ($employee === null) {
                throw new \Exception('User is not an employee.');
            }

            $exams = $this->examResultsDataEntryService
                ->setEmployee($employee)
                ->getEligibleSubjectsForTeacher();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData(SimpleSubjectResource::collection($exams))
                ->getResponse();

        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(400)
                ->setCode((int) $e->getCode())
                ->getResponse();
        }

    }

    public function getEligibleClasses(ExamResultsDataEntryGetEligibleClassesRequest $request)
    {

        $user = \Auth::user();
        $employee = $user->employee;

        $input = $request->validated();

        try {

            if ($employee === null) {
                throw new \Exception('User is not an employee.');
            }

            $subject = Subject::where('id', $input['subject_id'])->firstOrFail();

            $classes = $this->examResultsDataEntryService
                ->setEmployee($employee)
                ->setSubject($subject)
                ->getEligibleClassesForTeacherAndSubject();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData(SimpleClassModelResource::collection($classes))
                ->getResponse();

        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(400)
                ->setCode((int) $e->getCode())
                ->getResponse();
        }
    }

    public function getEligibleExams()
    {

        $exams = $this->examResultsDataEntryService->getEligibleExamsForDataEntry();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(ExamResource::collection($exams))
            ->getResponse();

    }

    public function getEligibleStudentsAndScores(ExamResultsDataEntryGetEligibleStudentsRequest $request)
    {

        $user = \Auth::user();
        $employee = $user->employee;

        $input = $request->validated();

        try {

            if ($employee === null) {
                throw new \Exception('User is not an employee.');
            }

            $subject = Subject::where('id', $input['subject_id'])->firstOrFail();
            $class = ClassModel::where('id', $input['class_id'])->firstOrFail();
            $exam = Exam::where('id', $input['exam_id'])->firstOrFail();

            $students = $this->examResultsDataEntryService
                ->setEmployee($employee)
                ->setSubject($subject)
                ->setClass($class)
                ->setExam($exam)
                ->getEligibleStudentsAndScores();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($students)
                ->getResponse();

        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(400)
                ->setCode((int) $e->getCode())
                ->getResponse();
        }
    }
}
