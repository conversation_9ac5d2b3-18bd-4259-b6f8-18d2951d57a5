<?php

namespace App\Http\Controllers\Api\Exam;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Exam\StudentReportCardIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\StudentReportCardResource;
use App\Services\Exam\ReportCard\StudentReportCardService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class StudentReportCardController extends Controller
{
    protected StudentReportCardService $studentReportCardService;

    use HandlesPagination;

    public function __construct(
        StudentReportCardService $student_report_card_service
    ) {
        $this->studentReportCardService = $student_report_card_service;

    }

    public function index(StudentReportCardIndexRequest $request): JsonResponse
    {
        $input = $request->validated();
        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $input['is_active'] = true;
        $input['includes'] = 'semesterSetting';

        $data = $this->fetchData($input, $this->studentReportCardService, 'getAllReportCard', 'getAllPaginatedReportCards');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(StudentReportCardResource::collection($data))->getResponse();
    }
}
