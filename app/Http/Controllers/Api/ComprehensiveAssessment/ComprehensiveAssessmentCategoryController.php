<?php

namespace App\Http\Controllers\Api\ComprehensiveAssessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ComprehensiveAssessment\ComprehensiveAssessmentCategoryCreateRequest;
use App\Http\Requests\Api\ComprehensiveAssessment\ComprehensiveAssessmentCategoryIndexRequest;
use App\Http\Requests\Api\ComprehensiveAssessment\ComprehensiveAssessmentCategoryUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ComprehensiveAssessmentCategoryResource;
use App\Models\ComprehensiveAssessmentCategory;
use App\Services\ComprehensiveAssessmentCategoryService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class ComprehensiveAssessmentCategoryController extends Controller
{
    use HandlesPagination;

    protected ComprehensiveAssessmentCategoryService $comprehensiveAssessmentCategoryService;

    public function __construct(ComprehensiveAssessmentCategoryService $comprehensive_assessment_category_service)
    {
        $this->comprehensiveAssessmentCategoryService = $comprehensive_assessment_category_service;
    }

    public function index(ComprehensiveAssessmentCategoryIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, 
            $this->comprehensiveAssessmentCategoryService, 
            'getAllComprehensiveAssessmentCategories', 
            'getAllPaginatedComprehensiveAssessmentCategories');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(ComprehensiveAssessmentCategoryResource::collection($data))->getResponse();
    }

    public function show(ComprehensiveAssessmentCategory $assessment_category): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ComprehensiveAssessmentCategoryResource($assessment_category))
            ->getResponse();
    }

    public function create(ComprehensiveAssessmentCategoryCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $assessment_category = $this->comprehensiveAssessmentCategoryService->createComprehensiveAssessmentCategory($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ComprehensiveAssessmentCategoryResource($assessment_category))
            ->getResponse();
    }

    public function update(ComprehensiveAssessmentCategory $assessment_category, ComprehensiveAssessmentCategoryUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $assessment_category = $this->comprehensiveAssessmentCategoryService->updateComprehensiveAssessmentCategory($assessment_category, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ComprehensiveAssessmentCategoryResource($assessment_category))
            ->getResponse();
    }

    public function destroy(ComprehensiveAssessmentCategory $assessment_category): JsonResponse
    {
        $this->comprehensiveAssessmentCategoryService->deleteComprehensiveAssessmentCategory($assessment_category);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
