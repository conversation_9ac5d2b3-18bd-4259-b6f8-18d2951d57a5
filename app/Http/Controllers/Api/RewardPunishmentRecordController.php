<?php

namespace App\Http\Controllers\Api;

use App\Enums\RewardPunishmentRecordStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\RewardPunishmentRecord\RewardPunishmentRecordCreateRequest;
use App\Http\Requests\Api\RewardPunishmentRecord\RewardPunishmentRecordIndexRequest;
use App\Http\Requests\Api\RewardPunishmentRecord\RewardPunishmentRecordUpdateRequest;
use App\Http\Requests\Api\RewardPunishmentRecord\RewardPunishmentRecordUpdateStatusInBulkRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\RewardPunishmentRecordResource;
use App\Models\RewardPunishmentRecord;
use App\Services\RewardPunishmentRecordService;
use Illuminate\Http\JsonResponse;

class RewardPunishmentRecordController extends Controller
{
    protected RewardPunishmentRecordService $rewardPunishmentRecordService;

    public function __construct(RewardPunishmentRecordService $reward_punishment_record_service)
    {
        $this->rewardPunishmentRecordService = $reward_punishment_record_service;
    }

    public function index(RewardPunishmentRecordIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['includes'] = array_merge($input['includes'] ?? [], ['student', 'rewardPunishment']);
        $input['includes'] = array_unique($input['includes']);

        if (in_array('studentLatestPrimaryClasses', $input['includes'])) {
            unset($input['includes'][array_search('studentLatestPrimaryClasses', $input['includes'])]);
            $input['includes'][] = 'studentLatestPrimaryClasses.semesterClass.classModel';
        }

        $data = $this->rewardPunishmentRecordService->getAllPaginatedRewardPunishmentRecords($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(RewardPunishmentRecordResource::collection($data))
            ->setPagination($data)
            ->getResponse();
    }

    public function show(RewardPunishmentRecord $reward_punishment_record): JsonResponse
    {
        $reward_punishment_record->load(['student', 'rewardPunishment.meritDemeritSettings', 'rewardPunishment.category', 'rewardPunishment.subCategory']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new RewardPunishmentRecordResource($reward_punishment_record))
            ->getResponse();
    }

    public function create(RewardPunishmentRecordCreateRequest $request): JsonResponse
    {
        $input = $request->validated();
        // Create reward punishment record as POSTED status
        $input['status'] = RewardPunishmentRecordStatus::POSTED->value;

        $this->rewardPunishmentRecordService->createRewardPunishmentRecord($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function update(RewardPunishmentRecord $reward_punishment_record, RewardPunishmentRecordUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $reward_punishment_record = $this->rewardPunishmentRecordService->updateRewardPunishmentRecord($reward_punishment_record, $input);

        $reward_punishment_record->load(['student', 'rewardPunishment.meritDemeritSettings', 'rewardPunishment.category', 'rewardPunishment.subCategory']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new RewardPunishmentRecordResource($reward_punishment_record))
            ->getResponse();
    }

    public function bulkUpdateStatus(RewardPunishmentRecordUpdateStatusInBulkRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->rewardPunishmentRecordService->updateRewardPunishmentRecordStatusInBulk($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function destroy(RewardPunishmentRecord $reward_punishment_record): JsonResponse
    {
        $this->rewardPunishmentRecordService->deleteRewardPunishmentRecordById($reward_punishment_record);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
