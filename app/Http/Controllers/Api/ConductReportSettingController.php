<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ConductReportSetting\ConductReportSettingBulkCreateOrUpdateRequest;
use App\Http\Requests\Api\ConductReportSetting\ConductReportSettingIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ConductReportSettingResource;
use App\Services\ConductReportSettingService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class ConductReportSettingController extends Controller
{
    use HandlesPagination;

    protected ConductReportSettingService $conductReportSettingService;

    public function __construct(ConductReportSettingService $conduct_report_setting_service)
    {
        $this->conductReportSettingService = $conduct_report_setting_service;
    }

    public function index(ConductReportSettingIndexRequest $request): JsonResponse
    {
        $input = $request->validated();
        $input['includes'] = array_unique(array_merge($input['includes'] ?? [],
        ['grade', 'semesterSetting']
    ));

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input, 
            $this->conductReportSettingService, 
            'getAllConductReportSettings', 
            'getAllPaginatedConductReportSettings');

        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(ConductReportSettingResource::collection($data))
            ->getResponse();
    }

    public function bulkCreateOrUpdate(ConductReportSettingBulkCreateOrUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->conductReportSettingService->bulkCreateOrUpdateConductReportSettings($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
