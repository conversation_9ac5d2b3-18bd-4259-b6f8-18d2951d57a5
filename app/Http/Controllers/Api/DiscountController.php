<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Discount\DiscountConfirmBulkRequest;
use App\Http\Requests\Api\Discount\DiscountCreateBulkRequest;
use App\Http\Requests\Api\Discount\DiscountIndexRequest;
use App\Http\Requests\Api\Discount\DiscountUpdateBulkRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\DiscountSettingResource;
use App\Interfaces\Userable;
use App\Models\DiscountSetting;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Repositories\DiscountSettingRepository;
use App\Repositories\StudentRepository;
use App\Services\Billing\DiscountSettingService;
use App\Traits\HandlesPagination;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class DiscountController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected DiscountSettingService $discountSettingService,
        protected DiscountSettingRepository $discountSettingRepository,
        protected StudentRepository $studentRepository,
    ) {
    }

    public function index(DiscountIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (isset($input['userable_type'])) {
            $input['userable_type'] = Userable::USERABLE_MAPPING[$input['userable_type']] ?? null;
        }

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        // make sure we eager load the correct morph sub-relationship under source
        if (isset($input['includes'])) {
            foreach ($input['includes'] as $include) {
                if (str_contains($include, 'source')) {
                    $input['includes']['source'] = function (MorphTo $morph_to) {
                        $morph_to->morphWith([
                            ScholarshipAward::class => ['scholarship'],
                        ]);
                    };
                }
            }
        }

        $data = $this->fetchData($input, $this->discountSettingService, 'getAllDiscountSettings', 'getAllPaginatedDiscountSettings');
        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(DiscountSettingResource::collection($data))
            ->getResponse();
    }

    public function createBulk(DiscountCreateBulkRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Resolve result sources
        $input_discount_sources = collect($input['discounts'])
            ->filter(function ($item) {
                return isset($item['source_type']) || isset($item['source_id']);
            })
            ->map(function ($item) {
                return [
                    'source_type' => $item['source_type'],
                    'source_id' => $item['source_id'],
                ];
            })->groupBy('source_type');

        $discount_sources = [];
        foreach ($input_discount_sources as $source_type => $sources) {
            $discount_sources[$source_type] = $source_type::whereIn('id', $sources->pluck('source_id'))->get()->keyBy('id');
        }

        try {
            DB::transaction(function () use ($input, $discount_sources) {
                $student_ids = collect($input['discounts'])->pluck('student_id')->toArray();
                $students = $this->studentRepository->getAll([
                    'id' => $student_ids,
                ])->keyBy('id');

                foreach ($input['discounts'] as $discount) {
                    /** @var DiscountSettingService $discount_setting_service */
                    $discount_setting_service = resolve(DiscountSettingService::class);

                    $discount_setting_service
                        ->setBasis($discount['basis'])
                        ->setBasisAmount($discount['basis_amount'])
                        ->setMaxAmount($discount['max_amount'])
                        ->setEffectiveFromDate($discount['effective_from'])
                        ->setEffectiveToDate($discount['effective_to'])
                        ->setDescription($discount['description'] ?? null)
                        ->setLimitToGlAccountCodes(collect($discount['gl_account_codes'] ?? []))
                        ->setUserable($students[$discount['student_id']]);

                    if (isset($discount['source_id']) && isset($discount['source_type']) && isset($discount_sources[$discount['source_type']][$discount['source_id']])) {
                        $discount_setting_service->setDiscountSource($discount_sources[$discount['source_type']][$discount['source_id']]);
                    }

                    $discount_setting_service->create();
                }
            });

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setMessage($e->getMessage())
                ->setCode(400)
                ->getResponse();
        }
    }

    /**
     * When confirmed, discounts will back apply all invoices eligible for advances
     **/
    public function confirm(DiscountConfirmBulkRequest $request): JsonResponse
    {
        try {
            $discount_ids = $request->validated()['discount_ids'];

            $discounts = $this->discountSettingRepository->find($discount_ids);

            // sort it so it always apply in order
            foreach ($discounts->sortBy('id') as $discount) {
                /** @var DiscountSettingService $discount_setting_service */
                $discount_setting_service = resolve(DiscountSettingService::class);

                $discount_setting_service->setDiscount($discount);

                if (!$discount->is_active) {
                    $discount_setting_service
                        ->setIsActive(true)
                        ->update();

                    $discount_setting_service->backApplyDiscountAsAdvancePayment();
                }
            }

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setMessage($e->getMessage())
                ->setCode(400)
                ->getResponse();
        }
    }

    public function updateBulk(DiscountUpdateBulkRequest $request): JsonResponse
    {
        $input = $request->validated();

        $discount_ids_in_requests = collect($input['discounts'])->pluck('discount_id')->toArray();

        $existing_discounts = $this->discountSettingService->getAllDiscountSettings([
            'id' => $discount_ids_in_requests,
        ])->keyBy('id');

        try {
            DB::transaction(function () use ($input, $existing_discounts) {
                foreach ($input['discounts'] as $discount) {
                    /** @var DiscountSettingService $discount_setting_service */
                    $discount_setting_service = resolve(DiscountSettingService::class);

                    $discount_setting_service
                        ->setDiscount($existing_discounts[$discount['discount_id']])
                        ->setEffectiveFromDate($discount['effective_from'])
                        ->setEffectiveToDate($discount['effective_to'])
                        ->setDescription($discount['description'] ?? null)
                        ->update();
                }
            });

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setMessage($e->getMessage())
                ->setCode(400)
                ->getResponse();
        }
    }

    public function show(DiscountSetting $discount_setting): JsonResponse
    {
        $discount_setting->load([
            'source' => function (MorphTo $morph_to) {
                $morph_to->morphWith([
                    ScholarshipAward::class => ['scholarship'],
                ]);
            },
            'userable' => function (MorphTo $morph_to) {
                $morph_to->morphWith([
                    Student::class => ['latestPrimaryClassAndGrade'],
                ]);
            }
        ]);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new DiscountSettingResource($discount_setting))
            ->getResponse();
    }
}
