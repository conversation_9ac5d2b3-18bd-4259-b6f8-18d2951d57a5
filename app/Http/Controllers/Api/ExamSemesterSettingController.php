<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ExamSemesterSetting\ExamSemesterSettingBulkCreateOrUpdateRequest;
use App\Http\Requests\Api\ExamSemesterSetting\ExamSemesterSettingIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ExamSemesterSettingResource;
use App\Services\ExamSemesterSettingService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class ExamSemesterSettingController extends Controller
{
    use HandlesPagination;

    protected ExamSemesterSettingService $examSemesterSettingService;

    public function __construct(ExamSemesterSettingService $exam_semester_setting_service)
    {
        $this->examSemesterSettingService = $exam_semester_setting_service;
    }

    public function index(ExamSemesterSettingIndexRequest $request): JsonResponse
    {
        $input = $request->validated();
        $input['includes'] = array_unique(array_merge($input['includes'] ?? [],
        ['grade', 'semesterSetting']
    ));

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input, 
            $this->examSemesterSettingService, 
            'getAllExamSemesterSettings', 
            'getAllPaginatedExamSemesterSettings');

        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(ExamSemesterSettingResource::collection($data))
            ->getResponse();
    }

    public function bulkCreateOrUpdate(ExamSemesterSettingBulkCreateOrUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->examSemesterSettingService->bulkCreateOrUpdateExamSemesterSettings($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
