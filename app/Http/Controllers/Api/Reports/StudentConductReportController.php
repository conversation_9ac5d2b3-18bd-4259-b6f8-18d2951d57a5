<?php

namespace App\Http\Controllers\Api\Reports;

use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\Conduct\StudentConductReportRequest;
use App\Http\Resources\ApiResponse;
use App\Services\StudentConductReportService;
use Illuminate\Http\JsonResponse;

class StudentConductReportController extends Controller
{
    protected StudentConductReportService $studentConductReportService;

    public function __construct(StudentConductReportService $student_conduct_report_service)
    {
        $this->studentConductReportService = $student_conduct_report_service;
    }
 
    public function studentConductReport(StudentConductReportRequest $request): JsonResponse
    {
        $filters = $request->validated();
        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('student-conduct-report');

        $response = $this->studentConductReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.conduct.student-conduct')
            ->setFileName($file_name)
            ->getConductReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }
}
