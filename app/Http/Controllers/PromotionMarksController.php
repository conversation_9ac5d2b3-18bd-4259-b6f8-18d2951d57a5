<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePromotionMarksRequest;
use App\Http\Requests\UpdatePromotionMarksRequest;
use App\Models\PromotionMarks;

class PromotionMarksController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePromotionMarksRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(PromotionMarks $promotionMarks)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PromotionMarks $promotionMarks)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePromotionMarksRequest $request, PromotionMarks $promotionMarks)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PromotionMarks $promotionMarks)
    {
        //
    }
}
