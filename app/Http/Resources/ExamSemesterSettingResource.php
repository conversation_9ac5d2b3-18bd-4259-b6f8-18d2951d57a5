<?php

namespace App\Http\Resources;

use App\Models\SemesterSetting;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExamSemesterSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'category' => $this->category,
            'year' => $this->year,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
            'grade' => new GradeResource($this->whenLoaded('grade')),
            'semester_setting' => new SemesterSettingResource($this->whenLoaded('semesterSetting'))
        ];
    }
}
