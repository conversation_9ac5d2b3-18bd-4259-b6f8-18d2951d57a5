<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PromotionMarkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'semester_class_id' => $this->semester_class_id,
            'net_average_for_promotion' => $this->net_average_for_promotion,
            'conduct_mark_for_promotion' => $this->conduct_mark_for_promotion,
        ];
    }
}
