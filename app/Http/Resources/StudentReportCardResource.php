<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentReportCardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'is_visible_to_student' => $this->is_visible_to_student,
            'file_url' => $this->file_url,
            'semester_setting' => new SemesterSettingResource($this->whenLoaded('semesterSetting'))
        ];
    }
}
