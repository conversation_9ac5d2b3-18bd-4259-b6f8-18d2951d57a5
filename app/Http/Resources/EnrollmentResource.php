<?php

namespace App\Http\Resources;

use App\Models\Config;
use App\Services\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnrollmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $config_service = resolve(ConfigService::class);

        $payable_fees = $config_service->getConfigValue(Config::ENROLLMENT_FEES);

        return [
            'id' => $this->id,
            'admission_year' => $this->admission_year,
            'admission_grade' => new GradeResource($this->admissionGrade),
            'student_name' => $this->student_name,
            'nric_no' => $this->nric_no,
            'passport_no' => $this->passport_no,
            'birthplace' => new CountryResource($this->birthplace),
            'nationality' => new CountryResource($this->nationality),
            'date_of_birth' => (string)$this->date_of_birth ?: null,
            'gender' => $this->gender,
            'birth_cert_no' => $this->birth_cert_no,
            'race' => new RaceResource($this->race),
            'religion' => new ReligionResource($this->religion),
            'phone_no' => $this->phone_no,
            'email' => $this->email,
            'address' => $this->address,
            'postal_code' => $this->postal_code,
            'city' => $this->city,
            'state' => new StateResource($this->state),
            'country' => new CountryResource($this->country),
            'remarks' => $this->remarks,
            'status' => $this->status,
            'step' => $this->step,
            'created_by' => new UserResource($this->createdBy),
            'guardians' => $this->guardians ? GuardianResource::collection($this->guardians) : null,
            'nric_files' => $this->nric_files,
            'passport_files' => $this->passport_files,
            'payable_fees' => (float)$payable_fees,
            'translations' => $this->translations,
        ];
    }
}
