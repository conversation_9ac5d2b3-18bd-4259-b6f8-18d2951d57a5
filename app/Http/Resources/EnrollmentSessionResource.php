<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnrollmentSessionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
            'code' => $this->code,
            'is_active' => $this->is_active,
            'fee_assignment_settings' => $this->fee_assignment_settings,
            'course' => $this->whenLoaded('course', function () {
                return new CourseResource($this->course);
            }),
            'exam_subjects' => $this->whenLoaded('examSubjects', function () {
                return SimpleSubjectResource::collection($this->examSubjects);
            }),
        ];
    }
}
