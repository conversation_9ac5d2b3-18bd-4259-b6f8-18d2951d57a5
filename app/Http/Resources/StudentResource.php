<?php

namespace App\Http\Resources;

use App\Models\StudentGradingFramework;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => $this->whenLoaded('user', function () {
                return new UserResource($this->user);
            }),
            'name' => $this->name,
            'phone_number' => $this->phone_number,
            'phone_number_2' => $this->phone_number_2,
            'email' => $this->email,
            'nric' => $this->nric,
            'passport_number' => $this->passport_number,
            'admission_year' => $this->admission_year,
            'admission_grade' => $this->whenLoaded('admissionGrade', function () {
                return new GradeResource($this->admissionGrade);
            }),
            'report_cards' => $this->whenLoaded('reportCards', function (){
                return StudentReportCardResource::collection($this->reportCards);
            }),
            'active_grading_framework' => $this->whenLoaded( 'activeGradingFramework' , function (){
                return StudentGradingFramework::resource($this->activeGradingFramework);
            }),
            'join_date' => $this->join_date,
            'leave_date' => $this->leave_date,
            'leave_status' => $this->leave_status,
            'student_number' => $this->student_number,
            'birthplace' => $this->birthplace,
            'nationality' => $this->whenLoaded('nationality', function () {
                return new CountryResource($this->nationality);
            }),
            'date_of_birth' => $this->date_of_birth,
            'gender' => $this->gender,
            'birth_cert_number' => $this->birth_cert_number,
            'race' => $this->whenLoaded('race', function () {
                return new RaceResource($this->race);
            }),
            'religion' => $this->whenLoaded('religion', function () {
                return new ReligionResource($this->religion);
            }),
            'address' => $this->address,
            'address_2' => $this->address_2,
            'postal_code' => $this->postal_code,
            'city' => $this->city,
            'state' => $this->whenLoaded('state', function () {
                return new StateResource($this->state);
            }),
            'country' => $this->whenLoaded('country', function () {
                return new CountryResource($this->country);
            }),
            'remarks' => $this->remarks,
            'is_active' => $this->is_active,
            'is_hostel' => $this->is_hostel,
            'custom_field' => $this->custom_field,
            'translations' => $this->translations,
            'photo' => $this->photo,
            'dietary_restriction' => $this->dietary_restriction,
            'guardians' => $this->whenLoaded('guardians', function () {
                return GuardianResource::collection($this->guardians);
            }),
            'active_hostel_bed_assignment' => $this->whenLoaded('activeHostelBedAssignments', function () {
                return new HostelBedAssignmentResource($this->activeHostelBedAssignments->first());
            }),
            'hostel_reward_punishment_points' => $this->when($request->get('fields') != null && in_array('hostel_reward_punishment_points', $request->get('fields')), function () {
                return $this->getHostelRewardPunishmentRecordsPoints();
            }),
            'leadership_position' => $this->whenLoaded('leadershipPositionRecord', function () {
                return new LeadershipPositionResource($this->leadershipPositionRecord->leadershipPosition);
            }),
            'current_primary_class' => $this->whenLoaded('currentSemesterPrimaryClass', function () {
                return new StudentClassResource($this->currentSemesterPrimaryClass);
            }),
            'health_concern' => $this->whenLoaded('healthConcern', function () {
                return new HealthConcernResource($this->healthConcern);
            }),
            'primary_school' => $this->whenLoaded('primarySchool', function () {
                return new SchoolResource($this->primarySchool);
            }),
            'society_positions' => $this->whenLoaded('societyPositions', function () use ($request) {
                $request = $request->all();

                if (isset($request['semester_class_id'])) {
                    $positions = $this->societyPositions->where('semester_class_id', $request['semester_class_id']);
                } else {
                    $positions = $this->societyPositions;
                }

                return StudentSocietyPositionResource::collection($positions);
            }),
            'admission_type' => $this->admission_type,
            'selected_semester_class' => $this->when(request()->input('semester_setting_id'), function () {
                $class = $this->latestPrimaryClassBySemesterSettings
                    ->where('semester_setting_id', request()->input('semester_setting_id'))
                    ->first();

                return isset($class->semesterClass) ? new SemesterClassResource($class->semesterClass) : null;
            }),
            'attendances' => $this->whenLoaded('attendances', function () {
                return AttendanceResource::collection($this->attendances);
            }),
            'historical_classes' => $this->whenLoaded('classAndGradeBySemesterSetting', function() {
                return ClassAndGradeResource::collection($this->classAndGradeBySemesterSetting);
            })
        ];
    }
}
