<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('period_labels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('period_group_id')->index();
            $table->unsignedInteger('period');
            $table->jsonb('name');
            $table->boolean('is_attendance_required')->default(true)->index();
            $table->boolean('can_apply_leave')->default(true)->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('period_labels');
    }
};
