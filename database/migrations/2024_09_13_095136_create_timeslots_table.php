<?php

use App\Enums\PeriodAttendanceStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('timeslots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('timetable_id')->index();
            $table->string('day')->index();
            $table->foreignId('period_id')->index();
            $table->foreignId('class_subject_id')->nullable()->index();
            $table->string('placeholder')->nullable();
            $table->time('attendance_from');
            $table->time('attendance_to');
            $table->string('default_init_status')->default(PeriodAttendanceStatus::ABSENT->value);
            $table->boolean('has_mark_deduction')->default(true);
            $table->timestamps();

            $table->index(['timetable_id', 'day'], 'idx_timeslots_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('timeslots');
    }
};
