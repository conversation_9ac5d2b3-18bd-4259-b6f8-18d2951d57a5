<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE MATERIALIZED VIEW exam_posting_pre_checks AS
                SELECT semester_classes.semester_setting_id AS semester_setting_id, rs.code AS result_source_code, subjects.type AS subject_type, subjects.code AS subject_code, subjects.id AS subject_id, subjects.name AS subject_name,
                matview.student_name, matview.student_number, matview.student_id, matview.class_code, matview.class_name, matview.class_id, matview.class_type, matview.grade_name, matview.grade_id, rss.grading_type,
                rssc.name AS result_source_subject_component_name, semester_classes.id AS semester_class_id,
                CASE
                    WHEN rss.grading_type = 'SCORE' THEN rssc.actual_score::varchar(6)
                    ELSE rss.actual_score_grade
                END AS input_value,
                CASE
                    WHEN rss.grading_type = 'SCORE' AND (rssc.actual_score is not null OR rss.is_exempted is true) THEN 1
                    WHEN rss.grading_type = 'GRADE' AND (rss.actual_score_grade is not null OR rss.is_exempted is true) THEN 1
                    WHEN rss.grading_type = 'SCORE' AND (rssc.actual_score is null AND rss.is_exempted is false) THEN 0
                    WHEN rss.grading_type = 'GRADE' AND (rss.actual_score_grade is null AND rss.is_exempted is false) THEN 0
                    ELSE 0
                END AS input_value_status
                , cst.employee_names AS teacher_names, cst.employee_ids AS teacher_ids, rssc.id AS result_source_subject_component_id, rs.id AS result_source_id, rss.id AS result_source_subject_id,
                CASE
                    WHEN rss.is_exempted is true THEN 1
                    ELSE 0
                END AS is_exempted
                FROM result_source_subjects rss
                LEFT JOIN result_source_subject_components rssc ON rss.id = rssc.result_source_subject_id
                INNER JOIN result_sources rs ON rs.id = rss.result_source_id
                INNER JOIN student_grading_frameworks sgf ON sgf.id = rs.student_grading_framework_id
                INNER JOIN subjects ON subjects.id = rss.subject_id
                INNER JOIN class_subject_student css ON css.student_id = sgf.student_id
                INNER JOIN class_subjects ON css.class_subject_id = class_subjects.id AND class_subjects.subject_id = rss.subject_id
                LEFT JOIN (
                    SELECT class_subject_id, jsonb_agg(employees.name) AS employee_names, string_agg(employees.id::varchar(16), ',') AS employee_ids FROM class_subject_teacher
                    INNER JOIN employees ON employees.id = class_subject_teacher.employee_id
                    GROUP BY class_subject_id
                ) cst ON cst.class_subject_id = class_subjects.id
                INNER JOIN semester_classes ON semester_classes.id = class_subjects.semester_class_id
                INNER JOIN historical_students_class_and_grade_view matview ON matview.student_id = sgf.student_id AND matview.class_id = semester_classes.class_id AND matview.semester_setting_id = semester_classes.semester_setting_id
                WHERE sgf.is_active = true
        ");

        // potential optimization is to exclude semester_setting_id that are older than 3 semesters since they won't be valid anymore for the current student grading framework.

        DatabaseHelper::createUniqueIndex('exam_posting_pre_checks', 'idx_eppc_unique_1', ['semester_setting_id', 'result_source_code', 'subject_id', 'student_id', 'result_source_subject_component_id', 'semester_class_id']);
        DatabaseHelper::createIndex('exam_posting_pre_checks', 'idx_eppc_1', ['semester_setting_id', 'result_source_code']);
        DatabaseHelper::createIndex('exam_posting_pre_checks', 'idx_eppc_2', ['semester_setting_id', 'result_source_code', 'class_id', 'subject_id']);
        DatabaseHelper::createIndex('exam_posting_pre_checks', 'idx_eppc_3', ['semester_setting_id', 'result_source_code', 'class_id', 'subject_id', 'student_id']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS exam_posting_pre_checks;");
    }
};
