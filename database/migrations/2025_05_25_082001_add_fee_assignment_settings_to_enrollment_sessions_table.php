<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('enrollment_sessions', function (Blueprint $table) {
            $table->jsonb('fee_assignment_settings')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('enrollment_sessions', function (Blueprint $table) {
            $table->dropColumn('fee_assignment_settings');
        });
    }
};
