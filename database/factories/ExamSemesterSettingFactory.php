<?php

namespace Database\Factories;

use App\Enums\ExamSemesterSettingCategory;
use App\Models\Grade;
use App\Models\SemesterSetting;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExamSemesterSetting>
 */
class ExamSemesterSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category' => ExamSemesterSettingCategory::ATTENDANCE->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01'),
            'to_date' => Carbon::parse('2025-04-30'),
            'grade_id' => Grade::factory(),
            'semester_setting_id' => SemesterSetting::factory()
        ];
    }
}
