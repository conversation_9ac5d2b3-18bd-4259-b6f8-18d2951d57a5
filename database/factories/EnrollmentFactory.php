<?php

namespace Database\Factories;

use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Models\Country;
use App\Models\Enrollment;
use App\Models\Grade;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Enrollment>
 */
class EnrollmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'admission_year' => rand(2000, 2030),
            'admission_grade_id' => Grade::factory(), // key to MasterGrade
            'student_name->en' => fake()->name(),
            'student_name->zh' => fake('zh_CN')->name(),
            'nric_no' => fake()->uuid(),
            'passport_no' => fake()->uuid(),
            'birthplace_id' => Country::factory(), // key to MasterCountry
            'nationality_id' => Country::factory(), // key to MasterCountry
            'date_of_birth' => now()->subYears(20)->toDateString(),
            'gender' => Gender::MALE->value,
            'birth_cert_no' => fake()->uuid(),
            'race_id' => Race::factory(), // key to MasterRace
            'religion_id' => Religion::factory(), // key to MasterReligion
            'phone_no' => fake()->phoneNumber(),
            'email' => fake()->email(),
            'address' => fake()->address(),
            'postal_code' => fake()->postcode(),
            'city' => fake()->city(),
            'state_id' => State::factory(),
            'country_id' => Country::factory(),
            'remarks' => null,
            'status' =>  EnrollmentStatus::DRAFT->value,
            'step' => Enrollment::STEP_TERMS_AND_CONDITIONS,
            'created_by' => User::factory(), // key to User
        ];
    }
}
