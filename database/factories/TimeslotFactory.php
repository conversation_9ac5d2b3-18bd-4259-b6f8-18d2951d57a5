<?php

namespace Database\Factories;

use App\Enums\Day;
use App\Enums\PeriodAttendanceStatus;
use App\Models\ClassSubject;
use App\Models\Period;
use App\Models\Timetable;
use Illuminate\Database\Eloquent\Factories\Factory;

class TimeslotFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'timetable_id' => Timetable::factory(),
            'day' => Day::MONDAY,
            'period_id' => Period::factory(),
            'class_subject_id' => ClassSubject::factory(),
            'placeholder' => fake()->text,
            'attendance_from' => '18:00:00',
            'attendance_to' => '19:00:00',
            'default_init_status' => PeriodAttendanceStatus::ABSENT,
            'has_mark_deduction' => true,
        ];
    }
}
