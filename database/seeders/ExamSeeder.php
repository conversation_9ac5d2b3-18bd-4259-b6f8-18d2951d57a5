<?php

namespace Database\Seeders;

use App\Enums\GradingSchemeType;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use Illuminate\Database\Seeder;

class ExamSeeder extends Seeder
{
    public function run(): void
    {
        $exams = [
            [
                'code' => '2025S1FE',
                'name' => [
                    'en' => '2025 First Semester Final-term Exam',
                    'zh' => '2025 第一学期期末考',
                ],
                'description' => '2025 第一学期期末考',
                'start_date' => '2025-04-01',
                'end_date' => '2025-05-01',
                'results_entry_period_from' => '2025-04-01',
                'results_entry_period_to' => '2025-05-01',
                'created_at' => now(),
            ],
            [
                'code' => '2025S1TOTAL',
                'name' => [
                    'en' => '2025 First Semester Total score',
                    'zh' => '2025 第一学期总分',
                ],
                'description' => '2025 第一学期总分 - Total score',
                'start_date' => '2025-04-01',
                'end_date' => '2025-05-01',
                'results_entry_period_from' => '2025-04-01',
                'results_entry_period_to' => '2025-06-01',
                'created_at' => now(),
            ],
            [
                'code' => '2025S2MTE',
                'name' => [
                    'en' => '2025 Second Semester Mid-term Exam',
                    'zh' => '2025 第二学期期中考',
                ],
                'description' => '2025 第二学期期中考',
                'start_date' => '2025-07-01',
                'end_date' => '2025-08-01',
                'results_entry_period_from' => '2025-07-01',
                'results_entry_period_to' => '2025-08-01',
                'created_at' => now(),
            ],
            [
                'code' => '2025S2FE',
                'name' => [
                    'en' => '2025 Second Semester Final-term Exam',
                    'zh' => '2025 第二学期期末考',
                ],
                'description' => '2025 第二学期期末考',
                'start_date' => '2025-10-01',
                'end_date' => '2025-11-01',
                'results_entry_period_from' => '2025-10-01',
                'results_entry_period_to' => '2025-11-01',
                'created_at' => now(),
            ],
            [
                'code' => '2025S2TOTAL',
                'name' => [
                    'en' => '2025 Second Semester Total score',
                    'zh' => '2025 第二学期总分',
                ],
                'description' => '2025 第二学期总分 - Total score',
                'start_date' => '2025-10-01',
                'end_date' => '2025-11-01',
                'results_entry_period_from' => '2025-10-01',
                'results_entry_period_to' => '2025-11-01',
                'created_at' => now(),
            ],
        ];

        foreach ( $exams as $exam ) {

            Exam::updateOrCreate([
                'code' => $exam['code']
            ], $exam);

        }

    }
}
