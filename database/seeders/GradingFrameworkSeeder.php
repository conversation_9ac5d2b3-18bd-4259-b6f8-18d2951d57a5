<?php

namespace Database\Seeders;

error_reporting(E_ALL);
ini_set('display_errors','On');
use App\Enums\ClassStream;
use App\Models\GradingFramework;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\StudentGradingFramework;
use App\Services\Exam\GradingFrameworkService;
use App\Services\StudentService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GradingFrameworkSeeder extends Seeder
{
    private int $junior_processed;
    private int $senior_processed;


    public function run(): void
    {
        $this->junior_processed = 0;
        $this->senior_processed = 0;

        DB::transaction(function() {
            $this->junior();
        });

        DB::transaction(function() {
            $this->senior();
        });

        echo "Processed Junior Students: ".$this->junior_processed."\n";
        echo "Processed Senior Students: ".$this->senior_processed."\n";

        $total = $this->junior_processed + $this->senior_processed;
        echo "Total Processed Students: ".$total."\n";
    }

    public function init_class_data(){
        $primary_semester_classes = SemesterClass::where('semester_setting_id', 16)
            ->whereRelation('classModel', 'type', 'PRIMARY')
            ->with(['students', 'classSubjects'])
            ->get();

        foreach ($primary_semester_classes as $primary_semester_class){
            echo 'Running for primary class: '. $primary_semester_class->classModel->code .  "\n";
            $student_ids = $primary_semester_class->students->pluck('id');

            $class_subjects = $primary_semester_class->classSubjects;

            foreach($class_subjects as $class_subject){
                $class_subject->students()->sync($student_ids);
            }
        }
    }

    public function junior() {

        echo "Applying Junior Grading Framework..\n";
        $junior_grading_framework_json = file_get_contents(storage_path('app/migration-json/junior-grading-framework-2025.json'));
        $junior_grading_framework_json = json_decode($junior_grading_framework_json, true);

        $grading_framework = GradingFramework::updateOrCreate(
            [
                'code' => 'JUNIOR',
            ],
            [
                'name' => [
                    'en' => 'Junior Grading Framework',
                    'zh' => '初中评分结构'
                ],
                'is_active' => true,
                'configuration' => $junior_grading_framework_json,
            ]
        );

        // apply grading framework to students
        $sem12025 = SemesterSetting::where('name', '2025 Sem 1')->first();

        $students = app(StudentService::class)->getAllStudents([
            'semester_setting_id' => $sem12025->id,
            'grade_ids' => [1,2,3],     // ID 1,2,3 refers to J1, J2, J3 in master_grades table
        ]);

        foreach ( $students as $student ) {
                echo "Running for student " . $student->student_number . "\n";
                if ( StudentGradingFramework::where('student_id', $student->id)->exists() ){
                    echo "SGF for student exists, skip.\n";
                    $this->junior_processed += 1;
                    return;
                }

                app(GradingFrameworkService::class)->applyStudentGradingFramework([
                    'student_id' => $student->id,
                    'grading_framework_id' => $grading_framework->id,
                    'effective_from' => '2025-01-01',
                    'effective_to' => '2025-12-31',
                ]);
                $this->junior_processed += 1;
        }

    }


    public function senior() {

        echo "Applying Senior Grading Framework..\n";
        $senior_grading_framework_json = file_get_contents(storage_path('app/migration-json/senior-grading-framework-2025.json'));
        $senior_grading_framework_json = json_decode($senior_grading_framework_json, true);

        $grading_framework = GradingFramework::updateOrCreate(
            [
                'code' => 'SENIOR',
            ],
            [
                'name' => [
                    'en' => 'Senior Grading Framework',
                    'zh' => '初中评分结构'
                ],
                'is_active' => true,
                'configuration' => $senior_grading_framework_json,
            ]
        );

        // apply grading framework to students
        $sem12025 = SemesterSetting::where('name', '2025 Sem 1')->first();

        $students = app(StudentService::class)->getAllStudents([
            'semester_setting_id' => $sem12025->id,
            'grade_ids' => [5, 6, 7],     // ID 1,2,3 refers to J1, J2, J3 in master_grades table
        ]);

        foreach ( $students as $student ) {
            echo "Running for student " . $student->student_number . "\n";
            if ( StudentGradingFramework::where(['student_id' => $student->id, "is_active" => true])->exists() ){
                echo "SGF for student exists, skip.\n";
                $this->senior_processed += 1;
                return;
            }

            app(GradingFrameworkService::class)->applyStudentGradingFramework([
                'student_id' => $student->id,
                'grading_framework_id' => $grading_framework->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-12-31',
            ]);
            $this->senior_processed += 1;
        }
    }
}
