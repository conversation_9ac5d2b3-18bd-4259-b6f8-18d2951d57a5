<?php

namespace Database\Seeders;

error_reporting(E_ALL);
ini_set('display_errors','On');
use App\Models\GradingFramework;
use App\Models\SemesterSetting;
use App\Models\StudentGradingFramework;
use App\Services\Exam\GradingFrameworkService;
use App\Services\StudentService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JuniorGradingFrameworkSeeder extends Seeder
{
    private int $processed;


    public function run(): void
    {
        $this->processed = 0;

        DB::transaction(function() {
            $this->junior(1, 'J1');
            $this->junior(2, 'J2');
            $this->junior(3, 'J3');
        });

        echo "Total Processed Students: ".$this->processed."\n";
    }

    public function junior($grade_id, $grade_code) {

        echo "Applying Grading Framework for ".$grade_code ."...\n";
        $grading_framework_json = file_get_contents(storage_path('app/migration-json/'.$grade_code.'-grading-framework-2025.json'));
        $grading_framework_json = json_decode($grading_framework_json, true);

        $grading_framework = GradingFramework::updateOrCreate(
            [
                'code' => $grading_framework_json['grading_framework_code'],
            ],
            [
                'name' => [
                    'en' => $grade_code. ' Grading Framework 2025',
                    'zh' => $grade_code. ' 初中评分结构 2025' 
                ],
                'is_active' => true,
                'configuration' => $grading_framework_json,
            ]
        );

        // apply grading framework to students
        $sem12025 = SemesterSetting::where('name', '2025 Sem 1')->first();

        $students = app(StudentService::class)->getAllStudents([
            'semester_setting_id' => $sem12025->id,
            'grade_id' => $grade_id,    
        ]);

        foreach ( $students as $student ) {
                echo "Running for student " . $student->student_number . "\n";
                if ( StudentGradingFramework::where('student_id', $student->id)->exists() ){
                    echo "SGF for student exists, skip.\n";
                    $this->processed += 1;
                    continue;
                }

                app(GradingFrameworkService::class)->applyStudentGradingFramework([
                    'student_id' => $student->id,
                    'grading_framework_id' => $grading_framework->id,
                    'effective_from' => '2025-01-01',
                    'effective_to' => '2025-12-31',
                    'academic_year' => '2025',
                ]);
                $this->processed += 1;
        }

    }
}
