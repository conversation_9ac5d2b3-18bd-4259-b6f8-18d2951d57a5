<?php

namespace Database\Seeders;

use App\Enums\GradingSchemeType;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use Illuminate\Database\Seeder;

class GradingSchemeSeeder extends Seeder
{
    public function run(): void
    {

        $grading_scheme = GradingScheme::firstOrCreate(
            [
                'code' =>' DEFAULT',
            ],
            [
                'type' => GradingSchemeType::EXAM->value,
                'code' => 'DEFAULT',
                'name' => 'Default Exam Grading Scheme',
                'is_active' => true,
            ]
        );

        $grading_scheme_items = [
            [
                'grading_scheme_id' => $grading_scheme->id,
                'name' => 'A',
                'display_as_name' => 'A',
                'from' => 80,
                'to' => 100,
                'extra_marks' => 0,
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ],
            [
                'grading_scheme_id' => $grading_scheme->id,
                'name' => 'B',
                'display_as_name' => 'B',
                'from' => 70,
                'to' => 79.99,
                'extra_marks' => 0,
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ],
            [
                'grading_scheme_id' => $grading_scheme->id,
                'name' => 'C',
                'display_as_name' => 'C',
                'from' => 60,
                'to' => 69.99,
                'extra_marks' => 0,
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ],
            [
                'grading_scheme_id' => $grading_scheme->id,
                'name' => 'D',
                'display_as_name' => 'D',
                'from' => 35,
                'to' => 59.99,
                'extra_marks' => 0,
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ],
            [
                'grading_scheme_id' => $grading_scheme->id,
                'name' => 'E',
                'display_as_name' => 'E',
                'from' => 0,
                'to' => 34.99,
                'extra_marks' => 0,
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ]
        ];

        GradingSchemeItem::insert($grading_scheme_items);
    }

}
