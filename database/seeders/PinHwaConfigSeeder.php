<?php

namespace Database\Seeders;

use App\Helpers\ConfigHelper;
use App\Models\BankAccount;
use App\Models\Config;
use App\Models\LegalEntity;
use Illuminate\Database\Seeder;

class PinHwaConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $legal_entity = LegalEntity::where('code', LegalEntity::CODE_PINHWA)->first();

        $bank_account = BankAccount::where('bankable_id', $legal_entity->id)
            ->where('bankable_type', LegalEntity::class)
            ->firstOrFail();

        $configs = [
            Config::CATEGORY_GENERAL => [
                Config::ENROLLMENT_DOCUMENT_TYPE => ['file_upsr_result'],
                Config::ENROLLMENT_FEES => 20,
                Config::WALLET_TRANSFERABLE_MODEL => [
                    'guardian' => ['student'],
                    'employee' => ['student'],
                    'student' => ['student']
                ],
                Config::WALLET_DEPOSIT_MIN_AMOUNT => 60,
                Config::WALLET_DEPOSIT_MAX_AMOUNT => 300,
            ],
            Config::CATEGORY_CANTEEN => [
                Config::CANTEEN_OPENING_TIME => '10:01',
                Config::CANTEEN_CUTOFF_DAY => 'FRIDAY',
                Config::CANTEEN_CUTOFF_TIME => '23:59',
                Config::CANTEEN_NOTIFICATION_TIME => '09:00',
                Config::CANTEEN_OPENING_DAY => 'MONDAY',
                Config::CANTEEN_NOTIFICATION_DAY => 'SATURDAY',
                Config::CANTEEN_OPEN_STATUS => 1,
            ],
            Config::CATEGORY_LIBRARY => [
                Config::LIBRARY_FINE_PER_DAY_OTHER => 0.2,
                Config::LIBRARY_BORROW_LIMIT_OTHER => 4
            ],
            Config::CATEGORY_BANK_ACCOUNT => [
                Config::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT => $bank_account->id
            ]
        ];

        foreach ($configs as $category => $config) {
            foreach ($config as $configKey => $configValue) {
                ConfigHelper::put($configKey, $configValue, $category);
            }
        }
    }
}
