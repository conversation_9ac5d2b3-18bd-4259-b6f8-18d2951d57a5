81a5ad5487c6aeba5d41f5761c740daf87e4da44 59f94863608ae8c56fb4ea34ff58fcc14870d76e Sim Zhen <PERSON>uan <<EMAIL>> 1738851920 +0800	pull: Fast-forward
59f94863608ae8c56fb4ea34ff58fcc14870d76e 6de66fbb0fb22e7cf136e960dacfa15d83b95e36 Sim Zhen Quan <<EMAIL>> 1738854506 +0800	commit: Deployed to PRD
6de66fbb0fb22e7cf136e960dacfa15d83b95e36 668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b Sim Zhen Quan <<EMAIL>> 1738923387 +0800	pull: Fast-forward
668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b 42beb70dcab1c3f81e7d190a3fc9818f31e752b7 Sim Zhen <PERSON>uan <<EMAIL>> 1739115245 +0800	pull: Fast-forward
42beb70dcab1c3f81e7d190a3fc9818f31e752b7 08147cdf3bd0896b9cc2135699f8b143ce44a892 Sim Zhen Quan <<EMAIL>> 1739119549 +0800	commit: Deployed to PRD
08147cdf3bd0896b9cc2135699f8b143ce44a892 ce82e2f2c55583f5b97ce3472ea20e61b2af5be2 Sim Zhen Quan <<EMAIL>> 1739157943 +0800	pull: Fast-forward
ce82e2f2c55583f5b97ce3472ea20e61b2af5be2 537e0ee93b0f8cedbc07b84cbb286b4b0d689824 Sim Zhen Quan <<EMAIL>> 1739157943 +0800	merge migration-hostel-reward-punishment: Merge made by the 'ort' strategy.
537e0ee93b0f8cedbc07b84cbb286b4b0d689824 4911b2f54d55a62c5043b5e09a65075ccb0f6598 Sim Zhen Quan <<EMAIL>> 1739183718 +0800	pull: Fast-forward
4911b2f54d55a62c5043b5e09a65075ccb0f6598 9d6948759a235b944facf7316f6b3fcd285ecb07 Sim Zhen Quan <<EMAIL>> 1739198111 +0800	pull: Fast-forward
9d6948759a235b944facf7316f6b3fcd285ecb07 cbe645208a66ec0211ba5c3651ee6b29cfe2de5d Sim Zhen Quan <<EMAIL>> 1739326032 +0800	pull: Fast-forward
cbe645208a66ec0211ba5c3651ee6b29cfe2de5d 80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 Sim Zhen Quan <<EMAIL>> 1739326037 +0800	commit: Deployed to PRD
80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739376290 +0800	pull: Fast-forward
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 Sim Zhen Quan <<EMAIL>> 1739445098 +0800	commit: Set Payex to single attempt only (No retries)
3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 2a0f44e4869dc12422709515d943299ca2985f90 Sim Zhen Quan <<EMAIL>> 1739461875 +0800	merge staging/2025-02-13: Fast-forward
2a0f44e4869dc12422709515d943299ca2985f90 ab349ed05aa33a248bbfa1d3e3a37459ed7b7990 Sim Zhen Quan <<EMAIL>> 1739461996 +0800	reset: moving to ab349ed
ab349ed05aa33a248bbfa1d3e3a37459ed7b7990 1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 Sim Zhen Quan <<EMAIL>> 1739462029 +0800	merge staging/2025-02-13: Merge made by the 'ort' strategy.
1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 586f1ab762b8839941095bce828f02fcb42e8a47 Sim Zhen Quan <<EMAIL>> 1739463882 +0800	pull: Fast-forward
586f1ab762b8839941095bce828f02fcb42e8a47 4b6841f96df0160e7616aada954894819fa10073 Sim Zhen Quan <<EMAIL>> 1739464705 +0800	commit: Deployed to PRD
4b6841f96df0160e7616aada954894819fa10073 bcc5e5b2becc6ebc51aef1a9ad8893826049a610 Sim Zhen Quan <<EMAIL>> 1739500129 +0800	commit: Increase max length for guardian occupation description to 200 characters
bcc5e5b2becc6ebc51aef1a9ad8893826049a610 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739522355 +0800	pull: Fast-forward
9e0dfb5c4f59059772e78526a2c13981ee853794 fddc2d8f2da766d2ad7aa2c3627d5da027d5acda Sim Zhen Quan <<EMAIL>> 1739547235 +0800	pull: Fast-forward
fddc2d8f2da766d2ad7aa2c3627d5da027d5acda 7e6e6813adf38bd4247159b94505a1dd4d6fc3fd Sim Zhen Quan <<EMAIL>> 1739547353 +0800	pull: Fast-forward
7e6e6813adf38bd4247159b94505a1dd4d6fc3fd 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739548695 +0800	commit: Deployed to PRD
551e6bedf269d8ebbe7af4f945756800987440a1 5d53150448e759fd1d0513eab998951e6602b050 Sim Zhen Quan <<EMAIL>> 1739721300 +0800	commit: Update rate limiter and update Payex cronjob check to process data within last 2hour - last 1hour only
5d53150448e759fd1d0513eab998951e6602b050 49718ae7191a192d7217d8825d1fccdabae9fc4c Sim Zhen Quan <<EMAIL>> 1739721311 +0800	cherry-pick: Set Payex payment intent to expire in 5minutes and sort canteen report
49718ae7191a192d7217d8825d1fccdabae9fc4c 51c38d7d5c0e675538f3a7adac60153bee1877e0 Sim Zhen Quan <<EMAIL>> 1739721710 +0800	commit: Deployed to PRD
51c38d7d5c0e675538f3a7adac60153bee1877e0 ae5527b8765f27943dda1e515546872890686611 Sim Zhen Quan <<EMAIL>> 1739755839 +0800	commit: Hotfix number_format issue
ae5527b8765f27943dda1e515546872890686611 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> 1739779635 +0800	pull: Fast-forward
9cc37e0ed353656a6218ef977789232f4cca370f 8087ad45ec0fb1af1580d189d94a9888badeb571 Sim Zhen Quan <<EMAIL>> 1739930855 +0800	commit: Fix resource nullable issue
8087ad45ec0fb1af1580d189d94a9888badeb571 4ba1cca35b436bbc376c4c273827730ab95350ac Sim Zhen Quan <<EMAIL>> 1739930870 +0800	merge origin/main: Merge made by the 'ort' strategy.
4ba1cca35b436bbc376c4c273827730ab95350ac a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1739932591 +0800	commit: Fix validation
a6c6a5a0d3699bea541929f19b084866eea40f6c aadd544c66bf3fbaf7576c81f1e6c3046596de30 Sim Zhen Quan <<EMAIL>> 1740373930 +0800	pull: Fast-forward
aadd544c66bf3fbaf7576c81f1e6c3046596de30 ba9f430ab636520e55497cb82c9e63458cf05556 Sim Zhen Quan <<EMAIL>> 1740373966 +0800	commit: Disable media conversion on testing env
ba9f430ab636520e55497cb82c9e63458cf05556 e70b3961e27b0cf52e75685a1345631b60a55eec Sim Zhen Quan <<EMAIL>> 1740392406 +0800	commit: Test case fixes
e70b3961e27b0cf52e75685a1345631b60a55eec 1e501ceac7a739cc8b80bf5e62517e890af6222c Sim Zhen Quan <<EMAIL>> 1740409421 +0800	commit: Fix weekly canteen report to show next week data
1e501ceac7a739cc8b80bf5e62517e890af6222c 8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 Sim Zhen Quan <<EMAIL>> 1740409465 +0800	merge origin/main: Merge made by the 'ort' strategy.
8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 f666563d28af33777f02028b28e0663dc966a467 Sim Zhen Quan <<EMAIL>> 1740411135 +0800	pull: Fast-forward
f666563d28af33777f02028b28e0663dc966a467 87ebb989d28ca7e96a6230852b9469af08004ab6 Sim Zhen Quan <<EMAIL>> 1740411480 +0800	commit: Test case fix
87ebb989d28ca7e96a6230852b9469af08004ab6 67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 Sim Zhen Quan <<EMAIL>> 1740412871 +0800	commit: Deployed to PRD
67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 fd59fdd9eb8d7bef3a6de59623750ec84529249c Sim Zhen Quan <<EMAIL>> 1740505440 +0800	merge grading-framework-crud: Fast-forward
fd59fdd9eb8d7bef3a6de59623750ec84529249c c09093f5cac1dfd8a9368d5ca8e3397708ee399f Sim Zhen Quan <<EMAIL>> 1740590163 +0800	pull: Fast-forward
c09093f5cac1dfd8a9368d5ca8e3397708ee399f 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740617376 +0800	commit: Deployed to PRD
4e724d4ad5555642e6c25238d8757780cb167c37 5321655a46edd4a6d0f0acbaac8987bb1d19724c Sim Zhen Quan <<EMAIL>> 1740632035 +0800	pull: Fast-forward
5321655a46edd4a6d0f0acbaac8987bb1d19724c d3dded91b9db442d6810e1a35a7d43538f1b6849 Sim Zhen Quan <<EMAIL>> 1740632069 +0800	commit: Enhancements
d3dded91b9db442d6810e1a35a7d43538f1b6849 b1e9a0b4ab315972bfaae2437fd675d0513c68d2 Sim Zhen Quan <<EMAIL>> 1740732488 +0800	pull: Fast-forward
b1e9a0b4ab315972bfaae2437fd675d0513c68d2 8cef72c02e0852f133fe7b2d492848a1062d1ddd Sim Zhen Quan <<EMAIL>> 1740756985 +0800	pull: Fast-forward
8cef72c02e0852f133fe7b2d492848a1062d1ddd 1c2976450b04d250575cc6a52f932084f98ebc64 Sim Zhen Quan <<EMAIL>> 1740962926 +0800	commit: Hotfix announcement to send out encoded html instead
1c2976450b04d250575cc6a52f932084f98ebc64 ed0e49d4d6081e5ae9e48e0c0e01773345914e84 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
ed0e49d4d6081e5ae9e48e0c0e01773345914e84 86888a9a93b1978aec5f3c89e7763fec13285697 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
86888a9a93b1978aec5f3c89e7763fec13285697 1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to PRD
1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d Sim Zhen Quan <<EMAIL>> ********** +0800	merge fix/accounting-flow-changes: Fast-forward
b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d df6c19bcb1f63b55bed8d1d1e8c2572641f32348 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
df6c19bcb1f63b55bed8d1d1e8c2572641f32348 a29963940a2b7b953e18c499abdae489db19fb50 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added todo
a29963940a2b7b953e18c499abdae489db19fb50 2112dfba27ef58426f7863b3a7fa0805362b4fcf Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
2112dfba27ef58426f7863b3a7fa0805362b4fcf 42b5fe97fdf535bf0dbd721962c5158c3814414e Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
42b5fe97fdf535bf0dbd721962c5158c3814414e 85b575f8bf630138e8bb83cf5ccc70830f30070d Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
85b575f8bf630138e8bb83cf5ccc70830f30070d 5b10200afe509ad749da9a57de93d36d8fe848d0 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
5b10200afe509ad749da9a57de93d36d8fe848d0 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742053527 +0800	commit: Deployed to PRD
63e88e731221cfcd429347a3540d1b65af81478e 3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 Sim Zhen Quan <<EMAIL>> 1742182097 +0800	pull: Fast-forward
3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff Sim Zhen Quan <<EMAIL>> 1742182139 +0800	commit: Move ConfigHelper call to handle() to prevent composer dump issue
52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff 061a165481e164679e5e8c0d5fcb9a2e971c5d6d Sim Zhen Quan <<EMAIL>> 1742370280 +0800	pull: Fast-forward
061a165481e164679e5e8c0d5fcb9a2e971c5d6d 903944840d3648668e9b4d311acbc0f61422b8b4 Sim Zhen Quan <<EMAIL>> 1742370317 +0800	commit: Remove bracket for exam marks formula
903944840d3648668e9b4d311acbc0f61422b8b4 81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c Sim Zhen Quan <<EMAIL>> 1742404419 +0800	pull: Fast-forward
81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c 7b0774741487bce02df6bd5811dfb5c06ba20bea Sim Zhen Quan <<EMAIL>> 1742404436 +0800	commit: Fixed substitute_record includes overwrite
7b0774741487bce02df6bd5811dfb5c06ba20bea 8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 Sim Zhen Quan <<EMAIL>> 1742532019 +0800	pull: Fast-forward
8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 291204e3fab53d759922a1d3d0c2e5243b04f28c Sim Zhen Quan <<EMAIL>> 1742534965 +0800	commit: Remove sending of invoice via email
291204e3fab53d759922a1d3d0c2e5243b04f28c 3dabd411866a1137188a44d028bc8be1eb398471 Sim Zhen Quan <<EMAIL>> 1742539530 +0800	pull: Fast-forward
3dabd411866a1137188a44d028bc8be1eb398471 1c6f001d71e5b6bb3c9ac56af292592dc814cfbb Sim Zhen Quan <<EMAIL>> 1742539540 +0800	commit: Update timetable permission dependency
1c6f001d71e5b6bb3c9ac56af292592dc814cfbb 07a23bfa559338fc93ebea54086d799ce9ece419 Sim Zhen Quan <<EMAIL>> 1742742984 +0800	pull: Fast-forward
07a23bfa559338fc93ebea54086d799ce9ece419 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742782975 +0800	commit: Deployed to prd
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 2054a6adcce6264d9de4e86aafd0e1706ed83834 Sim Zhen Quan <<EMAIL>> 1742799425 +0800	commit: Bug fix
2054a6adcce6264d9de4e86aafd0e1706ed83834 9ebc71b626650133146be5ba5aeeb39c922a87e3 Sim Zhen Quan <<EMAIL>> 1742833766 +0800	pull: Fast-forward
9ebc71b626650133146be5ba5aeeb39c922a87e3 8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 Sim Zhen Quan <<EMAIL>> 1742834036 +0800	commit: Deployed to prd
8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 Sim Zhen Quan <<EMAIL>> 1742874099 +0800	pull: Fast-forward
12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742874633 +0800	commit: Class subject added semester_setting_id
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 5ab269abc7a5aef7179b69be49d6e7f86d8d63cb Sim Zhen Quan <<EMAIL>> 1742922527 +0800	merge origin/main: Fast-forward
5ab269abc7a5aef7179b69be49d6e7f86d8d63cb 430c99f3301d86ed4033559c6230fa847514b318 Sim Zhen Quan <<EMAIL>> 1742923317 +0800	commit: Fix test cases
430c99f3301d86ed4033559c6230fa847514b318 4c5623fb7be96611042c2e9a3020e8f9751b2740 Sim Zhen Quan <<EMAIL>> 1743002463 +0800	pull: Fast-forward
4c5623fb7be96611042c2e9a3020e8f9751b2740 6122501c07bef394607b55ccf356bf0e78a52cc4 Sim Zhen Quan <<EMAIL>> 1743069910 +0800	pull: Fast-forward
6122501c07bef394607b55ccf356bf0e78a52cc4 b207bc9e50ef8b71be7038147d6d843d53acc80d Sim Zhen Quan <<EMAIL>> 1743070030 +0800	commit: Added posting in migration
b207bc9e50ef8b71be7038147d6d843d53acc80d 7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 Sim Zhen Quan <<EMAIL>> 1743091037 +0800	commit: Temporarily update hostel reward punishment order
7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 Sim Zhen Quan <<EMAIL>> 1743093087 +0800	commit: Temporarily update reward punishment order
9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743488546 +0800	pull: Fast-forward
a44125b11c443cb226dbc4db2954f4f85c25bdc0 64bf2da30b0fec631722b5da4aba4ed3ad72989a Sim Zhen Quan <<EMAIL>> 1743520053 +0800	pull: Fast-forward
64bf2da30b0fec631722b5da4aba4ed3ad72989a 48ceb328926fd71a8c406012a2cccb4b4322779f Sim Zhen Quan <<EMAIL>> 1743520814 +0800	commit: Deployed to PRD
48ceb328926fd71a8c406012a2cccb4b4322779f 9319430047f4d24df8d1783658fc189438c7c2eb Sim Zhen Quan <<EMAIL>> 1743671382 +0800	pull: Fast-forward
9319430047f4d24df8d1783658fc189438c7c2eb 41d6c3d5a9d1ff4611caea78bb136a813ba9e241 Sim Zhen Quan <<EMAIL>> 1743672617 +0800	commit: Added extra validation handling
41d6c3d5a9d1ff4611caea78bb136a813ba9e241 0ad7e44411d91dbb2ec01d083fec13bf755722a9 Sim Zhen Quan <<EMAIL>> 1743688686 +0800	commit: Fix student classes migration
0ad7e44411d91dbb2ec01d083fec13bf755722a9 ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743862648 +0800	pull: Fast-forward
ad4d6fae4d985515fbff52e7bfe51052351c087b 44d3715712a7d0b6d52b8cc4092959b8ef6ea79b Sim Zhen Quan <<EMAIL>> 1743956843 +0800	pull: Fast-forward
44d3715712a7d0b6d52b8cc4092959b8ef6ea79b 53cc56ed0c3775776460e6a57ed2ecd2ffcae188 Sim Zhen Quan <<EMAIL>> 1743990275 +0800	commit: Change invoice to receipt
53cc56ed0c3775776460e6a57ed2ecd2ffcae188 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744000217 +0800	commit: Bug fixes
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 499d565abe53914cf0beb84d7149947d1690fd27 Sim Zhen Quan <<EMAIL>> 1744081264 +0800	pull: Fast-forward
499d565abe53914cf0beb84d7149947d1690fd27 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744171402 +0800	pull: Fast-forward
793032f9240efbbdb6691aa07ea5eabb618c034b 09e817ea94ec1e34fa55118053cf604665bc2ae8 Sim Zhen Quan <<EMAIL>> 1744275586 +0800	pull: Fast-forward
09e817ea94ec1e34fa55118053cf604665bc2ae8 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744599493 +0800	pull: Fast-forward
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 Sim Zhen Quan <<EMAIL>> 1744765150 +0800	pull: Fast-forward
6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 6b68588446a958e5b4195206a40e6e613e4a14f1 Sim Zhen Quan <<EMAIL>> 1744783083 +0800	commit: Migration script fixes
6b68588446a958e5b4195206a40e6e613e4a14f1 3fecbcbcf9458138d27db0f3d602c5a73efa9dcb Sim Zhen Quan <<EMAIL>> 1744783878 +0800	commit: Added Calendar Seeder
3fecbcbcf9458138d27db0f3d602c5a73efa9dcb b55abc76e5f8409a62a43da6038aaf5d971a99c0 Sim Zhen Quan <<EMAIL>> 1744785921 +0800	pull: Fast-forward
b55abc76e5f8409a62a43da6038aaf5d971a99c0 c3be00488d4a82ac0a68d5866e57107de7e708aa Sim Zhen Quan <<EMAIL>> 1744790486 +0800	pull: Fast-forward
c3be00488d4a82ac0a68d5866e57107de7e708aa 9c278e106fffb99558f7161113e007fee3c2f66b Sim Zhen Quan <<EMAIL>> 1744812944 +0800	pull: Fast-forward
9c278e106fffb99558f7161113e007fee3c2f66b 7161685ae9b8c87340e5da023f20a64b2da94c33 Sim Zhen Quan <<EMAIL>> 1744814597 +0800	pull: Fast-forward
7161685ae9b8c87340e5da023f20a64b2da94c33 0be760e5a7634b219f8e5cdb1bfa36e7422050fe Sim Zhen Quan <<EMAIL>> 1744816141 +0800	commit: Update Kernel
0be760e5a7634b219f8e5cdb1bfa36e7422050fe 8d3479855bfaf0d000310c0d5ff76fea7e1a5585 Sim Zhen Quan <<EMAIL>> 1744817925 +0800	commit: Bug fix
8d3479855bfaf0d000310c0d5ff76fea7e1a5585 6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 Sim Zhen Quan <<EMAIL>> 1744822651 +0800	pull: Fast-forward
6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 9134576a366118c20b091d05c398cd82f2d18bbf Sim Zhen Quan <<EMAIL>> 1744876855 +0800	commit: Updated discount to add in nullable source type and student id
9134576a366118c20b091d05c398cd82f2d18bbf bbdb50115935c27329bbc8848c23093c3d9eae5f Sim Zhen Quan <<EMAIL>> 1744882694 +0800	pull: Fast-forward
bbdb50115935c27329bbc8848c23093c3d9eae5f c052125d5ee560b2cabcd3dae700470f42b33981 Sim Zhen Quan <<EMAIL>> 1744887765 +0800	commit: Add support for pending payment status in billing resources
c052125d5ee560b2cabcd3dae700470f42b33981 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744908817 +0800	pull: Fast-forward
03f1c1209a1746b28478c97627a803d451428f74 3c41baaf6548a8d2fa4c81521e06a5190e97c44c Sim Zhen Quan <<EMAIL>> 1744910337 +0800	pull: Fast-forward
3c41baaf6548a8d2fa4c81521e06a5190e97c44c 2cd3853b8402b7d26ca55fe009d607d127293aa7 Sim Zhen Quan <<EMAIL>> 1744942129 +0800	commit: Update materialized view queue config
2cd3853b8402b7d26ca55fe009d607d127293aa7 312a6d9834663f7e57a023fe88bcedac4333b2c4 Sim Zhen Quan <<EMAIL>> 1744943950 +0800	commit: Change attendance posting to 7.31am
312a6d9834663f7e57a023fe88bcedac4333b2c4 080a8cbb6cd6f64dda7980812858f1866a12690b Sim Zhen Quan <<EMAIL>> 1744948165 +0800	commit: Add hostel bed assignment data to employee resource
080a8cbb6cd6f64dda7980812858f1866a12690b e39a6f32914904ce1042f8fef0cd1e6175050276 Sim Zhen Quan <<EMAIL>> 1744948474 +0800	pull: Fast-forward
e39a6f32914904ce1042f8fef0cd1e6175050276 616e62624a1c2071025cbdeb0f123f86781ef4a6 Sim Zhen Quan <<EMAIL>> 1744948495 +0800	commit: Fix test cases
616e62624a1c2071025cbdeb0f123f86781ef4a6 2efe9e640e9f253df4f7a41dd8c7944644a4fb8c Sim Zhen Quan <<EMAIL>> 1744955213 +0800	pull: Fast-forward
2efe9e640e9f253df4f7a41dd8c7944644a4fb8c 242f255099a17a2074eb79700dfa8b4b31dd00c7 Sim Zhen Quan <<EMAIL>> 1744958692 +0800	pull: Fast-forward
242f255099a17a2074eb79700dfa8b4b31dd00c7 ee25d4b9229913f152347093bc304179e6dd83db Sim Zhen Quan <<EMAIL>> 1744964200 +0800	pull: Fast-forward
ee25d4b9229913f152347093bc304179e6dd83db 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744964235 +0800	commit: Fix discount typo
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973325 +0800	merge permission-changes: Fast-forward
3d28b2377d28514ecf9ea2296dc753057e58d01c 0d9401098843914569a56226dbbacf0edd683d5f Sim Zhen Quan <<EMAIL>> 1744983307 +0800	cherry-pick: Add migration step for withdrawal reasons in README
0d9401098843914569a56226dbbacf0edd683d5f 362cdeac8db6ce95f7daccc8b6251ae222da1617 Sim Zhen Quan <<EMAIL>> 1744984069 +0800	commit: Updated README.md
362cdeac8db6ce95f7daccc8b6251ae222da1617 c89d1e4dac7d52e6374ce88c030e7a57147f40f7 Sim Zhen Quan <<EMAIL>> 1745031251 +0800	commit: Minor fixes
c89d1e4dac7d52e6374ce88c030e7a57147f40f7 1de8cf744fce8c76fdfae4a7560618937b449edb Sim Zhen Quan <<EMAIL>> 1745031267 +0800	merge origin/main: Merge made by the 'ort' strategy.
1de8cf744fce8c76fdfae4a7560618937b449edb b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 Sim Zhen Quan <<EMAIL>> 1745034282 +0800	pull: Fast-forward
b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745035887 +0800	pull: Fast-forward
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 f8ab0638253a82d9fe23a52889a0df4b9d7fe42a Sim Zhen Quan <<EMAIL>> 1745036748 +0800	pull: Fast-forward
f8ab0638253a82d9fe23a52889a0df4b9d7fe42a 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745043732 +0800	pull: Fast-forward
1750e710732442cc41e8574cb50ad01d7e9889e2 92db3a8929dbf699e904d58aac529e8dd8602f3d Sim Zhen Quan <<EMAIL>> 1745167637 +0800	pull: Fast-forward
92db3a8929dbf699e904d58aac529e8dd8602f3d bd27fadffd2273ee5897a23741b6cda3a0ba3692 Sim Zhen Quan <<EMAIL>> 1745201063 +0800	pull: Fast-forward
bd27fadffd2273ee5897a23741b6cda3a0ba3692 d8df21279f1f0bd502546519f2a4eb3b28086aab Sim Zhen Quan <<EMAIL>> 1745201814 +0800	commit: Allow filter by late attendance also
d8df21279f1f0bd502546519f2a4eb3b28086aab 7d1807feb8e037d564a1f9169b2184db18167cb7 Sim Zhen Quan <<EMAIL>> 1745204456 +0800	pull: Fast-forward
7d1807feb8e037d564a1f9169b2184db18167cb7 edb1e9a85bdbebf5df03babb3d2dbeef4d827623 Sim Zhen Quan <<EMAIL>> 1745228186 +0800	pull: Fast-forward
edb1e9a85bdbebf5df03babb3d2dbeef4d827623 dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745228189 +0800	commit: Deployed to prd
dad60aa80fef74f84ff2c7cf750edbfcd649272f 3a552cd0f666ccc554d1c6174e4c131dba90a27e Sim Zhen Quan <<EMAIL>> 1745249748 +0800	pull: Fast-forward
3a552cd0f666ccc554d1c6174e4c131dba90a27e a420605c2b6183706543c11dcdb3c20156e80498 Sim Zhen Quan <<EMAIL>> 1745249959 +0800	commit: Deployed to PRD
a420605c2b6183706543c11dcdb3c20156e80498 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745289216 +0800	pull: Fast-forward
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b0e418323088db9153b22b408d4834221452118c Sim Zhen Quan <<EMAIL>> 1745335806 +0800	pull: Fast-forward
b0e418323088db9153b22b408d4834221452118c 889610d2cbcea65247e5cf9d30f79bfb8ec99834 Sim Zhen Quan <<EMAIL>> 1745335924 +0800	commit: Added patch for leave early period correction
889610d2cbcea65247e5cf9d30f79bfb8ec99834 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745336118 +0800	commit: Added patch period 15 incorrect attendance status
e90ce7169e07685db944c78bc6632c38c73813f7 e843c2078c1cf7147bc4b1fbccfb3384be06444c Sim Zhen Quan <<EMAIL>> 1745341820 +0800	pull: Fast-forward
e843c2078c1cf7147bc4b1fbccfb3384be06444c 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745343230 +0800	commit: Deployed to PRD
071181cc25fbce84151d14a6c95c2ea8acbf57c8 a04fbf5406e7bbd01735edd7e00cd4083a6941b8 Sim Zhen Quan <<EMAIL>> 1745429829 +0800	pull: Fast-forward
a04fbf5406e7bbd01735edd7e00cd4083a6941b8 173d0a084e0186f413078de50eafaa7246430390 Sim Zhen Quan <<EMAIL>> 1745460720 +0800	commit: Deployed to prd
173d0a084e0186f413078de50eafaa7246430390 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745483568 +0800	pull: Fast-forward
72b23a17fc9dae7ec7cf713667cd83b546d8f123 bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 Sim Zhen Quan <<EMAIL>> 1745513483 +0800	pull: Fast-forward
bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 c4b674b8d67df017ff476f0698671e831fd20d1f Sim Zhen Quan <<EMAIL>> 1745513843 +0800	merge origin/staging/2025-04-24: Fast-forward
c4b674b8d67df017ff476f0698671e831fd20d1f a078170d63d012ae73cccd8d74b5c6a1278b949e Sim Zhen Quan <<EMAIL>> 1745514100 +0800	commit: Remove comments
a078170d63d012ae73cccd8d74b5c6a1278b949e 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745514849 +0800	commit: Deployed to prd
6cbc3dd7880b8505008adc868ff38012343292a4 d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745563840 +0800	commit: Fix test case
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 3ffc52d4692fd8453b111d1dda104daa74d5b6f9 Sim Zhen Quan <<EMAIL>> 1745768300 +0800	pull: Fast-forward
3ffc52d4692fd8453b111d1dda104daa74d5b6f9 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745768605 +0800	commit: Deployed to PRD
28320bba79056630c5a09dc205dccc8140d57700 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745895657 +0800	merge origin/staging/2025-04-29: Fast-forward
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 63bd88fbe09bdfe9776f2b42235296a414631d12 Sim Zhen Quan <<EMAIL>> 1745941393 +0800	pull: Fast-forward
63bd88fbe09bdfe9776f2b42235296a414631d12 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745942354 +0800	commit: Deployed to PRD
38b120e9982b5022885bdc8d8958a2a445dc24ce 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745944046 +0800	commit: Temporary disable code
397cd210416fc3aa207241c1d586208f09c0907a 485681a98d509da56c42a70062e3f9d9f9cb8801 Sim Zhen Quan <<EMAIL>> 1746371784 +0800	pull: Fast-forward
485681a98d509da56c42a70062e3f9d9f9cb8801 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746375217 +0800	commit: Test case fixes
a136c9367666564da506b17abe51c02b6b724182 e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 Sim Zhen Quan <<EMAIL>> 1746612302 +0800	commit: Deployed to PRD at 4 May 2025
e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 be74f807e7710c62e1a48e22b07f45cd74fd83b7 Sim Zhen Quan <<EMAIL>> 1746625269 +0800	pull: Fast-forward
be74f807e7710c62e1a48e22b07f45cd74fd83b7 546e08449ac6fd710135dd220067691e671eecd5 Sim Zhen Quan <<EMAIL>> 1746688381 +0800	commit: Deployed to prd
546e08449ac6fd710135dd220067691e671eecd5 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688400 +0800	merge origin/main: Merge made by the 'ort' strategy.
15e2d16e802e9e2690986d55c2c2445ca5750878 534a9e177e126eab7fe33c6f9a60613b4806ff8f Sim Zhen Quan <<EMAIL>> 1746713918 +0800	pull: Fast-forward
534a9e177e126eab7fe33c6f9a60613b4806ff8f adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 Sim Zhen Quan <<EMAIL>> 1746714101 +0800	pull: Fast-forward
adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 f02f1656f453475c0f5ed9c33c6e32e872dd5338 Sim Zhen Quan <<EMAIL>> 1746756057 +0800	commit: Deployed to PRD
f02f1656f453475c0f5ed9c33c6e32e872dd5338 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1746768106 +0800	commit: Pagination issue fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 6c0cdcd307ead6c259d2c32a12252476769b5228 Sim Zhen Quan <<EMAIL>> 1747195254 +0800	pull: Fast-forward
6c0cdcd307ead6c259d2c32a12252476769b5228 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747195255 +0800	commit: Added report card worker
30fe292833e03147752506546cec0bea4f8275a0 56e4364b063771a8dfdc26af56781b90d349fbf7 Sim Zhen Quan <<EMAIL>> 1747243084 +0800	pull: Fast-forward
56e4364b063771a8dfdc26af56781b90d349fbf7 39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef Sim Zhen Quan <<EMAIL>> 1747244346 +0800	commit: Deployed to prd
39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef c6194433e2e1256da72317db5aefd7ed1fb88269 Sim Zhen Quan <<EMAIL>> 1747590119 +0800	commit: Changed report header
c6194433e2e1256da72317db5aefd7ed1fb88269 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747637704 +0800	commit: Hotfix hostel in out report repository
8536e64e3681503deda5c860fc678553f430eede 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747708359 +0800	commit: Added refresh view table for exam posting pre check mat view every 5 mins
7068f7efbcc996503b57996f24b4260a06b10d6a 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747807715 +0800	pull: Fast-forward
98cd6430fbec4914d05d8a4f4505416b22f74daa 7b569b520e54c0071477014207bdcb7143911d2d Sim Zhen Quan <<EMAIL>> 1747897995 +0800	pull: Fast-forward
7b569b520e54c0071477014207bdcb7143911d2d d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 Sim Zhen Quan <<EMAIL>> 1747898889 +0800	commit: Fix kernel issue
d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 11a18edb615809acc364116d77b5c404614b21dc Sim Zhen Quan <<EMAIL>> 1747933846 +0800	commit: Remove duplicated scheduler
11a18edb615809acc364116d77b5c404614b21dc 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1747969911 +0800	commit: Deployed to PRD
9873f23d406d7770d65aec65c428b7a1020aa6d4 7ec7fef357689ad9216b97a6e88aff2a48de217d Sim Zhen Quan <<EMAIL>> 1748110090 +0800	pull: Fast-forward
7ec7fef357689ad9216b97a6e88aff2a48de217d 35c5b053bb9bba723e533bae27e61ba3fc61862c Sim Zhen Quan <<EMAIL>> 1748183784 +0800	commit: Deployed to prd
35c5b053bb9bba723e533bae27e61ba3fc61862c 143e26bd9e386b5fb5bd056d199f57202890c488 Sim Zhen Quan <<EMAIL>> 1748193680 +0800	commit: Added patch for reward punishment record date of sign
143e26bd9e386b5fb5bd056d199f57202890c488 7cc66fe79d7ff0bcbf330a1680a22efa420c445c Sim Zhen Quan <<EMAIL>> 1748276558 +0800	pull: Fast-forward
7cc66fe79d7ff0bcbf330a1680a22efa420c445c 73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb Sim Zhen Quan <<EMAIL>> 1748279942 +0800	commit: Deployed to prd
