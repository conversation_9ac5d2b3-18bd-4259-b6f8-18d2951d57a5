# Deployment (DEV/QAS)

To deploy to AWS, just run:
> Note: If you have multiple AWS profile, make sure u manually use deploy-qas.sh:line 2 to authenticate

``` bash
sudo ./deploy-qas.sh
```

# Deployment (PROD)

1. Update `docker-compose.smpinhwa.prd.yml` version to the latest version
2. Update `deploy-prd.sh` to the latest version (same version as `docker-compose.smpinhwa.prd.yml`)
3. Run the following command to deploy to AWS
    ```bash
    ./deploy-prd.sh
    ```
4. SSH to PRD server
5. Run the following command to update the docker image
    ```bash
    ./deploy.sh 396192154208.dkr.ecr.ap-southeast-5.amazonaws.com/skribble-learn-api/smpinhwa-prd:{YOUR_VERSION_NUMBER_HERE}
    ```
6. To run any manual commands, use the following command to enter the container
    ```bash
    docker exec -it skribble-learn-api bash
    ```

# Printout Dependencies

1. For printout, download wkhtmltopdf from https://wkhtmltopdf.org/downloads.html
2. Then take note of the installation folder. In windows it should be something like "C:\wkhtmltopdf\bin\wkhtmltopdf"
3. Set the full installation path to bin\wkhtmltopdf executable to .env, and .env.testing WKHTML_PDF_BINARY key.

# Unit tests

to Run unit tests faster use `php artisan test --parallel`
https://laravel.com/docs/11.x/testing#running-tests-in-parallel

------------------------------

2024-10-22 migration changes

master_races

- add sequence column

master_religions

- add sequence column

students

- drop birthplace_id column
- add birthplace column as nullable string
- add phone_number_2 column, nullable. phone_number must be mobile phone number for TAC purposes.
- add address_2 column, nullable
- change postal_code, city, state_id, country_id, birth_cert_number to nullable

users

- change email column to nullable as some users may only have either one email / phone.

guardians

- add remarks column for v1 father_status, mother_status, guardian_status, or any remarks by admin

guardian_student

- add column "relation_to_student" to record what's the relationship between guardian and student

employees

- need to manually alter table set user_id to nullable
- add new columns address_2, epf_number, employment_start_date, employment_end_date, highest_education,
  highest_education_country_id, employment_type, employee_category_id, marriage_status, personal_email
- make user_id nullable as not all employees will have access to the app/BO
- set date_of_birth to nullable

master_employee_job_titles

- drop column type and is_teacher

student_classes

- add class_leave_date nullable

To implement laravel-phone validation for all phone number fields.

---

# Pinhwa Migration Script

## Phase 1 - Initial Setup

``` bash
php artisan db:seed --class=ProductSeeder
php artisan db:seed --class=PaymentMethodSeeder
php artisan db:seed --class=InternationalizationSeeder
php artisan db:seed --class=LegalEntitySeeder
php artisan db:seed --class=PaymentTermsSeeder
php artisan db:seed --class=UomSeeder
php artisan db:seed --class=GlAccountSeeder
php artisan db:seed --class=TaxSeeder
php artisan db:seed --class=CurrencySeeder
php artisan db:seed --class=BankSeeder
php artisan db:seed --class=BankAccountSeeder
php artisan db:seed --class=BookLanguageSeeder
php artisan db:seed --class=CountrySeeder
php artisan db:seed --class=StateSeeder
php artisan db:seed --class=CourseSeeder
php artisan db:seed --class=SemesterYearSeeder
php artisan db:seed --class=PinHwaConfigSeeder 
php artisan v1:migrate --step=migrateSchoolProfile --actual
php artisan v1:migrate --step=migrateRace --actual
php artisan v1:migrate --step=migrateReligion --actual
php artisan v1:migrate --step=migrateGradeMaster --actual
php artisan v1:migrate --step=migrateHealthConcerns --actual
php artisan v1:migrate --step='migratePrimarySchool' --actual
php artisan v1:migrate --step='migrateStudents' --actual
php artisan v1:migrate --step='migrateGuardians' --actual
php artisan v1:migrate --step='migrateHostelGuardians' --actual
php artisan v1:migrate --step='migrateEmployeeCategory' --actual
php artisan v1:migrate --step='migrateEmployeeSession' --actual
php artisan v1:migrate --step='migrateEmployees' --actual
php artisan db:seed --class=EmployeeSeeder # This one has to run after migrateEmployees because migrateEmployees hardcoded the id
php artisan v1:migrate --step='migrateSemesterSettings' --actual
php artisan v1:migrate --step='migrateClass' --actual
php artisan v1:migrate --step='migrateSemesterClass' --actual
php artisan v1:migrate --step='migrateStudentClasses' --actual
php artisan v1:migrate --step='migrateStudentCards' --actual
php artisan v1:migrate --step='migrateEmployeeCards' --actual
php artisan v1:migrate --step='migrateContractor' --actual
php artisan db:seed --class=SystemRoleSeeder
php artisan db:seed --class=PermissionSeeder
php artisan db:seed --class=UserSeeder # Don't run this on PRD
php artisan populate:wallet --actual

php artisan excel:migrate --step=migrateEmployeeEmail --actual
php artisan excel:migrate --step=migrateContractorInfo --actual

php artisan v1:migrate --step='migrateEducation' --actual
```

## Additional Notes:

1. Setup Payex Credentials
2. Configure Terminal and Terminal Key
3. Update running number for student
4. Create test account for apple, android and ios

---

## Phase 2

``` bash
php artisan app:migration-cleanup
php artisan migrate

#Library - Phase 2
php artisan v1:migrate --step='migrateBookSources' --actual
php artisan v1:migrate --step='migrateBookClassifications' --actual
php artisan v1:migrate --step='migrateBookSubClassifications' --actual
php artisan v1:migrate --step='migrateBookCategories' --actual
php artisan v1:migrate --step='migrateAuthors' --actual
php artisan v1:migrate --step='migrateBooks' --actual
php artisan v1:migrate --step='migrateAuthorBook' --actual
php artisan v1:migrate --step='migrateLibraryStudentMember' --actual
php artisan v1:migrate --step='migrateLibraryEmployeeMember' --actual
php artisan v1:migrate --step='migrateLibraryOtherMember' --actual
php artisan v1:migrate --step='migrateLibraryBookLoan' --actual
php artisan v1:migrate --step='updateBookStatus' --actual

#Hostel - Phase 2
php artisan v1:migrate --step='migrateHostelStudentBlocks' --actual
php artisan v1:migrate --step='migrateHostelEmployeeBlocks' --actual
php artisan v1:migrate --step='migrateHostelSavingAccounts' --actual
php artisan v1:migrate --step='migrateHostelStudentRooms' --actual
php artisan v1:migrate --step='migrateHostelEmployeeRooms' --actual
php artisan v1:migrate --step='generateHostelBeds' --actual
php artisan v1:migrate --step='migrateHostelBedStudentAssignments' --actual
php artisan v1:migrate --step='migrateHostelBedEmployeeAssignments' --actual
php artisan v1:migrate --step='migrateHostelPersonInCharge' --actual

php artisan v1:migrate --step='migrateHostelMeritDemeritSettings' --actual
php artisan v1:migrate --step='migrateHostelRewardPunishmentSettings' --actual
php artisan v1:migrate --step='migrateHostelRewardPunishmentRecords' --actual

#Subject - Phase 2
# NOTE: MUST Migrate subject first before Club
php artisan v1:migrate --step='migrateSubject' --actual
php artisan v1:migrate --step='migrateClassSubject' --actual
php artisan v1:migrate --step='migrateStudentClassSubject' --actual

#Club - Phase 2
php artisan v1:migrate --step='migrateClubCategory' --actual
php artisan v1:migrate --step='migrateClubClass' --actual
php artisan v1:migrate --step='migrateClub' --actual
php artisan v1:migrate --step='migrateClubSubject' --actual
php artisan v1:migrate --step='migrateClubClassSubject' --actual
php artisan v1:migrate --step='migrateClubClassSubjectStudent' --actual
php artisan v1:migrate --step='migrateClubStudentClass' --actual

# This should be already run in Phase 1
# php artisan v1:migrate --step='migrateContractor' --actual
php artisan v1:migrate --step='migrateClassSubjectContractor' --actual
php artisan v1:migrate --step='migrateCocoClassSubjectTeacher' --actual

# This should be already run in Phase 1, but is safe to run again
# php artisan populate:wallet --actual

# Competition (Off Campus/Performance)
php artisan v1:migrate --step=migrateCompetition --actual
php artisan v1:migrate --step=migrateCompetitionRecord --actual

# Society Positions
php artisan v1:migrate --step='migrateSocietyPosition' --actual
php artisan v1:migrate --step='migrateStudentSocietyPosition' --actual

# Comprehensive Assessment
php artisan v1:migrate --step='migrateComprehensiveAssessmentCategories' --actual
php artisan v1:migrate --step='migrateComprehensiveAssessmentQuestions' --actual

#Reward Punishment - Phase 2
php artisan v1:migrate --step=migrateMeritDemeritSettings --actual
php artisan v1:migrate --step=migrateRewardPunishments --actual
php artisan v1:migrate --step=migrateMeritDemeritRewardPunishments --actual
php artisan v1:migrate --step=migrateRewardPunishmentRecords --actual

php artisan v1:migrate --step='migrateGradingWithGradingSchemeItem' --actual
# Need to change to correct dates before running this
php artisan v1:migrate --step='migrateConductSettingWithConductSettingTeacher' --actual
php artisan v1:migrate --step='migrateConductRecord' --actual

# Calendar seeder
php artisan db:seed --class=CalendarSeeder

#Timetable
php artisan v1:migrate --step=migratePeriodGroup --actual
php artisan v1:migrate --step=migratePeriod --actual
php artisan v1:migrate --step=migrateTimetable --actual
php artisan v1:migrate --step=migrateTimeslot --actual

#Cocu Timetable
php artisan v1:migrate --step=migrateCocurriculumPeriodGroup --actual
php artisan v1:migrate --step=migrateCocurriculumPeriod --actual
php artisan v1:migrate --step=migrateCocurriculumTimetable --actual
php artisan v1:migrate --step=migrateCocurriculumTimeslot --actual

#Leadership
php artisan v1:migrate --step=migrateLeadershipPosition --actual
php artisan v1:migrate --step=migrateLeadershipPositionRecord --actual

#EnglishClass
php artisan v1:migrate --step=migrateEnglishClass --actual
php artisan v1:migrate --step=migrateEnglishClassInitTimetable --actual
php artisan v1:migrate --step=migrateEnglishClassTimeslots --actual

# Attendance
php artisan v1:migrate --step=migrateAttendance --actual
# Need to run attendance posting for each of the attendance days
php artisan v1:migrate --step=attendancePostingForMigratedData --actual
php artisan v1:migrate --step=migratePeriodAttendance --actual

# Leave Application
php artisan v1:migrate --step=migrateLeaveApplicationType --actual
php artisan v1:migrate --step=migrateLeaveApplication --actual
php artisan v1:migrate --step=migrateLeaveApplicationPeriodToPeriodAttendance --actual
php artisan v1:migrate --step=migrateAbsentLateRecords --actual

# Substitute Teacher
php artisan v1:migrate --step=migrateSubstituteTeachers --actual

# PermissionSeeder
php artisan db:seed --class=PermissionSeeder
php artisan permission:phase2 --actual

# Finance - Phase 2
php artisan v1:migrate --step='migrateScholarships' --actual
php artisan v1:migrate --step='migrateWithdrawalReason' --actual
php artisan v1:migrate --step='migrateUnpaidFees' --actual

```

---------

Internal LX notes for exam, report card and results posting module

Functions:

- assigning master grading framework to semester class (DONE)
- Grading framework master CRUD
- using master grading framework settings to setup students' own grading framework and sub-settings.  (DONE, pending API)
- update a particular student/students grading framework settings (Assign student grading framework via configuration json)  (DONE, pending API)

- Validation of configuration json (all DONE)
    - All result source subject components must sum up to 100
    - Each result source subject components must have unique code within the same subject
    - Each result source subject code must be unique within the same result source
    - Each result source subject must have valid subject code
    - Each result source exam tagging must have valid exam code
    - Each result source must have unique code within the same grading framework
    - Each report card output must have unique code within the same grading framework
    - Each report card output component must have unique code within the same report card output -
    - Each report card output component grading scheme code must be valid if provided.
    - Each report card output must contain all compulsory components declared at Grading framework output_system_components
    - Each report card output component code must be same as subject code if it contains a subject code
    - Report card output specified service must exists
    - Each report card output with subject must have valid subject code
    - Can only have 1 is_final report card output within a Grading framework
    - Validate each total_formula and label_formula in report card output component to ensure they are legit/correct formula and can be parsed

- manage/update/view student grading framework settings
- replace a student existing grading framework with another master grading framework  (DONE, pending API)
- Make materialized view for easier reporting on exam scores (For now just use line items table)
- all code columns must be A-Za-z0-9_only (DONE)
- Exam result data entry by teachers (DONE, but api not tested)
- Report card posting by grade/student & report output code. (DONE, but need to implement actual Service class for Pinhwa report card)
- Results posting dashboard and checklist
- add default_grading_framework_id to class table (DONE)
- check whether exam period is open when saving score (DONE)
- undo posting version to previous version = resetCurrentPostingToHeader (DONE)
- Support custom attendance date from-to for results posting related to attendance/reward/penalty. Use metadata in posting header. (DONE)

Incomplete:

- how to batch update student grading framework
- how to reapply master grading framework to all students grading framework if there are changes after assignment
- batch assign new student grading framework to entire class
- To complete the pinhwa-default blade file for report card
- To create a .json config for grading framework based on actual Pin hwa requirements.

Changes:

- Add default_grading_framework_id to semester_classes. Todo: when creating new semester class, default_grading_framework_id to inherit from previous semester.
- Todo: Make sure to assign default_grading_framework_id in semester_classes to student when student is assigned to the class.
- Add major_subject_id column to subjects table to indicate which major subject does a minor subject belong to (e.g. English Oral belongs to English)
- Add code to grading_schemes so that it's easier to define in JSON configuration for grading framework.
- Subjects table add sequence column. Todo: implement CRUD for subjects master to support sequence column.

https://github.com/madorin/matex/tree/master
(A.ENG.TOTAL x 0.5 + B.ENG.TOTAL x 0.5) x 0.6 + (C.ENG.TOTAL x 0.5 + D.ENG.TOTAL x 0.5) x 0.4

# Exam Module Golive

## Operational

1. Remember turn on permission for teacher to key in marks
2. Make sure result period open date is setup correctly

## Technical

1. Need to setup semester setting code

```bash
php artisan db:seed --class=ExamSeeder
php artisan db:seed --class=GradingSchemeSeeder
# IMPORTANT: init_class_data need to remove when golive
php artisan db:seed --class=GradingFrameworkSeeder

# IMPORTANT: UPDATE YEAR AND SEMESTER BEFORE RUNNING THESE 2 COMMANDS
# Update to 2025 Sem 1
php artisan v1:migrate --step=migrateConductSettingWithConductSettingTeacher --actual
php artisan v1:migrate --step=migrateConductRecord --actual

php artisan v1:migrate --step=migrateExemptStudents
```

