<?php

use App\Enums\Day;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\TimeslotTeacherType;
use App\Http\Resources\ClassSubjectResource;
use App\Http\Resources\PeriodGroupResource;
use App\Http\Resources\PeriodResource;
use App\Http\Resources\SemesterClassResource;
use App\Models\ClassSubject;
use App\Models\Employee;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Models\User;
use App\Services\PeriodService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Timetable::class)->getTable();
    $this->timeslotTable = resolve(Timeslot::class)->getTable();
    $this->timeslotTeacherTable = resolve(TimeslotTeacher::class)->getTable();
    $this->routeNamePrefix = 'timetables';
});

test('index', function () {
    $period_group = PeriodGroup::factory()->create();

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
    ]);

    $payload = [
        'period_group_id' => $period_group->id,
        'includes' => ['periodGroup', 'semesterClass', 'timeslots']
    ];

    $this->mock(PeriodService::class, function (MockInterface $mock) use ($payload, $timetable) {
        $mock->shouldReceive('getAllPaginatedTimetables')->with($payload)->andReturn(new LengthAwarePaginator([$timetable], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            [
                'id' => $timetable->id,
                'name' => $timetable->name,
                'period_group' => resourceToArray(new PeriodGroupResource($period_group)),
                'semester_class' => resourceToArray(new SemesterClassResource($timetable->semesterClass)),
                'is_active' => $timetable->is_active,
                'timeslots' => []
            ],
        ]);
});

test('show', function () {
    $period_group = PeriodGroup::factory()->create();

    $periods = Period::factory(5)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'from_time' => '11:00',
            'to_time' => '12:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'from_time' => '11:00',
            'to_time' => '12:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '13:00',
            'to_time' => '14:00',
            'day' => Day::MONDAY,
            'display_group' => 2
        ]
    ))->create();

    PeriodLabel::factory(2)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'name' => [
                'en' => 'First period',
                'zh' => '第一节',
            ]
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'name' => [
                'en' => 'Second period',
                'zh' => '第二节',
            ]
        ],
    ))->create();

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
    ]);

    $timeslots = Timeslot::factory(5)->state(new Sequence(
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[0]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[1]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::TUESDAY,
            'period_id' => $periods[2]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::TUESDAY,
            'period_id' => $periods[3]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[4]->id,
            'timetable_id' => $timetable->id,
        ],
    ))->create();

    $timetable->load(['semesterClass.semesterSetting', 'semesterClass.classModel']);

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['timetable' => $timetable->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $timetable->id,
            'name' => $timetable->name,
            'is_active' => $timetable->is_active,
            'semester_class_id' => $timetable->semester_class_id,
            'display_groups' => [
                [
                    'id' => 1,
                    'periods' => [
                        [
                            'period_label_name' => 'First period',
                            'period_label_name_translations' => [
                                'en' => 'First period',
                                'zh' => '第一节',
                            ],
                            'from_time' => '10:00:00',
                            'to_time' => '11:00:00',
                        ],
                        [
                            'period_label_name' => 'Second period',
                            'period_label_name_translations' => [
                                'en' => 'Second period',
                                'zh' => '第二节',
                            ],
                            'from_time' => '11:00:00',
                            'to_time' => '12:00:00',
                        ]
                    ],
                    'days' => [
                        'MONDAY' => [
                            '1' => [
                                "timeslot_id" => $timeslots[0]->id,
                                "timeslot_class_subject_id" => $timeslots[0]->class_subject_id,
                                "timeslot_placeholder" => $timeslots[0]->placeholder,
                                "timeslot_attendance_from" => $timeslots[0]->attendance_from,
                                "timeslot_attendance_to" => $timeslots[0]->attendance_to,
                                "timeslot_default_init_status" => $timeslots[0]->default_init_status->value,
                                "timeslot_has_mark_deduction" => $timeslots[0]->has_mark_deduction,
                                "timeslot_teachers" => [],
                            ],
                            '2' => [
                                "timeslot_id" => $timeslots[1]->id,
                                "timeslot_class_subject_id" => $timeslots[1]->class_subject_id,
                                "timeslot_placeholder" => $timeslots[1]->placeholder,
                                "timeslot_attendance_from" => $timeslots[1]->attendance_from,
                                "timeslot_attendance_to" => $timeslots[1]->attendance_to,
                                "timeslot_default_init_status" => $timeslots[1]->default_init_status->value,
                                "timeslot_has_mark_deduction" => $timeslots[1]->has_mark_deduction,
                                "timeslot_teachers" => [],
                            ]
                        ],
                        'TUESDAY' => [
                            '1' => [
                                "timeslot_id" => $timeslots[2]->id,
                                "timeslot_class_subject_id" => $timeslots[2]->class_subject_id,
                                "timeslot_placeholder" => $timeslots[2]->placeholder,
                                "timeslot_attendance_from" => $timeslots[2]->attendance_from,
                                "timeslot_attendance_to" => $timeslots[2]->attendance_to,
                                "timeslot_default_init_status" => $timeslots[2]->default_init_status->value,
                                "timeslot_has_mark_deduction" => $timeslots[2]->has_mark_deduction,
                                "timeslot_teachers" => [],
                            ],
                            '2' => [
                                "timeslot_id" => $timeslots[3]->id,
                                "timeslot_class_subject_id" => $timeslots[3]->class_subject_id,
                                "timeslot_placeholder" => $timeslots[3]->placeholder,
                                "timeslot_attendance_from" => $timeslots[3]->attendance_from,
                                "timeslot_attendance_to" => $timeslots[3]->attendance_to,
                                "timeslot_default_init_status" => $timeslots[3]->default_init_status->value,
                                "timeslot_has_mark_deduction" => $timeslots[3]->has_mark_deduction,
                                "timeslot_teachers" => [],
                            ],
                        ],
                    ]
                ],
                [
                    'id' => 2,
                    'periods' => [
                        [
                            'period_label_name' => 'First period',
                            'period_label_name_translations' => [
                                'en' => 'First period',
                                'zh' => '第一节',
                            ],
                            'from_time' => '13:00:00',
                            'to_time' => '14:00:00',
                        ]
                    ],
                    'days' => [
                        'MONDAY' => [
                            '1' => [
                                "timeslot_id" => $timeslots[4]->id,
                                "timeslot_class_subject_id" => $timeslots[4]->class_subject_id,
                                "timeslot_placeholder" => $timeslots[4]->placeholder,
                                "timeslot_attendance_from" => $timeslots[4]->attendance_from,
                                "timeslot_attendance_to" => $timeslots[4]->attendance_to,
                                "timeslot_default_init_status" => $timeslots[4]->default_init_status->value,
                                "timeslot_has_mark_deduction" => $timeslots[4]->has_mark_deduction,
                                "timeslot_teachers" => [],
                            ]
                        ]
                    ]
                ]
            ]
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['timetable' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    $semester_class = SemesterClass::factory()->create();
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => 'Timetable 1',
        'semester_class_id' => $semester_class->id,
        'is_active' => true,
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name'],
            'semester_class' => resourceToArray(new SemesterClassResource($semester_class)),
            'is_active' => $payload['is_active'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'is_active' => $payload['is_active'],
        'semester_class_id' => $semester_class->id,
    ]);
});

test('assignPeriodGroup assigned before', function () {
    $period_groups = PeriodGroup::factory(2)->create();
    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_groups[0]->id,
    ]);

    $payload = [
        'period_group_id' => $period_groups[1]->id,
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.assign-period-group", ['timetable' => $timetable->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Timetable already assigned period group before.');
});

test('assignPeriodGroup', function () {
    $period_group = PeriodGroup::factory()->create();
    $periods = Period::factory(2)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00:00',
            'to_time' => '11:00:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00:00',
            'to_time' => '11:00:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
    ))->create();

    $timetable = Timetable::factory()->create([
        'period_group_id' => null,
    ]);

    $payload = [
        'period_group_id' => $period_group->id,
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.assign-period-group", ['timetable' => $timetable->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $timetable->id,
            'name' => $timetable->name,
            'period_group' => resourceToArray(new PeriodGroupResource($period_group)),
            'semester_class' => resourceToArray(new SemesterClassResource($timetable->semesterClass)),
            'timeslots' => [
                [
                    'id' => $response['data']['timeslots']['0']['id'],
                    'day' => $periods[0]->day->value,
                    'period' => resourceToArray(new PeriodResource($periods[0])),
                    'class_subject' => null,
                    'placeholder' => null,
                    'attendance_from' => $periods[0]->from_time,
                    'attendance_to' => $periods[0]->to_time,
                    'default_init_status' => PeriodAttendanceStatus::ABSENT->value, // column default value (timeslots table)
                    'has_mark_deduction' => true, // column default value (timeslots table)
                    'teachers' => []
                ],
                [
                    'id' => $response['data']['timeslots']['1']['id'],
                    'day' => $periods[1]->day->value,
                    'period' => resourceToArray(new PeriodResource($periods[1])),
                    'class_subject' => null,
                    'placeholder' => null,
                    'attendance_from' => $periods[1]->from_time,
                    'attendance_to' => $periods[1]->to_time,
                    'default_init_status' => PeriodAttendanceStatus::ABSENT->value, // column default value (timeslots table)
                    'has_mark_deduction' => true, // column default value (timeslots table)
                    'teachers' => []
                ],
            ]
        ]);

    //check db record
    $this->assertDatabaseCount($this->timeslotTable, 2);

    foreach ($periods as $period) {
        $this->assertDatabaseHas($this->timeslotTable, [
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
            'class_subject_id' => null,
            'placeholder' => null,
            'period_id' => $period->id,
        ]);
    }
});

test('update', function () {
    $timetable = Timetable::factory()->create([
        'name' => 'Timetable 1',
        'is_active' => false,
    ]);

    $employees = Employee::factory(2)->create();

    $class_subjects = ClassSubject::factory(2)->create();

    $timeslots = Timeslot::factory(2)->state(new Sequence(
        [
            'timetable_id' => $timetable->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'day' => Day::MONDAY,
        ],
        [
            'timetable_id' => $timetable->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'day' => Day::MONDAY,
        ],
    ))->create();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->timeslotTable, 2);
    $this->assertDatabaseCount($this->timeslotTeacherTable, 0);

    $payload = [
        'name' => 'Timetable 2',
        'is_active' => true,
        'timeslots' => [
            [
                'id' => $timeslots[0]->id,
                'class_subject_id' => $class_subjects[0]->id,
                'placeholder' => null,
                'attendance_from' => '10:00:00',
                'attendance_to' => '11:00:00',
                'default_init_status' => PeriodAttendanceStatus::ABSENT->value,
                'has_mark_deduction' => true,
                'teachers' => [
                    [
                        'employee_id' => $employees[0]->id,
                        'type' => TimeslotTeacherType::PRIMARY->value
                    ],
                    [
                        'employee_id' => $employees[1]->id,
                        'type' => TimeslotTeacherType::SECONDARY->value
                    ],
                ]
            ],
            [
                'id' => $timeslots[1]->id,
                'class_subject_id' => null,
                'placeholder' => 'abc',
                'attendance_from' => '11:00:00',
                'attendance_to' => '12:00:00',
                'default_init_status' => PeriodAttendanceStatus::PRESENT->value,
                'has_mark_deduction' => false,
                'teachers' => []
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['timetable' => $timetable->id]), $payload)->json();
    $response['data']['timeslots'] = collect($response['data']['timeslots'])->sortBy('id')->values()->toArray();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $timetable->id,
            'name' => $payload['name'],
            'period_group' => resourceToArray(new PeriodGroupResource($timetable->periodGroup)),
            'semester_class' => resourceToArray(new SemesterClassResource($timetable->semesterClass)),
            'timeslots' => [
                [
                    'id' => $timeslots[0]->id,
                    'day' => $timeslots[0]->day->value,
                    'period' => resourceToArray(new PeriodResource($timeslots[0]->period)),
                    'class_subject' => resourceToArray(new ClassSubjectResource($class_subjects[0])),
                    'placeholder' => null,
                    'attendance_from' => $payload['timeslots']['0']['attendance_from'],
                    'attendance_to' => $payload['timeslots']['0']['attendance_to'],
                    'default_init_status' => $payload['timeslots']['0']['default_init_status'],
                    'has_mark_deduction' => $payload['timeslots']['0']['has_mark_deduction'],
                    'teachers' => [
                        [
                            'employee_id' => $employees[0]->id,
                            'employee_name' => $employees[0]->name,
                            'type' => TimeslotTeacherType::PRIMARY->value
                        ],
                        [
                            'employee_id' => $employees[1]->id,
                            'employee_name' => $employees[1]->name,
                            'type' => TimeslotTeacherType::SECONDARY->value
                        ],
                    ]
                ],
                [
                    'id' => $timeslots[1]->id,
                    'day' => $timeslots[1]->day->value,
                    'period' => resourceToArray(new PeriodResource($timeslots[1]->period)),
                    'class_subject' => null,
                    'placeholder' => $payload['timeslots']['1']['placeholder'],
                    'attendance_from' => $payload['timeslots']['1']['attendance_from'],
                    'attendance_to' => $payload['timeslots']['1']['attendance_to'],
                    'default_init_status' => $payload['timeslots']['1']['default_init_status'],
                    'has_mark_deduction' => $payload['timeslots']['1']['has_mark_deduction'],
                    'teachers' => []
                ]
            ]
        ]);

    //check db record
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->timeslotTable, 2);
    $this->assertDatabaseCount($this->timeslotTeacherTable, 2);

    foreach ($payload['timeslots'] as $timeslot) {
        $this->assertDatabaseHas($this->timeslotTable, [
            'id' => $timeslot['id'],
            'class_subject_id' => $timeslot['class_subject_id'],
            'placeholder' => $timeslot['placeholder'],
        ]);

        foreach ($timeslot['teachers'] as $teacher) {
            $this->assertDatabaseHas($this->timeslotTeacherTable, [
                'employee_id' => $teacher['employee_id'],
                'type' => $teacher['type'],
            ]);
        }
    }

});



