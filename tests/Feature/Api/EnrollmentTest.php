<?php

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Http\Resources\CountryResource;
use App\Http\Resources\EnrollmentSessionResource;
use App\Http\Resources\GradeResource;
use App\Http\Resources\HealthConcernResource;
use App\Http\Resources\RaceResource;
use App\Http\Resources\ReligionResource;
use App\Http\Resources\SchoolResource;
use App\Http\Resources\StateResource;
use App\Models\Country;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Enrollment;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\Product;
use App\Models\Subject;
use App\Models\User;
use App\Services\DocumentPrintService;
use App\Services\EnrollmentService;
use App\Services\ReportPrintService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);
    $this->seedAccountingData();

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456')
    ]);

    // Only guardian or employee able to access enrollment page -> EnrollmentController middleware
    $employee = Employee::factory()->create([
        'user_id' => $user->id
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeName = 'admin.enrollments';

    $this->enrollmentTableName = 'enrollments';

    $this->payexBaseUrl = config('services.payment_gateway.payex.base_url');
    $this->payexAuthUrl = $this->payexBaseUrl . '/' . config('services.payment_gateway.payex.auth_url');

    config(['media-library.disk_name' => 'local']);

    $this->reportPrintService = resolve(ReportPrintService::class);
});

dataset('enrollment session', [
    function () {
        $enrollment_session = EnrollmentSession::factory()->create([
            'name' => 'Session 2025',
            'from_date' => '2025-01-01',
            'to_date' => '2025-03-01',
            'code' => '0001',
        ]);

        $subjects = Subject::factory(4)->state(new Sequence(
            [
                'code' => 'ENGLISH',
                'name->en' => 'English',
            ],
            [
                'code' => 'MATHS',
                'name->en' => 'Mathematics',
            ],
            [
                'code' => 'SCIENCE',
                'name->en' => 'Science',
            ],
            [
                'code' => 'ART',
                'name->en' => 'Art',
            ],
        ))->create();

        $enrollment_session->examSubjects()->attach($subjects);

        return $enrollment_session;
    }
]);

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollment = Enrollment::factory()->create();

        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->once()
            ->andReturn(new LengthAwarePaginator([$enrollment], 1, 1));
    });

    $response = $this->getJson(route($this->routeName . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollments = Enrollment::factory(2)->create();

        $mock->shouldReceive('getAllEnrollments')->once()->andReturn($enrollments);
    });

    $response = $this->getJson(route($this->routeName . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('downloadPrePaymentTemplate', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $filename = 'enrollment-template';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeName . '.download-pre-payment-template', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
})->with('enrollment session');

test('downloadPostPaymentTemplate', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $filename = 'enrollment-post-payment-template';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeName . '.download-post-payment-template', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
})->with('enrollment session');

test('downloadPrePaymentTemplate - test excel content', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $report_data = app()->make(EnrollmentService::class)->getTemplateData($payload['enrollment_session_id'], false);

    $report_view_name = 'templates.enrollment-template';
    $file_name = 'enrollment-template';

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = [
                'No',
                '考生编号 / Exam Slip',
                'Student Name',
                '学生姓名',
                '身份证号码 / IC Number',
                'Passport Number',
                'Religion',
                '性别 / Gender (MALE, FEMALE)',
                '监护人电话号码 / Guardian Phone Number',
                'Guardian Email',
                '监护人姓名 / Guardian Name',
                '监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)',
                '总平均 / Total Average',
                'Status (APPROVED, REJECTED, SHORLISTED)',
                '地址 / Address',
                '就读小学 / Primary School',
                '宿舍 / Hostel  (TRUE, FALSE)',
                '兄弟姐妹 / Have Siblings  (TRUE, FALSE)',
                '膳食 / Dietary Restrictions (NONE, VEGETARIAN)',
                '健康问题 / Health Concern',
                '外藉生/Foreigner  (TRUE, FALSE)',
                '操行 / Conduct',
                '备注 /Remarks',
            ];

            expect($report_data['subject_lists'])->toHaveCount(4);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $expected_headers[] = $s->getTranslation('name', 'zh') . ' / ' . $s->getTranslation('name', 'en');
            }

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data'][0]['number']);
            $view->assertSee($report_data['data'][0]['exam_slip_number']);
            $view->assertSee($report_data['data'][0]['student_name_en']);
            $view->assertSee($report_data['data'][0]['student_name_zh']);
            $view->assertSee($report_data['data'][0]['nric']);
            $view->assertSee($report_data['data'][0]['passport_number']);
            $view->assertSee($report_data['data'][0]['religion']);
            $view->assertSee($report_data['data'][0]['gender']);
            $view->assertSee($report_data['data'][0]['guardian_phone_number']);
            $view->assertSee($report_data['data'][0]['guardian_email']);
            $view->assertSee($report_data['data'][0]['guardian_name']);
            $view->assertSee($report_data['data'][0]['guardian_type']);
            $view->assertSee(number_format($report_data['data'][0]['total_average']), 2);
            $view->assertSee($report_data['data'][0]['status']);
            $view->assertSee($report_data['data'][0]['address']);
            $view->assertSee($report_data['data'][0]['primary_school']);
            $view->assertSee($report_data['data'][0]['hostel']);
            $view->assertSee($report_data['data'][0]['have_siblings']);
            $view->assertSee($report_data['data'][0]['dietary_restriction']);
            $view->assertSee($report_data['data'][0]['health_concern']);
            $view->assertSee($report_data['data'][0]['foreigner']);
            $view->assertSee($report_data['data'][0]['conduct']);
            $view->assertSee($report_data['data'][0]['remarks']);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $view->assertSee($report_data['data'][0][$code]);
            }

            return true;
        }
    );
})->with('enrollment session');

test('downloadPostPaymentTemplate - test excel content', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $report_data = app()->make(EnrollmentService::class)->getTemplateData($payload['enrollment_session_id'], true);

    $report_view_name = 'templates.enrollment-template-post-payment';
    $file_name = 'enrollment-template';

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = [
                'No',
                'Student Name',
                '身份证号码 / IC Number',
                'Passport Number',
                '宿舍 / Hostel  (TRUE, FALSE)',
                '总平均 / Total Average',
                'Status (APPROVED, REJECTED, SHORLISTED)',
            ];

            foreach ($report_data['subject_lists'] as $code => $s) {
                $expected_headers[] = $s->getTranslation('name', 'zh') . ' / ' . $s->getTranslation('name', 'en');
            }

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data'][0]['number']);
            $view->assertSee($report_data['data'][0]['student_name_en']);
            $view->assertSee($report_data['data'][0]['nric']);
            $view->assertSee($report_data['data'][0]['passport_number']);
            $view->assertSee($report_data['data'][0]['hostel']);
            $view->assertSee(number_format($report_data['data'][0]['total_average']), 2);
            $view->assertSee($report_data['data'][0]['status']);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $view->assertSee($report_data['data'][0][$code]);
            }

            return true;
        }
    );
})->with('enrollment session');


test('importTemplateValidation success', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => '001',
            'name->en' => 'English',
        ],
        [
            'code' => '002',
            'name->en' => 'Mathematics',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    // convert Enrollment Template Excel to collection
    $row1 = [
        '1', 'R6566', 'John Doe', '约翰·多', '110113101982', 'A1234567', 'Christian', 'MALE', '60*********', '<EMAIL>', 'Frank Reynold', 'FATHER',
        '82.05', '80.05', '81.05', 'INVALID_STATUS', '123 Sample Street', 'Pin Hwa', 'TRUE', 'TRUE', 'VEGETARIAN', 'Adhd', 'TRUE', 'A+', 'remarks',
    ];

    $row2 = [
        '2', 'R6566', 'John Cena', '约翰·多', '110113101982', 'A1234568', 'Buddhism', 'MALE', '60123456790', '<EMAIL>', 'Dee Reynold', 'MOTHER',
        '83.05', '81.05', '82.05', 'REJECTED', '555 Sample Street', 'SK KB', 'TRUE', 'TRUE', 'VEGETARIAN', 'Adhd', 'TRUE', 'B', 'yohoo',
    ];

    $header = [
        'No', '考生编号 / Exam Slip', 'Student Name', '学生姓名', '身份证号码 / IC Number', 'Passport Number', 'Religion', '性别 / Gender (MALE, FEMALE)',
        '监护人电话号码 / Guardian Phone Number', 'Guardian Email', '监护人姓名 / Guardian Name', '监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)',
        '总平均 / Total Average', 'English Language', 'Mathematics', 'Status (APPROVED, REJECTED, SHORLISTED)', '地址 / Address',
        '就读小学 / Primary School', '宿舍 / Hostel (TRUE, FALSE)', '兄弟姐妹 / Have Siblings (TRUE, FALSE)', '膳食 / Dietary Restrictions (NONE, VEGETARIAN)',
        '健康问题 / Health Concern', '外藉生/Foreigner (TRUE, FALSE)', '操行 / Conduct', '备注 /Remarks',
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'file' => $test_excel,
    ];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['data'])->toHaveCount(2)
        ->and($response['data']['data'])->toEqual([
            [
                'number' => '1',
                'exam_slip_number' => 'R6566',
                'student_name_en' => 'John Doe',
                'student_name_zh' => '约翰·多',
                'nric' => '110113101982',
                'passport_number' => 'A1234567',
                'religion' => 'Christian',
                'gender' => 'MALE',
                'guardian_phone_number' => '+60*********',
                'guardian_email' => '<EMAIL>',
                'guardian_name' => 'Frank Reynold',
                'guardian_type' => 'FATHER',
                'total_average' => '82.05',
                '001' => '80.05',
                '002' => '81.05',
                'status' => 'INVALID_STATUS',
                'address' => '123 Sample Street',
                'primary_school' => 'Pin Hwa',
                'hostel' => true,
                'have_siblings' => true,
                'dietary_restriction' => 'VEGETARIAN',
                'health_concern' => 'Adhd',
                'foreigner' => true,
                'conduct' => 'A+',
                'remarks' => 'remarks',
                'errors' => [
                    'status' => [
                        'Status must be SHORTLISTED, REJECTED or APPROVED.'
                    ]
                ],
                'warnings' => [
                    'religion' => [
                        'Religion Christian does not exist in the database. It will be created.'
                    ],
                    'primary_school' => [
                        'School Pin Hwa does not exist in the database. It will be created.'
                    ],
                    'health_concern' => [
                        'Health concern Adhd does not exist in the database. It will be created.'
                    ]
                ]
            ],
            [
                'number' => '2',
                'exam_slip_number' => 'R6566',
                'student_name_en' => 'John Cena',
                'student_name_zh' => '约翰·多',
                'nric' => '110113101982',
                'passport_number' => 'A1234568',
                'religion' => 'Buddhism',
                'gender' => 'MALE',
                'guardian_phone_number' => '+60123456790',
                'guardian_email' => '<EMAIL>',
                'guardian_name' => 'Dee Reynold',
                'guardian_type' => 'MOTHER',
                'total_average' => '83.05',
                '001' => '81.05',
                '002' => '82.05',
                'status' => 'REJECTED',
                'address' => '555 Sample Street',
                'primary_school' => 'SK KB',
                'hostel' => true,
                'have_siblings' => true,
                'dietary_restriction' => 'VEGETARIAN',
                'health_concern' => 'Adhd',
                'foreigner' => true,
                'conduct' => 'B',
                'remarks' => 'yohoo',
                'errors' => [
                    'nric' => [
                        'NRIC is duplicated.'
                    ],
                    'exam_slip_number' => [
                        'Exam slip number is duplicated.'
                    ]
                ],
                'warnings' => [
                    'religion' => [
                        'Religion Buddhism does not exist in the database. It will be created.'
                    ],
                    'primary_school' => [
                        'School SK KB does not exist in the database. It will be created.'
                    ],
                    'health_concern' => [
                        'Health concern Adhd does not exist in the database. It will be created.'
                    ]
                ]
            ],
        ])
        ->and($response['data']['subject_lists'])->toHaveCount(2)
        ->and($response['data']['subject_lists'])->toHaveKey('001')
        ->and($response['data']['subject_lists'])->toHaveKey('002')
        ->and($response['data']['error_count'])->toBe(3)
        ->and($response['data']['warning_count'])->toBe(6);
});

test('importTemplateValidation failed because validation error', function () {
    /**
     * NO PAYLOAD
     */
    $payload = [];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field is required.'
            ],
            'enrollment_session_id' => [
                'The enrollment session id field is required.'
            ],
        ],
        'data' => null
    ]);

    /**
     * INVALID FILE TYPE NOT EXCEL && INVALID ENROLLMENT SESSION ID
     */
    $payload = [
        'enrollment_session_id' => 99999999,
        'file' => UploadedFile::fake()->create('file.png', 500),
    ];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field must be a file of type: xlsx.'
            ],
            'enrollment_session_id' => [
                'The selected enrollment session id is invalid.'
            ],
        ],
        'data' => null
    ]);
});


test('bulkSaveImportedData', function () {
    // Arrange
    $enrollment_session = EnrollmentSession::factory()->create();
    $subject = Subject::factory()->create(['code' => 'MATH']);
    $enrollment_session->examSubjects()->attach([$subject->id]);

    $dummy_enrollments = [
        [
            'student_name_en' => 'John Smith',
            'student_name_zh' => 'John Smith',
            'nric' => '990101123456',
            'passport_number' => '',
            'gender' => 'MALE',
            'address' => 'Test address',
            'hostel' => false,
            'have_siblings' => false,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
            'conduct' => 'A',
            'status' => 'APPROVED',
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'PRIMARY SCHOOL',
            'guardian_name' => 'Test Guardian',
            'guardian_phone_number' => '*********',
            'guardian_email' => '<EMAIL>',
            'guardian_type' => 'GUARDIAN',
            'exam_slip_number' => 'TEST123',
            'total_average' => '80.05',
            'MATH' => '80.05',
        ]
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-imported-data'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->enrollmentTableName, 1);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('enrollment_exams', 1);
    $this->assertDatabaseCount('enrollment_exam_marks', 1);
});

test('bulkSaveImportedData : error validation, nothing created', function () {
    // Arrange
    $enrollment_session = EnrollmentSession::factory()->create();

    $dummy_enrollments = [
        [
            'student_name_en' => 'John Smith',
            'student_name_zh' => 'John Smith',
            'nric' => '990101123456',
            'passport_number' => '',
            'gender' => 'MALEXX',
            'address' => 'Test address',
            'hostel' => false,
            'have_siblings' => false,
            'dietary_restriction' => 'NONEs',
            'foreigner' => false,
            'conduct' => 'A',
            'status' => 'APPROVED',
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'PRIMARY SCHOOL',
            'guardian_name' => 'Test Guardian',
            'guardian_phone_number' => '*********',
            'guardian_email' => '<EMAIL>',
            'guardian_type' => 'GUARDIAN',
            'exam_slip_number' => 'TEST123',
            'total_average' => '80.05',
            'MATH' => '80.05',
        ]
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-imported-data'), $payload)->json();

    expect($response['code'])->toEqual(422)
        ->and($response['data']['data'])->toHaveCount(1)
        ->and($response['data']['data'][0]['errors'])->toHaveCount(1)
        ->and($response['data']['data'][0]['warnings'])->toHaveCount(3);

    $this->assertDatabaseCount($this->enrollmentTableName, 0);
    $this->assertDatabaseCount('guardians', 0);
    $this->assertDatabaseCount('guardian_student', 0);
    $this->assertDatabaseCount('enrollment_exams', 0);
    $this->assertDatabaseCount('enrollment_exam_marks', 0);
});

test('show (ADMIN) enrollment with fees_to_be_paid', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $product_2 = Product::factory()->create([
        'name->en' => 'Hostel Fee',
        'unit_price' => 2000,
    ]);

    $temp_fee_assignment = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product_1->id,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product_2->id,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => $temp_fee_assignment,
    ]);

    // MALAYSIAN NON HOSTEL STUDENT
    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'is_foreigner' => false,
        'is_hostel' => false,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $enrollment->id,
            'name' => $enrollment->name,
            'email' => $enrollment->email,
            'phone_number' => $enrollment->phone_number,
            'phone_number_2' => $enrollment->phone_number_2,
            'nric' => $enrollment->nric,
            'passport_number' => $enrollment->passport_number,
            'admission_year' => $enrollment->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($enrollment->admissionGrade)),
            'join_date' => $enrollment->join_date,
            'leave_date' => $enrollment->leave_date,
            'leave_status' => $enrollment->leave_status,
            'student_number' => $enrollment->student_number,
            'birthplace' => $enrollment->birthplace,
            'nationality' => resourceToArray(new CountryResource($enrollment->nationality)),
            'date_of_birth' => $enrollment->date_of_birth,
            'gender' => $enrollment->gender->value,
            'birth_cert_number' => $enrollment->birth_cert_number,
            'race' => resourceToArray(new RaceResource($enrollment->race)),
            'religion' => resourceToArray(new ReligionResource($enrollment->religion)),
            'address' => $enrollment->address,
            'postal_code' => $enrollment->postal_code,
            'city' => $enrollment->city,
            'state' => resourceToArray(new StateResource($enrollment->state)),
            'country' => resourceToArray(new CountryResource($enrollment->country)),
            'address_2' => $enrollment->address_2,
            'is_hostel' => $enrollment->is_hostel,
            'is_active' => $enrollment->is_active,
            'remarks' => $enrollment->remarks,
            'custom_field' => $enrollment->custom_field,
            'dietary_restriction' => $enrollment->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($enrollment->healthConcern)),
            'primary_school' => resourceToArray(new SchoolResource($enrollment->primarySchool)),
            'admission_type' => $enrollment->admission_type->value,
            'enrollment_status' => $enrollment->enrollment_status,
            'payment_status' => $enrollment->payment_status,
            'have_siblings' => $enrollment->have_siblings,
            'is_foreigner' => $enrollment->is_foreigner,
            'conduct' => $enrollment->conduct,
            'token' => $enrollment->token,
            'enrollment_session' => resourceToArray(new EnrollmentSessionResource($enrollment->enrollmentSession)),
            'fees_to_be_paid' => [
                [
                    // only product_1 is applicable
                    'product' => $product_1->toArray(),
                    'amount' => 1000,
                    'period' => '2024-01-01',
                    'product_id' => $product_1->id,
                ],
            ]
        ]);

    // MALAYSIAN HOSTEL STUDENT
    $enrollment->update([
        'nationality_id' => $malaysia->id,
        'is_hostel' => true,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toEqual([
            [
                'product' => $product_1->toArray(),
                'amount' => 1000,
                'period' => '2024-01-01',
                'product_id' => $product_1->id,
            ],
            [
                'product' => $product_2->toArray(),
                'amount' => 2000,
                'period' => '2024-02-01',
                'product_id' => $product_2->id,
            ]
        ]);


    // no fee to be paid
    $enrollment_session->update([
        'fee_assignment_settings' => null,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toEqual([]);
});

test('show (USER) enrollment with authorization', function () {
    // Create enrollment user (guardian/parent)
    $enrollmentUser = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Guardian User', 'zh' => '监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0*********',
        'nric' => '990101123456'
    ]);

    // Create another enrollment user (unauthorized)
    $unauthorizedUser = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Other Guardian', 'zh' => '其他监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0987654321',
        'nric' => '880202234567'
    ]);

    // Create enrollment session
    $enrollmentSession = EnrollmentSession::factory()->create([
        'name' => 'Test Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST2024',
        'is_active' => true,
    ]);

    // Create enrollment belonging to the first user
    $enrollment = Enrollment::factory()->create([
        'enrollment_user_id' => $enrollmentUser->id,
        'enrollment_session_id' => $enrollmentSession->id,
        'name' => ['en' => 'Student Name', 'zh' => '学生姓名'],
        'email' => '<EMAIL>',
        'nric' => '050101123456',
    ]);

    // Test 1: Successful access by authorized user
    Sanctum::actingAs($enrollmentUser, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveKey('id', $enrollment->id)
        ->and($response['data'])->toHaveKey('name', $enrollment->name)
        ->and($response['data'])->toHaveKey('email', $enrollment->email)
        ->and($response['data'])->toHaveKey('nric', $enrollment->nric)
        ->and($response['data'])->toHaveKey('enrollment_session')
        ->and($response['data'])->toHaveKey('fees_to_be_paid');

    // Test 2: Unauthorized access by different user
    Sanctum::actingAs($unauthorizedUser, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(403)
        ->and($response['error'])->toBe(__('api.common.unauthorized'));

    // Test 3: Non-existent enrollment
    Sanctum::actingAs($enrollmentUser, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => 99999]));

    expect($response->status())->toBe(404);
});

test('show (USER) enrollment with fees calculation', function () {
    // Create enrollment user
    $enrollmentUser = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Guardian User', 'zh' => '监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0*********',
        'nric' => '990101123456'
    ]);

    // Create products for fee calculation
    $generalFee = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1500,
    ]);

    $hostelFee = Product::factory()->create([
        'name->en' => 'Hostel Fee',
        'unit_price' => 800,
    ]);

    // Create country for nationality condition
    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    // Create fee assignment settings
    $feeAssignmentSettings = [
        [
            'conditions' => null, // Apply to all students
            'outcome' => [
                'product_id' => $generalFee->id,
                'amount' => 1500,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $hostelFee->id,
                'amount' => 800,
                'period' => '2024-02-01',
            ]
        ]
    ];

    // Create enrollment session with fee settings
    $enrollmentSession = EnrollmentSession::factory()->create([
        'name' => 'Test Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST2024',
        'is_active' => true,
        'fee_assignment_settings' => $feeAssignmentSettings,
    ]);

    // Create enrollment - Malaysian hostel student
    $enrollment = Enrollment::factory()->create([
        'enrollment_user_id' => $enrollmentUser->id,
        'enrollment_session_id' => $enrollmentSession->id,
        'nationality_id' => $malaysia->id,
        'is_hostel' => true,
        'name' => ['en' => 'Malaysian Student', 'zh' => '马来西亚学生'],
        'email' => '<EMAIL>',
        'nric' => '050101123456',
    ]);

    // Authenticate as enrollment user
    Sanctum::actingAs($enrollmentUser, [], 'enrollment_user');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toHaveCount(2)
        ->and($response['data']['fees_to_be_paid'][0])->toMatchArray([
            'product_id' => $generalFee->id,
            'amount' => 1500,
            'period' => '2024-01-01',
        ])
        ->and($response['data']['fees_to_be_paid'][1])->toMatchArray([
            'product_id' => $hostelFee->id,
            'amount' => 800,
            'period' => '2024-02-01',
        ]);

    // Test non-hostel student (should only get general fee)
    $enrollment->update(['is_hostel' => false]);

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toHaveCount(1)
        ->and($response['data']['fees_to_be_paid'][0])->toMatchArray([
            'product_id' => $generalFee->id,
            'amount' => 1500,
            'period' => '2024-01-01',
        ]);
});
