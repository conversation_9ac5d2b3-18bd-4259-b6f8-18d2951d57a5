<?php

use App\Enums\EnrollmentAction;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\MarriedStatus;
use App\Models\Config;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Education;
use App\Models\Employee;
use App\Models\Enrollment;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Media;
use App\Models\PaymentMethod;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\TemporaryUpload;
use App\Models\User;
use App\Services\ConfigService;
use App\Services\EnrollmentService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);
    $this->seedAccountingData();

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456')
    ]);

    // Only guardian or employee able to access enrollment page -> EnrollmentController middleware
    $employee = Employee::factory()->create([
        'user_id' => $user->id
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeName = 'enrollments';

    $this->enrollmentTableName = 'enrollments';

    $this->payexBaseUrl = config('services.payment_gateway.payex.base_url');
    $this->payexAuthUrl = $this->payexBaseUrl.'/'.config('services.payment_gateway.payex.auth_url');

    config(['media-library.disk_name' => 'local']);
});

test('index : get enrollments as a SuperAdmin and able to see all', function () {
    $enrollments = Enrollment::factory(10)->create();

    $first_enrollment = $enrollments->first();
    $last_enrollment = $enrollments->last();

    $response = $this->getJson(route($this->routeName.'.index'))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(10);

    $collected_response = collect($response['data'])->keyBy('id');

    $response_first_enrollment = $collected_response[$first_enrollment->id];
    $response_last_enrollment = $collected_response[$last_enrollment->id];

    expect($response_first_enrollment)
        ->toHaveKey('admission_year', $first_enrollment->admission_year)
        ->toHaveKey('admission_grade.id', $first_enrollment->admission_grade_id)
        ->toHaveKey('student_name', $first_enrollment->student_name)
        ->toHaveKey('nric_no', $first_enrollment->nric_no)
        ->toHaveKey('passport_no', $first_enrollment->passport_no)
        ->toHaveKey('birthplace.id', $first_enrollment->birthplace_id)
        ->toHaveKey('nationality.id', $first_enrollment->nationality_id)
        ->toHaveKey('date_of_birth', $first_enrollment->date_of_birth)
        ->toHaveKey('gender', $first_enrollment->gender->value)
        ->toHaveKey('birth_cert_no', $first_enrollment->birth_cert_no)
        ->toHaveKey('race.id', $first_enrollment->race_id)
        ->toHaveKey('religion.id', $first_enrollment->religion_id)
        ->toHaveKey('phone_no', $first_enrollment->phone_no)
        ->toHaveKey('email', $first_enrollment->email)
        ->toHaveKey('address', $first_enrollment->address)
        ->toHaveKey('postal_code', $first_enrollment->postal_code)
        ->toHaveKey('city', $first_enrollment->city)
        ->toHaveKey('state.id', $first_enrollment->state_id)
        ->toHaveKey('country.id', $first_enrollment->country_id)
        ->toHaveKey('remarks', $first_enrollment->remarks)
        ->toHaveKey('status', $first_enrollment->status->value)
        ->toHaveKey('step', $first_enrollment->step)
        ->toHaveKey('created_by.id', $first_enrollment->created_by);

    expect($response_last_enrollment)
        ->toHaveKey('admission_year', $last_enrollment->admission_year)
        ->toHaveKey('admission_grade.id', $last_enrollment->admission_grade_id)
        ->toHaveKey('student_name', $last_enrollment->student_name)
        ->toHaveKey('nric_no', $last_enrollment->nric_no)
        ->toHaveKey('passport_no', $last_enrollment->passport_no)
        ->toHaveKey('birthplace.id', $last_enrollment->birthplace_id)
        ->toHaveKey('nationality.id', $last_enrollment->nationality_id)
        ->toHaveKey('date_of_birth', $last_enrollment->date_of_birth)
        ->toHaveKey('gender', $last_enrollment->gender->value)
        ->toHaveKey('birth_cert_no', $last_enrollment->birth_cert_no)
        ->toHaveKey('race.id', $last_enrollment->race_id)
        ->toHaveKey('religion.id', $last_enrollment->religion_id)
        ->toHaveKey('phone_no', $last_enrollment->phone_no)
        ->toHaveKey('email', $last_enrollment->email)
        ->toHaveKey('address', $last_enrollment->address)
        ->toHaveKey('postal_code', $last_enrollment->postal_code)
        ->toHaveKey('city', $last_enrollment->city)
        ->toHaveKey('state.id', $last_enrollment->state_id)
        ->toHaveKey('country.id', $last_enrollment->country_id)
        ->toHaveKey('remarks', $last_enrollment->remarks)
        ->toHaveKey('status', $last_enrollment->status->value)
        ->toHaveKey('step', $last_enrollment->step)
        ->toHaveKey('created_by.id', $last_enrollment->created_by);
});

test('index : get enrollments as a SuperAdmin by filtering', function () {
    // filter by student_name
    $unrelated_name = Enrollment::factory()->create([
        'student_name->en' => 'No Name'
    ]);

    $enrollment = Enrollment::factory()->create([
        'student_name->en' => 'Some English Name'
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['student_name' => 'Some English Name']))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $enrollment->id);

    // filter by created_by
    Enrollment::factory()->create();

    $guardian = Guardian::factory()->create();

    $guardian_enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['created_by' => $guardian->user_id]))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $guardian_enrollment->id);

    // filter by admission_grade_id
    $grade = Grade::factory()->create();

    $grade_enrollment = Enrollment::factory()->create([
        'admission_grade_id' => $grade->id
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['admission_grade_id' => $grade->id]))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $grade_enrollment->id);

    // filter by birthplace_id
    $country = Country::factory()->create();

    $country_enrollment = Enrollment::factory()->create([
        'birthplace_id' => $country->id
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['birthplace_id' => $country->id]))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $country_enrollment->id);

    // filter by race_id
    $race = Race::factory()->create();

    $race_enrollment = Enrollment::factory()->create([
        'race_id' => $race->id
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['race_id' => $race->id]))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $race_enrollment->id);

    // filter by religion_id
    $religion = Religion::factory()->create();

    $religion_enrollment = Enrollment::factory()->create([
        'religion_id' => $religion->id
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['religion_id' => $religion->id]))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $religion_enrollment->id);

    // filter by birth_cert_no
    $birth_cert_no_enrollment = Enrollment::factory()->create([
        'birth_cert_no' => '1220'
    ]);

    $response = $this->getJson(route($this->routeName.'.index', ['birth_cert_no' => '1220']))->json();

    expect($response['data'])->toHaveCount(1)->toHaveKey('0.id', $birth_cert_no_enrollment->id);
});

test('index : get enrollments as a Guardian, not able to see other enrollment not under own', function () {
    Enrollment::factory(10)->create();

    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $guardian_enrollments = Enrollment::factory(2)->create([
        'created_by' => $guardian->user_id
    ]);

    $this->assertDatabaseCount('enrollments', 12);

    $first_enrollment = $guardian_enrollments->first();
    $last_enrollment = $guardian_enrollments->last();

    $response = $this->getJson(route($this->routeName.'.index'))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2);

    $collected_response = collect($response['data'])->keyBy('id');

    $response_first_enrollment = $collected_response[$first_enrollment->id];
    $response_last_enrollment = $collected_response[$last_enrollment->id];

    expect($response_first_enrollment)
        ->toHaveKey('id', $first_enrollment->id)
        ->toHaveKey('created_by.id', $first_enrollment->created_by);

    expect($response_last_enrollment)
        ->toHaveKey('id', $last_enrollment->id)
        ->toHaveKey('created_by.id', $last_enrollment->created_by);
});

test('index : get all', function () {
    $enrollments = Enrollment::factory(10)->create();

    $first_enrollment = $enrollments->first();
    $last_enrollment = $enrollments->last();

    $response = $this->getJson(route($this->routeName.'.index'), ['per_page' => -1])->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(10);

    $collected_response = collect($response['data'])->keyBy('id');

    $response_first_enrollment = $collected_response[$first_enrollment->id];
    $response_last_enrollment = $collected_response[$last_enrollment->id];

    expect($response_first_enrollment)
        ->toHaveKey('admission_year', $first_enrollment->admission_year)
        ->toHaveKey('admission_grade.id', $first_enrollment->admission_grade_id)
        ->toHaveKey('student_name', $first_enrollment->student_name)
        ->toHaveKey('nric_no', $first_enrollment->nric_no)
        ->toHaveKey('passport_no', $first_enrollment->passport_no)
        ->toHaveKey('birthplace.id', $first_enrollment->birthplace_id)
        ->toHaveKey('nationality.id', $first_enrollment->nationality_id)
        ->toHaveKey('date_of_birth', $first_enrollment->date_of_birth)
        ->toHaveKey('gender', $first_enrollment->gender->value)
        ->toHaveKey('birth_cert_no', $first_enrollment->birth_cert_no)
        ->toHaveKey('race.id', $first_enrollment->race_id)
        ->toHaveKey('religion.id', $first_enrollment->religion_id)
        ->toHaveKey('phone_no', $first_enrollment->phone_no)
        ->toHaveKey('email', $first_enrollment->email)
        ->toHaveKey('address', $first_enrollment->address)
        ->toHaveKey('postal_code', $first_enrollment->postal_code)
        ->toHaveKey('city', $first_enrollment->city)
        ->toHaveKey('state.id', $first_enrollment->state_id)
        ->toHaveKey('country.id', $first_enrollment->country_id)
        ->toHaveKey('remarks', $first_enrollment->remarks)
        ->toHaveKey('status', $first_enrollment->status->value)
        ->toHaveKey('step', $first_enrollment->step)
        ->toHaveKey('created_by.id', $first_enrollment->created_by);

    expect($response_last_enrollment)
        ->toHaveKey('admission_year', $last_enrollment->admission_year)
        ->toHaveKey('admission_grade.id', $last_enrollment->admission_grade_id)
        ->toHaveKey('student_name', $last_enrollment->student_name)
        ->toHaveKey('nric_no', $last_enrollment->nric_no)
        ->toHaveKey('passport_no', $last_enrollment->passport_no)
        ->toHaveKey('birthplace.id', $last_enrollment->birthplace_id)
        ->toHaveKey('nationality.id', $last_enrollment->nationality_id)
        ->toHaveKey('date_of_birth', $last_enrollment->date_of_birth)
        ->toHaveKey('gender', $last_enrollment->gender->value)
        ->toHaveKey('birth_cert_no', $last_enrollment->birth_cert_no)
        ->toHaveKey('race.id', $last_enrollment->race_id)
        ->toHaveKey('religion.id', $last_enrollment->religion_id)
        ->toHaveKey('phone_no', $last_enrollment->phone_no)
        ->toHaveKey('email', $last_enrollment->email)
        ->toHaveKey('address', $last_enrollment->address)
        ->toHaveKey('postal_code', $last_enrollment->postal_code)
        ->toHaveKey('city', $last_enrollment->city)
        ->toHaveKey('state.id', $last_enrollment->state_id)
        ->toHaveKey('country.id', $last_enrollment->country_id)
        ->toHaveKey('remarks', $last_enrollment->remarks)
        ->toHaveKey('status', $last_enrollment->status->value)
        ->toHaveKey('step', $last_enrollment->step)
        ->toHaveKey('created_by.id', $last_enrollment->created_by);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollment = Enrollment::factory()->create();

        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->once()
            ->andReturn(new LengthAwarePaginator([$enrollment], 1, 1));
    });

    $response = $this->getJson(route($this->routeName . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollments = Enrollment::factory(2)->create();

        $mock->shouldReceive('getAllEnrollments')->once()->andReturn($enrollments);
    });

    $response = $this->getJson(route($this->routeName . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('show : enrollment with no guardians, no files', function () {
    $enrollment = Enrollment::factory()->create();

    $response = $this->getJson(route($this->routeName.'.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('admission_year', $enrollment->admission_year)
        ->toHaveKey('admission_grade.id', $enrollment->admission_grade_id)
        ->toHaveKey('student_name', $enrollment->student_name)
        ->toHaveKey('nric_no', $enrollment->nric_no)
        ->toHaveKey('passport_no', $enrollment->passport_no)
        ->toHaveKey('birthplace.id', $enrollment->birthplace_id)
        ->toHaveKey('nationality.id', $enrollment->nationality_id)
        ->toHaveKey('date_of_birth', $enrollment->date_of_birth)
        ->toHaveKey('gender', $enrollment->gender->value)
        ->toHaveKey('birth_cert_no', $enrollment->birth_cert_no)
        ->toHaveKey('race.id', $enrollment->race_id)
        ->toHaveKey('religion.id', $enrollment->religion_id)
        ->toHaveKey('phone_no', $enrollment->phone_no)
        ->toHaveKey('email', $enrollment->email)
        ->toHaveKey('address', $enrollment->address)
        ->toHaveKey('postal_code', $enrollment->postal_code)
        ->toHaveKey('city', $enrollment->city)
        ->toHaveKey('state.id', $enrollment->state_id)
        ->toHaveKey('country.id', $enrollment->country_id)
        ->toHaveKey('remarks', $enrollment->remarks)
        ->toHaveKey('status', $enrollment->status->value)
        ->toHaveKey('step', $enrollment->step)
        ->toHaveKey('created_by.id', $enrollment->created_by)
        ->toHaveKey('guardians', [])
        ->toHaveKey('nric_files', [])
        ->toHaveKey('passport_files', [])
        ->toHaveKey('translations', $enrollment->translations);
});

test('show : enrollment with 2 guardians attached', function () {
    $enrollment = Enrollment::factory()->create();

    $guardianFather = Guardian::factory()->create();
    $studentGuardianFather = GuardianStudent::factory()->create([
        'guardian_id' => $guardianFather->id,
        'studenable_id' => $enrollment->id,
        'studenable_type' => get_class($enrollment),
        'type' => GuardianType::FATHER->value,
    ]);

    $guardianMother = Guardian::factory()->create();
    $studentGuardianMother = GuardianStudent::factory()->create([
        'guardian_id' => $guardianMother->id,
        'studenable_id' => $enrollment->id,
        'studenable_type' => get_class($enrollment),
        'type' => GuardianType::MOTHER->value,
    ]);

    $response = $this->getJson(route($this->routeName.'.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and(count($response['data']['guardians']))->toEqual(2);

    $collected_response = collect($response['data']['guardians'])->keyBy('id');

    $response_first_guardian = $collected_response[$guardianFather->id];
    $response_last_guardian = $collected_response[$guardianMother->id];

    expect($response_first_guardian)
        ->toHaveKey('id', $guardianFather->id)
        ->toHaveKey('type', $studentGuardianFather->type->value)
        ->toHaveKey('name', $guardianFather->name)
        ->toHaveKey('translations.name.en', $guardianFather->name)
        ->toHaveKey('nric', $guardianFather->nric)
        ->toHaveKey('passport_number', $guardianFather->passport_number)
        ->toHaveKey('phone_number', $guardianFather->phone_number)
        ->toHaveKey('email', $guardianFather->email)
        ->toHaveKey('nationality.id', $guardianFather->nationality_id)
        ->toHaveKey('race.id', $guardianFather->race_id)
        ->toHaveKey('religion.id', $guardianFather->religion_id);

    expect($response_last_guardian)
        ->toHaveKey('id', $guardianMother->id)
        ->toHaveKey('type', $studentGuardianMother->type->value)
        ->toHaveKey('name', $guardianMother->name)
        ->toHaveKey('translations.name.en', $guardianMother->name)
        ->toHaveKey('nric', $guardianMother->nric)
        ->toHaveKey('passport_number', $guardianMother->passport_number)
        ->toHaveKey('phone_number', $guardianMother->phone_number)
        ->toHaveKey('email', $guardianMother->email)
        ->toHaveKey('nationality.id', $guardianMother->nationality_id)
        ->toHaveKey('race.id', $guardianMother->race_id)
        ->toHaveKey('religion.id', $guardianMother->religion_id);
});

test('show : enrollment with 2 files attached', function () {
    $enrollment = Enrollment::factory()->create();

    $file_1 = UploadedFile::fake()->create('file1.png', 500);
    $file_2 = UploadedFile::fake()->create('file2.png', 500);

    $enrollment->addMedia($file_1)->toMediaCollection(Enrollment::TYPE_FILE_NRIC);
    $enrollment->addMedia($file_2)->toMediaCollection(Enrollment::TYPE_FILE_PASSPORT);

    $nric_enrollment_media = $enrollment->getMedia(Enrollment::TYPE_FILE_NRIC)->first();
    $passport_enrollment_media = $enrollment->getMedia(Enrollment::TYPE_FILE_PASSPORT)->first();

    $response = $this->getJson(route($this->routeName.'.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['nric_files'])->toHaveCount(1)
        ->and($response['data']['passport_files'])->toHaveCount(1);

    expect(Arr::first($response['data']['nric_files']))->toMatchArray([
        'id' => $nric_enrollment_media->id,
        'uuid' => $nric_enrollment_media->uuid,
        'url' => $nric_enrollment_media->getTemporaryUrl(now()),
    ]);

    expect(Arr::first($response['data']['passport_files']))->toMatchArray([
        'id' => $passport_enrollment_media->id,
        'uuid' => $passport_enrollment_media->uuid,
        'url' => $passport_enrollment_media->getTemporaryUrl(now()),
    ]);
});

test('create success : as citizen', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $grade = Grade::factory()->create();

    $payload = [
        'admission_year' => 2024,
        'admission_grade_id' => $grade->id,
        'student_name' => [
            'en' => 'John Jones',
            'zh' => '初二',
        ],
        'nric_no' => '************',
    ];

    $response = $this->postJson(route($this->routeName.'.create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('admission_year', $payload['admission_year'])
        ->toHaveKey('admission_grade.id', $payload['admission_grade_id'])
        ->toHaveKey('student_name', $payload['student_name']['en'])
        ->toHaveKey('nric_no', $payload['nric_no'])
        ->toHaveKey('passport_no', null)
        ->toHaveKey('created_by.id', $guardian->user_id)
        ->toHaveKey('status', EnrollmentStatus::DRAFT->value)
        ->toHaveKey('step', Enrollment::STEP_STUDENT_PROFILE);

    $this->assertDatabaseCount($this->enrollmentTableName, 1);

    $this->assertDatabaseHas($this->enrollmentTableName, [
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'student_name->en' => $payload['student_name']['en'],
        'student_name->zh' => $payload['student_name']['zh'],
        'nric_no' => $payload['nric_no'],
        'passport_no' => null,
        'created_by' => $guardian->user_id,
        'status' => EnrollmentStatus::DRAFT->value,
        'step' => Enrollment::STEP_STUDENT_PROFILE,
    ]);
});

test('create success : as foreigner, passport is required', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $grade = Grade::factory()->create();

    $payload = [
        'admission_year' => 2024,
        'admission_grade_id' => $grade->id,
        'student_name' => [
            'en' => 'John Jones',
            'zh' => '初二',
        ],
        'passport_no' => '#ASD1231234',
    ];

    $response = $this->postJson(route($this->routeName.'.create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('admission_year', $payload['admission_year'])
        ->toHaveKey('admission_grade.id', $payload['admission_grade_id'])
        ->toHaveKey('student_name', $payload['student_name']['en'])
        ->toHaveKey('nric_no', null)
        ->toHaveKey('passport_no', $payload['passport_no'])
        ->toHaveKey('created_by.id', $guardian->user_id)
        ->toHaveKey('status', EnrollmentStatus::DRAFT->value)
        ->toHaveKey('step', Enrollment::STEP_STUDENT_PROFILE);

    $this->assertDatabaseCount($this->enrollmentTableName, 1);

    $this->assertDatabaseHas($this->enrollmentTableName, [
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'student_name->en' => $payload['student_name']['en'],
        'student_name->zh' => $payload['student_name']['zh'],
        'nric_no' => null,
        'passport_no' => $payload['passport_no'],
        'created_by' => $guardian->user_id,
        'status' => EnrollmentStatus::DRAFT->value,
        'step' => Enrollment::STEP_STUDENT_PROFILE,
    ]);
});

test('create validation error', function () {
    expect(Enrollment::count())->toBe(0);

    $response = $this->postJson(route($this->routeName.'.create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(Enrollment::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'admission_year' => [
                    'The admission year field is required.'
                ],
                'admission_grade_id' => [
                    'The admission grade id field is required.'
                ],
                'student_name' => [
                    'The student name field is required.'
                ],
                'nric_no' => [
                    'The nric number field is required when passport number is not present.'
                ],
                'passport_no' => [
                    'The passport number field is required when nric number is not present.'
                ],
            ],
            'data' => null
        ]);
});

test('update : enrollment Step 1::STEP_STUDENT_PROFILE, action is NEXT as citizen', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
        'nric_no' => null,
        'passport_no' => null,
    ]);

    $nationality = Country::factory()->create();
    $birthplace = Country::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = $state->country;

    $current_step = Enrollment::STEP_STUDENT_PROFILE;
    $next_step = Enrollment::STEP_GUARDIAN_PROFILE;

    $payload = [
        'step' => $current_step,
        'action' => EnrollmentAction::NEXT->value,
        'student_name' => [
            'en' => 'Jon Jones',
            'zh' => 'zh Jon Jones',
        ],
        'date_of_birth' => '2000-12-25',
        'phone_no' => '0106564444',
        'email' => '<EMAIL>',
        'nationality_id' => $nationality->id,
        'nric_no' => '090909128273',
        'birth_cert_no' => '0091209011290',
        'gender' => Gender::MALE->value,
        'birthplace_id' => $birthplace->id,
        'religion_id' => $religion->id,
        'race_id' => $race->id,
        'address' => 'real address',
        'postal_code' => 'real postal_code',
        'city' => 'real city',
        'state_id' => $state->id,
        'country_id' => $country->id,
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('step', $next_step)
        ->toHaveKey('student_name', $payload['student_name']['en'])
        ->toHaveKey('date_of_birth', $payload['date_of_birth'])
        ->toHaveKey('phone_no', $payload['phone_no'])
        ->toHaveKey('email', $payload['email'])
        ->toHaveKey('nationality.id', $payload['nationality_id'])
        ->toHaveKey('nric_no', $payload['nric_no'])
        ->toHaveKey('passport_no', null)
        ->toHaveKey('birth_cert_no', $payload['birth_cert_no'])
        ->toHaveKey('gender', $payload['gender'])
        ->toHaveKey('birthplace.id', $payload['birthplace_id'])
        ->toHaveKey('religion.id', $payload['religion_id'])
        ->toHaveKey('race.id', $payload['race_id'])
        ->toHaveKey('address', $payload['address'])
        ->toHaveKey('postal_code', $payload['postal_code'])
        ->toHaveKey('city', $payload['city'])
        ->toHaveKey('state.id', $payload['state_id'])
        ->toHaveKey('country.id', $payload['country_id'])
        ->toHaveKey('status', EnrollmentStatus::DRAFT->value)
        ->toHaveKey('created_by.id', $guardian->user_id);

    $this->assertDatabaseHas($this->enrollmentTableName, [
        'id' => $enrollment->id,
        'step' => $next_step,
        'student_name->en' => $payload['student_name']['en'],
        'student_name->zh' => $payload['student_name']['zh'],
        'date_of_birth' => $payload['date_of_birth'],
        'phone_no' => $payload['phone_no'],
        'email' => $payload['email'],
        'nationality_id' => $payload['nationality_id'],
        'nric_no' => $payload['nric_no'],
        'passport_no' => null,
        'birth_cert_no' => $payload['birth_cert_no'],
        'gender' => $payload['gender'],
        'birthplace_id' => $payload['birthplace_id'],
        'religion_id' => $payload['religion_id'],
        'race_id' => $payload['race_id'],
        'address' => $payload['address'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'status' => EnrollmentStatus::DRAFT->value,
        'created_by' => $guardian->user_id,
    ]);
});

test('update : enrollment Step 1::STEP_STUDENT_PROFILE, action is NEXT as foreigner', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
        'nric_no' => null,
        'passport_no' => null,
    ]);

    $nationality = Country::factory()->create();
    $birthplace = Country::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = $state->country;

    $current_step = Enrollment::STEP_STUDENT_PROFILE;
    $next_step = Enrollment::STEP_GUARDIAN_PROFILE;

    $payload = [
        'step' => $current_step,
        'action' => EnrollmentAction::NEXT->value,
        'student_name' => [
            'en' => 'Jon Jones',
            'zh' => 'zh Jon Jones',
        ],
        'date_of_birth' => '2000-12-25',
        'phone_no' => '0106564444',
        'email' => '<EMAIL>',
        'nationality_id' => $nationality->id,
        'passport_no' => 'A324234234324',
        'birth_cert_no' => '0091209011290',
        'gender' => Gender::MALE->value,
        'birthplace_id' => $birthplace->id,
        'religion_id' => $religion->id,
        'race_id' => $race->id,
        'address' => 'real address',
        'postal_code' => 'real postal_code',
        'city' => 'real city',
        'state_id' => $state->id,
        'country_id' => $country->id,
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('step', $next_step)
        ->toHaveKey('nric_no', null)
        ->toHaveKey('passport_no', $payload['passport_no'])
        ->toHaveKey('status', EnrollmentStatus::DRAFT->value)
        ->toHaveKey('created_by.id', $guardian->user_id);

    $this->assertDatabaseHas($this->enrollmentTableName, [
        'id' => $enrollment->id,
        'step' => $next_step,
        'nric_no' => null,
        'passport_no' => $payload['passport_no'],
        'status' => EnrollmentStatus::DRAFT->value,
        'created_by' => $guardian->user_id,
    ]);
});

test('update : enrollment Step 1::STEP_STUDENT_PROFILE, action is SAVE_AS_DRAFT', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
        'nric_no' => null,
        'passport_no' => null,
    ]);

    $payload = [
        'step' => Enrollment::STEP_STUDENT_PROFILE,
        'action' => EnrollmentAction::SAVE_AS_DRAFT->value,
        'student_name' => [
            'en' => 'Jon Jones',
            'zh' => 'zh Jon Jones',
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('step', $payload['step'])
        ->toHaveKey('student_name', $payload['student_name']['en'])
        ->toHaveKey('status', EnrollmentStatus::DRAFT->value)
        ->toHaveKey('created_by.id', $guardian->user_id);
});

test('update : enrollment Step 2::STEP_GUARDIAN_PROFILE, action is NEXT', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $father_nationality = Country::factory()->create();
    $father_race = Race::factory()->create();
    $father_religion = Religion::factory()->create();

    $mother_nationality = Country::factory()->create();
    $mother_race = Race::factory()->create();
    $mother_religion = Religion::factory()->create();

    $education = Education::factory()->create();

    $payload = [
        'step' => Enrollment::STEP_GUARDIAN_PROFILE,
        'action' => EnrollmentAction::NEXT->value,
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Paul The Dad',
                    'zh' => 'zh Paul The Dad',
                ],
                'nric' => '090909128273', // Dad is Malaysian
                'passport_number' => null,
                'phone_number' => '*********',
                'email' => '<EMAIL>',
                'nationality_id' => $father_nationality->id,
                'race_id' => $father_race->id,
                'religion_id' => $father_religion->id,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test'
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Meemaw The Mom',
                    'zh' => 'zh Meemaw The Mom',
                ],
                'nric' => null,
                'passport_number' => '090909128274', // Mom is foreigner
                'phone_number' => '*********',
                'email' => '<EMAIL>',
                'nationality_id' => $mother_nationality->id,
                'race_id' => $mother_race->id,
                'religion_id' => $mother_religion->id,
            ],
            [
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Koko The Guardian',
                    'zh' => 'zh Koko The Guardian',
                ],
                'nric' => null,
                'passport_number' => null,
                'phone_number' => null,
                'email' => null,
                'nationality_id' => null,
                'race_id' => null,
                'religion_id' => null,
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('guardians.0.type', $payload['guardians'][0]['type'])
        ->toHaveKey('guardians.0.name', $payload['guardians'][0]['name']['en'])
        ->toHaveKey('guardians.0.nric', $payload['guardians'][0]['nric'])
        ->toHaveKey('guardians.0.passport_number', $payload['guardians'][0]['passport_number'])
        ->toHaveKey('guardians.0.phone_number', $payload['guardians'][0]['phone_number'])
        ->toHaveKey('guardians.0.email', $payload['guardians'][0]['email'])
        ->toHaveKey('guardians.0.nationality.id', $payload['guardians'][0]['nationality_id'])
        ->toHaveKey('guardians.0.race.id', $payload['guardians'][0]['race_id'])
        ->toHaveKey('guardians.0.religion.id', $payload['guardians'][0]['religion_id'])
        ->toHaveKey('guardians.0.education.id', $payload['guardians'][0]['education_id'])
        ->toHaveKey('guardians.0.married_status', $payload['guardians'][0]['married_status'])
        ->toHaveKey('guardians.0.occupation', $payload['guardians'][0]['occupation'])
        ->toHaveKey('guardians.0.occupation_description', $payload['guardians'][0]['occupation_description'])
        ->toHaveKey('guardians.1.type', $payload['guardians'][1]['type'])
        ->toHaveKey('guardians.1.name', $payload['guardians'][1]['name']['en'])
        ->toHaveKey('guardians.1.nric', $payload['guardians'][1]['nric'])
        ->toHaveKey('guardians.1.passport_number', $payload['guardians'][1]['passport_number'])
        ->toHaveKey('guardians.1.phone_number', $payload['guardians'][1]['phone_number'])
        ->toHaveKey('guardians.1.email', $payload['guardians'][1]['email'])
        ->toHaveKey('guardians.1.nationality.id', $payload['guardians'][1]['nationality_id'])
        ->toHaveKey('guardians.1.race.id', $payload['guardians'][1]['race_id'])
        ->toHaveKey('guardians.1.religion.id', $payload['guardians'][1]['religion_id'])
        ->toHaveKey('guardians.2.type', $payload['guardians'][2]['type'])
        ->toHaveKey('guardians.2.name', $payload['guardians'][2]['name']['en'])
        ->toHaveKey('guardians.2.nric', $payload['guardians'][2]['nric'])
        ->toHaveKey('guardians.2.passport_number', $payload['guardians'][2]['passport_number'])
        ->toHaveKey('guardians.2.phone_number', $payload['guardians'][2]['phone_number'])
        ->toHaveKey('guardians.2.email', $payload['guardians'][2]['email'])
        ->toHaveKey('guardians.2.nationality', $payload['guardians'][2]['nationality_id'])
        ->toHaveKey('guardians.2.race', $payload['guardians'][2]['race_id'])
        ->toHaveKey('guardians.2.religion', $payload['guardians'][2]['religion_id']);

    $this->assertDatabaseHas('guardian_student', [
        'type' => $payload['guardians'][0]['type'],
        'studenable_type' => Enrollment::class,
        'studenable_id' => $enrollment->id,
    ]);

    $this->assertDatabaseHas('guardians', [
        'name->en' => $payload['guardians'][0]['name']['en'],
        'name->zh' => $payload['guardians'][0]['name']['zh'],
        'nric' => $payload['guardians'][0]['nric'],
        'passport_number' => $payload['guardians'][0]['passport_number'],
        'phone_number' => $payload['guardians'][0]['phone_number'],
        'email' => $payload['guardians'][0]['email'],
        'nationality_id' => $payload['guardians'][0]['nationality_id'],
        'race_id' => $payload['guardians'][0]['race_id'],
        'religion_id' => $payload['guardians'][0]['religion_id'],
        'education_id' => $payload['guardians'][0]['education_id'],
        'married_status' => $payload['guardians'][0]['married_status'],
        'occupation' => $payload['guardians'][0]['occupation'],
        'occupation_description' => $payload['guardians'][0]['occupation_description'],
    ]);

    $this->assertDatabaseHas('guardian_student', [
        'type' => $payload['guardians'][1]['type'],
        'studenable_type' => Enrollment::class,
        'studenable_id' => $enrollment->id,
    ]);

    $this->assertDatabaseHas('guardians', [
        'name->en' => $payload['guardians'][1]['name']['en'],
        'name->zh' => $payload['guardians'][1]['name']['zh'],
        'nric' => $payload['guardians'][1]['nric'],
        'passport_number' => $payload['guardians'][1]['passport_number'],
        'phone_number' => $payload['guardians'][1]['phone_number'],
        'email' => $payload['guardians'][1]['email'],
        'nationality_id' => $payload['guardians'][1]['nationality_id'],
        'race_id' => $payload['guardians'][1]['race_id'],
        'religion_id' => $payload['guardians'][1]['religion_id'],
    ]);


    $this->assertDatabaseHas('guardian_student', [
        'type' => $payload['guardians'][2]['type'],
        'studenable_type' => Enrollment::class,
        'studenable_id' => $enrollment->id,
    ]);

    $this->assertDatabaseHas('guardians', [
        'name->en' => $payload['guardians'][2]['name']['en'],
        'name->zh' => $payload['guardians'][2]['name']['zh'],
        'nric' => $payload['guardians'][2]['nric'],
        'passport_number' => $payload['guardians'][2]['passport_number'],
        'phone_number' => $payload['guardians'][2]['phone_number'],
        'email' => $payload['guardians'][2]['email'],
        'nationality_id' => $payload['guardians'][2]['nationality_id'],
        'race_id' => $payload['guardians'][2]['race_id'],
        'religion_id' => $payload['guardians'][2]['religion_id'],
    ]);
});

test('update : enrollment Step 2::STEP_GUARDIAN_PROFILE, action is draft', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $education = Education::factory()->create();

    $payload = [
        'step' => Enrollment::STEP_GUARDIAN_PROFILE,
        'action' => EnrollmentAction::SAVE_AS_DRAFT->value,
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Paul The Dad',
                    'zh' => 'zh Paul The Dad',
                ],
                'nric' => '090909128273', // Dad is Malaysian
                'passport_number' => null,
                'phone_number' => '*********',
                'email' => '<EMAIL>',
                'nationality_id' => null,
                'race_id' => null,
                'religion_id' => null,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test'

            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Meemaw The Mom',
                    'zh' => 'zh Meemaw The Mom',
                ],
                'nric' => null,
                'passport_number' => '090909128274', // Mom is foreigner
                'phone_number' => '*********',
                'email' => '<EMAIL>',
                'nationality_id' => null,
                'race_id' => null,
                'religion_id' => null,
                'education_id' => null,
                'married_status' => null,
                'occupation' => null,
                'occupation_description' => null
            ],
            [
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Koko The Guardian',
                    'zh' => 'zh Koko The Guardian',
                ],
                'nric' => null,
                'passport_number' => null,
                'phone_number' => null,
                'email' => null,
                'nationality_id' => null,
                'race_id' => null,
                'religion_id' => null,
                'education_id' => null,
                'married_status' => null,
                'occupation' => null,
                'occupation_description' => null
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    foreach ($payload['guardians'] as $key => $guardian_data) {
        expect($response['data']['guardians'][$key])
            ->toHaveKey('type', $guardian_data['type'])
            ->toHaveKey('name', $guardian_data['name']['en'])
            ->toHaveKey('nric', $guardian_data['nric'])
            ->toHaveKey('passport_number', $guardian_data['passport_number'])
            ->toHaveKey('phone_number', $guardian_data['phone_number'])
            ->toHaveKey('email', $guardian_data['email'])
            ->toHaveKey('nationality', $guardian_data['nationality_id'])
            ->toHaveKey('race', $guardian_data['race_id'])
            ->toHaveKey('religion', $guardian_data['religion_id'])
            ->toHaveKey('married_status', $guardian_data['married_status'])
            ->toHaveKey('occupation', $guardian_data['occupation'])
            ->toHaveKey('occupation_description', $guardian_data['occupation_description']);

        if ($guardian_data['education_id']) {
            expect($response['data']['guardians'][$key])->toHaveKey('education.id', $guardian_data['education_id']);
        }

        $this->assertDatabaseHas('guardians', [
            'name->en' => $guardian_data['name']['en'],
            'name->zh' => $guardian_data['name']['zh'],
            'nric' => $guardian_data['nric'],
            'passport_number' => $guardian_data['passport_number'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nationality_id' => $guardian_data['nationality_id'],
            'race_id' => $guardian_data['race_id'],
            'religion_id' => $guardian_data['religion_id'],
            'education_id' => $guardian_data['education_id'],
            'married_status' => $guardian_data['married_status'],
            'occupation' => $guardian_data['occupation'],
            'occupation_description' => $guardian_data['occupation_description'],
        ]);

        $this->assertDatabaseHas('guardian_student', [
            'type' => $guardian_data['type'],
            'studenable_type' => Enrollment::class,
            'studenable_id' => $enrollment->id,
        ]);
    }
});


test('update : enrollment Step 3::STEP_DOCUMENT_UPLOAD - validation failed - media id not found', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $config_document_type = getConfigDocumentType();

    $payload = [
        'step' => Enrollment::STEP_DOCUMENT_UPLOAD,
        'action' => EnrollmentAction::NEXT->value,
        'documents' => [
            [
                'id' => 1,
                'name' => $config_document_type[0],
            ],
            [
                'id' => 2,
                'name' => $config_document_type[1],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'documents.0.id' => [
                __('learn.temporary_uploaded.not_found'),
            ],
            'documents.1.id' => [
                __('learn.temporary_uploaded.not_found'),
            ],
        ]);
});

test('update : enrollment Step 3::STEP_DOCUMENT_UPLOAD - validation failed - media type is not valid TemporaryUpload::class', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $config_document_type = getConfigDocumentType();

    $employee_media = createAndGetFakeMedia(Employee::factory()->create());

    $payload = [
        'step' => Enrollment::STEP_DOCUMENT_UPLOAD,
        'action' => EnrollmentAction::NEXT->value,
        'documents' => [
            [
                'id' => $employee_media->id,
                'name' => $config_document_type[0],
            ],
            [
                'id' => 2,
                'name' => $config_document_type[1],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'documents.0.id' => [
                __('learn.temporary_uploaded.invalid_file_type'),
            ],
            'documents.1.id' => [
                __('learn.temporary_uploaded.not_found'),
            ],
        ]);
});

test('update : enrollment Step 3::STEP_DOCUMENT_UPLOAD - validation failed - media is not uploaded by current user', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $config_document_type = getConfigDocumentType();

    $another_temporary_uploaded_media = createAndGetFakeMedia(TemporaryUpload::create(['uploader_id' => 9999]));

    $payload = [
        'step' => Enrollment::STEP_DOCUMENT_UPLOAD,
        'action' => EnrollmentAction::NEXT->value,
        'documents' => [
            [
                'id' => $another_temporary_uploaded_media->id,
                'name' => $config_document_type[0],
            ],
            [
                'id' => 2,
                'name' => $config_document_type[1],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'documents.0.id' => [
                __('learn.temporary_uploaded.not_owner'),
            ],
            'documents.1.id' => [
                __('learn.temporary_uploaded.not_found'),
            ],
        ]);
});

test('update : enrollment Step 3::STEP_DOCUMENT_UPLOAD - validation failed - same file is given for the enrollment', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $config_document_type = getConfigDocumentType();

    $temporary_uploaded_media = createAndGetFakeMedia(TemporaryUpload::create(['uploader_id' => $guardian->user_id]));

    $payload = [
        'step' => Enrollment::STEP_DOCUMENT_UPLOAD,
        'action' => EnrollmentAction::NEXT->value,
        'documents' => [
            [
                'id' => $temporary_uploaded_media->id,
                'name' => $config_document_type[0],
            ],
            [
                'id' => $temporary_uploaded_media->id,
                'name' => $config_document_type[1],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'documents.0.id' => [
                __('validation.distinct', ['attribute' => 'document id']),
            ],
            'documents.1.id' => [
                __('validation.distinct', ['attribute' => 'document id']),
            ],
        ]);
});

test('update : enrollment Step 3::STEP_DOCUMENT_UPLOAD success', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
    ]);

    $config_document_type = getConfigDocumentType();

    $nric_file = createAndGetFakeMedia(TemporaryUpload::create(['uploader_id' => $guardian->user_id]), $config_document_type[0]);
    $passport_file = createAndGetFakeMedia(TemporaryUpload::create(['uploader_id' => $guardian->user_id]), $config_document_type[1]);

    $payload = [
        'step' => Enrollment::STEP_DOCUMENT_UPLOAD,
        'action' => EnrollmentAction::NEXT->value,
        'documents' => [
            [
                'id' => $nric_file->id,
                'name' => $config_document_type[0],
            ],
            [
                'id' => $passport_file->id,
                'name' => $config_document_type[1],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($enrollment->getMedia('*'))->toHaveCount(2);

    $this->assertDatabaseHas('media', [
        'model_type' => Enrollment::class,
        'model_id' => $enrollment->id,
        'collection_name' => $config_document_type[0],
    ]);

    $this->assertDatabaseHas('media', [
        'model_type' => Enrollment::class,
        'model_id' => $enrollment->id,
        'collection_name' => $config_document_type[1],
    ]);

    $first_media_type = $enrollment->getFirstMedia($config_document_type[0]);
    $second_media_type = $enrollment->getFirstMedia($config_document_type[1]);

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('nric_files.0.id', $first_media_type->id)
        ->toHaveKey('nric_files.0.uuid', $first_media_type->uuid)
        ->toHaveKey('nric_files.0.url', getMediaFullUrl($first_media_type))
        ->toHaveKey('passport_files.0.id', $second_media_type->id)
        ->toHaveKey('passport_files.0.uuid', $second_media_type->uuid)
        ->toHaveKey('passport_files.0.url', getMediaFullUrl($second_media_type));
});

test('update : enrollment Step 4::STEP_TERMS_AND_CONDITIONS - status will be updated to SUBMITTED because no fee', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id
    ]);

    $current_step = Enrollment::STEP_TERMS_AND_CONDITIONS;
    $next_step = Enrollment::FINAL_STEP;

    $payload = [
        'action' => EnrollmentAction::NEXT,
        'step' => $current_step,
        'terms_and_conditions' => 1,
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('status', EnrollmentStatus::SUBMITTED->value)
        ->toHaveKey('step', $next_step);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::SUBMITTED->value,
        'step' => $next_step,
    ]);
});

test('update : enrollment Step 4::STEP_TERMS_AND_CONDITIONS - status will be updated to PENDING_PAYMENT because got fee', function () {
    // up the fee for enrollment
    Config::create([
        'key' => Config::ENROLLMENT_FEES,
        'category' => Config::CATEGORY_GENERAL,
        'value' => 10
    ]);

    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id
    ]);

    $payload = [
        'action' => EnrollmentAction::NEXT,
        'step' => Enrollment::STEP_TERMS_AND_CONDITIONS,
        'terms_and_conditions' => 1,
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveKey('id', $enrollment->id)
        ->toHaveKey('status', EnrollmentStatus::PENDING_PAYMENT->value)
        ->toHaveKey('step', Enrollment::FINAL_STEP);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT->value,
        'step' => Enrollment::FINAL_STEP,
    ]);
});

test('update : try to update at Step 4::STEP_TERMS_AND_CONDITIONS unmodifiable enrollment, validation error', function () {
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
        'status' => EnrollmentStatus::SUBMITTED,
    ]);

    $payload = [
        'action' => EnrollmentAction::NEXT,
        'step' => Enrollment::STEP_TERMS_AND_CONDITIONS,
        'terms_and_conditions' => 1,
    ];

    $response = $this->putJson(route($this->routeName.'.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual('Submitted enrollments cannot be modified.');
});

test('update : try to make payment at Step 5::STEP_PAYMENT', function () {
    $currency = Currency::factory()->malaysiaCurrency()->create();
    $guardian = Guardian::factory()->create();

    $guardian->user->assignRole('Super Admin');

    Sanctum::actingAs($guardian->user);

    $enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user_id,
        'status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);

    $payload = [
        'enrollment' => $enrollment->id,
        'customer_email' => '<EMAIL>',
        'customer_name' => 'Test',
        'payment_type' => PaymentMethod::CODE_FPX
    ];

    Config::factory()->create([
        'key' => Config::ENROLLMENT_FEES,
        'value' => 500
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    $payex_payment_url = $this->payexBaseUrl.'/'.config('services.payment_gateway.payex.payment_url');

    Http::fake([
        $this->payexAuthUrl => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $response = $this->getJson(route($this->routeName.'.makePayment', $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => $currency->code,
            'amount' => 500,
            'payment_url' => 'VALID_PAYMENT_URL',
        ]);
});


//test('upload : enrollment file, nric', function () {
//    $user_guardian = User::factory()->guardian()->create();
//
//    $user_guardian->assignRole('Super Admin');
//
//    Sanctum::actingAs($user_guardian);
//
//    $enrollment = Enrollment::factory()->create([
//        'created_by' => $user_guardian->id
//    ]);
//
//    // nric file
//    $nric_file = UploadedFile::fake()->create('nric.png', 500);
//
//    $payload = [
//        'file' => $nric_file,
//        'type' => Enrollment::TYPE_FILE_NRIC,
//    ];
//
//    $response = $this->postJson(route($this->routeName . '.upload', ['enrollment' => $enrollment->id]), $payload)->json();
//
//    $enrollment_nric = $enrollment->getMedia(Enrollment::TYPE_FILE_NRIC)->first();
//
//    expect($response)->toHaveSuccessGeneralResponse()
//        ->and($response['data']['id'])->toEqual($enrollment_nric->id)
//        ->and($response['data']['url'])->toEqual(getMediaFullUrl($enrollment_nric));
//
//    $this->assertDatabaseHas('media', [
//        'name' => 'nric',
//        'file_name' => 'nric.png',
//        'collection_name' => Enrollment::TYPE_FILE_NRIC,
//    ]);
//});
//
//test('upload : enrollment file, passport', function () {
//    $user_guardian = User::factory()->guardian()->create();
//
//    $user_guardian->assignRole('Super Admin');
//
//    Sanctum::actingAs($user_guardian);
//
//    $enrollment = Enrollment::factory()->create([
//        'created_by' => $user_guardian->id
//    ]);
//
//    // passport file
//    $passport_file = UploadedFile::fake()->create('passport.png', 500);
//
//    $payload = [
//        'file' => $passport_file,
//        'type' => Enrollment::TYPE_FILE_PASSPORT,
//    ];
//
//    $response = $this->postJson(route($this->routeName . '.upload', ['enrollment' => $enrollment->id]), $payload)->json();
//
//    $enrollment_passport = $enrollment->getMedia(Enrollment::TYPE_FILE_PASSPORT)->first();
//
//    expect($response)->toHaveSuccessGeneralResponse()
//        ->and($response['data']['id'])->toEqual($enrollment_passport->id)
//        ->and($response['data']['url'])->toEqual(getMediaFullUrl($enrollment_passport));
//
//    $this->assertDatabaseHas('media', [
//        'name' => 'passport',
//        'file_name' => 'passport.png',
//        'collection_name' => Enrollment::TYPE_FILE_PASSPORT,
//    ]);
//});
//
//test('deleteMedia : guardian delete enrollment file', function () {
//    $user_guardian = User::factory()->guardian()->create();
//
//    $user_guardian->assignRole('Super Admin');
//
//    Sanctum::actingAs($user_guardian);
//
//    $enrollment = Enrollment::factory()->create([
//        'created_by' => $user_guardian->id
//    ]);
//
//    // passport file
//    $passport_file = UploadedFile::fake()->create('passport.png', 500);
//
//    $payload = [
//        'file' => $passport_file,
//        'type' => Enrollment::TYPE_FILE_PASSPORT,
//    ];
//
//    $response = $this->postJson(route($this->routeName . '.upload', ['enrollment' => $enrollment->id]), $payload)->json();
//
//    expect($response)->toHaveSuccessGeneralResponse();
//
//    $media_id = $response['data']['id'];
//
//    // DELETE ====================== delete media
//    $response = $this->deleteJson(route($this->routeName . '.delete-media', ['enrollment' => $enrollment->id, 'media' => $media_id]), $payload)->json();
//
//    expect($response)->toHaveSuccessGeneralResponse();
//
//    $this->assertDatabaseMissing('media', [
//        'id' => $media_id
//    ]);
//});

function getConfigDocumentType(): array
{
    $config_document_type = Arr::pluck(Enrollment::TYPE_FILES, 'value');
    $config_document_type = array_merge($config_document_type, app(ConfigService::class)->getConfigValue(Config::ENROLLMENT_DOCUMENT_TYPE));

    return $config_document_type;
}

function createAndGetFakeMedia($media_owner, $collection_name = 'new'): Media
{
    $media_owner->addMedia(UploadedFile::fake()->create("{$collection_name}.png", 500))->toMediaCollection($collection_name);

    return $media_owner->getFirstMedia('*');
}
