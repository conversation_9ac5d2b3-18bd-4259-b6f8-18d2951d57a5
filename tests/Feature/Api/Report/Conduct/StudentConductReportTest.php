<?php

use App\Enums\ExportType;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\User;
use App\Services\StudentConductReportService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.student-conduct.';

    Storage::fake('s3-downloads');
});


test('studentConductReport', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $grade = Grade::factory()->create();

    $semester_class = SemesterClass::factory()->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $semester_class->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(StudentConductReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.conduct.student-conduct')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getConductReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $file_url = $this->getJson(route($this->routeNamePrefix . 'student-conduct-report', $payload))->json();

    expect($file_url)->toHaveSuccessGeneralResponse()
        ->toHaveKey('data.url', 'http://localhost:8000/storage/s3-downloads/123456');
});
