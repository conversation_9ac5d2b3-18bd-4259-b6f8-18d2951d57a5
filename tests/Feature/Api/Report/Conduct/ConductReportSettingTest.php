<?php

use App\Enums\ConductReportSettingCategory;
use App\Http\Resources\GradeResource;
use App\Http\Resources\SemesterSettingResource;
use App\Models\ConductReportSetting;
use App\Models\Grade;
use App\Models\SemesterSetting;
use App\Models\User;
use App\Services\ConductReportSettingService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    Cache::clear();
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(ConductReportSetting::class)->getTable();
    $this->routeNamePrefix = 'conduct-report-settings';
});

test('index', function () {
    $grade_1 = Grade::factory()->create();
    $grade_2 = Grade::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();

    $conduct_report_settings = ConductReportSetting::factory(3)->state(new Sequence(
        [
            'category' => ConductReportSettingCategory::ATTENDANCE->value,
            'year' => '2024',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_2->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MARK_DEDUCT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_2->id
        ]
    ))->create();

    $filters = [
        'semester_setting_id' => $semester_setting_2->id
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.index', $filters))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'category' => $conduct_report_settings[2]->category->value,
            'year' => $conduct_report_settings[2]->year,
            'from_date' => $conduct_report_settings[2]->from_date,
            'to_date' => $conduct_report_settings[2]->to_date,
            'grade' => resourceToArray(new GradeResource($grade_1)),
            'semester_setting' => resourceToArray(new SemesterSettingResource($semester_setting_2))
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(ConductReportSettingService::class, function (MockInterface $mock) {
        $conduct_report_setting = ConductReportSetting::factory()->create();

        $mock->shouldReceive('getAllPaginatedConductReportSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([$conduct_report_setting], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index determine getAll per_page is -1', function () {
    $this->mock(ConductReportSettingService::class, function (MockInterface $mock) {
        $conduct_report_setting = ConductReportSetting::factory(2)->create();

        $mock->shouldReceive('getAllConductReportSettings')->once()->andReturn($conduct_report_setting);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);
    expect($response->json())->not()->toHaveKey('pagination');
});

test('bulkCreateOrUpdate', function () {
    $this->assertDatabaseCount($this->table, 0);

    $grade_1 = Grade::factory()->create();
    $grade_2 = Grade::factory()->create();
    $semester_setting = SemesterSetting::factory()->create();


    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'conduct_report_settings' => [
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 6);

    foreach($payload['conduct_report_settings'] as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_setting_id' => $semester_setting->id,
            'category' => $input['category'],
            'from_date' => $input['from_date'],
            'to_date' => $input['to_date'],
            'year' => $input['year'],
            'grade_id' => $input['grade_id']
        ]);
    }

    // Validator testing
    $payload = [
        'semester_setting_id' => 9999,
        'conduct_report_settings' => [
            [
                'category' => 'asd',
                'from_date'  => '9999-999123',
                'to_date'  => '9999',
                'year' => '12a1',
                'grade_id' => '9999',
            ]
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'conduct_report_settings.0.category' => ['The selected conduct_report_settings.0.category is invalid.'],
            'conduct_report_settings.0.from_date' => ['The conduct_report_settings.0.from_date field must be a valid date.'],
            'conduct_report_settings.0.to_date' => ['The conduct_report_settings.0.to_date field must be a valid date.'],
            'conduct_report_settings.0.year' => ['The conduct_report_settings.0.year field must match the format Y.'],
            'conduct_report_settings.0.grade_id' => ['The selected conduct_report_settings.0.grade_id is invalid.']
        ]);
    $this->assertDatabaseCount($this->table, 6);

    // bulk update 
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'conduct_report_settings' => [
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-05-01')->toDateString(),
                'to_date' => Carbon::parse('2025-05-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-06-01')->toDateString(),
                'to_date' => Carbon::parse('2025-10-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-08-01')->toDateString(),
                'to_date' => Carbon::parse('2025-09-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-10-01')->toDateString(),
                'to_date' => Carbon::parse('2025-11-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-11-01')->toDateString(),
                'to_date' => Carbon::parse('2025-11-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-01-01')->toDateString(),
                'to_date' => Carbon::parse('2025-12-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 6);

    foreach($payload['conduct_report_settings'] as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_setting_id' => $semester_setting->id,
            'category' => $input['category'],
            'from_date' => $input['from_date'],
            'to_date' => $input['to_date'],
            'year' => $input['year'],
            'grade_id' => $input['grade_id']
        ]);
    }
});