<?php

use App\Http\Resources\CourseResource;
use App\Http\Resources\SimpleSubjectResource;
use App\Models\Country;
use App\Models\Course;
use App\Models\EnrollmentSession;
use App\Models\Product;
use App\Models\Subject;
use App\Models\User;
use App\Services\EnrollmentSessionService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;


beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(EnrollmentSession::class)->getTable();
    $this->routeNamePrefix = 'admin.enrollment-sessions';
});

test('index: test service accepting params', function () {
    $subject = Subject::factory()->create();

    $course = Course::factory()->create();

    $enrollment_sessions = EnrollmentSession::factory(3)->state(new Sequence(
        [
            'name' => 'Enrollment Session 2023',
            'from_date' => '2023-01-01',
            'to_date' => '2023-05-31',
            'code' => 'SESSION_2023',
            'is_active' => false,
            'course_id' => $course->id,
        ],
        [
            'name' => 'Enrollment Session 2024',
            'from_date' => '2024-01-01',
            'to_date' => '2024-05-31',
            'code' => 'SESSION_2024',
            'is_active' => false,
            'course_id' => $course->id,
        ],
        [
            'name' => 'Enrollment Session 2025',
            'from_date' => '2025-01-01',
            'to_date' => '2025-05-31',
            'code' => 'SESSION_2025',
            'is_active' => true,
            'course_id' => $course->id,
        ],
    ))->create();

    $enrollment_sessions[1]->examSubjects()->attach($subject->id);

    $filters = [
        'name' => 'Enrollment Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-05-31',
        'includes' => ['exam_subjects', 'course'],
    ];

    $this->mock(EnrollmentSessionService::class, function (MockInterface $mock) use ($filters, $enrollment_sessions) {
        $mock->shouldReceive('getAllPaginatedEnrollmentSessions')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$enrollment_sessions[1]->refresh()->loadMissing(['examSubjects', 'course'])], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $enrollment_sessions[1]->id,
            'name' => $enrollment_sessions[1]->name,
            'from_date' => $enrollment_sessions[1]->from_date,
            'to_date' => $enrollment_sessions[1]->to_date,
            'code' => $enrollment_sessions[1]->code,
            'is_active' => $enrollment_sessions[1]->is_active,
            'fee_assignment_settings' => $enrollment_sessions[1]->fee_assignment_settings,
            'course' => resourceToArray(new CourseResource($course)),
            'exam_subjects' => resourceToArray(SimpleSubjectResource::collection($enrollment_sessions[1]->examSubjects)),
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EnrollmentSessionService::class, function (MockInterface $mock) {
        $enrollment_session = EnrollmentSession::factory()->create();

        $mock->shouldReceive('getAllPaginatedEnrollmentSessions')
            ->once()
            ->andReturn(new LengthAwarePaginator([$enrollment_session], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index determine getAll per_page is -1', function () {
    $this->mock(EnrollmentSessionService::class, function (MockInterface $mock) {
        $enrollment_sessions = EnrollmentSession::factory(2)->create();

        $mock->shouldReceive('getAllEnrollmentSessions')->once()->andReturn($enrollment_sessions);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {
    $course = Course::factory()->create();

    // without subjects
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'SESSION_2023',
        'is_active' => false,
        'course_id' => $course->id,
    ]);

    $response = $this->getJson(route("$this->routeNamePrefix.show", ['enrollment_session' => $enrollment_session->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $enrollment_session->id,
            'name' => $enrollment_session->name,
            'from_date' => $enrollment_session->from_date,
            'to_date' => $enrollment_session->to_date,
            'code' => $enrollment_session->code,
            'is_active' => $enrollment_session->is_active,
            'fee_assignment_settings' => $enrollment_session->fee_assignment_settings,
            'course' => resourceToArray(new CourseResource($course)),
            'exam_subjects' => [],
        ]);

    // with subjects + fee_assignment_settings
    $subject = Subject::factory()->create();

    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => 1,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '1',
                ],
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '2',
                ],
            ],
            'outcome' => [
                'product_id' => 2,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $enrollment_session_2 = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session 2023 With Subjects',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'SESSION_2023_WITH_SUBJECTS',
        'is_active' => false,
        'course_id' => $course->id,
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ]);

    $enrollment_session_2->examSubjects()->attach($subject->id);

    $response = $this->getJson(route("$this->routeNamePrefix.show", ['enrollment_session' => $enrollment_session_2->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $enrollment_session_2->id,
            'name' => $enrollment_session_2->name,
            'from_date' => $enrollment_session_2->from_date,
            'to_date' => $enrollment_session_2->to_date,
            'code' => $enrollment_session_2->code,
            'is_active' => $enrollment_session_2->is_active,
            'fee_assignment_settings' => $temp_fee_assignment_settings,
            'course' => resourceToArray(new CourseResource($course)),
            'exam_subjects' => resourceToArray(SimpleSubjectResource::collection($enrollment_session_2->examSubjects)),
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['enrollment_session' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create', function () {
    //create success
    $this->assertDatabaseCount($this->table, 0);

    $country = Country::factory()->create();
    $product = Product::factory()->create();

    $subject = Subject::factory()->create();
    $course = Course::factory()->create();

    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product->id,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product->id,
                'amount' => 1000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $payload = [
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'session_2023',
        'is_active' => false,
        'course_id' => $course->id,
        'subject_ids' => [$subject->id],
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name'],
            'from_date' => $payload['from_date'],
            'to_date' => $payload['to_date'],
            'code' => 'SESSION_2023', // make sure code is upper case
            'is_active' => $payload['is_active'],
            'fee_assignment_settings' => $temp_fee_assignment_settings,
            'course' => resourceToArray(new CourseResource($course)),
            'exam_subjects' => resourceToArray(SimpleSubjectResource::collection([$subject])),
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount('enrollment_session_exam_subject', 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'from_date' => $payload['from_date'],
        'to_date' => $payload['to_date'],
        'code' => 'SESSION_2023',
        'is_active' => $payload['is_active'],
        'course_id' => $course->id,
        'fee_assignment_settings' => json_encode($temp_fee_assignment_settings),
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response['data']['id'],
        'subject_id' => $subject->id,
    ]);
});

test('create validation error', function () {
    $this->assertDatabaseCount($this->table, 0);

    $payload = [];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $this->assertDatabaseCount($this->table, 0);

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'name' => [
                'The name field is required.'
            ],
            'from_date' => [
                'The from date field is required.'
            ],
            'to_date' => [
                'The to date field is required.'
            ],
            'code' => [
                'The code field is required.'
            ],
            'is_active' => [
                'The is active field is required.'
            ],
            'course_id' => [
                'The course id field is required.'
            ],
            'subject_ids' => [
                'The subject ids field is required.'
            ],
        ]);


    // subject not exist
    $payload = [
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'session_2023',
        'is_active' => false,
        'course_id' => 1, // course not exist
        'subject_ids' => [9999], // subject not exist
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $this->assertDatabaseCount($this->table, 0);

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'course_id' => [
                'The selected course id is invalid.'
            ],
            'subject_ids' => [
                'The selected subject ids is invalid.'
            ],
        ]);

    // Duplicated subject ids
    $subject = Subject::factory()->create();

    $payload = [
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'session_2023',
        'is_active' => false,
        'course_id' => 1, // course not exist
        'subject_ids' => [$subject->id, $subject->id],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $this->assertDatabaseCount($this->table, 0);

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'course_id' => [
                'The selected course id is invalid.'
            ],
            'subject_ids' => [
                'The subject field has a duplicate value.'
            ],
        ]);
});

test('create validation error : fee_assignment_settings validation', function () {
    $subject = Subject::factory()->create();
    $course = Course::factory()->create();
    $country = Country::factory()->create();
    $product = Product::factory()->create();

    $base_payload = [
        'name' => 'Test Enrollment Session',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject->id],
    ];

    // Test 1: Valid fee assignment with conditions
    $valid_fee_settings = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    EnrollmentSession::truncate();

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $valid_fee_settings])
    );

    expect($response->status())->toBe(200);

    // Test 2: Valid fee assignment with null conditions (default case)
    $valid_null_conditions = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 50
            ]
        ]
    ];

    EnrollmentSession::truncate();

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $valid_null_conditions])
    );

    expect($response->status())->toBe(200);

    EnrollmentSession::truncate();

    // Test 3: Invalid - Missing required fields in conditions
    $invalid_missing_fields = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    // missing operator and value
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_missing_fields])
    )->json();

    expect($response['error'])->dd()->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.conditions.0.operator field is required.",
                "The fee_assignment_settings.0.conditions.0.value field is required.",
            ]
        ]);

    // Test 4: Invalid - Wrong operator value
    $invalid_operator = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => 'INVALID',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_operator])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0.conditions.0.operator is invalid.",
            ]
        ]);

    // Test 5: Invalid - Non-existent nationality_id
    $invalid_nationality = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => 99999 // Non-existent ID
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_nationality])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0 nationality id : 99999 is invalid.",
            ]
        ]);

    // Test 6: Invalid - Wrong boolean value for is_hostel
    $invalid_hostel = [
        [
            'conditions' => [
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => 'not_a_boolean' // Should be true/false
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_hostel])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0 is hostel field must be true or false.",
            ]
        ]);

    // Test 7: Invalid - Wrong date format in outcome
    $invalid_date = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024/06/01', // Wrong format
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_date])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.outcome.period field must match the format Y-m-d.",
            ]
        ]);

    // Test 8: Invalid - Non-numeric amount
    $invalid_amount = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 'not_a_number'
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_amount])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.outcome.amount field must be a number.",
            ]
        ]);

    // Test 9: Invalid - Non-existent product_id
    $invalid_product = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => 99999, // Non-existent ID
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->postJson(
        route("$this->routeNamePrefix.create"),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_product])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0.outcome.product_id is invalid.",
            ]
        ]);
});

test('update', function () {
    $old_subject = Subject::factory()->create();
    $new_subject = Subject::factory()->create();

    $course = Course::factory()->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'SESSION_2023',
        'is_active' => false,
        'course_id' => $course->id,
    ]);

    $enrollment_session->examSubjects()->attach($old_subject->id);

    $payload = [
        'name' => 'Update Enrollment Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-05-31',
        'code' => 'session_2024',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$new_subject->id],
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]), $payload)->json();

    $enrollment_session->refresh();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $enrollment_session->id,
            'name' => $payload['name'],
            'from_date' => $payload['from_date'],
            'to_date' => $payload['to_date'],
            'code' => 'SESSION_2024', // make sure code is upper case
            'is_active' => $payload['is_active'],
            'course' => resourceToArray(new CourseResource($course)),
            'exam_subjects' => resourceToArray(SimpleSubjectResource::collection([$new_subject])),
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount('enrollment_session_exam_subject', 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $enrollment_session->id,
        'name' => $payload['name'],
        'from_date' => $payload['from_date'],
        'to_date' => $payload['to_date'],
        'code' => 'SESSION_2024',
        'is_active' => $payload['is_active'],
        'course_id' => $course->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $enrollment_session->id,
        'subject_id' => $new_subject->id,
    ]);

    $this->assertDatabaseMissing('enrollment_session_exam_subject', [
        'enrollment_session_id' => $enrollment_session->id,
        'subject_id' => $old_subject->id,
    ]);
});

test('update validation error', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'SESSION_2023',
        'is_active' => false,
        'course_id' => Course::factory()->create()->id,
    ]);

    $payload = [];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'name' => [
                'The name field is required.'
            ],
            'from_date' => [
                'The from date field is required.'
            ],
            'to_date' => [
                'The to date field is required.'
            ],
            'code' => [
                'The code field is required.'
            ],
            'is_active' => [
                'The is active field is required.'
            ],
            'course_id' => [
                'The course id field is required.'
            ],
            'subject_ids' => [
                'The subject ids field is required.'
            ],
        ]);

    // subject not exist
    $payload = [
        'name' => 'Update Enrollment Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-05-31',
        'code' => 'session_2024',
        'is_active' => true,
        'course_id' => 9999, // course not exist
        'subject_ids' => [9999], // subject not exist
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'course_id' => [
                'The selected course id is invalid.'
            ],
            'subject_ids' => [
                'The selected subject ids is invalid.'
            ],
        ]);
});


test('update validation error : fee_assignment_settings validation', function () {
    $subject = Subject::factory()->create();
    $course = Course::factory()->create();
    $country = Country::factory()->create();
    $product = Product::factory()->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session 2023',
        'from_date' => '2023-01-01',
        'to_date' => '2023-05-31',
        'code' => 'SESSION_2023',
        'is_active' => false,
        'course_id' => $course->id,
    ]);

    $base_payload = [
        'name' => 'Test Enrollment Session',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject->id],
    ];

    // Test 1: Invalid - Missing required fields in conditions
    $invalid_missing_fields = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    // missing operator and value
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_missing_fields])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.conditions.0.operator field is required.",
                "The fee_assignment_settings.0.conditions.0.value field is required.",
            ]
        ]);

    // Test 2: Invalid - Wrong operator value
    $invalid_operator = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => 'INVALID',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_operator])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0.conditions.0.operator is invalid.",
            ]
        ]);

    // Test 3: Invalid - Non-existent nationality_id
    $invalid_nationality = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => 99999 // Non-existent ID
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_nationality])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0 nationality id : 99999 is invalid.",
            ]
        ]);

    // Test 4: Invalid - Wrong boolean value for is_hostel
    $invalid_hostel = [
        [
            'conditions' => [
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => 'not_a_boolean' // Should be true/false
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_hostel])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0 is hostel field must be true or false.",
            ]
        ]);

    // Test 5: Invalid - Wrong date format in outcome
    $invalid_date = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024/06/01', // Wrong format
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_date])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.outcome.period field must match the format Y-m-d.",
            ]
        ]);

    // Test 6: Invalid - Non-numeric amount
    $invalid_amount = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => $product->id,
                'period' => '2024-06-01',
                'amount' => 'not_a_number'
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_amount])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The fee_assignment_settings.0.outcome.amount field must be a number.",
            ]
        ]);

    // Test 7: Invalid - Non-existent product_id
    $invalid_product = [
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $country->id
                ]
            ],
            'outcome' => [
                'product_id' => 99999, // Non-existent ID
                'period' => '2024-06-01',
                'amount' => 1200
            ]
        ]
    ];

    $response = $this->putJson(
        route("$this->routeNamePrefix.update", ['enrollment_session' => $enrollment_session->id]),
        array_merge($base_payload, ['fee_assignment_settings' => $invalid_product])
    )->json();

    expect($response['error'])->toHaveKey('fee_assignment_settings')
        ->toEqual([
            "fee_assignment_settings" => [
                "The selected fee_assignment_settings.0.outcome.product_id is invalid.",
            ]
        ]);
});

test('destroy', function () {
    $enrollment_session = EnrollmentSession::factory()->create();
    $other_enrollment_sessions = EnrollmentSession::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['enrollment_session' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['enrollment_session' => $enrollment_session->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);

    $this->assertDatabaseMissing($this->table, ['id' => $enrollment_session->id]);

    foreach ($other_enrollment_sessions as $e) {
        $this->assertDatabaseHas($this->table, ['id' => $e->id]);
    }
});

test('allConditions', function () {
    $malaysia = Country::factory()->create(['name->en' => 'Malaysia']);
    $indonesia = Country::factory()->create(['name->en' => 'Indonesia']);

    $response = $this->getJson(route("$this->routeNamePrefix.all-conditions"))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'])->toEqual([
            [
                'value' => 'nationality_id',
                'label' => 'Nationality',
                'options' => [
                    [
                        'value' => $malaysia->id,
                        'label' => $malaysia->name,
                    ],
                    [
                        'value' => $indonesia->id,
                        'label' => $indonesia->name,
                    ]
                ]
            ],
            [
                'value' => 'is_hostel',
                'label' => 'Staying in hostel',
                'options' => [
                    [
                        'value' => true,
                        'label' => 'Yes'
                    ],
                    [
                        'value' => false,
                        'label' => 'No'
                    ]
                ]
            ]
        ]);
});
