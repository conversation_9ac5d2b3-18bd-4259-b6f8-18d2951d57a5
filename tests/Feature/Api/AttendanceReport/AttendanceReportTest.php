<?php

use App\Enums\ExportType;
use App\Models\Employee;
use App\Models\SemesterClass;
use App\Models\User;
use App\Services\Report\AttendanceReportService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mo<PERSON>y\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.attendances.';
});

test('reportByAttendanceSummary, preview data', function () {
    $expected_result = [
        '2024-01-01' => [
            [
                'date' => '2024-01-01',
                'id' => 1,
                'name' => [
                    'en' => 'Class A',
                    'zh' => '班级A',
                ],
                'records' => [
                    'LATE' => 0,
                    'PRESENT' => 0,
                    'ABSENT' => 2,
                ],
            ]
        ],
        '2024-01-02' => [
            [
                'date' => '2024-01-02',
                'id' => 2,
                'name' => [
                    'en' => 'Class B',
                    'zh' => '班级B',
                ],
                'records' => [
                    'LATE' => 0,
                    'PRESENT' => 2,
                    'ABSENT' => 0,
                ],
            ]
        ],
    ];

    $payload = [
        'report_language' => 'en',
        'semester_class_ids' => [
            SemesterClass::factory()->create()->id,
            SemesterClass::factory()->create()->id,
        ],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-02',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getReportByAttendanceSummaryData')
            ->with($payload)
            ->andReturn($expected_result);

        $mock->shouldReceive('setExportType')
            ->once()
            ->with(null)
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.by-attendances-summary')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock
            ->shouldReceive('getReportByAttendanceSummaryData')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-summary-list', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual($expected_result);
});

test('reportByAttendanceSummary, download excel', function () {

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'report_language' => 'en',
        'semester_class_ids' => [
            SemesterClass::factory()->create()->id,
            SemesterClass::factory()->create()->id,
        ],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-02',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.by-attendances-summary')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getReportByAttendanceSummaryData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456.xlsx']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-summary-list', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456.xlsx');
});


test('reportByAttendanceSummary, download pdf', function () {

    $payload = [
        'export_type' => ExportType::PDF->value,
        'report_language' => 'en',
        'semester_class_ids' => [
            SemesterClass::factory()->create()->id,
            SemesterClass::factory()->create()->id,
        ],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-02',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.by-attendances-summary')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getReportByAttendanceSummaryData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456.pdf']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-summary-list', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456.pdf');
});

test('reportByClassAttendanceTaking, preview data', function () {
    $expected_result = [
        'class' => [
            'id' => 1,
            'name' => [
                'en' => 'Class A',
                'zh' => '班级A',
            ],
        ],
        'headers' => [
            [
                'from_time' => '07:00:00',
                'to_time' => '07:30:00',
                'period' => 1,
                'name' => [
                    'en' => 'First period',
                    'zh' => '第一节',
                ],
            ],
            [
                'from_time' => '07:30:00',
                'to_time' => '08:00:00',
                'period' => 2,
                'name' => [
                    'en' => 'Second period',
                    'zh' => '第二节',
                ],
            ],
            [
                'from_time' => '08:00:00',
                'to_time' => '08:30:00',
                'period' => 3,
                'name' => [
                    'en' => 'Third period',
                    'zh' => '第三节',
                ],
            ],
            [
                'from_time' => '08:30:00',
                'to_time' => '09:00:00',
                'period' => 4,
                'name' => [
                    'en' => 'Fourth period',
                    'zh' => '第四节',
                ],
            ],
            [
                'from_time' => '09:00:00',
                'to_time' => '09:30:00',
                'period' => 5,
                'name' => [
                    'en' => 'Fifth period',
                    'zh' => '第五节',
                ],
            ],
            [
                'from_time' => '09:30:00',
                'to_time' => '10:00:00',
                'period' => 6,
                'name' => [
                    'en' => 'Sixth period',
                    'zh' => '第六节',
                ],
            ],
            [
                'from_time' => '10:00:00',
                'to_time' => '10:30:00',
                'period' => 7,
                'name' => [
                    'en' => 'Seventh period',
                    'zh' => '第七节',
                ],
            ],
            [
                'from_time' => '10:30:00',
                'to_time' => '11:00:00',
                'period' => 8,
                'name' => [
                    'en' => 'Eighth period',
                    'zh' => '第八节',
                ],
            ],
            [
                'from_time' => '11:00:00',
                'to_time' => '11:30:00',
                'period' => 9,
                'name' => [
                    'en' => 'Ninth period',
                    'zh' => '第九节',
                ],
            ],
        ],
        'attendance_data' => [
            [
                'student_id' => 50,
                'student_name' => [
                    'en' => 'Albert',
                    'zh' => '丛龙',
                ],
                'student_number' => '8272554729',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 51,
                'student_name' => [
                    'en' => 'Bob',
                    'zh' => '尤娟',
                ],
                'student_number' => '5102835683',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 52,
                'student_name' => [
                    'en' => 'Charlie',
                    'zh' => '欧阳梅',
                ],
                'student_number' => '5079128467',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 53,
                'student_name' => [
                    'en' => 'David',
                    'zh' => '周强',
                ],
                'student_number' => '1296499080',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
        ]
    ];

    $payload = [
        'report_language' => 'en',
        'semester_class_id' => SemesterClass::factory()->create()->id,
        'date' => '2024-01-01',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getReportByClassAttendanceTakingData')
            ->with($payload)
            ->andReturn($expected_result);

        $mock->shouldReceive('setExportType')
            ->once()
            ->with(null)
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.class-attendance-report')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock
            ->shouldReceive('getReportByClassAttendanceTakingData')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'class-attendance-report', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual($expected_result);
});

test('reportByClassAttendanceTaking, download excel', function () {

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'report_language' => 'en',
        'semester_class_id' => SemesterClass::factory()->create()->id,
        'date' => '2024-01-01',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.class-attendance-report')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getReportByClassAttendanceTakingData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'class-attendance-report', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});

test('reportByStudentAttendanceMarkDeduction, preview data', function () {
    $expected_result = [
        [
            "class_name" => "J11",
            "students" => [
                [
                    "student_name" => [
                        "en" => "Albert",
                        "zh" => "石楠",
                    ],
                    "student_number" => "3771335427",
                    "class" => [
                        "en" => "J11",
                        "zh" => "周芬",
                    ],
                    "attendances" => [
                        [
                            "date" => "2025-04-11",
                            "type" => "LATE",
                            "base_deduct_average_point" => 0.01,
                            "periods" => 12,
                            "total_deduct_average_point" => 0.12,
                            "reason" => null,
                        ],
                    ],
                ],
                [
                    "student_name" => [
                        "en" => "Bob",
                        "zh" => "吕金凤",
                    ],
                    "student_number" => "1848070925",
                    "class" => [
                        "en" => "J11",
                        "zh" => "周芬",
                    ],
                    "attendances" => [
                        [
                            "date" => "2025-04-11",
                            "type" => "ABSENT",
                            "base_deduct_average_point" => 0.02,
                            "periods" => 12,
                            "total_deduct_average_point" => 0.24,
                            "reason" => null,
                        ],
                    ],
                ],
            ],
        ],
    ];

    $payload = [
        'report_language' => 'en',
        'semester_class_ids' => [SemesterClass::factory()->create()->id],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-01',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with(null)
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.by-student-attendance-mark-deduction')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock
            ->shouldReceive('getReportByAttendanceMarkDeductionData')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-student-attendance-mark-deduction', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual($expected_result);
});

test('reportByStudentAttendanceMarkDeduction, download excel', function () {

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'report_language' => 'en',
        'semester_class_ids' => [SemesterClass::factory()->create()->id],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-01',
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.by-student-attendance-mark-deduction')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getReportByAttendanceMarkDeductionData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-student-attendance-mark-deduction', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});

test('classAttendanceTakingStatusReport, preview data', function () {
    $expected_result = [
        'periods' => [
            [
                "from_time" => "07:31",
                "to_time" => "07:45"
            ],
            [
                "from_time" => "07:45",
                "to_time" => "08:20"
            ],
            [
                "from_time" => "08:25",
                "to_time" => "09:00"
            ]
        ],
        'dates' => [
            "date" => "2025-04-17",
            "day" => "Thursday",
            "attendances" => [
                null,
                [
                    "subject_name" => "English Language",
                    "class_name" => "PD"
                ],
                null
            ]
        ],
        "date_from" => "2025-01-03",
        "date_to" => "2025-04-30",
        "employee" => [
            "number" => "00156",
            "name" => "KOSALYA NAIR NARAYANAN"
        ]
    ];

    $payload = [
        'report_language' => 'en',
        'date_from' => '2025-01-03',
        'date_to' => '2025-04-30',
        "employee_id" => Employee::factory()->create()->id
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getClassAttendanceTakingStatus')
            ->with($payload)
            ->andReturn($expected_result);

        $mock->shouldReceive('setExportType')
            ->once()
            ->with(null)
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.class-attendance-taking-status-report')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock
            ->shouldReceive('getClassAttendanceTakingStatus')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'class-attendance-taking-status-report', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual($expected_result);
});

test('classAttendanceTakingStatusReport, download pdf', function () {

    $payload = [
        'report_language' => 'en',
        'date_from' => '2025-01-03',
        'date_to' => '2025-04-30',
        "employee_id" => Employee::factory()->create()->id,
        'export_type' => ExportType::PDF->value,
    ];

    $this->mock(AttendanceReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.attendances.class-attendance-taking-status-report')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getClassAttendanceTakingStatus')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456.pdf']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'class-attendance-taking-status-report', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456.pdf');
});
