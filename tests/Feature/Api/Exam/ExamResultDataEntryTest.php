<?php

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ResultSourceSubject;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\User;
use App\Services\Exam\ExamResultsDataEntryService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function() {

    Cache::clear();

    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'exam-results-data-entry';

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();

});

test('getEligibleExams', function() {

    Carbon::setTestNow('2024-11-20 16:00:00');

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleExams',)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray(['code' => 'SEM1EXAM'])
        ->and($response['data'][1])->toMatchArray(['code' => 'SEM2EXAM']);

    Carbon::setTestNow('2024-12-06 16:00:00');
    
    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleExams',)
    )->json();
    
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray(['code' => 'FINALEXAM']);

});


test('getEligibleSubjects', function (){

    $employee = Employee::factory()->create(['user_id' => $this->user->id]);
    $employee2 = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);

    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleSubjects',)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $class_subject->subject_id,
            'code' => $this->subjects[0]->code
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $class_subject2->subject_id,
            'code' => $this->subjects[1]->code
        ]);
});

test('getEligibleClasses', function() {

    $employee = Employee::factory()->create(['user_id' => $this->user->id]);
    $employee2 = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);

    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleClasses', ['subject_id' => $class_subject->subject_id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $first_class->id,
            'name' => $first_class->name,
        ]);

    // Validation guard check
    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleClasses', ['subject_id' => 9999])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'subject_id' => ['The selected subject id is invalid.']
    ]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleClasses' )
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'subject_id' => ['The subject id field is required.']
    ]);
});

test('getEligibleStudentsAndScores', function() {

    $employee = Employee::factory()->create(['user_id' => $this->user->id]);
    $employee2 = Employee::factory()->create([]);

    /**
     *
     * Class 1
     *
     * - subject 0 (Employee)
     *   - student 1
     *   - student 2
     * - subject 1 (Employee)
     *   - student 1
     * - subject 2 (Employee2)
     *   - student 1
     *
     * Class 2
     *
     * - subject 0 (Employee)
     *   - student 3
     * - subject 1 (Employee)
     *   - student 3
     *
     * Class 3
     *
     * - subject 0 (Employee2)
 */

    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $second_class = ClassModel::factory()->create([
        'name->en' => 'Class 2',
    ]);
    $third_class = ClassModel::factory()->create([
        'name->en' => 'Class 3',
    ]);


    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);
    $first_semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $second_class->id
    ]);
    $first_semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $third_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $first_semester_class2->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);
    $class_subject5 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject6 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject7 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class3->id,
        'subject_id' => $this->subjects[0]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject5->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject6->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject7->id
    ]);

   ClassSubjectStudent::factory()->create([
       'class_subject_id' => $class_subject->id,
       'student_id' => $student->id,
   ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject2->id,
        'student_id' => $student->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject3->id,
        'student_id' => $student->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject->id,
        'student_id' => $student2->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject5->id,
        'student_id' => $student3->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject6->id,
        'student_id' => $student3->id,
    ]);


    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // employee 1, subject 0, class 1
    // Update score for 1 component to assume data was entered before
    $sgf1->resultSources()->where('code', $this->exams[0]->code)->first()->subjects()
        ->where('subject_id', $this->subjects[0]->id)->first()->components()->where('code', 'FINAL')
        ->update(['actual_score' => 99.65]);

    $payload = [
        'subject_id' => $this->subjects[0]->id,
        'class_id' => $first_class->id,
        'exam_id' => $this->exams[0]->id
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleStudentsAndScores', $payload)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'student_id' => $student->id,
            'student_number' => $student->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf1->id,
        ])
        ->and($response['data'][0]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => 99.65,      // previously entered score pre-populated
            'component_weightage_percent' => 100,
        ])
        ->and($response['data'][1])->toMatchArray([
            'student_id' => $student2->id,
            'student_number' => $student2->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf2->id,
        ])
        ->and($response['data'][1]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => null,
            'component_weightage_percent' => 100,
        ]);

    
    // validation check
    $payload = [
        'subject_id' => 99999,
        'class_id' => 99999,
        'exam_id' => 99999
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleStudentsAndScores', $payload)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'subject_id' => ['The selected subject id is invalid.'],
            'class_id' => ['The selected class id is invalid.'],
            'exam_id' => ['The selected exam id is invalid.']
        ]);

    $payload = [];
    
    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleStudentsAndScores', $payload)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'subject_id' => ['The subject id field is required.'],
            'class_id' => ['The class id field is required.'],
            'exam_id' => ['The exam id field is required.']
        ]);
});

test('save', function () {

    $employee = Employee::factory()->create(['user_id' => $this->user->id]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);

    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' =>$first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2];
    foreach ($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    //score - 1 component
    app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[0])
        ->setScore(56.66)
        ->setSubjectComponentCode('FINAL')
        ->save();

    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first();

    $result_source_subject_component = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first()
        ->components()->where('code', 'FINAL')->first();

    $payload = [
        'result_source_subject_id' => $result_source_subject->id,
        'result_source_subject_component_id' => $result_source_subject_component->id,
        'score' => 56.66
    ];
    $response = $this->postJson(route("$this->routeNamePrefix". ".save"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $component = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first()
        ->components()->where('code', 'FINAL')->first();

    expect($component->actual_score)->toEqual(56.66)
        ->and($component->actual_score_grade)->toBeNull()
        ->and($component->data_entry_employee_id)->toBe($employee->id)
        ->and($component->data_entry_at)->not()->toBeNull();


    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[2]->id)->first();

    $payload = [
        'result_source_subject_id' => $result_source_subject->id,
        'result_source_subject_component_id' => null,
        'grade' => 'A-'
    ];
    $response = $this->postJson(route("$this->routeNamePrefix". ".save"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[2]->id)->first();

    expect($result_source_subject->actual_score_grade)->toBe('A-')
        ->and($result_source_subject->actual_score)->toBeNull()
        ->and($result_source_subject->data_entry_at)->not()->toBeNull()
        ->and($result_source_subject->data_entry_employee_id)->toBe($employee->id);


    // validation check
    $payload = [
        'result_source_subject_id' => 99999,
        'result_source_subject_component_id' => 99999,
    ];

    $response = $this->postJson(route("$this->routeNamePrefix". ".save"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'result_source_subject_id' => ['The selected result source subject id is invalid.'],
            'result_source_subject_component_id' => ['The selected result source subject component id is invalid.'],
            'score' => ['The score field is required when grade is not present.'],
            'grade' => ['The grade field is required when score is not present.'],
        ]);

    $payload = [];
    
    $response = $this->postJson(route("$this->routeNamePrefix". ".save"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'result_source_subject_id' => ['The result source subject id field is required.'],
            'score' => ['The score field is required when grade is not present.'],
            'grade' => ['The grade field is required when score is not present.'],
        ]);
});

test('reopenPostedEntry', function() {

    $employee = Employee::factory()->create(['user_id' => $this->user->id]);
    $rss = ResultSourceSubject::factory()->create([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-25 00:00:00',
        'actual_score' => 100,
    ]);

    $rss2 = ResultSourceSubject::factory()->create([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-20 00:00:00',
        'actual_score' => 99,
    ]);

    $payload = ['result_source_subject_id' => $rss->id];
    $response = $this->postJson(route("$this->routeNamePrefix". ".reopenPostedEntry"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('result_source_subjects', [
        'id' => $rss->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
        'posted_at' => null,
        'actual_score' => null,
    ]);
    $this->assertDatabaseHas('result_source_subjects', [
        'id' => $rss2->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-20 00:00:00',
        'actual_score' => 99,
    ]);

    // validation check
    $payload = ['result_source_subject_id' => 99999];
    $response = $this->postJson(route("$this->routeNamePrefix". ".reopenPostedEntry"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'result_source_subject_id' => ['The selected result source subject id is invalid.'],
        ]);

    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix". ".reopenPostedEntry"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'result_source_subject_id' => ['The result source subject id field is required.'],
        ]);
});
