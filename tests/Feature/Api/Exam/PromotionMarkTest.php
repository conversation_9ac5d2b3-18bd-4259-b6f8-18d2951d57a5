<?php

use App\Models\ClassModel;
use App\Models\Grade;
use App\Models\PromotionMark;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\User;
use App\Services\Exam\PromotionMarkService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    Cache::clear();
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(PromotionMark::class)->getTable();

    $this->routeNamePrefix = 'promotion-marks';

    $this->semesterClass = SemesterClass::factory()->create();
});

test('index', function () {

    $grade1 = Grade::factory()->create();
    $grade2 = Grade::factory()->create();

    $class1 = ClassModel::factory()->create(['grade_id' => $grade1->id]);
    $class2 = ClassModel::factory()->create(['grade_id' => $grade2->id]);
    $class3 = ClassModel::factory()->create(['grade_id' => $grade1->id]);

    $semester_setting1 = SemesterSetting::factory()->create();
    $semester_setting2 = SemesterSetting::factory()->create();

    $semester_classes = SemesterClass::factory(3)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class1->id
        ],
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class2->id
        ],
        [
            'semester_setting_id' => $semester_setting2->id,
            'class_id' => $class3->id
        ]

    ))->create();

    $promotion_marks = PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_classes[0]->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[1]->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[2]->id,
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();

    $filters = [
        'semester_class_id' => $semester_classes[0]->id
    ];

    $filters2 = [
        'grade_id' => $grade2->id,
        'semester_setting_id' => $semester_setting1->id
    ];

    $this->mock(PromotionMarkService::class, function (MockInterface $mock) use ($filters, $filters2, $promotion_marks) {
        $mock->shouldReceive('getAllPaginatedPromotionMarks')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$promotion_marks[0]], 1, 1));


        $mock->shouldReceive('getAllPaginatedPromotionMarks')
            ->with($filters2)
            ->once()
            ->andReturn(new LengthAwarePaginator([$promotion_marks[1]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'semester_class_id' => $semester_classes[0]->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters2)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'semester_class_id' => $semester_classes[1]->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(PromotionMarkService::class, function (MockInterface $mock) {
        $promotion_mark = PromotionMark::factory()->create();

        $mock->shouldReceive('getAllPaginatedPromotionMarks')
            ->once()
            ->andReturn(new LengthAwarePaginator([$promotion_mark], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index determine getAll per_page is -1', function () {

    $this->mock(PromotionMarkService::class, function (MockInterface $mock) {
        $promotion_mark = PromotionMark::factory(2)->create();

        $mock->shouldReceive('getAllPromotionMarks')->once()->andReturn($promotion_mark);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('bulkCreateOrUpdate - create', function () {
    $this->assertDatabaseCount($this->table, 0);

    $semester_class = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();
    $semester_class3 = SemesterClass::factory()->create();

    $payload = [
        [
            'semester_class_id' => $semester_class->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 46.00,
            'conduct_mark_for_promotion' => 50.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 66.00,
            'conduct_mark_for_promotion' => 70.00,
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    foreach ($payload as $input) {
        $this->assertDatabaseHas($this->table, [
            'semester_class_id' => $input['semester_class_id'],
            'net_average_for_promotion' => $input['net_average_for_promotion'],
            'conduct_mark_for_promotion' => $input['conduct_mark_for_promotion']
        ]);
    }

    // Validator testing
    $payload = [
        [
            'semester_class_id' => 9999,
            'net_average_for_promotion' => 'some-text',
            'conduct_mark_for_promotion' => 'another text'
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            '0.semester_class_id' => ['The selected 0.semester_class_id is invalid.'],
            '0.net_average_for_promotion' => ['The 0.net_average_for_promotion field must have 0-2 decimal places.'],
            '0.conduct_mark_for_promotion' => ['The 0.conduct_mark_for_promotion field must have 0-2 decimal places.']
        ]);
    $this->assertDatabaseCount($this->table, 3);
});


test('bulkCreateOrUpdate - update', function () {

    $semester_class1 = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();
    $semester_class3 = SemesterClass::factory()->create();

    $promotion_marks = PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class1->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();


    $payload = [
        [
            'semester_class_id' => $semester_class1->id,
            'net_average_for_promotion' => 76.00,
            'conduct_mark_for_promotion' => 80.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 30.00,
            'conduct_mark_for_promotion' => 40.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 30.00,
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();
    $this->assertDatabaseCount($this->table, 3);

    foreach ($payload as $input) {
        $this->assertDatabaseHas($this->table, [
            'semester_class_id' => $input['semester_class_id'],
            'net_average_for_promotion' => $input['net_average_for_promotion'],
            'conduct_mark_for_promotion' => $input['conduct_mark_for_promotion']
        ]);
    }

    // Validator testing
    $payload = [
        [
            'semester_class_id' => 9999,
            'net_average_for_promotion' => 'some-text',
            'conduct_mark_for_promotion' => 'another text'
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.bulk-create-or-update"), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            '0.semester_class_id' => ['The selected 0.semester_class_id is invalid.'],
            '0.net_average_for_promotion' => ['The 0.net_average_for_promotion field must have 0-2 decimal places.'],
            '0.conduct_mark_for_promotion' => ['The 0.conduct_mark_for_promotion field must have 0-2 decimal places.']
        ]);
    $this->assertDatabaseCount($this->table, 3);

});
