<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\Calendar;
use App\Models\CalendarSetting;
use App\Models\CalendarTarget;
use App\Models\Country;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\HealthConcern;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\Race;
use App\Models\Religion;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use Carbon\Carbon;

test('run', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $semester_setting2 = SemesterSetting::factory()->create();

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create(['number_of_periods' => 15]);
    $j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1->id,
    ]);
    for ($i = 1; $i <= 15; $i++) {
        $period = Period::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => $i,
            'day' => Day::WEDNESDAY,
            'display_group' => 1,
        ]);
        PeriodLabel::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => $i,
            'is_attendance_required' => true,
        ]);
        $j111_timetable_timeslot = Timeslot::factory()->create([
            'timetable_id' => $j111_timetable->id,
            'period_id' => $period->id,
            'day' => Day::WEDNESDAY,
            'has_mark_deduction' => true,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $j111_timetable_timeslot->id,
        ]);
    }
    $j111_timetable_timeslot1 = Timeslot::where('timetable_id', $j111_timetable->id)
        ->where('period_id', Period::where('period_group_id', $period_group1->id)->where('period', 1)->first()->id)
        ->first();

    // set J111 first period timeslot default_init_status to present
    $j111_timetable_timeslot1->update(['default_init_status' => PeriodAttendanceStatus::PRESENT]);
    
    $j111_timetable_timeslot2 = Timeslot::where('timetable_id', $j111_timetable->id)
        ->where('period_id', Period::where('period_group_id', $period_group1->id)->where('period', 2)->first()->id)
        ->first();
    $period_group2 = PeriodGroup::factory()->create(['number_of_periods' => 20]);
    $j211_timetable = Timetable::factory()->create([
        'name' => 'J211 timetable',
        'is_active' => true,
        'period_group_id' => $period_group2->id,
        'semester_class_id' => $semester_class2->id,
    ]);
    for ($i = 1; $i <= 20; $i++) {
        $period = Period::factory()->create([
            'period_group_id' => $period_group2->id,
            'period' => $i,
            'day' => Day::WEDNESDAY,
            'display_group' => 1,
        ]);
        PeriodLabel::factory()->create([
            'period_group_id' => $period_group2->id,
            'period' => $i,
            'is_attendance_required' => true,
        ]);
        $j211_timetable_timeslot = Timeslot::factory()->create([
            'timetable_id' => $j211_timetable->id,
            'period_id' => $period->id,
            'day' => Day::WEDNESDAY,
            'has_mark_deduction' => false,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $j211_timetable_timeslot->id,
        ]);
    }
    $j211_timetable_timeslot1 = Timeslot::where('timetable_id', $j211_timetable->id)
        ->where('period_id', Period::where('period_group_id', $period_group2->id)->where('period', 1)->first()->id)
        ->first();
    $j211_timetable_timeslot2 = Timeslot::where('timetable_id', $j211_timetable->id)
        ->where('period_id', Period::where('period_group_id', $period_group2->id)->where('period', 2)->first()->id)
        ->first();
    $j211_timetable_timeslot3 = Timeslot::where('timetable_id', $j211_timetable->id)
        ->where('period_id', Period::where('period_group_id', $period_group2->id)->where('period', 3)->first()->id)
        ->first();

    $time_in = '2025-02-19 01:00:00';       // in UTC
    $today = '2025-02-19';
    $tomorrow = '2025-02-20';
    // active student with existing attendance record, will not be overwritten.
    $active_student = Student::factory()->create(['is_active' => true]);
    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $active_student->id,
        'date' => $today,
        'check_in_datetime' => $time_in,
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);
    $approved_leave_application_period_1_2_active_student = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $active_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_1_2_active_student->id,
        'date' => $today,
        'period' => 1,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_1_2_active_student->id,
        'date' => $today,
        'period' => 2,
    ]);
    $approved_leave_application_period_1_2_other_date_active_student = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $active_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_1_2_other_date_active_student->id,
        'date' => $tomorrow,
        'period' => 1,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_1_2_other_date_active_student->id,
        'date' => $tomorrow,
        'period' => 2,
    ]);

    // active student without existing attendance record, with existing period attendance (period 1 + present + updated_by_employee_id not null)
    $active_student2 = Student::factory()->create(['is_active' => true]);
    $approved_leave_application_period_1_active_student2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $active_student2->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_1_active_student2->id,
        'date' => $today,
        'period' => 1,
    ]);
    $j211_timetable_timeslot1_employee = Employee::factory()->create();
    $existing_present_period_attendance_active_student2 = PeriodAttendance::factory()->create([
        'student_id' => $active_student2->id,
        'timeslot_id' => $j211_timetable_timeslot1->id,
        'date' => $today,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'updated_by_employee_id' => $j211_timetable_timeslot1_employee->id,
        'period' => $j211_timetable_timeslot1->period->period,
        'has_mark_deduction' => false,
    ]);
    $approved_leave_application_period_2_active_student2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $active_student2->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_leave_application_period_2_active_student2->id,
        'date' => $today,
        'period' => 2,
    ]);
    $pending_leave_application_period_3_active_student2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $active_student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $pending_leave_application_period_3_active_student2->id,
        'date' => $today,
        'period' => 3,
    ]);

    $grade = Grade::factory()->create();
    $country = Country::factory()->create();
    $race = Race::factory()->create();
    $religion = Religion::factory()->create();
    $state = State::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    // 1000 active students
    $students = Student::factory(3000)->make(
        [
            'admission_grade_id' => $grade->id,
            'birthplace' => $country->name,
            'nationality_id' => $country->id,
            'race_id' => $race->id,
            'religion_id' => $religion->id,
            'state_id' => $state->id,
            'country_id' => $country->id,
            'is_active' => true,
            'custom_field' => null,
            'health_concern_id' => $health_concern->id,
        ]
    )->toArray();
    foreach ($students as &$student) {
        $student['name'] = json_encode($student['name']);
    }
    foreach (array_chunk($students, 500) as $chunk_students) {
        Student::insert($chunk_students);
    }

    $student_ids_j111 = Student::whereNotIn('id', [$active_student->id, $active_student2->id])->take(1200)->pluck('id')->toArray();
    $students_j111 = [];
    foreach ($student_ids_j111 as $id) {
        $students_j111[] = [
            'semester_setting_id' => $semester_class1->semester_setting_id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY,
            'class_enter_date' => $today,
            'is_active' => true,
        ];
    }
    StudentClass::insert($students_j111);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class1->semester_setting_id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $active_student->id,
        'class_type' => ClassType::PRIMARY,
        'class_enter_date' => $today,
        'is_active' => true,
    ]);

    $student_ids_j211 = Student::whereNotIn('id', array_merge([$active_student->id, $active_student2->id], $student_ids_j111))->take(1800)->pluck('id')->toArray();
    $students_j211 = [];
    foreach ($student_ids_j211 as $id) {
        $students_j211[] = [
            'semester_setting_id' => $semester_class2->semester_setting_id,
            'semester_class_id' => $semester_class2->id,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY,
            'class_enter_date' => $today,
            'is_active' => true,
        ];
    }
    StudentClass::insert($students_j211);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $active_student2->id,
        'class_type' => ClassType::PRIMARY,
        'class_enter_date' => $today,
        'is_active' => true,
    ]);

    $deactivated_student = Student::factory()->create(['is_active' => false]);
    $active_student3_without_timetable = Student::factory()->create(['is_active' => true]);
    // student without calendar consider is_attendance_required = false
    $active_student4_without_calendar = Student::factory()->create(['is_active' => true]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $active_student4_without_calendar->id,
        'class_type' => ClassType::PRIMARY,
        'class_enter_date' => $today,
        'is_active' => true,
    ]);
    // student5 has another calendar (is_attendance_required = false) with higher priority
    $active_student5_with_2_calendars = Student::factory()->create(['is_active' => true]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $active_student5_with_2_calendars->id,
        'class_type' => ClassType::PRIMARY,
        'class_enter_date' => $today,
        'is_active' => true,
    ]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    $active_calendar_19_02_2025 = CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => '2025-02-19',
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $calendar_targets = [];
    foreach (array_merge([$active_student->id, $active_student2->id, $deactivated_student->id, $active_student3_without_timetable->id, $active_student5_with_2_calendars->id], $student_ids_j111, $student_ids_j211) as $student_id) {
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $student_id,
        ];
    }
    CalendarTarget::insert($calendar_targets);

    $active_calendar_2025_for_student5 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025 (student5)',
        'name->zh' => '日历 2025 (student5)',
        'is_default' => false,
        'is_active' => true,
    ]);
    $active_calendar_19_02_2025_for_student5 = CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025_for_student5->id,
        'date' => '2025-02-19',
        'is_attendance_required' => false,
        'description' => 'holiday',
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar_2025_for_student5->id,
        'priority' => 15,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $active_student5_with_2_calendars->id,
    ]);

    StudentTimetable::refreshViewTable(false);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow('2025-02-18 19:00:00'); // malaysia timezone = 2025-02-19 03:00:00 am
    $this->artisan('init:student-attendance')
        ->assertExitCode(0);

    // won't create/update new attendance for student with existing attendance
    expect(Attendance::where('attendance_recordable_id', $active_student->id)->where('date', $today)->count())->toBe(1);

    $this->assertDatabaseHas(Attendance::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $active_student->id,
        'date' => $today,
        'check_in_datetime' => $time_in,
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    // won't create attendance for deactivated student
    $this->assertDatabaseMissing(Attendance::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $deactivated_student->id,
        'date' => Carbon::now()->toDateString(),
    ]);

    expect(Student::count())->toBe(3006) // 3000 + 2 active + 1 inactive + 1 active without timetable + 1 active without calendar + 1 active with 2 calendars
    ->and(Attendance::count())->toBe(3003) // inactive student and student without calendar won't create attendance
    ->and(Attendance::where('status', AttendanceStatus::ABSENT->value)->whereNull('check_in_datetime')->whereNull('check_out_datetime')->count())->toBe(3002) // - 1 existing attendance for $active_student
    ->and(Attendance::where('date', $today)->count())->toBe(3003)
        ->and(PeriodAttendance::whereNull('leave_application_id')->count())->toBe(54032)
         // (1200 - J111 * 15) + (1800 - J211 * 20) + $active_student (J111) + $active_student2 (J211)= 18000 + 36000 + 15 + 20
        ->and(PeriodAttendance::count())->toBe(54035)
        // (1200 - J111 * 14) + (1800 - J211 * 20) + $active_student (J111) + $active_student2 (J211) - $existing_present_period_attendance_active_student2 
        // 16800 + 36000 + 14 + (20-1) 
        // = 52833
        ->and(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT)->count())->toBe(52833)
        // (1200 * 1) + $active_student + $existing_present_period_attendance_active_student2 (J111 first period default_init_status = present)
        // 1200 + 1 + 1
        // = 1202
        ->and(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->count())->toBe(1202)
        ->and(PeriodAttendance::where('has_mark_deduction', true)->count())->toBe(18015) // (1200 + 1) * 15
        ->and(PeriodAttendance::where('has_mark_deduction', false)->count())->toBe(36020); // (1800 + 1) * 20

    $this->assertDatabaseHas(PeriodAttendance::class, [
        'student_id' => $active_student->id,
        'date' => $today,
        'timeslot_id' => $j111_timetable_timeslot1->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $approved_leave_application_period_1_2_active_student->id,
    ]);
    $this->assertDatabaseHas(PeriodAttendance::class, [
        'student_id' => $active_student->id,
        'date' => $today,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $approved_leave_application_period_1_2_active_student->id,
    ]);
    $this->assertDatabaseHas(PeriodAttendance::class, [
        'student_id' => $active_student2->id,
        'date' => $today,
        'timeslot_id' => $j211_timetable_timeslot1->id,
        'updated_by_employee_id' => $j211_timetable_timeslot1_employee->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null, // This remains untouched, leave_application_id should be updated when approving/rejecting leave
    ]);
    $this->assertDatabaseHas(PeriodAttendance::class, [
        'student_id' => $active_student2->id,
        'date' => $today,
        'timeslot_id' => $j211_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $approved_leave_application_period_2_active_student2->id,
    ]);
    $this->assertDatabaseHas(PeriodAttendance::class, [
        'student_id' => $active_student2->id,
        'date' => $today,
        'timeslot_id' => $j211_timetable_timeslot3->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null, // won't store pending leave application
    ]);
});
