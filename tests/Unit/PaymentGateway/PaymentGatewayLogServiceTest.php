<?php

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Enums\WalletTransactionType;
use App\Models\BillingDocument;
use App\Models\Currency;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\PaymentGatewayLogService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

beforeEach(function () {

    $this->seed([
        \Database\Seeders\ProductSeeder::class,
        \Database\Seeders\TaxSeeder::class,
        \Database\Seeders\UomSeeder::class,
        \Database\Seeders\GlAccountSeeder::class,
        \Database\Seeders\PaymentMethodSeeder::class,
        \Database\Seeders\PaymentTermsSeeder::class,
        \Database\Seeders\LegalEntitySeeder::class,
    ]);

    $this->paymentGatewayLogService = app(PaymentGatewayLogService::class);

    $this->table = resolve(PaymentGatewayLog::class)->getTable();

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->currency = Currency::factory()->create();

    $this->wallet = Wallet::factory()->create([
        'user_id' => $this->user->id,
        'currency_id' => $this->currency->id,
        'balance' => 10
    ]);
});

test('createWalletDepositTransaction()', function () {
    Carbon::setTestNow('2024-01-01 00:00:00');
    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $payload = [
        'amount' => 1.05,
        'type' => PaymentType::WALLET_DEPOSIT,
        'provider' => PaymentProvider::PAYEX,
        'currency' => $this->currency->code,
        'description' => 'Deposit wallet'
    ];

    $new_balance = (float)$this->wallet->balance + $payload['amount'];

    $wallet_transaction = WalletTransaction::factory()->create([
        'type' => WalletTransactionType::DEPOSIT,
        'wallet_id' => $this->wallet->id,
        'total_amount' => $payload['amount'],
        'balance_before' => $this->wallet->balance,
        'balance_after' => $new_balance
    ]);

    //create success
    $this->assertDatabaseCount($this->table, 0);

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);


    $response = $this->paymentGatewayLogService
        ->setProvider($payload['provider'])
        ->setUser($this->user)
        ->setTransactionLoggable($wallet_transaction)
        ->setBillingDocument($invoice)
        ->setPaymentMethod(PaymentMethod::where('code', PaymentMethod::CODE_FPX)->firstOrFail())
        ->createWalletDepositTransaction($payload['amount']);

    expect($response->toArray())->toMatchArray([
        'provider' => $payload['provider']->value,
        'requested_by_id' => $this->user->id,
        'transaction_loggable_type' => WalletTransaction::class,
        'transaction_loggable_id' => $wallet_transaction->id,
        'type' => PaymentType::WALLET_DEPOSIT->value,
        'amount' => $payload['amount'],
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'description' => 'Deposit Wallet',
        'status' => PaymentStatus::PENDING->value,
        'created_at' => $wallet_transaction->created_at->toISOString(),
        'updated_at' => $wallet_transaction->updated_at->toISOString()
    ])
    ->and($response->toArray()['token'])->not->toBeEmpty();

    $this->assertDatabaseHas($this->table, [
        'provider' => $payload['provider']->value,
        'requested_by_id' => $this->user->id,
        'transaction_loggable_type' => WalletTransaction::class,
        'transaction_loggable_id' => $wallet_transaction->id,
        'type' => PaymentType::WALLET_DEPOSIT->value,
        'amount' => $payload['amount'],
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'description' => 'Deposit Wallet',
        'status' => PaymentStatus::PENDING->value,
        'created_at' => $wallet_transaction->created_at->toISOString(),
        'updated_at' => $wallet_transaction->updated_at->toISOString()
    ]);

    $this->assertDatabaseCount($this->table, 1);
});

test('updatePaymentGatewayLog()', function () {
    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'payment_url' => null
    ]);

    $payload = [
        'payment_url' => 'PAYMENT_URL'
    ];

    //update success
    $this->assertDatabaseCount($this->table, 1);
    $response = $this->paymentGatewayLogService
        ->setModel($payment_gateway_log)
        ->setPaymentUrl($payload['payment_url'])
        ->updatePaymentGatewayLog()
        ->toArray();

    expect($response)->toMatchArray([
        'id' => $payment_gateway_log->id,
        'payment_url' => 'PAYMENT_URL',
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $payment_gateway_log->id,
        'payment_url' => 'PAYMENT_URL',
    ]);

    $this->assertDatabaseCount($this->table, 1);
});

test('updatePaymentGatewayLog() status', function () {
    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $response = $this->paymentGatewayLogService
        ->setModel($payment_gateway_log)
        ->setStatus(PaymentStatus::SUCCESS)
        ->updatePaymentGatewayLog();

    expect($response->status)->toBe(PaymentStatus::SUCCESS);

    $this->assertDatabaseHas($this->table, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);
});
