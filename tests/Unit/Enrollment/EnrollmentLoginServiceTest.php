<?php

use App\Models\EnrollmentLoginOtp;
use App\Models\EnrollmentUser;
use App\Services\EnrollmentLoginService;
use App\Services\ISmsService;
use Carbon\Carbon;
use Database\Factories\EnrollmentUserFactory;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Laravel\Sanctum\PersonalAccessToken;
use Mockery\MockInterface;

beforeEach(function () {
    $this->enrollmentLoginService = resolve(EnrollmentLoginService::class);

});

test('requestOtp(): by phone', function () {
    // Scenario 1: User found
    $user = EnrollmentUser::factory()->create(['phone_number' => '+601122222222']);
    $payload = [
        'phone_number' => '+601122222222',
    ];

    $this->assertDatabaseCount('enrollment_login_otps', 0);

    $this->partialMock(ISmsService::class, function (MockInterface $mock) use ($payload, $user) {
        $mock->shouldReceive('sendSms')->once();
    });

    $login_service = resolve(EnrollmentLoginService::class);
    $response = $login_service->requestOtp($payload);


    $this->assertDatabaseCount('enrollment_login_otps', 1);
    expect($response['model']->id)->toBe(EnrollmentLoginOtp::query()->first()->id)
        ->and($response['otp'])->toBeNumeric();

    // Scenario 2: User not found
    $payload = [
        'phone_number' => '+601133333333',
    ];

    $response = $login_service->requestOtp($payload);

    // No new otp created
    $this->assertDatabaseCount('enrollment_login_otps', 1);
    expect($response)->toBe(null);
});


test('login(): by phone_number and otp', function () {
    // Scenario 1: User found + submit invalid otp
    $user = EnrollmentUser::factory()->create(['phone_number' => '99999111']);

    $valid_otp = EnrollmentLoginOtp::factory()->create([
        'enrollment_user_id' => $user->id,
        'otp' => hash('sha256', 123456),
        'expired_at' => now()->addMinutes(5)
    ]);

    $invalid_otp = EnrollmentLoginOtp::factory()->create([
        'enrollment_user_id' => $user->id,
        'otp' => hash('sha256', 654321),
        'expired_at' => now()->subMinutes(5)
    ]);

    $payload = [
        'phone_number' => '99999111',
        'otp' => 654321,
        'use_otp' => true
    ];

    expect(function () use ($payload) {
        $this->enrollmentLoginService->login($payload);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(4004);
    }, 'Invalid OTP.');

    // Scenario 2: User found + submit valid otp
    $payload = [
        'phone_number' => '99999111',
        'otp' => 123456,
        'use_otp' => true
    ];

    $this->assertDatabaseCount('personal_access_tokens', 0);

    $result = $this->enrollmentLoginService->login($payload)->generateToken();

    $this->assertDatabaseCount('user_login_otps', 0);
    $this->assertDatabaseCount('personal_access_tokens', 1);
    $this->assertDatabaseHas('personal_access_tokens', ['tokenable_id' => $user->id]);
    $this->assertDatabaseHas('enrollment_users', [
        'id' => $user->id,
        'last_login_at' => now()->format('Y-m-d H:i:s')
    ]);

    expect(PersonalAccessToken::findToken($result))->toHaveKey('tokenable_id', $user->id);

    // Scenario 3: User not found
    $payload = [
        'phone_number' => '99999-TEST-111',
        'otp' => 123456,
        'use_otp' => true
    ];

    expect(function () use ($payload) {
        $this->enrollmentLoginService->login($payload);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(4006);
    }, 'The provided credentials does not match our records.');
});