<?php

use App\Models\EnrollmentUser;
use App\Models\Exam;
use App\Models\User;
use App\Services\EnrollmentUserService;
use App\Services\Exam\ExamService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = EnrollmentUser::factory()->create();
    Sanctum::actingAs($user, ['*'], 'enrollment');

    $this->table = resolve(EnrollmentUser::class)->getTable();

    $this->routeNamePrefix = 'enrollment-users';
});

test('index', function () {

    $users = EnrollmentUser::factory(3)->state(new Sequence(
        [
            'name->en' => 'User A',
            'email' => '<EMAIL>',
            'phone_number' => '0123456789',
            'nric' => '999000112233'
        ],
        [
            'name->en' => 'User B',
            'email' => '<EMAIL>',
            'phone_number' => '0198765432',
            'nric' => '000999332211',
        ],
        [    'name->en' => 'Someone C' ]
    ))->create();


    $filters = [
        'name' => 'User A',
    ];

    $this->mock(EnrollmentUserService::class, function (MockInterface $mock) use ($filters, $users) {
        $mock->shouldReceive('getAllPaginatedEnrollmentUsers')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$users[0]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'name' => $users[0]->name,
            'email' => $users[0]->email,
            'phone_number' => $users[0]->phone_number,
            'nric' => $users[0]->nric
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EnrollmentUserService::class, function (MockInterface $mock) {
        $users = EnrollmentUser::factory()->create();

        $mock->shouldReceive('getAllPaginatedEnrollmentUsers')
            ->once()
            ->andReturn(new LengthAwarePaginator([$users], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index determine getAll per_page is -1', function () {

    $this->mock(EnrollmentUserService::class, function (MockInterface $mock) {
        $users = EnrollmentUser::factory(2)->create();

        $mock->shouldReceive('getAllEnrollmentUsers')->once()->andReturn($users);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function (){

    $user = EnrollmentUser::factory()->create([
        'name->en' => 'User A',
        'email' => '<EMAIL>',
        'phone_number' => '0123456789',
        'nric' => '999000112233'
    ]);

    // 1 user already created during beforeEach (so total is 2)
    $this->assertDatabaseCount($this->table, 2);


    $response = $this->getJson(route($this->routeNamePrefix . '.show', $user->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $user->name,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'nric' => $user->nric
        ]);
});

/*
test('store', function (){ 

    // 1 user already created during beforeEach
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'name' => [
            'en' => 'User A',
            'zh' => '名字A'
        ],
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789',
        'nric' => '999000112233'
    ];

    $response = $this->postJson(route("enrollment-user.create"), $payload)->json();
    
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'phone_number' => $payload['phone_number'],
            'email' => $payload['email'],
            'nric' => $payload['nric']
        ]);

    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'phone_number' => $payload['phone_number'],
        'nric' => $payload['nric'],
    ]);
    
});*/

test('update', function(){

    EnrollmentUser::factory(3)->create();
    $user = EnrollmentUser::factory()->create([
        'name->en' => 'User A',
        'email' => '<EMAIL>',
        'phone_number' => '0123456789',
        'nric' => '999000112233'
    ]);

    // 1 user already created during beforeEach (so total is 5)
    $this->assertDatabaseCount($this->table, 5);

    $payload = [
        'name' => [
            'en' => 'User B',
            'zh' => '名字B'
        ],
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'nric' => '000999332211'
    ];

    $response = $this->putJson(route($this->routeNamePrefix . '.update', ['enrollment_user' => $user->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'phone_number' => $payload['phone_number'],
            'email' => $payload['email'],
            'nric' => $payload['nric']
        ]);

    $this->assertDatabaseCount($this->table, 5);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'phone_number' => $payload['phone_number'],
        'nric' => $payload['nric'],
    ]);

});
