<?php

use App\Models\EnrollmentUser;
use App\Repositories\EnrollmentUserRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    Cache::clear();
    $this->seed(InternationalizationSeeder::class);
    $this->enrollmentUserRepository = app(EnrollmentUserRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(EnrollmentUser::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->enrollmentUserRepository->getModelClass();

    expect($response)->toEqual(EnrollmentUser::class);
});


test('getAll()', function (int $expected_count, array $filters, array $expected_model) {
    $keys = [
        'first' => EnrollmentUser::factory()->create([
            'name->en' => 'User A',
            'email' => '<EMAIL>',
            'phone_number' => '0123456789',
            'nric' => '999000112233'
        ]),
        'second' => EnrollmentUser::factory()->create([
            'name->en' => 'User B',
            'email' => '<EMAIL>',
            'phone_number' => '0198765432',
            'nric' => '000999332211',
        
        ]),
        'third' => EnrollmentUser::factory()->create(['name->en' => 'Someone C']),
    ];

    $result = $this->enrollmentUserRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = User A' => [1, ['name' => 'User A'], ['first']],
    'filter by name = User B' => [1, ['name' => 'User B'],  ['second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by partial name = User' => [2, ['name' => 'User'], ['first', 'second']],
    'filter by email = <EMAIL>' => [1, ['email' => '<EMAIL>'], ['first']],
    'filter by email = <EMAIL>' => [1, ['email' => '<EMAIL>'],  ['second']],
    'filter by email = Non Existing' => [0, ['email' => 'Bla Bla'], []],
    'filter by phone_number = 0123456789' => [1, ['phone_number' => '0123456789'], ['first']],
    'filter by phone_number = 0198765432' => [1, ['phone_number' => '0198765432'],  ['second']],
    'filter by phone_number = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by nric = 999000112233' => [1, ['nric' => '999000112233'], ['first']],
    'filter by nric = 000999332211' => [1, ['nric' => '000999332211'],  ['second']],
    'filter by nric = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
]);


test('getAllPaginated()', function (int $expected_count, array $filters, array $expected_model) {
    
    $keys = [
        'first' => EnrollmentUser::factory()->create([
            'name->en' => 'User A',
            'email' => '<EMAIL>',
            'phone_number' => '0123456789',
            'nric' => '999000112233'
        ]),
        'second' => EnrollmentUser::factory()->create([
            'name->en' => 'User B',
            'email' => '<EMAIL>',
            'phone_number' => '0198765432',
            'nric' => '000999332211',
        
        ]),
        'third' => EnrollmentUser::factory()->create(['name->en' => 'Someone C']),
    ];

    $result = $this->enrollmentUserRepository->getAllPaginated($filters)->toArray();
    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = User A' => [1, ['name' => 'User A'], ['first']],
    'filter by name = User B' => [1, ['name' => 'User B'],  ['second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by partial name = User' => [2, ['name' => 'User'], ['first', 'second']],
    'filter by email = <EMAIL>' => [1, ['email' => '<EMAIL>'], ['first']],
    'filter by email = <EMAIL>' => [1, ['email' => '<EMAIL>'],  ['second']],
    'filter by email = Non Existing' => [0, ['email' => 'Bla Bla'], []],
    'filter by phone_number = 0123456789' => [1, ['phone_number' => '0123456789'], ['first']],
    'filter by phone_number = 0198765432' => [1, ['phone_number' => '0198765432'],  ['second']],
    'filter by phone_number = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by nric = 999000112233' => [1, ['nric' => '999000112233'], ['first']],
    'filter by nric = 000999332211' => [1, ['nric' => '000999332211'],  ['second']],
    'filter by nric = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
]);