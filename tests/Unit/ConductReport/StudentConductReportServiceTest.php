<?php

use App\Enums\ConductReportSettingCategory;
use App\Enums\ExportType;
use App\Enums\GradingSchemeType;
use App\Enums\RewardPunishmentRecordStatus;
use App\Helpers\FileHelper;
use App\Models\ClassModel;
use App\Models\ConductRecord;
use App\Models\ConductReportSetting;
use App\Models\ConductSetting;
use App\Models\ConductSettingTeacher;
use App\Models\Grade;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Services\DocumentPrintService;
use App\Services\GradingSchemeService;
use App\Services\ReportPrintService;
use App\Services\StudentConductReportService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

});

test('getConductReportData', function (){
    $semester_setting = SemesterSetting::factory()->create(['code' => '2025SEM1', 'name' => '2025 Sem 1']);
    $grade = Grade::factory()->create();
    $class = ClassModel::factory()->create(['grade_id' => $grade->id]);

    $semester_class= SemesterClass::factory() ->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    $conduct_grading_scheme_1 = GradingScheme::factory()->withItems()->create([
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CONDUCTSEM1',
        'name' => 'Conduct Sem 1',
        'is_active' => true,
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);

    $conduct_setting_sem1 = ConductSetting::factory()->create([
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'grading_scheme_id' => $conduct_grading_scheme_1->id
    ]);


    $students = Student::factory(3)->create();

    foreach ($students as $student){
        StudentClass::factory([
            'student_id' => $student->id,
            'semester_class_id' => $semester_class->id,
            'semester_setting_id' => $semester_setting->id
        ])->create();
    }

    $conduct_setting_homeroom_teacher = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem1->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,
    ]);

    $conduct_setting_teachers = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();

    foreach ($students as $student){
        ConductRecord::factory(6)->state(new Sequence(
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher->id,
                'student_id' => $student->id,
                'marks' => 82,
            ],
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_teachers[0]->id,
                'student_id' => $student->id,
                'marks' => 90,
            ],
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_teachers[1]->id,
                'student_id' => $student->id,
                'marks' => 83,
            ],
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_teachers[2]->id,
                'student_id' => $student->id,
                'marks' => 94,
            ],
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_teachers[3]->id,
                'student_id' => $student->id,
                'marks' => 85,
            ],
            [
                'conduct_setting_id' => $conduct_setting_sem1->id,
                'conduct_setting_teacher_id' => $conduct_setting_teachers[4]->id,
                'student_id' => $student->id,
                'marks' => 86,
            ]
        ))->create();
    }

    $reward_punishments = RewardPunishment::factory(3)->state(new Sequence(
        ['name->en' => 'Cleaning'],
        ['name->en' => 'Charity'],
        ['name->en' => 'Stealing'],
    ))->create();

    $reward_punishment_records = RewardPunishmentRecord::factory(3)->state(new Sequence(
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => -3,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-03-01',
            'student_id' => $students[2]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => -2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ]
    ))->create();

    ConductReportSetting::factory()->create([
        'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
        'year' => '2025',
        'from_date' => Carbon::parse('2025-03-01'),
        'to_date' => Carbon::parse('2025-04-30'),
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
    ]);

    $filters = [
        'report_language' => 'en',
        'export_type' => 'PDF',
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'grade_id' => $grade->id
    ];

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    SnappyPdf::fake();
    $file_name = FileHelper::generateFileName('student-conduct-report-unit-test');
    $studentConductReportService = resolve(StudentConductReportService::class);
    $studentConductReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName('reports.conduct.student-conduct')
        ->setFileName($file_name)
        ->getConductReportData($filters);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.conduct.student-conduct');

    SnappyPdf::assertSee(__('2025 Sem 1 Student Conduct Report'));

    // check name
    foreach ($students as $student){
        SnappyPdf::assertSee($students[0]->name);
        SnappyPdf::assertSee($students[0]->student_number);
    }

    SnappyPdf::assertSee($conduct_setting_homeroom_teacher->employee->name);
    foreach ($conduct_setting_teachers as $teacher){
        SnappyPdf::assertSee($teacher->employee->name);
    }

    // check raw values
    $conduct_records = ConductRecord::where('student_id', $students[0]->id)->get();
    foreach ($conduct_records as $record){
        SnappyPdf::assertSee(round($record->marks, 0));
    }

    // check merit/demerit value
    $reward_punishment_records = RewardPunishmentRecord::where('student_id', $students[0]->id)->get();
    SnappyPdf::assertSee($reward_punishment_records->where('conduct_marks', '>', 0)->sum('conduct_marks'));
    SnappyPdf::assertSee(abs($reward_punishment_records->where('conduct_marks', '<', 0)->sum('conduct_marks')));
    
    // check calculation
    $conduct_records = ConductRecord::where('student_id', $students[0]->id)
        ->with('conductSettingTeacher.employee', 'conductSetting.gradingScheme')
        ->get();
    SnappyPdf::assertSee($conduct_records->where('conductSettingTeacher.is_homeroom_teacher', false)->avg('marks'));
    SnappyPdf::assertSee(0.67 * $conduct_records->where('conductSettingTeacher.is_homeroom_teacher', false)->avg('marks'));
    SnappyPdf::assertSee(0.33 * $conduct_records->where('conductSettingTeacher.is_homeroom_teacher', true)->first()->marks);

    // check grade
    $total_conduct_marks = (0.67 * $conduct_records->where('conductSettingTeacher.is_homeroom_teacher', false)->avg('marks'))
        + (0.33 * $conduct_records->where('conductSettingTeacher.is_homeroom_teacher', true)->first()->marks)
        + $reward_punishment_records->sum('conduct_marks');
    $conduct_grade = resolve(GradingSchemeService::class)->setGradingScheme($conduct_records[0]->conductSetting->gradingScheme)
        ->applyAndGetGrade($total_conduct_marks);

    SnappyPdf::assertSee($conduct_grade['display_as_name']);

    
});