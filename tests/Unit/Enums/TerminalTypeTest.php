<?php

use App\Enums\TerminalType;

test('getLabel()', function () {
    expect(TerminalType::getLabel(TerminalType::BOOKSHOP))->toBe('Bookshop')
        ->and(TerminalType::getLabel(TerminalType::CANTEEN))->toBe('Canteen')
        ->and(TerminalType::getLabel(TerminalType::OPERATOR_APP))->toBe('Operator App');
});

test('values()', function () {
    expect(TerminalType::values())->toBe(['BOOKSHOP', 'CANTEEN', 'OPERATOR_APP']);
});

test('options()', function () {
    expect(TerminalType::options())->toEqual([
        ['value' => 'BOOKSHOP', 'name' => 'Bookshop'],
        ['value' => 'CANTEEN', 'name' => 'Canteen'],
        ['value' => 'OPERATOR_APP', 'name' => 'Operator App'],
    ]);
});
