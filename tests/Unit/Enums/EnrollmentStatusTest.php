<?php

use App\Enums\EnrollmentStatus;

test('getLabel()', function () {
    expect(EnrollmentStatus::getLabel(EnrollmentStatus::DRAFT))->toBe('Draft')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::PENDING_PAYMENT))->toBe('Pending Payment')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::PAYMENT_FAILED))->toBe('Payment Failed')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::SUBMITTED))->toBe('Submitted')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::APPROVED))->toBe('Approved')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::REJECTED))->toBe('Rejected')
        ->and(EnrollmentStatus::getLabel(EnrollmentStatus::ENROLLED))->toBe('Enrolled');
});

test('values()', function () {
    expect(EnrollmentStatus::values())->toBe([
        'DRAFT',
        'PENDING_PAYMENT',
        'PAYMENT_FAILED',
        'SUBMITTED',
        'APPROVED',
        'REJECTED',
        'ENROLLED',
    ]);
});

test('options()', function () {
    expect(EnrollmentStatus::options())->toEqual([
        ['value' => 'DRAFT', 'name' => 'Draft'],
        ['value' => 'PENDING_PAYMENT', 'name' => 'Pending Payment'],
        ['value' => 'PAYMENT_FAILED', 'name' => 'Payment Failed'],
        ['value' => 'SUBMITTED', 'name' => 'Submitted'],
        ['value' => 'APPROVED', 'name' => 'Approved'],
        ['value' => 'REJECTED', 'name' => 'Rejected'],
        ['value' => 'ENROLLED', 'name' => 'Enrolled'],
    ]);
});
