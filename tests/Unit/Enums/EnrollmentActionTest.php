<?php

use App\Enums\EnrollmentAction;

test('getLabel()', function () {
    expect(EnrollmentAction::getLabel(EnrollmentAction::SAVE_AS_DRAFT))->toBe('Save as draft')
        ->and(EnrollmentAction::getLabel(EnrollmentAction::NEXT))->toBe('Next');
});

test('values()', function () {
    expect(EnrollmentAction::values())->toBe(['SAVE_AS_DRAFT', 'NEXT']);
});

test('options()', function () {
    expect(EnrollmentAction::options())->toEqual([
        ['value' => 'SAVE_AS_DRAFT', 'name' => 'Save as draft'],
        ['value' => 'NEXT', 'name' => 'Next'],
    ]);
});
