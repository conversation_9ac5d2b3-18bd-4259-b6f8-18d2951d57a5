<?php

use App\Enums\HostelRoomGender;

test('getLabel()', function () {
    expect(HostelRoomGender::getLabel(HostelRoomGender::MALE))->toBe('Male')
        ->and(HostelRoomGender::getLabel(HostelRoomGender::FEMALE))->toBe('Female')
        ->and(HostelRoomGender::getLabel(HostelRoomGender::BOTH))->toBe('Both');
});

test('values()', function () {
    expect(HostelRoomGender::values())->toBe(['MALE', 'FEMALE', 'BOTH']);
});

test('options()', function () {
    expect(HostelRoomGender::options())->toEqual([
        ['value' => 'MALE', 'name' => 'Male'],
        ['value' => 'FEMALE', 'name' => 'Female'],
        ['value' => 'BOTH', 'name' => 'Both'],
    ]);
});
