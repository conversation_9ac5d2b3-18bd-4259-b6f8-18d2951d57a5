<?php

use App\Enums\PaymentStatus;
use App\Factories\BillingDocumentFactory;
use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentAdvanceTransaction;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\Payment;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentTerm;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\Uom;
use App\Models\User;
use App\Services\Billing\AdvancePaymentService;
use App\Services\Billing\BillingDocumentService;
use App\Services\DocumentRunningNumberService;
use Carbon\Carbon;
use Illuminate\Events\CallQueuedListener;
use Illuminate\Pagination\LengthAwarePaginator;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);
    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);
    GlAccount::factory()->create([
        'code' => GlAccount::CODE_ANY,
        'label' => 'ANY'
    ]);
    $this->glAccount = GlAccount::factory()->create([
        'code' => GlAccount::CODE_OTHERS,
        'label' => 'OTHERS'
    ]);
    $this->glAccount2 = GlAccount::factory()->create([
        'code' => '**********',
        'label' => 'SCHOOL FEES'
    ]);

    $this->glAccount3 = GlAccount::factory()->create([
        'code' => 'SCH00000001',
        'label' => 'SCHOOL FEES 2'
    ]);
    $this->glAccount4 = GlAccount::factory()->create([
        'code' => 'HOS0000001',
        'label' => 'HOSTEL FEES'
    ]);
});

test('calculate payment due date', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->setPaymentTerm($this->paymentTerm)
        ->setDocumentDate(\Carbon\Carbon::parse('2024-05-01')->startOfDay())
        ->calculatePaymentDueDate();

    expect($invoice_service->getPaymentDueDate()->toDateString())
        ->toBe('2024-05-11');

});

test('calculate payment due date cross month', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->setPaymentTerm($this->paymentTerm)
        ->setDocumentDate(\Carbon\Carbon::parse('2024-05-26')->startOfDay())
        ->calculatePaymentDueDate();

    expect($invoice_service->getPaymentDueDate()->toDateString())
        ->toBe('2024-06-05');

});

test('generate reference number', function () {

    $this->mock(DocumentRunningNumberService::class, function (\Mockery\MockInterface $mock) {
        $mock->shouldReceive('setDocumentType')->once()->andReturnSelf();
        $mock->shouldReceive('setYear')->once()->with('2024')->andReturnSelf();
        $mock->shouldReceive('setIdentifier1')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addCustomComponent')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addPresetComponent')->twice()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturn('ABC1234');
    });

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->init();

    $this->assertNull($invoice_service->getReferenceNo());

    $invoice_service
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-20')->startOfDay())
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->generateReferenceNumber();

    $this->assertEquals('ABC1234', $invoice_service->getReferenceNo());

});

test('create invoice with no line items', function () {

    $this->mock(DocumentRunningNumberService::class, function (\Mockery\MockInterface $mock) {
        $mock->shouldReceive('setDocumentType')->once()->andReturnSelf();
        $mock->shouldReceive('setYear')->once()->with('2024')->andReturnSelf();
        $mock->shouldReceive('setIdentifier1')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addCustomComponent')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addPresetComponent')->twice()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturn('INV00009');
    });


    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->calculateAmountBeforeTax()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    $invoice = $invoice_service->getBillingDocument();

    $this->assertDatabaseHas('billing_documents', [
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'currency_code' => 'MYR',
        'amount_before_tax' => 0,
        'amount_before_tax_after_less_advance' => 0,
        'tax_amount' => 0,
        'amount_after_tax' => 0,
        'classification' => 'AR',
        'document_date' => '2024-07-29',
        'posting_date' => null,
        'status' => BillingDocument::STATUS_DRAFT,
        'reference_no' => 'INV00009',
        'legal_entity_id' => $this->legalEntity->id,
        'legal_entity_name' => $this->legalEntity->name,
        'legal_entity_address' => $this->legalEntity->address,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'counterparty_reference_no' => null,
        'tax_code' => $this->tax->code,
        'tax_description' => $this->tax->name,
        'tax_percentage' => $this->tax->percentage,
        'payment_due_date' => '2024-08-08',
        'payment_term_id' => $this->paymentTerm->id,
        'remit_to_id' => $this->bankAccount->id,
        'remit_to_account_number' => $this->bankAccount->account_number,
        'remit_to_account_name' => $this->bankAccount->account_name,
        'remit_to_bank_address' => $this->bankAccount->address,
        'remit_to_bank_name' => $this->bankAccount->bank->name,
        'remit_to_swift_code' => $this->bankAccount->bank->swift_code,
    ]);

    expect($invoice)
        ->toBeObject(BillingDocument::class);

});

test('create invoice with line items', function () {

    $this->mock(DocumentRunningNumberService::class, function (\Mockery\MockInterface $mock) {
        $mock->shouldReceive('setDocumentType')->once()->andReturnSelf();
        $mock->shouldReceive('setYear')->once()->with('2024')->andReturnSelf();
        $mock->shouldReceive('setIdentifier1')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addCustomComponent')->once()->with('INV')->andReturnSelf();
        $mock->shouldReceive('addPresetComponent')->twice()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturn('INV00009');
    });


    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem(
            \App\Models\BillingDocumentLineItem::factory()->make([
                'amount_before_tax' => 100.50
            ])
        )->addLineItem(
            \App\Models\BillingDocumentLineItem::factory()->make([
                'amount_before_tax' => 3.96
            ])
        )
        ->calculateAmountBeforeTax()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    $invoice = $invoice_service->getBillingDocument();

    $this->assertDatabaseHas('billing_documents', [
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'currency_code' => 'MYR',
        'amount_before_tax' => 104.46,
        'amount_before_tax_after_less_advance' => 104.46,
        'tax_amount' => 0,
        'amount_after_tax' => 104.46,
        'classification' => 'AR',
        'document_date' => '2024-07-29',
        'posting_date' => null,
        'status' => BillingDocument::STATUS_DRAFT,
        'reference_no' => 'INV00009',
        'legal_entity_id' => $this->legalEntity->id,
        'legal_entity_name' => $this->legalEntity->name,
        'legal_entity_address' => $this->legalEntity->address,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'counterparty_reference_no' => null,
        'tax_code' => $this->tax->code,
        'tax_description' => $this->tax->name,
        'tax_percentage' => $this->tax->percentage,
        'payment_due_date' => '2024-08-08',
        'payment_term_id' => $this->paymentTerm->id,
        'remit_to_id' => $this->bankAccount->id,
        'remit_to_account_number' => $this->bankAccount->account_number,
        'remit_to_account_name' => $this->bankAccount->account_name,
        'remit_to_bank_address' => $this->bankAccount->address,
        'remit_to_bank_name' => $this->bankAccount->bank->name,
        'remit_to_swift_code' => $this->bankAccount->bank->swift_code,
    ]);

    expect($invoice)
        ->toBeObject(BillingDocument::class);

});

test('add line item', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getLineItems())
        ->toHaveCount(0);

    $line_item = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50
    ]);

    $invoice_service
        ->addLineItem($line_item);

    expect($invoice_service->getLineItems())
        ->toHaveCount(1)
        ->and($invoice_service->getLineItems()->first())
        ->toBeObject(\App\Models\BillingDocumentLineItem::class)
        ->toBe($line_item);

    // add second line item with billable
    $unpaid_item = \App\Models\UnpaidItem::factory()->create([]);

    $line_item = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 200,
        'billable_item_type' => get_class($unpaid_item),
        'billable_item_id' => $unpaid_item->id,
    ]);

    $invoice_service
        ->addLineItem($line_item);

    expect($invoice_service->getLineItems())
        ->toHaveCount(2)
        ->and($line_item->billableItem)
        ->toBeInstanceOf(\App\Models\UnpaidItem::class)
        ->and($line_item->billableItem->id)
        ->toBe($unpaid_item->id);

    // add third line item - discount
    $line_item = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => -50,
        'is_discount' => true,
    ]);

    $invoice_service
        ->addLineItem($line_item);

    expect($invoice_service->getLineItems())
        ->toHaveCount(3);

});

test('get all paginated billing documents', function () {
    $billing_documents = BillingDocument::factory(2)->create();

    $this->mock(BillingDocumentService::class, function (\Mockery\MockInterface $mock) use ($billing_documents) {
        $mock->shouldReceive('getAllPaginatedBillingDocuments')
        ->once()
        ->andReturn(new LengthAwarePaginator($billing_documents, 2, 1));
    });

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $response = $invoice_service->getAllPaginatedBillingDocuments()->toArray();

    expect($response['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $billing_documents[0]->id)
        ->toHaveKey('1.id', $billing_documents[1]->id);
});

test('get all billing documents', function () {
    $billing_documents = BillingDocument::factory(2)->create();

    $this->mock(BillingDocumentService::class, function (\Mockery\MockInterface $mock) use ($billing_documents) {
        $mock->shouldReceive('getAllBillingDocuments')
        ->once()
        ->andReturn($billing_documents);
    });

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $response = $invoice_service->getAllBillingDocuments()->toArray();

    expect($response)->toHaveCount(2)
        ->toHaveKey('0.id', $billing_documents[0]->id)
        ->toHaveKey('1.id', $billing_documents[1]->id);
});

test('add advance offset transaction', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAdvanceOffsetTransactions())
        ->toHaveCount(0);

    $transaction = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 300
    ]);

    $invoice_service
        ->addAdvanceOffsetTransaction($transaction);

    expect($invoice_service->getAdvanceOffsetTransactions())
        ->toHaveCount(1)
        ->and($invoice_service->getAdvanceOffsetTransactions()->first())
        ->toBeObject(\App\Models\BillingDocumentAdvanceTransaction::class)
        ->toBe($transaction);

    // add second line item
    $transaction = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 0.21
    ]);

    $invoice_service
        ->addAdvanceOffsetTransaction($transaction);

    expect($invoice_service->getAdvanceOffsetTransactions())
        ->toHaveCount(2);

});

test('calculate less advance amount', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    $transaction1 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 300
    ]);

    $transaction2 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 0.21
    ]);

    $invoice_service
        ->addAdvanceOffsetTransaction($transaction1)
        ->addAdvanceOffsetTransaction($transaction2)
        ->calculateLessAdvanceAmount();

    expect($invoice_service->getAdvanceOffsetTransactions())
        ->toHaveCount(2)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toBe(300.21);

});


test('calculate amount before tax without advance', function () {


    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300
    ]);

    $invoice_service
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax();


    expect($invoice_service->getAmountBeforeTax())
        ->toBe(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toBe(400.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toBe(400.50);

});

test('calculate amount before tax with advance', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300
    ]);

    $transaction1 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 150
    ]);

    $transaction2 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 0.21
    ]);

    $invoice_service
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->addAdvanceOffsetTransaction($transaction1)
        ->addAdvanceOffsetTransaction($transaction2)
        ->calculateLessAdvanceAmount()
        ->calculateAmountBeforeTax();

    expect($invoice_service->getAmountBeforeTax())
        ->toBe(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toBe(250.29)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toBe(250.29)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toBe(150.21);

});


test('apply tax without advance', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300
    ]);

    $tax = Tax::factory()->create([
        'percentage' => 10
    ]);

    $invoice_service
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyTax($tax);

    expect($invoice_service->getAmountBeforeTax())
        ->toBe(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toBe(440.55)
        ->and($invoice_service->getTaxAmount())
        ->toBe(40.05)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toBe(400.50);

});


test('apply tax with advance', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300
    ]);

    $transaction1 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 200
    ]);

    $tax = Tax::factory()->create([
        'percentage' => 10
    ]);

    $invoice_service
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->addAdvanceOffsetTransaction($transaction1)
        ->calculateLessAdvanceAmount()
        ->calculateAmountBeforeTax();


    expect($invoice_service->getAmountBeforeTax())
        ->toBe(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toBe(200.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(200, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toBe(200.50);


    $invoice_service
        ->applyTax($tax);

    expect($invoice_service->getAmountBeforeTax())
        ->toBe(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toBe(220.55)
        ->and($invoice_service->getTaxAmount())
        ->toBe(20.05)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(200, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toBe(200.50);

});


test('calculate amount before tax with advance should not be less than 0', function () {

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 200
    ]);

    $transaction1 = \App\Models\BillingDocumentAdvanceTransaction::factory()->create([
        'amount_before_tax' => 500
    ]);

    $invoice_service
        ->addLineItem($line_item_1)
        ->addAdvanceOffsetTransaction($transaction1)
        ->calculateLessAdvanceAmount();

    $this->expectException(\App\Exceptions\BillingLogicRestrictionException::class);
    $this->expectExceptionMessage('Less advance amount cannot be more than amount before tax.');

    $invoice_service
        ->calculateAmountBeforeTax();

});

test('create invoice with partial less advance without tax', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-03',
        'reference_no' => 'ADVINV0002A',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 50,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => GlAccount::CODE_ANY,       // any
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(250.50)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(250.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(150);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 4);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -100,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => -50,
        'currency_code' => $advance_invoice_2->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_ANY,
    ]);

    // lineitems
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => -100,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => -50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_2->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0002A dated 2024-06-03',
    ]);


    // sanity check, next advance balance should be 0
    $service = app()->make(\App\Services\Billing\AdvancePaymentService::class);

    $data = $service->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($data)
        ->toHaveCount(0);

});


test('create invoice with partial less advance with tax', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'reference_no' => 'ADVINV0002',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 50,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $tax = Tax::factory()->create([
        'percentage' => 10
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(275.55)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(250.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(25.05)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(150);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 4);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -100,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => -50,
        'currency_code' => $advance_invoice_2->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => -100,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => -50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_2->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0002 dated 2024-06-02',
    ]);

});


test('create invoice with full less advance without tax', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 500,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(0)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(400.50);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -400.50,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => -400.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
    ]);

    // sanity check, next advance balance should be 99.50
    $service = app()->make(\App\Services\Billing\AdvancePaymentService::class);

    $data = $service->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($data)
        ->toHaveCount(1)
        ->and($data->first()->toArray())
        ->toMatchArray([
            'advance_invoice_id' => $advance_invoice_1->id,
            'balance_before_tax' => 99.50,
            'gl_account_code' => GlAccount::CODE_OTHERS,
        ]);

});


test('create invoice with full less advance without tax with advance applicable to all', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 500,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_ANY,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(0)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(400.50);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -400.50,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_ANY,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'unit_price' => 0,
        'quantity' => 0,
        'amount_before_tax' => -400.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'uom_code' => Uom::CODE_DEFAULT,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice_service->getBillingDocument()->id,
        'amount_before_tax' => 400.50,
        'amount_before_tax_after_less_advance' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0
    ]);


    // sanity check, next advance balance should be 99.50
    $service = app()->make(\App\Services\Billing\AdvancePaymentService::class);

    $data = $service->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($data)
        ->toHaveCount(1)
        ->and($data->first()->toArray())
        ->toMatchArray([
            'advance_invoice_id' => $advance_invoice_1->id,
            'gl_account_code' => GlAccount::CODE_ANY,
            'balance_before_tax' => 99.50
        ]);

});

test('create invoice with full less advance with tax', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 500,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'currency_code' => 'MYR',
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $tax = Tax::factory()->create([
        'percentage' => 10
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset();

    expect($invoice_service->getLineItems()->count())
        ->toBe(3);

    $invoice_service
        ->applyTax($tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(0)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(400.50);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -400.50,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'unit_price' => 0,
        'quantity' => 0,
        'amount_before_tax' => -400.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'uom_code' => Uom::CODE_DEFAULT,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice_service->getBillingDocument()->id,
        'amount_before_tax' => 400.50,
        'amount_before_tax_after_less_advance' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0
    ]);

});


test('create invoice with different gl accounts line items, with partial less advance', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-03',
        'reference_no' => 'ADVINV0002A',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 50,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => '**********',
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 80.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 220,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_3 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 40,
        'gl_account_code' => '**********',
    ]);


    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->addLineItem($line_item_3)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(340.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(200.50)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(200.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(140);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 4);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -100,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => -40,
        'currency_code' => $advance_invoice_2->currency_code,
        'used_in_invoice_id' => $invoice_service->getBillingDocument()->id,
        'gl_account_code' => '**********',
    ]);

    expect($invoice_service->getLineItems())->toHaveCount(5);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 80.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => null,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 220,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => null,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 40,
        'gl_account_code' => '**********',
        'offset_billing_document_id' => null,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'unit_price' => 0,
        'quantity' => 0,
        'amount_before_tax' => -100,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'description' => 'Less advance payment for OTHERS. Ref No: ADVINV0001 dated 2024-06-01',
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'uom_code' => Uom::CODE_DEFAULT,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $invoice_service->getBillingDocument()->id,
        'currency_code' => 'MYR',
        'unit_price' => 0,
        'quantity' => 0,
        'amount_before_tax' => -40,
        'gl_account_code' => '**********',
        'offset_billing_document_id' => $advance_invoice_2->id,
        'description' => 'Less advance payment for SCHOOL FEES. Ref No: ADVINV0002A dated 2024-06-03',
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'uom_code' => Uom::CODE_DEFAULT,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice_service->getBillingDocument()->id,
        'amount_before_tax' => 340.50,
        'amount_before_tax_after_less_advance' => 200.50,
        'amount_after_tax' => 200.50,
        'tax_amount' => 0
    ]);

    // sanity check, next advance balance should be 1
    $service = app()->make(\App\Services\Billing\AdvancePaymentService::class);

    $data = $service->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($data)
        ->toHaveCount(1);

});

test('create credit note cannot apply advance', function () {

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 50,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);
    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);
    $invoice_service->init();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getTaxAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqualWithDelta(0, 0)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqualWithDelta(0, 0);

    $line_item_1 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 100.50,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_item_2 = \App\Models\BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $invoice_service->init()
        ->setType(\App\Models\BillingDocument::TYPE_CREDIT_NOTE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->addLineItem($line_item_1)
        ->addLineItem($line_item_2)
        ->calculateAmountBeforeTax()
        ->applyAdvanceOffset()
        ->applyTax($this->tax)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->create();

    expect($invoice_service->getAmountBeforeTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountAfterTax())
        ->toEqual(400.50)
        ->and($invoice_service->getAmountBeforeTaxLessAdvance())
        ->toEqual(400.50)
        ->and($invoice_service->getTaxAmount())
        ->toEqual(0)
        ->and($invoice_service->getLessAdvanceAmount())
        ->toEqual(0);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

});

test('change billing document status draft to confirmed', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_DRAFT,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_CONFIRMED);

    $invoice->refresh();

    expect($invoice->status)
        ->toBe(BillingDocument::STATUS_CONFIRMED);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);

});


test('change billing document status confirmed to posted', function () {

    Carbon::setTestNow('2024-08-10 04:50:01');

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'posting_date' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_POSTED);

    $invoice->refresh();

    expect($invoice->status)
        ->toBe(BillingDocument::STATUS_POSTED)
        ->and($invoice->posting_date->toDateString())->toBe('2024-08-10');

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_POSTED,
        'posting_date' => '2024-08-10',
    ]);

});


test('change billing document status confirmed to confirmed, nothing changed', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'updated_at' => '2024-02-13 02:00:00',
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_CONFIRMED);

    $invoice->refresh();

    expect($invoice->status)
        ->toBe(BillingDocument::STATUS_CONFIRMED);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'updated_at' => '2024-02-13 02:00:00',
    ]);

});

test('change advance invoice status confirmed to voided - error because it was used', function () {

    $adv_invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);
    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => $adv_invoice->amount_before_tax,
        'billable_type' => $adv_invoice->billTo->getBillToType(),
        'billable_id' => $adv_invoice->billTo->getBillToId(),
        'currency_code' => $adv_invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => null,
    ]);

    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => -$invoice->amount_before_tax,
        'billable_type' => $invoice->billTo->getBillToType(),
        'billable_id' => $invoice->billTo->getBillToId(),
        'currency_code' => $invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => $invoice->id,
    ]);

    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_ADVANCE_INVOICE);

    $this->expectExceptionMessage('Unable to void advance invoice that was previously used.');
    $invoice_service->setBillingDocument($adv_invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

});

test('change advance invoice status confirmed to voided - success', function () {

    $adv_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice_1->id,
        'amount_before_tax' => $adv_invoice_1->amount_before_tax,
        'billable_type' => $adv_invoice_1->billTo->getBillToType(),
        'billable_id' => $adv_invoice_1->billTo->getBillToId(),
        'currency_code' => $adv_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => null,
    ]);

    $adv_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-03',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 50,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice_2->id,
        'amount_before_tax' => $adv_invoice_2->amount_before_tax,
        'billable_type' => $adv_invoice_2->billTo->getBillToType(),
        'billable_id' => $adv_invoice_2->billTo->getBillToId(),
        'currency_code' => $adv_invoice_2->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => null,
    ]);

    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_ADVANCE_INVOICE);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

    $invoice_service->setBillingDocument($adv_invoice_1)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 3);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'advance_invoice_id' => $adv_invoice_1->id,
        'amount_before_tax' => -30,
        'billable_type' => $adv_invoice_1->billTo->getBillToType(),
        'billable_id' => $adv_invoice_1->billTo->getBillToId(),
        'currency_code' => $adv_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => null,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $adv_invoice_1->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);
    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $adv_invoice_2->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);

    // try to get advance balance
    $service = app()->make(AdvancePaymentService::class);

    $data = $service->setBillable($adv_invoice_1->billTo)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($data)
        ->toHaveCount(1)
        ->and($data[0])
        ->toMatchArray([
            'balance_before_tax' => 50,
        ])
        ->and($data[0]->getAdvanceInvoice()->id)
        ->toBe($adv_invoice_2->id);

});


test('cannot change invoice status voided to any status', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_VOIDED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);
    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->expectException(\App\Exceptions\BillingLogicRestrictionException::class);
    $this->expectExceptionMessage('Unable to change status for a voided billing document.');

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_CONFIRMED);

});


test('cannot change invoice status draft to posted', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_DRAFT,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_DRAFT,
    ]);
    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->expectException(\App\Exceptions\BillingLogicRestrictionException::class);
    $this->expectExceptionMessage('Unable to change from Draft to Posted status.');

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_POSTED);

});


test('cannot change invoice status posted to draft', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_POSTED,
    ]);
    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->expectException(\App\Exceptions\BillingLogicRestrictionException::class);
    $this->expectExceptionMessage('Unable to change from Posted to Draft status.');

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_DRAFT);

});


test('change invoice status confirmed to voided without advance', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);
    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 0);

    Event::fake();

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

    Event::assertDispatchedTimes(\App\Events\InvoiceVoidedEvent::class, 1);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 0);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

});


test('change invoice status confirmed to voided after payment success - error', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);

    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->expectExceptionMessage('Unable to cancel a paid billing document.');

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

});


test('change invoice status confirmed to voided when have pending payment via payment gateway - error', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

     PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING,
        'amount' => 30,
        'billing_document_id' => $invoice->id,
    ]);

    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->expectExceptionMessage(__('system_error.36011'));

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

});


test('change invoice status confirmed to voided with advance, advance amount refunded', function () {

    $adv_invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);
    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => $adv_invoice->amount_before_tax,
        'billable_type' => $adv_invoice->billTo->getBillToType(),
        'billable_id' => $adv_invoice->billTo->getBillToId(),
        'currency_code' => $adv_invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => null,
    ]);

    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => -10,
        'billable_type' => $invoice->billTo->getBillToType(),
        'billable_id' => $invoice->billTo->getBillToId(),
        'currency_code' => $invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => $invoice->id,
    ]);
    $transaction_3 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => -20,
        'billable_type' => $invoice->billTo->getBillToType(),
        'billable_id' => $invoice->billTo->getBillToId(),
        'currency_code' => $invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => $invoice->id,
    ]);

    $invoice_service = BillingDocumentFactory::getInstance(BillingDocument::TYPE_INVOICE);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 3);

    Event::fake();

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

    Event::assertDispatchedTimes(\App\Events\InvoiceVoidedEvent::class, 1);

    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 5);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);
    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $adv_invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => 10,
        'billable_type' => $invoice->billTo->getBillToType(),
        'billable_id' => $invoice->billTo->getBillToId(),
        'currency_code' => $invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => $invoice->id,
    ]);
    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'advance_invoice_id' => $adv_invoice->id,
        'amount_before_tax' => 20,
        'billable_type' => $invoice->billTo->getBillToType(),
        'billable_id' => $invoice->billTo->getBillToId(),
        'currency_code' => $invoice->currency_code,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'used_in_invoice_id' => $invoice->id,
    ]);

});


test('change invoice payment status to same status nothing changed', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => '2024-06-20 12:30:00',
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $invoice->id,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->setBillingDocument($invoice)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => '2024-06-20 12:30:00',
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);

});



test('change invoice payment status unpaid to paid error without payment object', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $this->expectExceptionMessage('Billing document requires at least 1 payment to be marked as paid.');

    $invoice_service->setBillingDocument($invoice)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));


});


test('change advance invoice payment status unpaid to paid error without payment object', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $this->expectExceptionMessage('Billing document requires at least 1 payment to be marked as paid.');

    $invoice_service->setBillingDocument($invoice)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));

});


test('change credit note payment status unpaid to paid success without payment object', function () {

    $credit_note = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_CREDIT_NOTE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    $invoice_service->setBillingDocument($credit_note)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));

    $credit_note->refresh();

    expect($credit_note->payment_status)->toBe(BillingDocument::PAYMENT_STATUS_PAID)
        ->and($credit_note->paid_at)->not()->toBeNull();

});

test('change billing document payment status unpaid to paid', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $invoice->id,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    Event::fake();

    $invoice_service->setBillingDocument($invoice)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));

    Event::assertDispatchedTimes(\App\Events\InvoicePaidEvent::class, 1);

    $invoice->refresh();

    expect($invoice->payment_status)
        ->toBe(BillingDocument::PAYMENT_STATUS_PAID)
        ->and($invoice->paid_at->toDateTimeString())
        ->toBe('2024-06-26 04:30:00');

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => '2024-06-26 04:30:00',
    ]);

});


test('change billing document payment status unpaid to paid - assert listener triggered', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $invoice->id,
    ]);

    $invoice_service = app()->make(\App\Services\Billing\BillingDocumentService::class);

    Queue::fake();

    $invoice_service->setBillingDocument($invoice)
        ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, \Carbon\Carbon::parse('2024-06-26 12:30:00', 'Asia/Kuala_Lumpur'));

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function($job) {
            return $job->class === \App\Listeners\CreateAdvanceTransactions::class;
        }
    );

    $invoice->refresh();

    expect($invoice->payment_status)
        ->toBe(BillingDocument::PAYMENT_STATUS_PAID)
        ->and($invoice->paid_at->toDateTimeString())
        ->toBe('2024-06-26 04:30:00');

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => '2024-06-26 04:30:00',
    ]);

});

test('determine apply dates from line items', function () {

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 60,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 100,
        'period' => '2024-02-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 10,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 20,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 30,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'currency_code' => 'MYR',
        'billable_item_id' => null,
        'billable_item_type' => null,
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 40,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'currency_code' => 'MYR',
        'billable_item_id' => null,
        'billable_item_type' => null,
    ]);

    $service = app()->make(BillingDocumentService::class);
    $dates = $service->addLineItem($line_item2)
        ->addLineItem($line_item1)
        ->addLineItem($line_item3)
        ->setDocumentDate(Carbon::parse('2024-08-12'))
        ->setBillToParty($this->student)
        ->determineApplyDates();

    expect($dates->count())->toBe(3)
        ->and($dates[0])->toBe('2024-01-01')
        ->and($dates[1])->toBe('2024-02-01')
        ->and($dates[2])->toBe('2024-08-12');


});


test('apply eligible discounts - no discount available', function () {

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
    ]);

    $service = app()->make(BillingDocumentService::class);

    $dates = $service->addLineItem($line_item2)
        ->addLineItem($line_item1)
        ->addLineItem($line_item3)
        ->addLineItem($line_item4)
        ->setDocumentDate(Carbon::parse('2024-08-12'))
        ->setBillToParty($this->student)
        ->determineApplyDates();

    expect($dates->count())->toBe(2)
        ->and($dates[0])->toBe('2024-01-01')
        ->and($dates[1])->toBe('2024-02-01');

    $service->calculateEligibleDiscounts();

    $applied_discounts = collect($service->getEligibleDiscounts());

    expect($applied_discounts->count())
        ->toEqual(0);

});

test('apply eligible discounts - 50% discount school fees only', function () {

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $this->student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['SCH00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jan 2024',
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);

    $service = app()->make(BillingDocumentService::class);

    // create invoice and line items first without discount
    $dates = $service
        ->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->addLineItem($line_item2)
        ->addLineItem($line_item1)
        ->addLineItem($line_item3)
        ->addLineItem($line_item4)
        ->calculateAmountBeforeTax()
        ->applyTax($this->tax)
        ->create();

    expect($service->getLineItems()->count())->toEqual(4);
    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 4);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 1900,
    ]);

    $dates = $service->determineApplyDates();

    expect($dates->count())->toBe(2)
        ->and($dates[0])->toBe('2024-01-01')
        ->and($dates[1])->toBe('2024-02-01');

    // apply discount afterwards and recalculate amount before tax
    $service->calculateEligibleDiscounts()
        ->addDiscountLineItems()
        ->recalculateAndUpdateAfterDiscount();

    $applied_discounts = collect($service->getEligibleDiscounts());

    expect($applied_discounts->count())
        ->toEqual(2)
        ->and(array_keys($applied_discounts[0]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[0]['original_line_item']->id)->toBe($line_item2->id)
        ->and($applied_discounts[0]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[0]['discount_setting']->used_amount)->toEqual(350)
        ->and($applied_discounts[0]['amount_before_tax'])->toEqual(150)
        ->and(array_keys($applied_discounts[1]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[1]['original_line_item']->id)->toBe($line_item1->id)
        ->and($applied_discounts[1]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[1]['discount_setting']->used_amount)->toEqual(350)
        ->and($applied_discounts[1]['amount_before_tax'])->toEqual(200)
        ->and($service->getLineItems()->count())->toEqual(6);

    $setting1->refresh();

    expect($setting1->used_amount)->toEqual(350);

    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 6);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item1->id,
        'gl_account_code' => $line_item1->gl_account_code,
        'description' => "Discount for School Fees Jan 2024.\nPin Hwa High School High Achievers Scholarship",
        'amount_before_tax' => -200
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item2->id,
        'gl_account_code' => $line_item2->gl_account_code,
        'description' => "Discount for School Fees Feb 2024.\nPin Hwa High School High Achievers Scholarship",
        'amount_before_tax' => -150
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 1550,
        'amount_before_tax_after_less_advance' => 1550,
        'amount_after_tax' => 1550,
        'tax_amount' => 0,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 350,
        'max_amount' => null,
    ]);
});


test('apply eligible discounts - fixed fees for both school and hostel fees, with advance payment and tax', function () {

    $tax = Tax::factory()->create([
        'percentage' => 10
    ]);

    // advance invoice
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 200,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_1 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => 'SCH00000001',
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'reference_no' => 'ADVINV0002',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 200,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
    ]);

    $transaction_2 = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => 'HOS0000001',
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['SCH00000001', 'HOS0000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 300
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 200,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 200,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jan 2024',
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);

    $service = app()->make(BillingDocumentService::class);

    // create invoice and line items first without discount and advance
    $service
        ->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-08-15'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->addLineItem($line_item1)
        ->addLineItem($line_item2)
        ->addLineItem($line_item3)
        ->addLineItem($line_item4)
        ->calculateAmountBeforeTax()
        ->applyTax($tax)
        ->create();

    expect($service->getLineItems()->count())->toEqual(4);

    $this->assertDatabaseCount(BillingDocument::class, 3);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 4);
    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 2);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 1800,
        'amount_before_tax_after_less_advance' => 1800,
        'amount_after_tax' => 1980,
        'tax_amount' => 180
    ]);

    $dates = $service->determineApplyDates();

    expect($dates->count())->toBe(2)
        ->and($dates[0])->toBe('2024-01-01')
        ->and($dates[1])->toBe('2024-02-01');

    // check advance balances
    $advance_data = app()->make(AdvancePaymentService::class)
        ->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($advance_data->where('advanceInvoice.id', $advance_invoice_1->id)->first()->balanceBeforeTax)->toEqual(200);
    expect($advance_data->where('advanceInvoice.id', $advance_invoice_2->id)->first()->balanceBeforeTax)->toEqual(200);

    // apply discount first, then apply advance and recalculate amount before tax
    $service->calculateEligibleDiscounts()
        ->addDiscountLineItems()
        ->applyAdvanceOffset()
        ->createAdvanceOffsetTransactions()     // need to call this if advance is applied after invoice already created.
        ->recalculateAndUpdateAfterDiscount();

    $applied_discounts = collect($service->getEligibleDiscounts());

    expect($applied_discounts->count())
        ->toEqual(4)
        ->and(array_keys($applied_discounts[0]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[0]['original_line_item']->id)->toBe($line_item1->id)
        ->and($applied_discounts[0]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[0]['discount_setting']->used_amount)->toEqual(1100)
        ->and($applied_discounts[0]['amount_before_tax'])->toEqual(300)
        ->and(array_keys($applied_discounts[1]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[1]['original_line_item']->id)->toBe($line_item2->id)
        ->and($applied_discounts[1]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[1]['discount_setting']->used_amount)->toEqual(1100)
        ->and($applied_discounts[1]['amount_before_tax'])->toEqual(200)
        ->and(array_keys($applied_discounts[2]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[2]['original_line_item']->id)->toBe($line_item3->id)
        ->and($applied_discounts[2]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[2]['discount_setting']->used_amount)->toEqual(1100)
        ->and($applied_discounts[2]['amount_before_tax'])->toEqual(300)
        ->and(array_keys($applied_discounts[3]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[3]['original_line_item']->id)->toBe($line_item4->id)
        ->and($applied_discounts[3]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[3]['discount_setting']->used_amount)->toEqual(1100)
        ->and($applied_discounts[3]['amount_before_tax'])->toEqual(300)
        ->and($service->getLineItems()->count())->toEqual(10);

    $this->assertDatabaseCount(BillingDocument::class, 3);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 10);
    $this->assertDatabaseCount(BillingDocumentAdvanceTransaction::class, 4);

    expect(BillingDocumentLineItem::where('billable_item_type', UnpaidItem::class)->count())->toBe(4);
    expect(BillingDocumentLineItem::where('is_discount', true)->count())->toBe(4);
    expect(BillingDocumentLineItem::whereNotNull('offset_billing_document_id')->count())->toBe(2);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item1->id,
        'gl_account_code' => $line_item1->gl_account_code,
        'description' => "Discount for School Fees Jan 2024.",
        'amount_before_tax' => -300
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item2->id,
        'gl_account_code' => $line_item2->gl_account_code,
        'description' => "Discount for School Fees Feb 2024.",
        'amount_before_tax' => -200
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item3->id,
        'gl_account_code' => $line_item3->gl_account_code,
        'description' => "Discount for Hostel Fees Jan 2024.",
        'amount_before_tax' => -300
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item4->id,
        'gl_account_code' => $line_item4->gl_account_code,
        'description' => "Discount for Hostel Fees Feb 2024.",
        'amount_before_tax' => -300
    ]);

    // less advance
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
        'offset_billing_document_id' => $advance_invoice_1->id,
        'gl_account_code' => $line_item1->gl_account_code,
        'description' => "Less advance payment for {$line_item1->glAccount->label}. Ref No: {$advance_invoice_1->reference_no} dated {$advance_invoice_1->document_date}",
        'amount_before_tax' => -100
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => false,
        'discount_id' => null,
        'discount_original_line_item_id' => null,
        'offset_billing_document_id' => $advance_invoice_2->id,
        'gl_account_code' => $line_item3->gl_account_code,
        'description' => "Less advance payment for {$line_item3->glAccount->label}. Ref No: {$advance_invoice_2->reference_no} dated {$advance_invoice_2->document_date}",
        'amount_before_tax' => -200
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 700,
        'amount_before_tax_after_less_advance' => 400,
        'amount_after_tax' => 440,
        'tax_amount' => 40,
        'tax_percentage' => 10,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 1100,
        'max_amount' => null,
    ]);

    // advance transaction
    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => -100,
        'currency_code' => $advance_invoice_1->currency_code,
        'used_in_invoice_id' => $service->getBillingDocument()->id,
        'gl_account_code' => $line_item1->gl_account_code,
    ]);

    $this->assertDatabaseHas(BillingDocumentAdvanceTransaction::class, [
        'billable_type' => $this->student->getBillToType(),
        'billable_id' => $this->student->getBillToId(),
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => -200,
        'currency_code' => $advance_invoice_2->currency_code,
        'used_in_invoice_id' => $service->getBillingDocument()->id,
        'gl_account_code' => $line_item3->gl_account_code,
    ]);

    // check adv balance after that
    $advance_data = app()->make(AdvancePaymentService::class)
        ->setBillable($this->student)
        ->getEligibleAdvancesForCurrency('MYR')
        ->getAdvanceBalances();

    expect($advance_data->where('advanceInvoice.id', $advance_invoice_1->id)->first()->balanceBeforeTax)->toEqual(100);
    expect($advance_data->where('advanceInvoice.id', $advance_invoice_2->id)->first())->toBeNull();

});



test('apply eligible discounts - fixed fees with multiple eligible discount', function () {

    $tax = Tax::factory()->create([
        'percentage' => 0
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['HOS0000001']),
        'effective_from' => '2025-05-01',
        'effective_to' => '2025-10-01',
        'is_active' => 1,
        'basis_amount' => 320,
        'max_amount' => 1920,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['HOS0000001']),
        'effective_from' => '2025-04-01',
        'effective_to' => '2025-06-01',
        'is_active' => 1,
        'basis_amount' => 710,
        'max_amount' => 1280,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 710,
        'period' => '2025-04-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 710,
        'period' => '2025-05-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 710,
        'period' => '2025-06-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 710,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'Hostel Fees Apr 2025',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 710,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'Hostel Fees May 2025',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 710,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jun 2025',
    ]);

    $service = app()->make(BillingDocumentService::class);

    // create invoice and line items first without discount and advance
    $service
        ->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2025-04-22'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->addLineItem($line_item1)
        ->addLineItem($line_item2)
        ->addLineItem($line_item3)
        ->calculateAmountBeforeTax()
        ->applyTax($tax)
        ->create();

    expect($service->getLineItems()->count())->toEqual(3);

    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 3);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 2130,
        'amount_before_tax_after_less_advance' => 2130,
        'amount_after_tax' => 2130,
        'tax_amount' => 0
    ]);

    $dates = $service->determineApplyDates();

    expect($dates->count())->toBe(3)
        ->and($dates[0])->toBe('2025-04-01')
        ->and($dates[1])->toBe('2025-05-01')
        ->and($dates[2])->toBe('2025-06-01');

    // apply discount
    $service->calculateEligibleDiscounts()
        ->addDiscountLineItems()
        ->recalculateAndUpdateAfterDiscount();

    $applied_discounts = collect($service->getEligibleDiscounts());

    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 8);

    expect(BillingDocumentLineItem::where('billable_item_type', UnpaidItem::class)->count())->toBe(3);
    expect(BillingDocumentLineItem::where('is_discount', true)->count())->toBe(5);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting2->id,
        'discount_original_line_item_id' => $line_item1->id,
        'gl_account_code' => $line_item1->gl_account_code,
        'description' => "Discount for Hostel Fees Apr 2025.",
        'amount_before_tax' => -710
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item2->id,
        'gl_account_code' => $line_item2->gl_account_code,
        'description' => "Discount for Hostel Fees May 2025.",
        'amount_before_tax' => -320
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting2->id,
        'discount_original_line_item_id' => $line_item2->id,
        'gl_account_code' => $line_item2->gl_account_code,
        'description' => "Discount for Hostel Fees May 2025.",
        'amount_before_tax' => -390
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item3->id,
        'gl_account_code' => $line_item3->gl_account_code,
        'description' => "Discount for Hostel Fees Jun 2025.",
        'amount_before_tax' => -320
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting2->id,
        'discount_original_line_item_id' => $line_item3->id,
        'gl_account_code' => $line_item3->gl_account_code,
        'description' => "Discount for Hostel Fees Jun 2025.",
        'amount_before_tax' => -180
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 210,
        'amount_before_tax_after_less_advance' => 210,
        'amount_after_tax' => 210,
        'tax_amount' => 0,
        'tax_percentage' => 0,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 640,
        'max_amount' => 1920,
    ]);
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting2->id,
        'used_amount' => 1280,
        'max_amount' => 1280,
    ]);

});

test('apply eligible discounts - 100% discount hostel fees with max limit', function () {

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $this->student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['HOS0000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 200,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jan 2024',
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->make([
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);

    $service = app()->make(BillingDocumentService::class);

    // create invoice and line items first without discount
    $dates = $service
        ->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setDocumentDate(\Carbon\Carbon::parse('2024-07-29'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($this->student)
        ->setPaymentTerm($this->paymentTerm)
        ->setRemitToBankAccount($this->bankAccount)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->addLineItem($line_item4)
        ->addLineItem($line_item2)
        ->addLineItem($line_item1)
        ->addLineItem($line_item3)
        ->calculateAmountBeforeTax()
        ->applyTax($this->tax)
        ->create();

    expect($service->getLineItems()->count())->toEqual(4);
    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 4);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 1900,
    ]);

    $dates = $service->determineApplyDates();

    expect($dates->count())->toBe(2)
        ->and($dates[0])->toBe('2024-01-01')
        ->and($dates[1])->toBe('2024-02-01');

    expect($setting1->used_amount)->toEqual(200);

    // apply discount afterwards and recalculate amount before tax
    $service->calculateEligibleDiscounts()
        ->addDiscountLineItems()
        ->recalculateAndUpdateAfterDiscount();

    $applied_discounts = collect($service->getEligibleDiscounts());

    expect($applied_discounts->count())
        ->toEqual(2)
        ->and(array_keys($applied_discounts[0]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[0]['original_line_item']->id)->toBe($line_item4->id)
        ->and($applied_discounts[0]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[0]['discount_setting']->used_amount)->toEqual(1000)
        ->and($applied_discounts[0]['amount_before_tax'])->toEqual(600)
        ->and(array_keys($applied_discounts[1]))
        ->toMatchArray(['original_line_item', 'discount_setting', 'amount_before_tax'])
        ->and($applied_discounts[1]['original_line_item']->id)->toBe($line_item3->id)
        ->and($applied_discounts[1]['discount_setting']->id)->toBe($setting1->id)
        ->and($applied_discounts[1]['discount_setting']->used_amount)->toEqual(1000)
        ->and($applied_discounts[1]['amount_before_tax'])->toEqual(200)
        ->and($service->getLineItems()->count())->toEqual(6);

    $setting1->refresh();

    expect($setting1->used_amount)->toEqual(1000);

    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 6);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item3->id,
        'gl_account_code' => $line_item3->gl_account_code,
        'description' => "Discount for Hostel Fees Jan 2024.\nPin Hwa High School High Achievers Scholarship",
        'amount_before_tax' => -200
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $service->getBillingDocument()->id,
        'product_id' => null,
        'billable_item_type' => null,
        'billable_item_id' => null,
        'is_discount' => true,
        'discount_id' => $setting1->id,
        'discount_original_line_item_id' => $line_item4->id,
        'gl_account_code' => $line_item4->gl_account_code,
        'description' => "Discount for Hostel Fees Feb 2024.\nPin Hwa High School High Achievers Scholarship",
        'amount_before_tax' => -600
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $service->getBillingDocument()->id,
        'amount_before_tax' => 1100,
        'amount_before_tax_after_less_advance' => 1100,
        'amount_after_tax' => 1100,
        'tax_amount' => 0,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 1000,
        'max_amount' => 1000,
    ]);
});


test('test billable item get reset to unpaid when invoice is voided', function() {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'period' => '2025-01-01',
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 60,
        'paid_at' => null,
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'period' => '2025-02-01',
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 100,
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($unpaid_item1),
        'billable_item_id' => $unpaid_item1->id,
        'amount_before_tax' => 60,
        'gl_account_code' => 'ABC123',
        'currency_code' => 'MYR',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($unpaid_item2),
        'billable_item_id' => $unpaid_item2->id,
        'amount_before_tax' => 100,
        'gl_account_code' => 'ABC456',
        'currency_code' => 'MYR',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 70,
        'gl_account_code' => 'ABC456',
        'currency_code' => 'MYR',
    ]);

    // trigger event listener
    $event = new \App\Events\InvoiceVoidedEvent($invoice);
    $listener = new \App\Listeners\InvoiceVoidedCallback();

    $listener->handle($event);

    // unpaid item reset to unpaid
    $this->assertDatabaseHas(UnpaidItem::class, [
        'id' => $unpaid_item1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'billing_document_id' => null,
    ]);
    $this->assertDatabaseHas(UnpaidItem::class, [
        'id' => $unpaid_item2->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'billing_document_id' => null,
    ]);


});

test('refund discounted amount when invoice is voided', function() {

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $this->student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['SCH00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 150,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['SCH00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 1400,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 550,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jan 2024',
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);
    $discount_line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -150,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting1->id,
    ]);
    $discount_line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting2->id,
    ]);
    $discount_line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting2->id,
    ]);

    $invoice->refresh();
    expect($invoice->lineItems)->toHaveCount(7);
    expect($invoice->lineItems->where('is_discount', true)->values())->toHaveCount(3);

    $invoice_service = BillingDocumentFactory::getInstance($invoice->type);

    $invoice_service->setBillingDocument($invoice)
        ->changeStatusTo(BillingDocument::STATUS_VOIDED);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_VOIDED,
        'tax_amount' => 0,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 0,
        'max_amount' => null,
    ]);
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting2->id,
        'used_amount' => 200,
        'max_amount' => null,
    ]);

});


test('refundDiscountedAmount', function() {


    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $this->student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['SCH00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 150,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode(['SCH00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 1400,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
    ]);

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'amount_before_tax' => 550,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Hostel Fees Jan 2024',
    ]);
    $line_item4 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);
    $discount_line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -150,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting1->id,
    ]);
    $discount_line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting2->id,
    ]);
    $discount_line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'amount_before_tax' => -600,
        'gl_account_code' => 'HOS0000001',
        'currency_code' => 'MYR',
        'is_discount' => true,
        'discount_id' => $setting2->id,
    ]);

    $invoice->refresh();
    expect($invoice->lineItems)->toHaveCount(7);
    expect($invoice->lineItems->where('is_discount', true)->values())->toHaveCount(3);

    $invoice_service = BillingDocumentFactory::getInstance($invoice->type);

    $invoice_service->setBillingDocument($invoice)
        ->refundDiscountedAmount();

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting1->id,
        'used_amount' => 0,
        'max_amount' => null,
    ]);
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $setting2->id,
        'used_amount' => 200,
        'max_amount' => null,
    ]);

});


test('add unpaid items to invoice', function () {
    // todo implement this
    // unpaid items when added to invoice needs to be sorted by apply date asc.
    // add function to conveniently create invoice from list of unpaid items
})->todo();
