<?php

use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\PaymentTerm;
use App\Models\Product;
use App\Models\Student;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Repositories\BillingDocumentRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->billingDocumentRepository = resolve(BillingDocumentRepository::class);

    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);

    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);

    GlAccount::factory()->create([
        'code' => GlAccount::CODE_ANY,
        'label' => 'ANY'
    ]);
    $this->glAccount = GlAccount::factory()->create([
        'code' => GlAccount::CODE_OTHERS,
        'label' => 'OTHERS'
    ]);
    $this->glAccount2 = GlAccount::factory()->create([
        'code' => '**********',
        'label' => 'SCHOOL FEES'
    ]);

});

test('test getPaidInvoicesEligibleForBackApply', function () {

    // paid invoice, eligible for backapply discount
    $invoice1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    // paid invoice but already has discount applied.
    $invoice2 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    // unpaid invoice
    $invoice3 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['SCH00000001', 'HOS00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item5 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2023-12-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item6 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-03-01',
        'gl_account_code' => 'SCH00000001',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 600,
        'gl_account_code' => 'XXX00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Other Fees Jan 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item5->id,
        'billable_item_type' => get_class($unpaid_item5),
        'description' => 'School Fees Dec 2023',
    ]);


    $line_item4 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item5 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 600,
        'gl_account_code' => 'XXX00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Other Fees Feb 2024',
    ]);

    // discount for invoice2 school fees lineitem4
    $line_item6 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => -300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => null,
        'billable_item_type' => null,
        'description' => 'Discount for School Fees Feb 2024',
        'is_discount' => true,
        'discount_original_line_item_id' => $line_item4->id,
    ]);

    $line_item7 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice3,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item6->id,
        'billable_item_type' => get_class($unpaid_item6),
        'description' => 'School Fees March 2024',
    ]);

    $data = $this->billingDocumentRepository->getPaidInvoicesEligibleForBackApply([
        'period_from' => '2024-01-01',
        'period_to' => '2024-06-01',
        'bill_to_id' => $this->student->id,
        'bill_to_type' => Student::class,
        'gl_account_codes' => $discount->getGlAccountCodes(),
    ]);

    expect($data)->toHaveCount(1)
        ->and($data->first())->toBeInstanceOf(BillingDocument::class)
        ->and($data->first()->id)->toBe($invoice1->id);

});

test('getModelClass()', function () {
    $response = $this->billingDocumentRepository->getModelClass();

    expect($response)->toEqual(BillingDocument::class);
});

test('getAll()', function () {
    $billing_documents = BillingDocument::factory(3)->state(new Sequence(
        [
            'type' => BillingDocument::TYPE_INVOICE,
            'status' => BillingDocument::STATUS_DRAFT,
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        ],
        [
            'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        ],
        [
            'type' => BillingDocument::TYPE_CREDIT_NOTE,
            'status' => BillingDocument::STATUS_VOIDED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PARTIAL,
        ],
    ))->create();

    $response = $this->billingDocumentRepository->getAll()->toArray();

    expect($response)->toEqual($billing_documents->toArray());
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $second_student = Student::factory()->create();

    $billing_documents = [
        'first' => BillingDocument::factory()->create([
            'reference_no' => 'INV-*********',
            'document_date' => '2024-12-29',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 100,
            'amount_after_tax' => 100,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
            'bill_to_name' => $this->student->getBillToName(),
            'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
            'bill_to_address' => $this->student->address,
            'paid_at' => '2025-01-01 14:30:00',
        ]),
        'second' => BillingDocument::factory()->create([
            'document_date' => '2024-12-30',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ECOMMERCE,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 100,
            'amount_after_tax' => 100,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
            'bill_to_name' => $this->student->getBillToName(),
            'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
            'bill_to_address' => $this->student->address,
            'paid_at' => '2024-12-30 14:30:00',
        ]),
        'third' => BillingDocument::factory()->create([
            'document_date' => '2024-12-30',
            'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ENROLLEMENT_EXAM_FEES,
            'status' => BillingDocument::STATUS_DRAFT,
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
            'amount_before_tax' => 100,
            'amount_after_tax' => 100,
            'bill_to_type' => $second_student->getBillToType(),
            'bill_to_id' => $second_student->getBillToId(),
            'bill_to_name' => $second_student->getBillToName(),
            'bill_to_reference_number' => $second_student->getBillToReferenceNumber(),
            'bill_to_address' => $second_student->address,
            'paid_at' => null
        ]),
    ];

    $payment = Payment::factory()->create([
        'billing_document_id' => $billing_documents['first']->id,
        'payment_method_id' => PaymentMethod::factory()->create()->id,
        'payment_reference_no' => 'PAYEX-123456',
    ]);

    $filter_options = [
        'first_document_date' => [
            'document_date_from' => '2024-12-29',
            'document_date_to' => '2024-12-29',
        ],
        'second_document_date' => [
            'document_date_from' => '2024-12-30',
            'document_date_to' => '2024-12-30',
        ],
        'all_document_date' => [
            'document_date_from' => '2024-12-29',
            'document_date_to' => '2024-12-30',
        ],
        'first_paid_at' => [
            'paid_at_from' => '2025-01-01 00:00:01',
            'paid_at_to' => '2025-01-01 23:00:01',
        ],
        'second_paid_at' => [
            'paid_at_from' => '2024-12-01 00:00:01',
            'paid_at_to' => '2024-12-31 23:00:01',
        ],
        'first_student' => [
            'bill_to_type' => get_class($this->student),
            'bill_to_id' => $this->student->id,
        ],
        'second_student' => [
            'bill_to_type' => get_class($second_student),
            'bill_to_id' => $second_student->id,
        ],
        'first_id' => [
            'id' => $billing_documents['first']->id,
        ],
    ];

    $actual_filters = isset($filter_options[$filter_by]) ? $filter_options[$filter_by] : [$filter_by => $filter_value];

    $result = $this->billingDocumentRepository->getAllPaginated($actual_filters)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($billing_documents[$value]->toArray());
    }
})->with([
    'filter by first_document_date' => [1, 'first_document_date', null, ['first']],
    'filter by second_document_date' => [2, 'second_document_date', null, ['second', 'third']],
    'filter by all_document_date' => [3, 'all_document_date', null, ['first', 'second', 'third']],
    'filter by reference_no = INV-*********' => [1, 'reference_no', 'INV-*********', ['first']],
    'filter by payment_reference_no = PAYEX-123456' => [1, 'payment_reference_no', 'PAYEX-123456', ['first']],
    'filter by payment_reference_no = NON_EXISTING' => [0, 'payment_reference_no', 'NON_EXISTING', []],
    'filter by first_paid_at' => [1, 'first_paid_at', null, ['first']],
    'filter by second_paid_at' => [1, 'second_paid_at', null, ['second']],
    'filter by sub_type = SUB_TYPE_ENROLLEMENT_EXAM_FEES' => [1, 'sub_type', BillingDocument::SUB_TYPE_ENROLLEMENT_EXAM_FEES, ['third']],
    'filter by sub_type = SUB_TYPE_ECOMMERCE' => [1, 'sub_type', BillingDocument::SUB_TYPE_ECOMMERCE, ['second']],
    'filter by type = TYPE_INVOICE' => [2, 'type', BillingDocument::TYPE_INVOICE, ['first', 'second']],
    'filter by type = TYPE_ADVANCE_INVOICE' => [1, 'type', BillingDocument::TYPE_ADVANCE_INVOICE, ['third']],
    'filter by payment_status = PAYMENT_STATUS_PAID' => [2, 'payment_status', BillingDocument::PAYMENT_STATUS_PAID, ['first', 'second']],
    'filter by payment_status = PAYMENT_STATUS_UNPAID' => [1, 'payment_status', BillingDocument::PAYMENT_STATUS_UNPAID, ['third']],
    'filter by status = STATUS_CONFIRMED' => [2, 'status', BillingDocument::STATUS_CONFIRMED, ['first']],
    'filter by status = STATUS_DRAFT' => [1, 'status', BillingDocument::STATUS_DRAFT, ['third']],
    'filter by first_student' => [2, 'first_student', null, ['first', 'second']],
    'filter by second_student' => [1, 'second_student', null, ['third']],
    'filter by first_id' => [1, 'first_id', null, ['first']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by document_date asc' => [3, 'order_by', ['document_date' => 'asc'], ['first', 'second', 'third']],
    'sort by document_date desc' => [3, 'order_by', ['document_date' => 'desc'], ['second', 'third', 'first']],
    'sort by type asc' => [3, 'order_by', ['type' => 'asc'], ['third', 'first', 'second']],
    'sort by type desc' => [3, 'order_by', ['type' => 'desc'], ['first', 'second', 'third']],
]);

test('getPaidInvoiceReportData()', function () {
    $students = Student::factory(5)->create();

    $billing_documents = BillingDocument::factory(6)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 234.50,
            'bill_to_type' => $students[0]->getBillToType(),
            'bill_to_id' => $students[0]->getBillToId(),
            'bill_to_name' => $students[0]->getBillToName(),
            'bill_to_reference_number' => $students[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:00:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 123.50,
            'bill_to_type' => $students[1]->getBillToType(),
            'bill_to_id' => $students[1]->getBillToId(),
            'bill_to_name' => $students[1]->getBillToName(),
            'bill_to_reference_number' => $students[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-05-01 12:00:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-08-02',
            'amount_after_tax' => 345.50,
            'bill_to_type' => $students[2]->getBillToType(),
            'bill_to_id' => $students[2]->getBillToId(),
            'bill_to_name' => $students[2]->getBillToName(),
            'bill_to_reference_number' => $students[2]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-31 15:59:59',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-01-01',
            'amount_after_tax' => 456.50,
            'bill_to_type' => $students[2]->getBillToType(),
            'bill_to_id' => $students[2]->getBillToId(),
            'bill_to_name' => $students[2]->getBillToName(),
            'bill_to_reference_number' => $students[2]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-31 16:00:00',     // excluded coz past the filter time
        ],
        [
            'status' => BillingDocument::STATUS_POSTED, // to be excluded because status is not STATUS_CONFIRMED
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-03-01',
            'amount_after_tax' => 456.50,
            'bill_to_type' => $students[3]->getBillToType(),
            'bill_to_id' => $students[3]->getBillToId(),
            'bill_to_name' => $students[3]->getBillToName(),
            'bill_to_reference_number' => $students[3]->getBillToReferenceNumber(),
            'paid_at' => null,
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ECOMMERCE, // to be excluded because sub_type is not SUB_TYPE_FEES
            'document_date' => '2024-01-01',
            'amount_after_tax' => 156.50,
            'bill_to_type' => $students[4]->getBillToType(),
            'bill_to_id' => $students[4]->getBillToId(),
            'bill_to_name' => $students[4]->getBillToName(),
            'bill_to_reference_number' => $students[4]->getBillToReferenceNumber(),
            'paid_at' => now()->toDateTimeString(),
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(5)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'School Fees Aug 2024',
            'period' => '2024-08-02',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'School Fees Aug 2024',
            'period' => '2024-08-02',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[3]->id,
            'description' => 'School Fees Dec 2024',
            'period' => '2024-12-01',
        ],
    ))->create();

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $line_items = BillingDocumentLineItem::factory(9)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Jan 2024',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 134.50, // 234.50 - 100 = 134.50
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Mar 2024',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 99, // 234.50 - 134.50 - 1 = 99
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Unrelated Charge 2024',
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'School Fees Aug 2024',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 122.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Unrelated Charge 2024',
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'School Fees Aug 2024',
            'billable_item_id' => $unpaid_items[3]->id,
            'billable_item_type' => get_class($unpaid_items[3]),
            'amount_before_tax' => 344.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'Unrelated Charge 2024',
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[3]->id,
            'description' => 'School Fees Dec 2024',
            'billable_item_id' => $unpaid_items[4]->id,
            'billable_item_type' => get_class($unpaid_items[4]),
        ],
        [
            'billing_document_id' => $billing_documents[4]->id,
            'description' => 'School Fees Dec 2024',
            'billable_item_id' => $unpaid_items[4]->id,
            'billable_item_type' => get_class($unpaid_items[4]),
        ],
    ))->create();

    $payment_methods = PaymentMethod::factory(3)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ],
    ))->create();

    $payment_requests = PaymentRequest::factory(2)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // BANK_TRANSFER
            'bank_id' => $this->bank->id, // to MAYBANK
        ],
        // below payment requests are excluded because not in filtered
        [
            'billing_document_id' => $billing_documents[3]->id,
            'payment_method_id' => $payment_methods[2]->id, // BANK_TRANSFER
            'bank_id' => $this->bank->id, // to MAYBANK
        ],
    ))->create();

    $payments = Payment::factory(6)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[0]->reference_no,
            'amount_received' => 50,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 184.50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Bank Transfer
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 123.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[2]->reference_no,
            'amount_received' => 345.50,
        ],
        [
            'billing_document_id' => $billing_documents[3]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 456.50,
        ],
        [
            'billing_document_id' => $billing_documents[5]->id,
            'payment_method_id' => $payment_methods[2]->id, // Bank Transfer
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 156.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[1]->id,
        ],
    ))->create();


    $filters = [
        'payment_date_from' => '2024-01-01',
        'payment_date_to' => '2024-12-31',
    ];

    $response = $this->billingDocumentRepository
        ->getPaidInvoiceReportData($filters)
        ->keyBy('id')
        ->toArray();

    expect($response)->toHaveCount(3)
        ->and($response)->toHaveKeys([$billing_documents[0]->id, $billing_documents[1]->id, $billing_documents[2]->id])
        ->and($response)->not->toHaveKeys([$billing_documents[3]->id, $billing_documents[4]->id])

        // Invoice 1
        ->and($response[$billing_documents[0]->id])->toMatchArray([
            'id' => $billing_documents[0]->id,
            'document_date' => $billing_documents[0]->document_date,
            'reference_no' => $billing_documents[0]->reference_no,
            'bill_to_reference_number' => $billing_documents[0]->bill_to_reference_number,
            'paid_at' => Carbon::parse($billing_documents[0]->paid_at)->toISOString(),
            'currency_code' => $billing_documents[0]->currency_code,
            'amount_after_tax' => $billing_documents[0]->amount_after_tax,
            'line_items' => [
                [
                    'id' => $line_items[0]->id,
                    'billing_document_id' => $line_items[0]->billing_document_id,
                    'description' => $line_items[0]->description,
                    'is_discount' => $line_items[0]->is_discount,
                    'offset_billing_document_id' => $line_items[0]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[0]->amount_before_tax,
                    'product_id' => $line_items[0]->product_id,
                    'discount_original_line_item_id' => $line_items[0]->discount_original_line_item_id,
                ],
                [
                    'id' => $line_items[1]->id,
                    'billing_document_id' => $line_items[1]->billing_document_id,
                    'description' => $line_items[1]->description,
                    'is_discount' => $line_items[1]->is_discount,
                    'offset_billing_document_id' => $line_items[1]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[1]->amount_before_tax,
                    'product_id' => $line_items[1]->product_id,
                    'discount_original_line_item_id' => $line_items[1]->discount_original_line_item_id,
                ],
                [
                    'id' => $line_items[2]->id,
                    'billing_document_id' => $line_items[2]->billing_document_id,
                    'description' => $line_items[2]->description,
                    'is_discount' => $line_items[2]->is_discount,
                    'offset_billing_document_id' => $line_items[2]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[2]->amount_before_tax,
                    'product_id' => $line_items[2]->product_id,
                    'discount_original_line_item_id' => $line_items[2]->discount_original_line_item_id,
                ],
            ],
            'payments' => [
                [
                    'id' => $payments[0]->id,
                    'billing_document_id' => $payments[0]->billing_document_id,
                    'payment_method_id' => $payments[0]->payment_method_id,
                    'payment_reference_no' => $payments[0]->payment_reference_no,
                    'payment_method' => [
                        'id' => $payment_methods[0]->id,
                        'code' => PaymentMethod::CODE_CASH,
                    ],
                    'payment_source_type' => $payments[0]->payment_source_type,
                    'payment_source_id' => $payments[0]->payment_source_id,
                    'payment_source' => null,
                ],
                [
                    'id' => $payments[1]->id,
                    'billing_document_id' => $payments[1]->billing_document_id,
                    'payment_method_id' => $payments[1]->payment_method_id,
                    'payment_reference_no' => $payments[1]->payment_reference_no,
                    'payment_method' => [
                        'id' => $payment_methods[1]->id,
                        'code' => PaymentMethod::CODE_FPX,
                    ],
                    'payment_source_type' => $payments[1]->payment_source_type,
                    'payment_source_id' => $payments[1]->payment_source_id,
                    'payment_source' => null,
                ],
            ],
            'bill_to_type' => $billing_documents[0]->bill_to_type,
            'bill_to_id' => $billing_documents[0]->bill_to_id,
        ])
        ->and($response[$billing_documents[0]->id]['line_items'])->toHaveCount(3)
        ->and($response[$billing_documents[0]->id]['payments'])->toHaveCount(2)

        // Invoice 2
        ->and($response[$billing_documents[1]->id])->toMatchArray([
            'id' => $billing_documents[1]->id,
            'document_date' => $billing_documents[1]->document_date,
            'reference_no' => $billing_documents[1]->reference_no,
            'bill_to_type' => $billing_documents[1]->bill_to_type,
            'bill_to_id' => $billing_documents[1]->bill_to_id,
            'bill_to_reference_number' => $billing_documents[1]->bill_to_reference_number,
            'paid_at' => Carbon::parse($billing_documents[1]->paid_at)->toISOString(),
            'currency_code' => $billing_documents[1]->currency_code,
            'amount_after_tax' => $billing_documents[1]->amount_after_tax,
            'line_items' => [
                [
                    'id' => $line_items[3]->id,
                    'billing_document_id' => $line_items[3]->billing_document_id,
                    'description' => $line_items[3]->description,
                    'is_discount' => $line_items[3]->is_discount,
                    'offset_billing_document_id' => $line_items[3]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[3]->amount_before_tax,
                    'product_id' => $line_items[3]->product_id,
                    'discount_original_line_item_id' => $line_items[3]->discount_original_line_item_id,
                ],
                [
                    'id' => $line_items[4]->id,
                    'billing_document_id' => $line_items[4]->billing_document_id,
                    'description' => $line_items[4]->description,
                    'is_discount' => $line_items[4]->is_discount,
                    'offset_billing_document_id' => $line_items[4]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[4]->amount_before_tax,
                    'product_id' => $line_items[4]->product_id,
                    'discount_original_line_item_id' => $line_items[4]->discount_original_line_item_id,
                ],
            ],
            'payments' => [
                [
                    'id' => $payments[2]->id,
                    'billing_document_id' => $payments[2]->billing_document_id,
                    'payment_method_id' => $payments[2]->payment_method_id,
                    'payment_reference_no' => $payments[2]->payment_reference_no,
                    'payment_method' => [
                        'id' => $payment_methods[2]->id,
                        'code' => PaymentMethod::CODE_BANK_TRANSFER,
                    ],
                    'payment_source_type' => $payments[2]->payment_source_type,
                    'payment_source_id' => $payments[2]->payment_source_id,
                    'payment_source' => $payment_requests[0]->loadMissing('bank')->toArray(),
                ],
            ],
        ])
        ->and($response[$billing_documents[1]->id]['line_items'])->toHaveCount(2)
        ->and($response[$billing_documents[1]->id]['payments'])->toHaveCount(1)

        // Invoice 3
        ->and($response[$billing_documents[2]->id])->toMatchArray([
            'id' => $billing_documents[2]->id,
            'document_date' => $billing_documents[2]->document_date,
            'reference_no' => $billing_documents[2]->reference_no,
            'bill_to_type' => $billing_documents[2]->bill_to_type,
            'bill_to_id' => $billing_documents[2]->bill_to_id,
            'bill_to_reference_number' => $billing_documents[2]->bill_to_reference_number,
            'paid_at' => Carbon::parse($billing_documents[2]->paid_at)->toISOString(),
            'currency_code' => $billing_documents[2]->currency_code,
            'amount_after_tax' => $billing_documents[2]->amount_after_tax,
            'line_items' => [
                [
                    'id' => $line_items[5]->id,
                    'billing_document_id' => $line_items[5]->billing_document_id,
                    'description' => $line_items[5]->description,
                    'is_discount' => $line_items[5]->is_discount,
                    'offset_billing_document_id' => $line_items[5]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[5]->amount_before_tax,
                    'product_id' => $line_items[5]->product_id,
                    'discount_original_line_item_id' => $line_items[5]->discount_original_line_item_id,
                ],
                [
                    'id' => $line_items[6]->id,
                    'billing_document_id' => $line_items[6]->billing_document_id,
                    'description' => $line_items[6]->description,
                    'is_discount' => $line_items[6]->is_discount,
                    'offset_billing_document_id' => $line_items[6]->offset_billing_document_id,
                    'amount_before_tax' => $line_items[6]->amount_before_tax,
                    'product_id' => $line_items[6]->product_id,
                    'discount_original_line_item_id' => $line_items[6]->discount_original_line_item_id,
                ],
            ],
            'payments' => [
                [
                    'id' => $payments[3]->id,
                    'billing_document_id' => $payments[3]->billing_document_id,
                    'payment_method_id' => $payments[3]->payment_method_id,
                    'payment_reference_no' => $payments[3]->payment_reference_no,
                    'payment_method' => [
                        'id' => $payment_methods[0]->id,
                        'code' => PaymentMethod::CODE_CASH,
                    ],
                    'payment_source_type' => $payments[3]->payment_source_type,
                    'payment_source_id' => $payments[3]->payment_source_id,
                    'payment_source' => null,
                ],
            ],
        ])
        ->and($response[$billing_documents[2]->id]['line_items'])->toHaveCount(2)
        ->and($response[$billing_documents[2]->id]['payments'])->toHaveCount(1)
    ;

    // ensure billing document sorted correctly by paid_at date DESC
    $response = $this->billingDocumentRepository
        ->getPaidInvoiceReportData($filters)
        ->toArray();

    expect($response)->toHaveCount(3)
        ->and($response[0]['id'])->toBe($billing_documents[2]->id)
        ->and($response[0]['document_date'])->toBe('2024-08-02')
        ->and($response[1]['id'])->toBe($billing_documents[0]->id)
        ->and($response[1]['document_date'])->toBe('2024-12-01')
        ->and($response[2]['id'])->toBe($billing_documents[1]->id)
        ->and($response[2]['document_date'])->toBe('2024-09-01')
    ;
});

