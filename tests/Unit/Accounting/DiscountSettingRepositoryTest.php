<?php


use App\Models\DiscountSetting;
use App\Models\Enrollment;
use App\Models\GlAccount;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Repositories\DiscountSettingRepository;

beforeEach(function () {
    $this->discountSettingRepository = app(DiscountSettingRepository::class);

    $this->table = resolve(DiscountSetting::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->discountSettingRepository->getModelClass();

    expect($response)->toEqual(DiscountSetting::class);
});

test('getAll()', function () {
    $discount_settings = DiscountSetting::factory(3)->create();

    $response = $this->discountSettingRepository->getAll(['order_by' => 'id'])->toArray();

    expect($response)->toEqual($discount_settings->toArray());
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $student = Student::factory()->create();
    $scholarship_award = ScholarshipAward::factory()->create();
    $scholarship_award_2 = ScholarshipAward::factory()->create();
    $scholarship_award_3 = ScholarshipAward::factory()->create();
    $enrollment = Enrollment::factory()->create();

    $keys = [
        'first' => DiscountSetting::factory()->studentUserable()->create([
            'is_active' => true,
            'basis' => DiscountSetting::BASIS_PERCENT,
            'effective_from' => '2024-10-10',
            'effective_to' => '2024-10-11',
            'description' => 'first description',
            'gl_account_codes' => json_encode(['TESTING', 'TESTING2']),
            'source_type' => get_class($scholarship_award_2),
            'source_id' => $scholarship_award_2->id,
            'max_amount' => 1000,
            'used_amount' => 0, // available for use
        ]),
        'second' => DiscountSetting::factory()->studentUserable()->create([
            'is_active' => true,
            'basis' => DiscountSetting::BASIS_PERCENT,
            'effective_from' => '2025-10-10',
            'effective_to' => '2025-10-11',
            'description' => 'second description',
            'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, GlAccount::CODE_SCHOOL_FEES]),
            'source_type' => get_class($scholarship_award_3),
            'source_id' => $scholarship_award_3->id,
            'max_amount' => 1000,
            'used_amount' => 1000,
        ]),
        'third' => DiscountSetting::factory()->studentUserable()->create([
            'is_active' => true,
            'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
            'effective_from' => '2025-10-10',
            'effective_to' => '2025-10-11',
            'description' => 'third description',
            'gl_account_codes' => json_encode([]),
            'source_type' => get_class($scholarship_award),
            'source_id' => $scholarship_award->id,
            'max_amount' => 1000,
            'used_amount' => 0, // available for use
        ]),
        'fourth' => DiscountSetting::factory()->studentUserable()->create([
            'is_active' => false,
            'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
            'effective_from' => '2025-10-10',
            'effective_to' => '2025-10-11',
            'description' => 'fourth description',
            'gl_account_codes' => json_encode([]),
            'source_type' => get_class($enrollment),
            'source_id' => $enrollment->id,
            'max_amount' => null,
            'used_amount' => 0, // no max_amount and available for use
        ]),
        'fifth' => DiscountSetting::factory()->studentUserable()->create([
            'is_active' => false,
            'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
            'userable_type' => get_class($student),
            'userable_id' => $student->id,
            'effective_from' => '2025-10-10',
            'effective_to' => '2025-10-11',
            'description' => null,
            'gl_account_codes' => json_encode([]),
            'max_amount' => 1000,
            'used_amount' => 1000,
        ]),
    ];

    $filter_options = [
        'student' => [
            'userable_type' => get_class($student),
            'userable_id' => $student->id,
        ],
        'scholarship_award' => [
            'source_type' => get_class($scholarship_award),
            'source_id' => $scholarship_award->id,
        ],
        'multiple_source_id' => [ // in array
            'source_type' => get_class($scholarship_award),
            'source_id' => [
                $keys['second']->source_id,
                $keys['third']->source_id,
            ],
        ],
        'enrollment' => [
            'source_type' => get_class($enrollment),
            'source_id' => $enrollment->id,
        ],
        'id_1' => [
            'id' => $keys['first']->id,
        ],
        'id_2' => [
            'id' => [
                $keys['second']->id,
                $keys['third']->id,
            ],
        ],
    ];

    $actual_filters = isset($filter_options[$filter_by]) ? $filter_options[$filter_by] : [$filter_by => $filter_value];
    if (!isset($actual_filters['order_by']['id'])) {
        $actual_filters['order_by']['id'] = 'asc';
    }

    $result = $this->discountSettingRepository->getAllPaginated($actual_filters)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key]['id'])->toEqual($keys[$value]->id);
    }
})->with([
    'filter by is_active = true' => [3, 'is_active', true, ['first', 'second', 'third']],
    'filter by is_active = false' => [2, 'is_active', false, ['fourth', 'fifth']],
    'filter by student' => [1, 'student', null, ['fifth']],
    'filter by basis = PERCENT' => [2, 'basis', DiscountSetting::BASIS_PERCENT, ['first', 'second']],
    'filter by basis = FIXED_AMOUNT' => [3, 'basis', DiscountSetting::BASIS_FIXED_AMOUNT, ['third', 'fourth', 'fifth']],
    'filter by effective_from = 2024-10-10' => [5, 'effective_from', '2024-10-10', ['first', 'second', 'third', 'fourth', 'fifth']],
    'filter by effective_from = 2025-10-10' => [4, 'effective_from', '2025-10-10', ['second', 'third', 'fourth', 'fifth']],
    'filter by effective_to = 2024-10-11' => [1, 'effective_to', '2024-10-11', ['first']],
    'filter by effective_to = 2025-10-11' => [5, 'effective_to', '2025-10-11', ['first', 'second', 'third', 'fourth', 'fifth']],
    'filter by gl_account_code = TESTING' => [1, 'gl_account_code', 'TESTING', ['first']],
    'filter by gl_account_code = TESTING2' => [1, 'gl_account_code', 'TESTING2', ['first']],
    'filter by gl_account_code = SCHOOL_FEES' => [1, 'gl_account_code', GlAccount::CODE_SCHOOL_FEES, ['second']],
    'filter by gl_account_code = OTHERS' => [1, 'gl_account_code', GlAccount::CODE_OTHERS, ['second']],
    'filter by scholarship_award' => [1, 'scholarship_award', null, ['third']],
    'filter by enrollment' => [1, 'enrollment', null, ['fourth']],
    'filter by id_1' => [1, 'id_1', null, ['first']],
    'filter by id_2' => [2, 'id_2', null, ['second', 'third']],
    'filter by multiple_source_id' => [2, 'multiple_source_id', null, ['second', 'third']],
    'filter by still_available' => [3, 'still_available', true, ['first', 'third', 'fourth']],
    'sort by id asc' => [5, 'order_by', ['id' => 'asc'], ['first', 'second', 'third', 'fourth', 'fifth']],
    'sort by id desc' => [5, 'order_by', ['id' => 'desc'], ['fifth', 'fourth', 'third', 'second', 'first']],
    'sort by basis asc' => [5, 'order_by', ['basis' => 'asc'], ['third', 'fourth', 'fifth', 'first', 'second']],
    'sort by basis desc' => [5, 'order_by', ['basis' => 'desc'], ['first', 'second', 'third', 'fourth', 'fifth']],
    'sort by is_active asc' => [5, 'order_by', ['is_active' => 'asc'], ['fourth', 'fifth', 'first', 'second', 'third']],
    'sort by is_active desc' => [5, 'order_by', ['is_active' => 'desc'], ['first', 'second', 'third', 'fourth', 'fifth']],
]);
