<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\ResultsPostingLineItemHistory;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Models\User;
use App\Repositories\ResultsPostingHeaderRepository;
use App\Repositories\ResultsPostingLineItemRepository;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function() {

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();


    $this->grade1 = Grade::factory()->create([
        'name->en' => 'J1',
    ]);
    $this->grade2 = Grade::factory()->create([
        'name->en' => 'J2',
    ]);

    $this->student1 = Student::factory()->create();
    $this->student2 = Student::factory()->create();
    $this->student3 = Student::factory()->create();

    $course = Course::factory()->uec()->create();

    $this->semester_setting1 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
    ]);
    $this->semester_setting2 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 2',
    ]);

    $this->class1 = ClassModel::factory()->create([
        'grade_id' => $this->grade1->id,
    ]);
    $this->class2 = ClassModel::factory()->create([
        'grade_id' => $this->grade2->id,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'class_id' => $this->class1->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'class_id' => $this->class2->id,
    ]);
    $semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'class_id' => $this->class1->id,
    ]);
    $semester_class4 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'class_id' => $this->class2->id,
    ]);

    // in sem 1, student 1 & 2 in class 1, student 3 in class 2
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $this->student1->id,
        'class_type' => ClassType::PRIMARY,
        'is_active' => false,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $this->student2->id,
        'class_type' => ClassType::PRIMARY,
        'is_active' => false,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $this->student3->id,
        'class_type' => ClassType::PRIMARY,
        'is_active' => false,
    ]);

    // in sem 2, student 1 & 2 & 3 in class 2, no one in class 1
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $this->student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $this->student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $this->student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class4->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class4->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class4->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class4->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class4->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$this->student1, $this->student2, $this->student3];
    foreach($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }


    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student1->id,
        'is_active' => true,
    ]);
    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student2->id,
        'is_active' => true,
    ]);
    $sgf3 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student3->id,
        'is_active' => true,
    ]);

    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf1->id,
        'code' => 'output1',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf1->id,
        'code' => 'output2',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf2->id,
        'code' => 'output1',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf2->id,
        'code' => 'output2',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf3->id,
        'code' => 'output3',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf3->id,
        'code' => 'output4',
    ]);

    $this->academic_year = '2024';
});

test('archivePreviousData', function () {

    $header1 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student1)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 4);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 0);

    app()->make(ResultsPostingLineItemRepository::class)->archivePreviousData($this->student1->id, [
        $report_card_output->components[0]->id,
        $report_card_output->components[1]->id,
        $report_card_output->components[2]->id,
        $report_card_output->components[3]->id,
    ]);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 0);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 4);

    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

});



test('archivePreviousDataByHeader', function () {

    $header1 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);


    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student1)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

    // doesnt affect this row
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 5);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 0);

    app()->make(ResultsPostingLineItemRepository::class)->archivePreviousDataByHeader($header1);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 1);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 4);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

});


test('restoreLineItemsByHeader', function () {

    $header1 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student1)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);

    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

    // will be not be moved
    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 0);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 5);

    app()->make(ResultsPostingLineItemRepository::class)->restoreLineItemsByHeader($header1);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 4);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 1);

    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

});



test('getValue', function() {

    $item = ResultsPostingLineItem::factory()->create([
        'label' => 'A',
        'total_grade' => [
            'display_as_name' => 'AA',
            'name' => 'AAA',
            'extra_marks' => 0
        ],
        'total' => 40.23
    ]);

    expect($item->getValue('total'))->toBe(40.23)
        ->and($item->getValue('label'))->toBe('A')
        ->and($item->getValue('total_grade'))->toBe('AA')
        ->and($item->getValue('invalid'))->toBe('ERR');


});
