<?php

use App\Models\StudentGradingFramework;
use App\Repositories\StudentGradingFrameworkRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;

test('ExpireStudentGradingFramework', function () {

    Carbon::setTestNow('2024-06-01');

    $sgfs = StudentGradingFramework::factory(4)->state(new Sequence(
        [
            'effective_from' => Carbon::parse('2024-01-01'),
            'effective_to' => Carbon::parse('2024-05-31'),
            'is_active' => true,
        ],
        [
            'effective_from' => Carbon::parse('2024-01-01'),
            'effective_to' => Carbon::parse('2025-05-31'),
            'is_active' => true,
        ],
        [
            'effective_from' => Carbon::parse('2024-01-01'),
            'effective_to' => Carbon::parse('2023-05-31'),
            'is_active' => false,
        ],
        [
            'effective_from' => Carbon::parse('2024-01-01'),
            'effective_to' => Carbon::parse('2024-06-01'),
            'is_active' => true,
        ],
    ))->create();

    $repository = app()->make(StudentGradingFrameworkRepository::class);
    $repository->expireStudentGradingFrameworkByDate(Carbon::yesterday(config('school.timezone'))->toDateString());

    $data = StudentGradingFramework::orderBy('id')->get()->toArray();
    expect($data)->toHaveCount(4)
        ->and($data[0])->toMatchArray([
            'id' => $sgfs[0]->id,
            'is_active' => false
        ])
        ->and($data[1])->toMatchArray([
            'id' => $sgfs[1]->id,
            'is_active' => true
        ])
        ->and($data[2])->toMatchArray([
            'id' => $sgfs[2]->id,
            'is_active' => false
        ])
        ->and($data[3])->toMatchArray([
            'id' => $sgfs[3]->id,
            'is_active' => true
        ]);
});
