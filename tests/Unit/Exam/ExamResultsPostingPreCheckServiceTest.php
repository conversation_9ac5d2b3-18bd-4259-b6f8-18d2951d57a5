<?php

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\ExamPostingPreCheck;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\HistoricalStudentClassAndGrade;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Services\Exam\ExamResultsDataEntryService;
use App\Services\Exam\ExamResultsPostingPreCheckService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {

    Cache::clear();

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01', 'name' => ['en' => '01']],
        ['code' => '02', 'name' => ['en' => '02']],
        ['code' => '03', 'name' => ['en' => '03']],       // GRADE
        ['code' => '04', 'name' => ['en' => '04']],       // 2 components
        ['code' => '70', 'name' => ['en' => '70']],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();


    $employee = Employee::factory()->create([]);
    $this->teacher1 = Employee::factory()->create([]);
    $this->teacher2 = Employee::factory()->create([]);

    $this->class1 = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $this->class2 = ClassModel::factory()->create([
        'name->en' => 'Class 2',
    ]);
    $this->semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $sem_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'class_id' => $this->class1->id
    ]);
    $sem_class_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'class_id' => $this->class2->id
    ]);

    $this->student = Student::factory()->create(['student_number' => 1]);
    $this->student2 = Student::factory()->create(['student_number' => 2]);
    $this->student3 = Student::factory()->create(['student_number' => 3]);
    $this->student4 = Student::factory()->create(['student_number' => 4]);


    // student 1 and 2 from class 1
    // student 2 and 3 from class 2
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'semester_class_id' => $sem_class_1->id,
        'student_id' => $this->student->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'semester_class_id' => $sem_class_1->id,
        'student_id' => $this->student2->id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'semester_class_id' => $sem_class_2->id,
        'student_id' => $this->student3->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'semester_class_id' => $sem_class_2->id,
        'student_id' => $this->student4->id,
    ]);

    foreach ($this->subjects as $subject) {

        $class_subject1 = ClassSubject::factory()->create([
            'semester_class_id' => $sem_class_1->id,
            'subject_id' => $subject->id,
        ]);
        $class_subject2 = ClassSubject::factory()->create([
            'semester_class_id' => $sem_class_2->id,
            'subject_id' => $subject->id,
        ]);

        ClassSubjectTeacher::factory()->create([
            'class_subject_id' => $class_subject1->id,
            'employee_id' => $this->teacher1->id,
        ]);
        ClassSubjectTeacher::factory()->create([
            'class_subject_id' => $class_subject2->id,
            'employee_id' => $this->teacher2->id,
        ]);

        ClassSubjectStudent::factory()->create([
            'class_subject_id' => $class_subject1->id,
            'student_id' => $this->student->id,
        ]);
        ClassSubjectStudent::factory()->create([
            'class_subject_id' => $class_subject1->id,
            'student_id' => $this->student2->id,
        ]);
        ClassSubjectStudent::factory()->create([
            'class_subject_id' => $class_subject2->id,
            'student_id' => $this->student3->id,
        ]);
        ClassSubjectStudent::factory()->create([
            'class_subject_id' => $class_subject2->id,
            'student_id' => $this->student4->id,
        ]);
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student4)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // enter some results for SEM1EXAM
    // class 1 subject 0 - 1 data entry for student 1 only
    ResultSourceSubjectComponent::where('result_source_subject_id', ResultSourceSubject::where('subject_id', $this->subjects[0]->id)->where('result_source_id', ResultSource::where('student_grading_framework_id', $sgf1->id)->where('code', 'SEM1EXAM')->first()->id)->first()->id)
        ->update([
            'actual_score' => 80,
        ]);

    // class 1 subject 1 - all data entry
    // class 1 subject 2,3,4 - all blank
    ResultSourceSubjectComponent::where('result_source_subject_id', ResultSourceSubject::where('subject_id', $this->subjects[1]->id)->where('result_source_id', ResultSource::where('student_grading_framework_id', $sgf1->id)->where('code', 'SEM1EXAM')->first()->id)->first()->id)
        ->update([
            'actual_score' => 79,
        ]);
    ResultSourceSubjectComponent::where('result_source_subject_id', ResultSourceSubject::where('subject_id', $this->subjects[1]->id)->where('result_source_id', ResultSource::where('student_grading_framework_id', $sgf2->id)->where('code', 'SEM1EXAM')->first()->id)->first()->id)
        ->update([
            'actual_score' => 78,
        ]);

    // class 2 - all filled in
    ResultSourceSubjectComponent::whereIn('result_source_subject_id', ResultSourceSubject::whereIn('result_source_id', ResultSource::whereIn('student_grading_framework_id', [$sgf3->id, $sgf4->id])->where('code', 'SEM1EXAM')->pluck('id'))->pluck('id'))
        ->update([
            'actual_score' => 77,
        ]);
    ResultSourceSubject::where('subject_id', $this->subjects[2]->id)->whereIn('result_source_id', ResultSource::whereIn('student_grading_framework_id', [$sgf3->id, $sgf4->id])->where('code', 'SEM1EXAM')->pluck('id'))
        ->update([
            'actual_score_grade' => 'AA',
        ]);

});

test('test_exam_posting_pre_check_mat_view', function () {

    HistoricalStudentClassAndGrade::refreshViewTable(false);
    ExamPostingPreCheck::refreshViewTable(false);

    $this->assertDatabaseCount(ExamPostingPreCheck::class, 72);

    //class 1, subject 0
    $this->assertDatabaseHas(ExamPostingPreCheck::class, [
        'semester_setting_id' => $this->semester_setting->id,
        'result_source_code' => 'SEM1EXAM',
        'subject_id' => $this->subjects[0]->id,
        'student_id' => $this->student->id,
        'class_id' => $this->class1->id,
        'input_value' => '80.00',
        'input_value_status' => 1,
        'teacher_ids' => (string) $this->teacher1->id,
    ]);
    $this->assertDatabaseHas(ExamPostingPreCheck::class, [
        'semester_setting_id' => $this->semester_setting->id,
        'result_source_code' => 'SEM1EXAM',
        'subject_id' => $this->subjects[0]->id,
        'student_id' => $this->student2->id,
        'class_id' => $this->class1->id,
        'input_value' => null,
        'input_value_status' => 0,
        'teacher_ids' => (string) $this->teacher1->id,
    ]);

    //class 1, subject 1
    $this->assertDatabaseHas(ExamPostingPreCheck::class, [
        'semester_setting_id' => $this->semester_setting->id,
        'result_source_code' => 'SEM1EXAM',
        'subject_id' => $this->subjects[1]->id,
        'student_id' => $this->student->id,
        'class_id' => $this->class1->id,
        'input_value' => '79.00',
        'input_value_status' => 1,
        'teacher_ids' => (string) $this->teacher1->id,
    ]);
    $this->assertDatabaseHas(ExamPostingPreCheck::class, [
        'semester_setting_id' => $this->semester_setting->id,
        'result_source_code' => 'SEM1EXAM',
        'subject_id' => $this->subjects[1]->id,
        'student_id' => $this->student2->id,
        'class_id' => $this->class1->id,
        'input_value' => '78.00',
        'input_value_status' => 1,
        'teacher_ids' => (string) $this->teacher1->id,
    ]);

    //class 1, subject 2,3,4 all empty
    foreach ([$this->subjects[2], $this->subjects[3], $this->subjects[4]] as $subject) {

        $this->assertDatabaseHas(ExamPostingPreCheck::class, [
            'semester_setting_id' => $this->semester_setting->id,
            'result_source_code' => 'SEM1EXAM',
            'subject_id' => $subject->id,
            'student_id' => $this->student->id,
            'class_id' => $this->class1->id,
            'input_value' => null,
            'input_value_status' => 0,
            'teacher_ids' => (string) $this->teacher1->id,
        ]);
        $this->assertDatabaseHas(ExamPostingPreCheck::class, [
            'semester_setting_id' => $this->semester_setting->id,
            'result_source_code' => 'SEM1EXAM',
            'subject_id' => $subject->id,
            'student_id' => $this->student2->id,
            'class_id' => $this->class1->id,
            'input_value' => null,
            'input_value_status' => 0,
            'teacher_ids' => (string) $this->teacher1->id,
        ]);
    }

    // class 2
    $this->assertDatabaseMissing(ExamPostingPreCheck::class, [
        'semester_setting_id' => $this->semester_setting->id,
        'result_source_code' => 'SEM1EXAM',
        'class_id' => $this->class2->id,
        'input_value' => null,
        'input_value_status' => 0,
    ]);

    foreach ($this->subjects as $subject) {
        foreach ([$this->student3, $this->student4] as $student) {
            $this->assertDatabaseHas(ExamPostingPreCheck::class, [
                'semester_setting_id' => $this->semester_setting->id,
                'result_source_code' => 'SEM1EXAM',
                'subject_id' => $subject->id,
                'student_id' => $student->id,
                'class_id' => $this->class2->id,
                'input_value' => $subject->code === '03' ? 'AA' : '77.00',
                'input_value_status' => 1,
                'teacher_ids' => (string) $this->teacher2->id,
            ]);
        }
    }

});


test('test_getDrillDownLevelZeroData', function () {

    HistoricalStudentClassAndGrade::refreshViewTable(false);
    ExamPostingPreCheck::refreshViewTable(false);

    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setDrillDownLevel(0)
        ->getData();

    $data = $data->sortBy('class_id');

    // class1 - total row 12, filled 3
    // class2 - total row 12, filled 12
    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'total_rows' => 12,
            'completed_rows' => 3,
            'incomplete_rows' => 9,
            'completed_percentage' => '25.00'
        ])
        ->and($data[1])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'total_rows' => 12,
            'completed_rows' => 12,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ]);

});


test('test_getDrillDownLevelOneData', function () {

    HistoricalStudentClassAndGrade::refreshViewTable(false);
    ExamPostingPreCheck::refreshViewTable(false);

    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setDrillDownLevel(1)
        ->getData();

    // class1 - total row 12, filled 3
    // subject 0 - 1 / 2
    // subject 1 - 2 / 2
    // subject 2,3,4 - 0 / 2
    expect($data)->toHaveCount(5)
        ->and($data[0])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[0]->id,
            'total_rows' => 2,
            'completed_rows' => 1,
            'incomplete_rows' => 1,
            'completed_percentage' => '50.00'
        ])
        ->and($data[1])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[1]->id,
            'total_rows' => 2,
            'completed_rows' => 2,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ])
        ->and($data[2])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[2]->id,
            'total_rows' => 2,
            'completed_rows' => 0,
            'incomplete_rows' => 2,
            'completed_percentage' => '0.00'
        ])
        ->and($data[3])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[3]->id,
            'total_rows' => 4,
            'completed_rows' => 0,
            'incomplete_rows' => 4,
            'completed_percentage' => '0.00'
        ])
        ->and($data[4])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[4]->id,
            'total_rows' => 2,
            'completed_rows' => 0,
            'incomplete_rows' => 2,
            'completed_percentage' => '0.00'
        ]);

    // class2 - total row 12, filled 12
    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class2->id)
        ->setDrillDownLevel(1)
        ->getData();

    expect($data)->toHaveCount(5)
        ->and($data[0])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[0]->id,
            'total_rows' => 2,
            'completed_rows' => 2,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ])
        ->and($data[1])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[1]->id,
            'total_rows' => 2,
            'completed_rows' => 2,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ])
        ->and($data[2])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[2]->id,
            'total_rows' => 2,
            'completed_rows' => 2,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ])
        ->and($data[3])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[3]->id,
            'total_rows' => 4,
            'completed_rows' => 4,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ])
        ->and($data[4])->toMatchArray([
            'result_source_code' => 'SEM1EXAM',
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[4]->id,
            'total_rows' => 2,
            'completed_rows' => 2,
            'incomplete_rows' => 0,
            'completed_percentage' => '100.00'
        ]);
});


test('test_getDrillDownLevelTwoData', function () {

    HistoricalStudentClassAndGrade::refreshViewTable(false);
    ExamPostingPreCheck::refreshViewTable(false);

    // class 1 students
    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setSubjectId($this->subjects[0]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[0]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => '80.00',
            'input_value_status' => 1,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[0]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => null,
            'input_value_status' => 0,
        ]);


    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setSubjectId($this->subjects[1]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[1]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => '79.00',
            'input_value_status' => 1,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[1]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => '78.00',
            'input_value_status' => 1,
        ]);

    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setSubjectId($this->subjects[2]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[2]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_GRADE,
            'result_source_subject_component_name' => null,
            'input_value' => null,
            'input_value_status' => 0,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[2]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_GRADE,
            'result_source_subject_component_name' => null,
            'input_value' => null,
            'input_value_status' => 0,
        ]);


    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setSubjectId($this->subjects[3]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(4)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Final Score',
                'zh' => '最终分数',
            ],
            'input_value' => null,
            'input_value_status' => 0,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Homework',
                'zh' => '功课',
            ],
            'input_value' => null,
            'input_value_status' => 0,
        ])
        ->and($data[2])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Final Score',
                'zh' => '最终分数',
            ],
            'input_value' => null,
            'input_value_status' => 0,
        ])
        ->and($data[3])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Homework',
                'zh' => '功课',
            ],
            'input_value' => null,
            'input_value_status' => 0,
        ]);


    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class1->id)
        ->setSubjectId($this->subjects[4]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[4]->id,
            'student_id' => $this->student->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => null,
            'input_value_status' => 0,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class1->id,
            'subject_id' => $this->subjects[4]->id,
            'student_id' => $this->student2->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'input_value' => null,
            'input_value_status' => 0,
        ]);

    // class 2 students
    foreach ([$this->subjects[0], $this->subjects[1], $this->subjects[4]] as $subject) {
        $data = app(ExamResultsPostingPreCheckService::class)
            ->setResultSourceCode('SEM1EXAM')
            ->setSemesterSettingId($this->semester_setting->id)
            ->setClassId($this->class2->id)
            ->setSubjectId($subject->id)
            ->setDrillDownLevel(2)
            ->getData();

        expect($data)->toHaveCount(2)
            ->and($data[0])->toMatchArray([
                'class_id' => $this->class2->id,
                'subject_id' => $subject->id,
                'student_id' => $this->student3->id,
                'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
                'input_value' => '77.00',
                'input_value_status' => 1,
            ])
            ->and($data[1])->toMatchArray([
                'class_id' => $this->class2->id,
                'subject_id' => $subject->id,
                'student_id' => $this->student4->id,
                'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
                'input_value' => '77.00',
                'input_value_status' => 1,
            ]);
    }

    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class2->id)
        ->setSubjectId($this->subjects[2]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[2]->id,
            'student_id' => $this->student3->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_GRADE,
            'result_source_subject_component_name' => null,
            'input_value' => 'AA',
            'input_value_status' => 1,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[2]->id,
            'student_id' => $this->student4->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_GRADE,
            'result_source_subject_component_name' => null,
            'input_value' => 'AA',
            'input_value_status' => 1,
        ]);


    $data = app(ExamResultsPostingPreCheckService::class)
        ->setResultSourceCode('SEM1EXAM')
        ->setSemesterSettingId($this->semester_setting->id)
        ->setClassId($this->class2->id)
        ->setSubjectId($this->subjects[3]->id)
        ->setDrillDownLevel(2)
        ->getData();

    expect($data)->toHaveCount(4)
        ->and($data[0])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student3->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Final Score',
                'zh' => '最终分数',
            ],
            'input_value' => '77.00',
            'input_value_status' => 1,
        ])
        ->and($data[1])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student3->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Homework',
                'zh' => '功课',
            ],
            'input_value' => '77.00',
            'input_value_status' => 1,
        ])
        ->and($data[2])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student4->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Final Score',
                'zh' => '最终分数',
            ],
            'input_value' => '77.00',
            'input_value_status' => 1,
        ])
        ->and($data[3])->toMatchArray([
            'class_id' => $this->class2->id,
            'subject_id' => $this->subjects[3]->id,
            'student_id' => $this->student4->id,
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'result_source_subject_component_name' => [
                'en' => 'Homework',
                'zh' => '功课',
            ],
            'input_value' => '77.00',
            'input_value_status' => 1,
        ]);


});
