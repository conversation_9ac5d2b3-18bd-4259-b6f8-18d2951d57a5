<?php

use App\Models\GradingFramework;
use App\Repositories\GradingFrameworkRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->gradingFrameworkRepository = app(GradingFrameworkRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(GradingFramework::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->gradingFrameworkRepository->getModelClass();

    expect($response)->toEqual(GradingFramework::class);
});

test('getAll()', function (int $expected_count, array $filters, array $expected_model) {
    $keys = [
        'first' => GradingFramework::factory()->create([
            'code' => 'J001',
            'name->en' => 'J1DEFAULT',
            'name->zh' => 'J1号码',
            'is_active' => true
        ]),
        'second' => GradingFramework::factory()->create([
            'code' => 'J002',
            'name->en' => 'J2DEFAULT',
            'name->zh' => 'J2号码',
            'is_active' => true
        ]),
        'third' => GradingFramework::factory()->create([
            'code' => 'J003',
            'name->en' => 'J3DEFAULT',
            'name->zh' => 'J3号码',
            'is_active' => false
        ]),
    ];

    $result = $this->gradingFrameworkRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = J1DEFAULT' => [1, ['name' => 'J1DEFAULT'], ['first']],
    'filter by name = J3' => [1, ['name' => 'J3'], ['third']],
    'filter by name = NOTEXIST' => [0, ['name' => 'NOTEXIST'], []],
    'filter by code = J001' => [1, ['code' => 'J001'], ['first']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'Bla Bla'], []],
    'filter by is_active = TRUE' => [2, ['is_active' => true], ['first', 'second']],
    'filter by is_active = FALSE' => [1, ['is_active' => false], ['third']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
    'sort by code asc' => [3, ['order_by' => ['code' => 'asc']], ['first', 'second', 'third']],
    'sort by code desc' => [3, ['order_by' => ['code' => 'desc']], ['third', 'second', 'first']],
    'sort by name asc' => [3, ['order_by' => ['name' => 'asc']], ['first', 'second', 'third']],
    'sort by name desc' => [3, ['order_by' => ['name' => 'desc']], ['third', 'second', 'first']],
]);


test('getAllPaginated()', function () {
    $keys = [
        'first' => GradingFramework::factory()->create([
            'code' => 'J001',
            'name->en' => 'J1DEFAULT',
            'name->zh' => 'J1号码',
            'is_active' => true
        ]),
        'second' => GradingFramework::factory()->create([
            'code' => 'J002',
            'name->en' => 'J2DEFAULT',
            'name->zh' => 'J2号码',
            'is_active' => true
        ]),
        'third' => GradingFramework::factory()->create([
            'code' => 'J003',
            'name->en' => 'J3DEFAULT',
            'name->zh' => 'J3号码',
            'is_active' => false
        ]),
    ];

    // share same filter and sorting logic as getAll(), only checking return type here
    $result = $this->gradingFrameworkRepository->getAllPaginated([]);
    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);
});