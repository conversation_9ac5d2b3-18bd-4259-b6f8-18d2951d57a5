<?php

use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Events\ExamSubjectExemptionEvent;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Services\Exam\ExamSubjectExemptionService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;

beforeEach(function() {

    Cache::clear();

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();

});

test('setBulkExamExemption', function(){
    $students = Student::factory(4)->create();
    $semester_class = SemesterClass::factory()->create();

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    foreach($students as $student){
        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ]);    
        ClassSubjectStudent::factory(4)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $sgfs = [];
    foreach($students as $student){
        $sgfs[] = app()->make(StudentGradingFrameworkService::class)
            ->setStudent($student)
            ->setGradingFramework($grading_framework)
            ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
            ->getStudentGradingFramework();
    }

    $payload = [
        'exam_id' => $this->exams[0]->id,
        'subject_id' => $this->subjects[0]->id,
        'records' => [
            [
                'student_id' => $students[0]->id,
                'is_exempted' => true,
            ],
            [
                'student_id' => $students[1]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[2]->id,
                'is_exempted' => false,
            ],
            [
                'student_id' => $students[3]->id,
                'is_exempted' => false
            ]
        ]
    ];

    app()->make(ExamSubjectExemptionService::class)
        ->setExamSubjectExemption($payload);

    // Checking Exempted Students (Student 0 and Student 1)
    // SEM1EXAM, STUDENT 0 is exempted
    $sem1_exam_student_0 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereRelation('resultSource', 'student_grading_framework_id', $sgfs[0]->id)
        ->whereRelation('resultSource', 'code', 'SEM1EXAM')
        ->first();
    
    expect($sem1_exam_student_0->is_exempted)->toBe(true);

    // SEM2EXAM, STUDENT 0 not exempted
    $other_exams_student_0 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereHas('resultSource', function($query) use ($sgfs) {
            $query->where('student_grading_framework_id', $sgfs[0]->id);
            $query->where('code', '!=', 'SEM1EXAM');
        })
        ->get();
    
    foreach($other_exams_student_0 as $result_source_subject){
        expect($result_source_subject->is_exempted)->toBe(false);
    }

    // SEM1EXAM, STUDENT 1 is exempted
    $sem1_exam_student_1 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereRelation('resultSource', 'student_grading_framework_id', $sgfs[1]->id)
        ->whereRelation('resultSource', 'code', 'SEM1EXAM')
        ->first();

    expect($sem1_exam_student_1->is_exempted)->toBe(true);

    // SEM2EXAM, STUDENT 1 not exempted
    $other_exams_student_1 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereHas('resultSource', function($query) use ($sgfs) {
            $query->where('student_grading_framework_id', $sgfs[1]->id);
            $query->where('code', '!=', 'SEM1EXAM');
        })
        ->get();
    
    foreach($other_exams_student_1 as $result_source_subject){
        expect($result_source_subject->is_exempted)->toBe(false);
    }

    // Student 2, Student 3 should both be false (untouched)
    $remaining_students = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereHas('resultSource', function($query) use ($sgfs) {
            $query->whereIn('student_grading_framework_id', [$sgfs[2]->id, $sgfs[3]->id]);
        })
        ->get();

    foreach($remaining_students as $result_source_subject){
        expect($result_source_subject->is_exempted)->toBe(false);
    }

    // Setting Student 0 back to false for SEM1EXAM
    $payload = [
        'exam_id' => $this->exams[0]->id,
        'subject_id' => $this->subjects[0]->id,
        'records' => [
            [
                'student_id' => $students[0]->id,
                'is_exempted' => false,
            ],
            [
                'student_id' => $students[1]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[2]->id,
                'is_exempted' => false,
            ],
            [
                'student_id' => $students[3]->id,
                'is_exempted' => false
            ]
        ]
    ];

    Event::fake();
    app()->make(ExamSubjectExemptionService::class)
        ->setExamSubjectExemption($payload);

    Event::assertDispatchedTimes(ExamSubjectExemptionEvent::class, 1);
    Event::assertDispatched(ExamSubjectExemptionEvent::class, function ($e) use ($payload) {
        return $e->getInput() == $payload;
    });

    // Student 0, Student 2 and Student 3 should be false (not exempted)
    $remaining_students = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereHas('resultSource', function($query) use ($sgfs) {
            $query->whereIn('student_grading_framework_id', [$sgfs[0]->id, $sgfs[2]->id, $sgfs[3]->id]);
        })
        ->get();

    foreach($remaining_students as $result_source_subject){
        expect($result_source_subject->is_exempted)->toBe(false);
    }

    // SEM1EXAM, STUDENT 1 is exempted
    $sem1_exam_student_1 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereRelation('resultSource', 'student_grading_framework_id', $sgfs[1]->id)
        ->whereRelation('resultSource', 'code', 'SEM1EXAM')
        ->first();

    expect($sem1_exam_student_1->is_exempted)->toBe(true);

    // SEM2EXAM, STUDENT 1 not exempted
    $other_exams_student_1 = ResultSourceSubject::where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->whereHas('resultSource', function($query) use ($sgfs) {
            $query->where('student_grading_framework_id', $sgfs[1]->id);
            $query->where('code', '!=', 'SEM1EXAM');
        })
        ->get();
    
    foreach($other_exams_student_1 as $result_source_subject){
        expect($result_source_subject->is_exempted)->toBe(false);
    }

});

test('getEligibleSubjectsForExemption', function (){
    $student = Student::factory()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'is_active' => true,
        'semester_setting_id' => $semester_setting->id
    ]);
    $semester_class_2 = SemesterClass::factory()->create([
        'is_active' => true,
        'semester_setting_id' => $semester_setting->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'subject_id' => $this->subjects[0]->id,
            'semester_class_id' => $semester_class_1->id
        ],
        [
            'subject_id' => $this->subjects[0]->id,
            'semester_class_id' => $semester_class_2->id
        ],
        [
            'subject_id' => $this->subjects[1]->id,
            'semester_class_id' => $semester_class_1->id
        ],
        [
            'subject_id' => $this->subjects[2]->id,
            'semester_class_id' => $semester_class_2->id
        ]
    ))->create();

    $response = app()->make(ExamSubjectExemptionService::class)
        ->getEligibleSubjectsForExemption();
    
    expect($response)->toBeInstanceOf(Collection::class)
        ->and($response)->toHaveCount(3)
        ->and($response[0])->toMatchArray([
            'id' => $this->subjects[0]->id,
            'type' => $this->subjects[0]->type,
            'code' => $this->subjects[0]->code,
        ])
        ->and($response[1])->toMatchArray([
            'id' => $this->subjects[1]->id,
            'type' => $this->subjects[1]->type,
            'code' => $this->subjects[1]->code,
        ])
        ->and($response[2])->toMatchArray([
            'id' => $this->subjects[2]->id,
            'type' => $this->subjects[2]->type,
            'code' => $this->subjects[2]->code,
        ]);
});

test('getEligibleClassesForExemption', function (){
    $student = Student::factory()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $class_1 = ClassModel::factory()->create([
        'code' => 'CLASSONE',
        'type' => ClassType::PRIMARY,
        'grade_id' => Grade::factory(),
        'is_active' => true
    ]);

    $class_2 = classModel::factory()->create([
        'code' => 'CLASSTWO',
        'type' => ClassType::PRIMARY,
        'grade_id' => Grade::factory(),
        'is_active' => true
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'is_active' => true,
        'class_id' => $class_1->id, 
        'semester_setting_id' => $semester_setting->id
    ]);
    $semester_class_2 = SemesterClass::factory()->create([
        'is_active' => true,
        'class_id' => $class_2->id,
        'semester_setting_id' => $semester_setting->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'subject_id' => $this->subjects[0]->id,
            'semester_class_id' => $semester_class_1->id
        ],
        [
            'subject_id' => $this->subjects[0]->id,
            'semester_class_id' => $semester_class_2->id
        ],
        [
            'subject_id' => $this->subjects[1]->id,
            'semester_class_id' => $semester_class_1->id
        ],
        [
            'subject_id' => $this->subjects[2]->id,
            'semester_class_id' => $semester_class_2->id
        ]
    ))->create();

    $payload = ['subject_id' => $this->subjects[0]->id];
    $response = app()->make(ExamSubjectExemptionService::class)
        ->getEligibleClassesForExemption($payload);

    expect($response)->toBeInstanceOf(Collection::class)
        ->and($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'id' => $class_1->id,
            'code' => $class_1->code,
            'grade_id' => $class_1->grade_id
        ])
        ->and($response[1])->toMatchArray([
            'id' => $class_2->id,
            'code' => $class_2->code,
            'grade_id' => $class_2->grade_id
        ]);
});

test('getStudentExamExemptionStatus', function (){

    // Final data sorted by ascending name order, so name is inserted here to ensure order
    $students = Student::factory(4)->state(new Sequence(
        ['name->en' => 'Student A'],
        ['name->en' => 'Student B'],
        ['name->en' => 'Student C'],
        ['name->en' => 'Student D'],
    ))->create();
    
    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $class = ClassModel::factory()->create([
        'code' => 'CLASSONE',
        'stream' => ClassStream::NOT_APPLICABLE,
        'type' => ClassType::PRIMARY,
        'grade_id' => Grade::factory(),
        'is_active' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting,
        'is_active' => true,
        'class_id' => $class->id
]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    foreach($students as $student){
        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_class_id' => $semester_class->id,
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true
        ]);    
        ClassSubjectStudent::factory(4)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    foreach($students as $student){
        app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'));
    }

    $payload = [
        'exam_id' => $this->exams[0]->id,
        'class_id' => $class->id,
        'subject_id' => $this->subjects[0]->id
    ];

    $response = app()->make(ExamSubjectExemptionService::class)
        ->getStudentExamExemptionStatus($payload);

    expect($response)->toBeInstanceOf(Collection::class)
        ->and($response)->toHaveCount(4)
        ->and($response[0])->toMatchArray([
            'student_id' => $students[0]->id,
            'student_number' => $students[0]->student_number,
            'is_exempted' => false,
        ])
        ->and($response[0]['student_name'])->toMatchObject([
            'en' => $students[0]->getTranslation('name', 'en'),
            'zh' => $students[0]->getTranslation('name', 'zh')
        ])
        ->and($response[1])->toMatchArray([
            'student_id' => $students[1]->id,
            'student_number' => $students[1]->student_number,
            'is_exempted' => false,
        ])
        ->and($response[1]['student_name'])->toMatchObject([
            'en' => $students[1]->getTranslation('name', 'en'),
            'zh' => $students[1]->getTranslation('name', 'zh')
        ])
        ->and($response[2])->toMatchArray([
            'student_id' => $students[2]->id,
            'student_number' => $students[2]->student_number,
            'is_exempted' => false,
        ])
        ->and($response[2]['student_name'])->toMatchObject([
            'en' => $students[2]->getTranslation('name', 'en'),
            'zh' => $students[2]->getTranslation('name', 'zh')
        ])
        ->and($response[3])->toMatchArray([
            'student_id' => $students[3]->id,
            'student_number' => $students[3]->student_number,
            'is_exempted' => false,
        ])
        ->and($response[3]['student_name'])->toMatchObject([
            'en' => $students[3]->getTranslation('name', 'en'),
            'zh' => $students[3]->getTranslation('name', 'zh')
        ]);
});