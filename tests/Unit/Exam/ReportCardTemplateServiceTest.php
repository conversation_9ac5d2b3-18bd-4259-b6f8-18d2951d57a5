<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentReportCard;
use App\Models\Subject;
use App\Services\Exam\ReportCard\PinHwaDefaultReportCardTemplateService;
use App\Services\Exam\ReportCard\ReportCardPrintService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Mockery\MockInterface;

test('test run', function () {

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);

    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();

    $students = Student::factory(5)->create();

    $course = Course::factory()->create();

    $this->semester_setting1 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
        'is_current_semester' => true,
    ]);

    $this->grade1 = Grade::factory()->create([
        'name->en' => 'J1',
    ]);

    $this->class1 = ClassModel::factory()->create([
        'grade_id' => $this->grade1->id,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'class_id' => $this->class1->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $students[0]->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class1->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();


    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $students[0]->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $students[0]->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $students[0]->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $students[0]->id,
            'class_subject_id' => $class_subjects[4]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[0])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output1 = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '03')->first()->id)
        ->first();
    $result_source_subject_4 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '04')->first()->id)
        ->first();
    $result_source_subject_5 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '70')->first()->id)
        ->first();


    $result_source_subject_1->components()->update([
        'actual_score' => 40
    ]);
    $result_source_subject_2->components()->update([
        'actual_score' => 75
    ]);
    $result_source_subject_3->update([
        'actual_score_grade' => 'B+'
    ]);
    $result_source_subject_4->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100
    ]);
    $result_source_subject_4->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 80
    ]);
    $result_source_subject_5->components()->update([
        'actual_score' => 100
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
        'semester_setting_id' => $this->semester_setting1->id,
        'grade_id' => $this->grade1->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    // mock results
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[0]->id,
        'report_card_output_component_code' => $report_card_output1->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[1]->id,
        'report_card_output_component_code' => $report_card_output1->components[1]->code,
        'total' => 50
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[2]->id,
        'report_card_output_component_code' => $report_card_output1->components[2]->code,
        'label' => 'A+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[3]->id,
        'report_card_output_component_code' => $report_card_output1->components[3]->code,
        'total' => 60
    ]);

    $filename = 'report-card-' . $header1->code . '-' . $students[0]->student_number;

    $this->mock(ReportCardPrintService::class, function (MockInterface $mock) use ($filename) {
        $mock->shouldReceive('setExportFileAdapter')->times(1)->andReturnSelf();
        $mock->shouldReceive('setFileName')->times(1)->with($filename)->andReturnSelf();
        $mock->shouldReceive('generate')->times(1)->andReturnSelf();
        $mock->shouldReceive('upload')->times(1)->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->times(1)->andReturn('some-url');
    });

    $template_service = app()->make(PinHwaDefaultReportCardTemplateService::class);

    $template_service
        ->setResultsPostingHeader($header1)
        ->setStudent($students[0])
        ->generate();

    $this->assertDatabaseCount(StudentReportCard::class, 1);
    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header1->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
    ]);

    $student1_report_card = StudentReportCard::where('student_id', $students[0]->id)->first();

    expect($student1_report_card->file_url)->not()->toBeNull()
        ->and($student1_report_card->file_url)->toBe('some-url');

    // Reposting the same code should set the old report card to false, and the new one to active
    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
        'semester_setting_id' => $this->semester_setting1->id,
        'grade_id' => $this->grade1->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    // mock results
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[0]->id,
        'report_card_output_component_code' => $report_card_output1->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[1]->id,
        'report_card_output_component_code' => $report_card_output1->components[1]->code,
        'total' => 50
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[2]->id,
        'report_card_output_component_code' => $report_card_output1->components[2]->code,
        'label' => 'A+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[3]->id,
        'report_card_output_component_code' => $report_card_output1->components[3]->code,
        'total' => 60
    ]);

    $filename = 'report-card-' . $header2->code . '-' . $students[0]->student_number;

    $this->mock(ReportCardPrintService::class, function (MockInterface $mock) use ($filename) {
        $mock->shouldReceive('setExportFileAdapter')->times(1)->andReturnSelf();
        $mock->shouldReceive('setFileName')->times(1)->with($filename)->andReturnSelf();
        $mock->shouldReceive('generate')->times(1)->andReturnSelf();
        $mock->shouldReceive('upload')->times(1)->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->times(1)->andReturn('some-url');
    });

    $template_service = app()->make(PinHwaDefaultReportCardTemplateService::class);

    $template_service
        ->setResultsPostingHeader($header2)
        ->setStudent($students[0])
        ->generate();

    $this->assertDatabaseCount(StudentReportCard::class, 2);
    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header1->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
        'is_active' => false
    ]);

    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header2->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
        'is_active' => true
    ]);

    // Posting with a new code should not affect is_active of report card generated with other codes
    $header3 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
        'semester_setting_id' => $this->semester_setting1->id,
        'grade_id' => $this->grade1->id,
        'report_card_output_code' => 'SEM2RESULT',
    ]);

    // mock results
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header3->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[0]->id,
        'report_card_output_component_code' => $report_card_output1->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header3->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[1]->id,
        'report_card_output_component_code' => $report_card_output1->components[1]->code,
        'total' => 50
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header3->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[2]->id,
        'report_card_output_component_code' => $report_card_output1->components[2]->code,
        'label' => 'A+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header3->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[3]->id,
        'report_card_output_component_code' => $report_card_output1->components[3]->code,
        'total' => 60
    ]);

    $filename = 'report-card-' . $header3->code . '-' . $students[0]->student_number;

    $this->mock(ReportCardPrintService::class, function (MockInterface $mock) use ($filename) {
        $mock->shouldReceive('setExportFileAdapter')->times(1)->andReturnSelf();
        $mock->shouldReceive('setFileName')->times(1)->with($filename)->andReturnSelf();
        $mock->shouldReceive('generate')->times(1)->andReturnSelf();
        $mock->shouldReceive('upload')->times(1)->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->times(1)->andReturn('some-url');
    });

    $template_service = app()->make(PinHwaDefaultReportCardTemplateService::class);

    $template_service
        ->setResultsPostingHeader($header3)
        ->setStudent($students[0])
        ->generate();

    $this->assertDatabaseCount(StudentReportCard::class, 3);
    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header1->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
        'is_active' => false
    ]);

    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header2->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
        'is_active' => true
    ]);

    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header3->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => false,
        'is_active' => true
    ]);

    Carbon::setTestNow('2024-12-10 00:00:00'); // UTC Time, UTC+8 time 2024-12-10 08:00:00

    // Today publish date results will cause student report card is_visible_to_student to be set to true immediately
    $header4 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
        'semester_setting_id' => $this->semester_setting1->id,
        'grade_id' => $this->grade1->id,
        'report_card_output_code' => 'SEM2RESULT',
        'publish_date' => '2024-12-10'
    ]);

    // mock results
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header4->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[0]->id,
        'report_card_output_component_code' => $report_card_output1->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header4->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[1]->id,
        'report_card_output_component_code' => $report_card_output1->components[1]->code,
        'total' => 50
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header4->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[2]->id,
        'report_card_output_component_code' => $report_card_output1->components[2]->code,
        'label' => 'A+'
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header4->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_id' => $report_card_output1->components[3]->id,
        'report_card_output_component_code' => $report_card_output1->components[3]->code,
        'total' => 60
    ]);

    $filename = 'report-card-' . $header4->code . '-' . $students[0]->student_number;

    $this->mock(ReportCardPrintService::class, function (MockInterface $mock) use ($filename) {
        $mock->shouldReceive('setExportFileAdapter')->times(1)->andReturnSelf();
        $mock->shouldReceive('setFileName')->times(1)->with($filename)->andReturnSelf();
        $mock->shouldReceive('generate')->times(1)->andReturnSelf();
        $mock->shouldReceive('upload')->times(1)->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->times(1)->andReturn('some-url');
    });

    $template_service = app()->make(PinHwaDefaultReportCardTemplateService::class);

    $template_service
        ->setResultsPostingHeader($header4)
        ->setStudent($students[0])
        ->generate();

    $this->assertDatabaseCount(StudentReportCard::class, 4);
    $this->assertDatabaseHas(StudentReportCard::class, [
        'student_id' => $students[0]->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'results_posting_header_id' => $header4->id,
        'grade_id' => $this->grade1->id,
        'is_visible_to_student' => true,
        'is_active' => true
    ]);

});
