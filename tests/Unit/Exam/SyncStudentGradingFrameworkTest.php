<?php

use App\Events\ClassSubjectStudentUpdatedEvent;
use App\Events\ExamSubjectExemptionEvent;
use App\Listeners\SyncStudentGradingFramework;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Services\ClassSubjectService;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;


beforeEach(function () {

    $this->config = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true);
    $this->studentGradingFrameworkService = app()->make(StudentGradingFrameworkService::class);

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70']
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM']
    ))->create();

});


test('handle ClassSubjectStudentUpdatedEvent', function(){
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $effective_from = \Carbon\Carbon::parse('2024-01-01');
    $effective_to = \Carbon\Carbon::parse('2024-12-31');

    $students = Student::factory(4)->create();
    $semester_class = SemesterClass::factory()->create();

    StudentClass::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ]
    ))->create();

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ],
    ))->create();

    foreach($students as $student){
        ClassSubjectStudent::factory(4)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ]
        ))->create();
    }
    
    foreach($students as $student){
        $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod($effective_from, $effective_to);
    }

    // Add Student 0 and Student 1 to Subject 70
    $student_ids = [$students[0]->id, $students[1]->id];

    $class_subject = $class_subjects[4];
    $class_subject->students()->sync($student_ids);

    $event = new ClassSubjectStudentUpdatedEvent($student_ids);
    $listener = new SyncStudentGradingFramework();
    
    $listener->handle($event);

    // Assert that all students have the result source and output components created for the subjects they are taking
    foreach($students as $student){
        $config_output = $this->studentGradingFrameworkService
            ->setStudent($student)
            ->setGradingFramework($grading_framework)
            ->mapConfigToValidSubjects();

        foreach ($config_output['result_sources'] as $result_source_config) {
            $this->assertDatabaseHas(ResultSource::class, [
                'student_grading_framework_id' => $student->activeGradingFramework()->id,
                'code' => $result_source_config['code'],
                'name->en' => $result_source_config['name']['en'],
                'name->zh' => $result_source_config['name']['zh'],
            ]);
        }

        $result_source = ResultSource::where([
            'code' => $result_source_config['code'],
            'student_grading_framework_id' => $student->activeGradingFramework()->id
        ])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));
        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));
        foreach ($result_source_config['subjects'] as $subject_config) {
            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {

                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }


        foreach ($config_output['output'] as $output_config) {

            $this->assertDatabaseHas(ReportCardOutput::class, [
                'student_grading_framework_id' => $student->activeGradingFramework()->id,
                'code' => $output_config['code'],
                'name->en' => $output_config['name']['en'],
                'name->zh' => $output_config['name']['zh'],
                'service_class' => $output_config['service'],
                'is_final' => $output_config['is_final'],
            ]);
    
            $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student->activeGradingFramework()->id)->first();
    
            expect($output)->not()->toBeNull()
                ->and($output->components)->toHaveCount(count($output_config['components']));
    
            foreach ($output_config['components'] as $component_config) {
    
                $subject = null;
                $grading_scheme = null;
    
                if (isset($component_config['subject_code'])) {
                    $subject = Subject::where('code', $component_config['subject_code'])->first();
                }
                if (isset($component_config['grading_scheme_code'])) {
                    $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
                }
    
    
                // use name if provided, else fall back to subject name if subject_code is provided.
                $name = $component_config['name'] ?? null;
    
                if ($name === null && isset($component_config['subject_code'])) {
                    $name = $subject->getTranslations('name');
                }
    
                $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                    'report_card_output_id' => $output->id,
                    'code' => $component_config['code'],
                    'name->en' => $name['en'],
                    'name->zh' => $name['zh'],
                    'subject_id' => $subject->id ?? null,
                    'grading_scheme_id' => $grading_scheme->id ?? null,
                    'total_formula' => $component_config['total_formula'] ?? null,
                    'label_formula' => $component_config['label_formula'] ?? null,
                    'resolve_priority' => $component_config['priority'] ?? 0,
                    'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                    'output_type' => $component_config['output_type'] ?? null,
                    'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
                ]);
            }  
        }
    }

    // Check that student 0 and student 1 are the only students with result source/output for subject 70
    $included_students = [$students[0], $students[1]];

    foreach($included_students as $student){
        $result_source_subject = ResultSourceSubject::where('subject_id', $this->subjects[4]->id)
            ->whereRelation('resultSource', 'student_grading_framework_id', $student->activeGradingFramework()->id)
            ->get();
        
        // Count is 3 as there are 3 exams
        expect($result_source_subject)->toHaveCount(3);
    }

    $excluded_students = [$students[2], $students[3]];
    foreach($excluded_students as $student){
        $result_source_subject = ResultSourceSubject::where('subject_id', $this->subjects[4]->id)
        ->whereRelation('resultSource', 'student_grading_framework_id', $students[2]->activeGradingFramework()->id)
        ->get();
        expect($result_source_subject)->toHaveCount(0);
    }


    // Remove Student 1 and Add Student 3 into Subject 70
    $student_ids = [$students[0]->id, $students[3]->id];

    $class_subject = $class_subjects[4];
    $class_subject->students()->sync($student_ids);

    // Input is always the difference, i.e. the students that are added and removed
    $event = new ClassSubjectStudentUpdatedEvent([$students[1]->id, $students[3]->id]);
    $listener = new SyncStudentGradingFramework();

    $listener->handle($event);

    // Assert that all students have the result source and output components created for the subjects they are taking
    foreach($students as $student){
        $config_output = $this->studentGradingFrameworkService
            ->setStudent($student)
            ->setGradingFramework($grading_framework)
            ->mapConfigToValidSubjects();

        foreach ($config_output['result_sources'] as $result_source_config) {
            $this->assertDatabaseHas(ResultSource::class, [
                'student_grading_framework_id' => $student->activeGradingFramework()->id,
                'code' => $result_source_config['code'],
                'name->en' => $result_source_config['name']['en'],
                'name->zh' => $result_source_config['name']['zh'],
            ]);
        }

        $result_source = ResultSource::where([
            'code' => $result_source_config['code'],
            'student_grading_framework_id' => $student->activeGradingFramework()->id
        ])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));
        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));
        foreach ($result_source_config['subjects'] as $subject_config) {
            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {

                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }


        foreach ($config_output['output'] as $output_config) {

            $this->assertDatabaseHas(ReportCardOutput::class, [
                'student_grading_framework_id' => $student->activeGradingFramework()->id,
                'code' => $output_config['code'],
                'name->en' => $output_config['name']['en'],
                'name->zh' => $output_config['name']['zh'],
                'service_class' => $output_config['service'],
                'is_final' => $output_config['is_final'],
            ]);
    
            $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student->activeGradingFramework()->id)->first();
    
            expect($output)->not()->toBeNull()
                ->and($output->components)->toHaveCount(count($output_config['components']));
    
            foreach ($output_config['components'] as $component_config) {
    
                $subject = null;
                $grading_scheme = null;
    
                if (isset($component_config['subject_code'])) {
                    $subject = Subject::where('code', $component_config['subject_code'])->first();
                }
                if (isset($component_config['grading_scheme_code'])) {
                    $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
                }
    
    
                // use name if provided, else fall back to subject name if subject_code is provided.
                $name = $component_config['name'] ?? null;
    
                if ($name === null && isset($component_config['subject_code'])) {
                    $name = $subject->getTranslations('name');
                }
    
                $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                    'report_card_output_id' => $output->id,
                    'code' => $component_config['code'],
                    'name->en' => $name['en'],
                    'name->zh' => $name['zh'],
                    'subject_id' => $subject->id ?? null,
                    'grading_scheme_id' => $grading_scheme->id ?? null,
                    'total_formula' => $component_config['total_formula'] ?? null,
                    'label_formula' => $component_config['label_formula'] ?? null,
                    'resolve_priority' => $component_config['priority'] ?? 0,
                    'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                    'output_type' => $component_config['output_type'] ?? null,
                    'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
                ]);
            }  
        }
    }

    // Check that student 0 and student 3 are the only students with result source/output for subject 70
    $included_students = [$students[0], $students[3]];

    foreach($included_students as $student){
        $result_source_subject = ResultSourceSubject::where('subject_id', $this->subjects[4]->id)
            ->whereRelation('resultSource', 'student_grading_framework_id', $student->activeGradingFramework()->id)
            ->get();
        
        // Count is 3 as there are 3 exams
        expect($result_source_subject)->toHaveCount(3);
    }

    $excluded_students = [$students[1], $students[2]];
    foreach($excluded_students as $student){
        $result_source_subject = ResultSourceSubject::where('subject_id', $this->subjects[4]->id)
            ->whereRelation('resultSource', 'student_grading_framework_id', $students[2]->activeGradingFramework()->id)
            ->get();
        expect($result_source_subject)->toHaveCount(0);
    }

});

test('handle ExamSubjectExemptionEvent', function(){

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $effective_from = \Carbon\Carbon::parse('2024-01-01');
    $effective_to = \Carbon\Carbon::parse('2024-12-31');

    $students = Student::factory(4)->create();
    $semester_class = SemesterClass::factory()->create();

    StudentClass::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ]
    ))->create();

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ],
    ))->create();

    foreach($students as $student){
        ClassSubjectStudent::factory(4)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ]
        ))->create();
    }
    
    foreach($students as $student){
        $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod($effective_from, $effective_to);
    }

    $payload = [
        'exam_id' => $this->exams[0]->id,
        'subject_id' => $this->subjects[0]->id,
        'records' => [
            [
                'student_id' => $students[0]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[1]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[2]->id,
                'is_exempted' => false
            ],
            [
                'student_id' => $students[3]->id,
                'is_exempted' => false
            ]
        ]
    ];

    $event = new ExamSubjectExemptionEvent($payload);
    $listener = new SyncStudentGradingFramework();
    
    $listener->handle($event);

    // Check Config for Student 0 and Student 1 (should be true for Subject 0, Exam 1)
    $config = $students[0]->activeGradingFramework()->configuration;
    $subject = $this->subjects[0];
    $exam = $this->exams[0];

    foreach($config['result_sources'] as $result_source){

        foreach($result_source['subjects'] as $result_source_subject){
            if($result_source_subject['code'] == $subject->code && in_array($exam->code, $result_source['exams'])){
                expect($result_source_subject['is_exempted'])->toBe(true);
            }
            else {
                expect($result_source_subject['is_exempted'])->toBe(false);
            }
        }
    }

    $config = $students[1]->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){

        foreach($result_source['subjects'] as $result_source_subject){
            if($result_source_subject['code'] == $subject->code && in_array($exam->code, $result_source['exams'])){
                expect($result_source_subject['is_exempted'])->toBe(true);
            }
            else {
                expect($result_source_subject['is_exempted'])->toBe(false);
            }
        }
    }

    // Check config for Student 2 and Student 3 (should be false)
    $config = $students[2]->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){
        foreach($result_source['subjects'] as $result_source_subject){
            expect($result_source_subject['is_exempted'])->toBe(false);
        }
    }

    $config = $students[3]->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){
        foreach($result_source['subjects'] as $result_source_subject){
            expect($result_source_subject['is_exempted'])->toBe(false);
        }
    }

    $payload = [
        'exam_id' => $this->exams[0]->id,
        'subject_id' => $this->subjects[0]->id,
        'records' => [
            [
                'student_id' => $students[0]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[1]->id,
                'is_exempted' => false
            ],
            [
                'student_id' => $students[2]->id,
                'is_exempted' => true
            ],
            [
                'student_id' => $students[3]->id,
                'is_exempted' => false
            ]
        ]
    ];

    $event = new ExamSubjectExemptionEvent($payload);
    $listener = new SyncStudentGradingFramework();
    $listener->handle($event);

    // Check Config for Student 0 and Student 2 (should be true for Subject 0, Exam 1)
    $config = $students[0]->refresh()->activeGradingFramework()->configuration;
    $subject = $this->subjects[0];
    $exam = $this->exams[0];

    foreach($config['result_sources'] as $result_source){

        foreach($result_source['subjects'] as $result_source_subject){
            if($result_source_subject['code'] == $subject->code && in_array($exam->code, $result_source['exams'])){
                expect($result_source_subject['is_exempted'])->toBe(true);
            }
            else {
                expect($result_source_subject['is_exempted'])->toBe(false);
            }
        }
    }

    $config = $students[2]->refresh()->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){

        foreach($result_source['subjects'] as $result_source_subject){
            if($result_source_subject['code'] == $subject->code && in_array($exam->code, $result_source['exams'])){
                expect($result_source_subject['is_exempted'])->toBe(true);
            }
            else {
                expect($result_source_subject['is_exempted'])->toBe(false);
            }
        }
    }

    // Check config for Student 2 and Student 3 (should be false)
    $config = $students[1]->refresh()->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){
        foreach($result_source['subjects'] as $result_source_subject){
            expect($result_source_subject['is_exempted'])->toBe(false);
        }
    }

    $config = $students[3]->refresh()->activeGradingFramework()->configuration;
    foreach($config['result_sources'] as $result_source){
        foreach($result_source['subjects'] as $result_source_subject){
            expect($result_source_subject['is_exempted'])->toBe(false);
        }
    }
});