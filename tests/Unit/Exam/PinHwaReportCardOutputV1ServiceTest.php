<?php

use App\Enums\ClassType;
use App\Enums\ConductRecordStatus;
use App\Enums\GradingSchemeType;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\RewardPunishmentRecordStatus;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\ConductRecord;
use App\Models\ConductSetting;
use App\Models\ConductSettingTeacher;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\LeadershipPosition;
use App\Models\LeadershipPositionRecord;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\MeritDemeritRewardPunishment;
use App\Models\MeritDemeritSetting;
use App\Models\PeriodAttendance;
use App\Models\PromotionMark;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SocietyPosition;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\StudentSocietyPosition;
use App\Models\Subject;
use App\Services\Exam\Output\PinHwaReportCardOutputV1Service;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Conditional;

beforeEach(function() {

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    $this->reportCardOutputService = app()->make(PinHwaReportCardOutputV1Service::class);

    $this->semester_setting_1 = SemesterSetting::factory()->create([
        'code' => '2025SEM1'
    ]);
    $this->semester_setting_2 = SemesterSetting::factory()->create([
        'code'=> '2025SEM2'
    ]);

    $this->semester_class_1 = SemesterClass::factory() ->create([
        'semester_setting_id' => $this->semester_setting_1->id
    ]);
    $this->semester_class_2 = SemesterClass::factory() ->create([
        'semester_setting_id' => $this->semester_setting_2->id
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();
    $this->academic_year = '2024';
});

test('getGrossTotal', function(){

    $student = Student::factory()->create();
    StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id
    ]);

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[2]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' =>$student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source_sem1 = ResultSource::where('code', 'SEM1EXAM')->first();
    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
    ->where('subject_id', $this->subjects->where('code', '03')->first()->id)
    ->first();

    $result_source_subject_1->first()->update([
        'actual_score' => 70
    ]);
    $result_source_subject_2->first()->update([
        'actual_score' => 92
    ]);
    $result_source_subject_3->first()->update([
        'actual_score' => 50
    ]);

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgf)
        ->getGrossTotal('SEM1EXAM');

    // student only taking subject 1 and 2, so total should be (70*2) + (92*2) = 324
    expect($response)->toBeFloat()
        ->toBe(324.0);
});

test('getGrossWeightage', function(){

    $student = Student::factory()->create();
    StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id,
    ]);

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[2]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' =>$student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgf)
        ->getGrossWeightage('SEM1EXAM');

    // student only taking subject 1 and 2, so total should be (70*2) + (92*2) = 324
    expect($response)->toBeFloat()
        ->toBe(4.0);
});

test('getGrossAverage', function(){

    $student = Student::factory()->create();
    StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id,
    ]);

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $this->semester_class_1 ->id,
            'subject_id' => $this->subjects[2]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' =>$student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $gt = $this->reportCardOutputService
        ->setStudentGradingFramework($sgf)
        ->getGrossTotal('SEM1EXAM');

    $gw = $this->reportCardOutputService
        ->setStudentGradingFramework($sgf)
        ->getGrossWeightage('SEM1EXAM');

    $output_values = [];
    $output_values['SEM1RESULT']['GT']['total'] = 340.0;
    $output_values['SEM1RESULT']['GW']['total'] = 4.0;

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgf)
        ->setOutputValues($output_values)
        ->getGrossAverage('SEM1RESULT');

    // 340/4 = 85
    expect($response)->toBeFloat()
        ->toBe(85.0);
});

test('getFloat', function(){
    $input = "0.50";
    $response = $this->reportCardOutputService
        ->getFloat($input);

    expect($response)->toBeFloat()->toEqual(0.5);
});

test('getMarksAdded', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    // semclass1, student 0 will have 3 marks
    // semclass1, student 1 will have 1 mark
    // semclass2, student 0 will have 2 marks
    $competition_records = CompetitionRecord::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 1,
        ],
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_2->id,
            'mark' => 2,
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 1,
        ],
    ))->create();

    $conduct_grading_scheme_1 = GradingScheme::factory()->withItems()->create([
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CONDUCTSEM1',
        'name' => 'Conduct Sem 1',
        'is_active' => true,
    ]);

    $conduct_grading_scheme_2 = GradingScheme::factory()->withItems()->create([
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CONDUCTSEM2',
        'name' => 'Conduct Sem 2',
        'is_active' => true,
    ]);

    // 0 - 50, 0.5 marks
    // 51 - 79.99, 0.6 marks
    // 80 - 100, 0.8 marks
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 0,
        'to' => 50,
        'name' => 'Bad Conduct',
        'display_as_name' => 'BAD',
        'extra_marks' => 0.5,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 51,
        'to' => 79.99,
        'name' => 'ok only',
        'display_as_name' => 'OKAY',
        'extra_marks' => 0.6,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
        'from' => 80,
        'to' => 100,
        'name' => 'good heart',
        'display_as_name' => 'GOOD',
        'extra_marks' => 0.8,
    ]);

    // 0 - 60, 0.3 marks
    // 61 - 80, 0.5 marks
    // 81 - 100, 0.8 marks
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_2->id,
        'from' => 0,
        'to' => 60,
        'name' => 'decent kid',
        'display_as_name' => 'DECENT',
        'extra_marks' => 0.3,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_2->id,
        'from' => 61,
        'to' => 80,
        'name' => 'great kid',
        'display_as_name' => 'GREAT',
        'extra_marks' => 0.5,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $conduct_grading_scheme_2->id,
        'from' => 81,
        'to' => 100,
        'name' => 'fantastic kid',
        'display_as_name' => 'FANTASTIC',
        'extra_marks' => 0.8,
    ]);

    $conduct_setting_sem1 = ConductSetting::factory()->create([
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id,
        'grading_scheme_id' => $conduct_grading_scheme_1->id
    ]);

    $conduct_setting_sem2 = ConductSetting::factory()->create([
        'semester_class_id' => $this->semester_class_2->id,
        'semester_setting_id' => $this->semester_setting_2->id,
        'grading_scheme_id' => $conduct_grading_scheme_2->id
    ]);

    $conduct_setting_homeroom_teacher = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem1->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,
    ]);

    $conduct_setting_teacher_sem1 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();

    $conduct_setting_homeroom_teacher_2 = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem2->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,
    ]);

    $conduct_setting_teacher_sem2 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();

    // final_conduct_mark = (0.33 * homeroomteacher) + (0.67 * avg(all_other_teachers)
    // every teacher will mark the student conduct.
    $cr_sem1_student_0 = ConductRecord::factory(7)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher->id,
            'student_id' => $students[0]->id,
            'marks' => 82,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 94,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[4]->id,
            'student_id' => $students[0]->id,
            'marks' => 86,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[4]->id,
            'student_id' => $students[0]->id,
            'marks' => 93,
            'status' => ConductRecordStatus::DRAFT->value,
        ]
    ))->create();

    $cr_sem1_student_1 = ConductRecord::factory(4)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher->id,
            'student_id' => $students[1]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[0]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[1]->id,
            'student_id' => $students[1]->id,
            'marks' => 89,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[2]->id,
            'student_id' => $students[1]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $cr_sem2_student_0 = ConductRecord::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher_2->id,
            'student_id' => $students[0]->id,
            'marks' => 95,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 74,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    // 79.999 edge case
    $cr_sem2_student_1 = ConductRecord::factory(3)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher_2->id,
            'student_id' => $students[1]->id,
            'marks' => 79.999,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[0]->id,
            'student_id' => $students[1]->id,
            'marks' => 79.999,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[1]->id,
            'student_id' => $students[1]->id,
            'marks' => 79.999,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $reward_punishments = RewardPunishment::factory(3)->state(new Sequence(
        ['name->en' => 'Cleaning'],
        ['name->en' => 'Charity'],
        ['name->en' => 'Stealing'],
    ))->create();

    $reward_punishment_records = RewardPunishmentRecord::factory(6)->state(new Sequence(
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[0]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => -2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2026-04-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'average_exam_marks' => 0.4,
            'conduct_marks' => 1,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.6,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::DRAFT->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[1]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ]
    ))->create();

    $report_card_output_component = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => 1,
        'code' => 'SEM1RESULT',
        'name->en' => 'Marks Added',
        'grading_scheme_id' => $conduct_grading_scheme_1->id,
    ]);

    // competition_marks = 3,
    // conduct_marks = (85.75 + 2).scheme = 0.8
    // avg_exam_marks = 0.6
    // total sum = 4.4
    $response = $this->reportCardOutputService
        ->setReportCardOutputComponent($report_card_output_component)
        ->setStudentGradingFramework($sgfs[0])
        ->getMarksAdded('2025SEM1', '2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
        ->toBe(4.4);

    // competition_marks = 1
    // conduct_marks = (76.48 + 2).scheme = 0.6
    // avg_exam_marks = 0.3
    // total sum = 1.9
    $response = $this->reportCardOutputService
        ->setReportCardOutputComponent($report_card_output_component)
        ->setStudentGradingFramework($sgfs[1])
        ->getMarksAdded('2025SEM1', '2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
        ->toBe(1.9);

    // competition_marks = 2
    // conduct_marks = (83.95 + 1).scheme = 0.8
    // avg_exam_marks = 0.4
    // total sum = 3.2
    $response = $this->reportCardOutputService
        ->setReportCardOutputComponent($report_card_output_component)
        ->setStudentGradingFramework($sgfs[0])
        ->getMarksAdded('2025SEM2', '2026-01-01', '2026-06-01');

    expect($response)->toBeFloat()
        ->toBe(3.2);

    // competition_marks = 0
    // conduct_marks = (80 + 0).scheme = 0.8
    // avg_exam_marks = 0
    // total sum = 0.8
    $response = $this->reportCardOutputService
        ->setReportCardOutputComponent($report_card_output_component)
        ->setStudentGradingFramework($sgfs[1])
        ->getMarksAdded('2025SEM2', '2026-01-01', '2026-06-01');

    expect($response)->toBeFloat()
        ->toBe(0.8);

    // everything 0, (0).scheme = 0.5
    $response = $this->reportCardOutputService
        ->setReportCardOutputComponent($report_card_output_component)
        ->setStudentGradingFramework($sgfs[2])
        ->getMarksAdded('2025SEM1', '2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
        ->toBe(0.5);

});

test('getMarksSubtracted', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $leave_application_type_1 = LeaveApplicationType::factory()->create(['name->en' => 'MC',]);
    $leave_application_type_2 = LeaveApplicationType::factory()->create(['name->en' => 'Emergency']);

    $leave_applications = LeaveApplication::factory(3)->state(new Sequence(
        [
            'leave_applicable_id' => $students[0],
            'leave_application_type_id' => $leave_application_type_1->id,
            'average_point_deduction' => 0.01
        ],
        [
            'leave_applicable_id' => $students[0],
            'leave_application_type_id' => $leave_application_type_2->id,
            'average_point_deduction' => 0.0
        ],
        [
            'leave_applicable_id' => $students[1],
            'leave_application_type_id' => $leave_application_type_1->id,
            'average_point_deduction' => 0.01
        ]
    ))->create();

    // Creating ABSENT/LATE data for student 0
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-02-01',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
        ],
    ))->create();

    PeriodAttendance::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-03-01',
            'status' => PeriodAttendanceStatus::LATE->value,
            'leave_application_id' => null,
        ],
    ))->create();

    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[0]->id,
        ],
    ))->create();

    PeriodAttendance::factory(5)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-04-05',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[1]->id,
        ],
    ))->create();

    // Creating ABSENT/LATE data for student 1
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'date' => '2025-02-01',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
        ],
    ))->create();

    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[2]->id,
        ],
    ))->create();

    // Data that should be filtered out (out of date range)
    PeriodAttendance::factory(5)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2026-04-05',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null
        ],
    ))->create();

    // Dummy Present Data, repeating eight times for each
    PeriodAttendance::factory(24)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ],
        [
            'student_id' => $students[1]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ],
        [
            'student_id' => $students[2]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ]
    ))->create();

    $reward_punishments = RewardPunishment::factory(3)->state(new Sequence(
        ['name->en' => 'Stealing'],
        ['name->en' => 'Exam Cheating'],
        ['name->en' => 'Charity'],
    ))->create();

    $reward_punishment_records = RewardPunishmentRecord::factory(7)->state(new Sequence(
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => 2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => -0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[0]->id,
            'average_exam_marks' => -0.3,
            'conduct_marks' => -2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-06',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.9,    // positive, should be ignored
            'conduct_marks' => 0,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2026-04-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'average_exam_marks' => -0.3,
            'conduct_marks' => 1,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => -0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::DRAFT->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[1]->id,
            'average_exam_marks' => -0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ]
    ))->create();

    // attendance period = 0.48
    // reward punishment = 1.1
    // total = 1.58
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[0])
        ->getMarksSubtracted('2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
            ->toBe(1.58);

    // attendance period = 0.2 (0.04 * 5)
    // reward punishment = 0.3
    // total = 0.5
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[0])
        ->getMarksSubtracted('2026-01-01', '2026-06-01');

    expect($response)->toBeFloat()
            ->toBe(0.5);

    // attendance period = 0.4 (0.04 * 8 + 0.01 * 8)
    // reward punishment = 0.3
    // total = 0.7
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[1])
        ->getMarksSubtracted('2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
            ->toBe(0.7);

    // student 2 all present
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[2])
        ->getMarksSubtracted('2025-01-01', '2025-06-01');

    expect($response)->toBeFloat()
            ->toBe(0.0);
});


test('getConductScore', function(){

    $conduct_grading_scheme_1 = GradingScheme::factory()->withItems()->create([
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CONDUCTSEM1',
        'name' => 'Conduct Sem 1',
        'is_active' => true,
    ]);

    $conduct_grading_scheme_2 = GradingScheme::factory()->withItems()->create([
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CONDUCTSEM2',
        'name' => 'Conduct Sem 2',
        'is_active' => true,
    ]);

    $conduct_setting_sem1 = ConductSetting::factory()->create([
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id,
        'grading_scheme_id' => $conduct_grading_scheme_1->id
    ]);

    $conduct_setting_sem2 = ConductSetting::factory()->create([
        'semester_class_id' => $this->semester_class_2->id,
        'semester_setting_id' => $this->semester_setting_2->id,
        'grading_scheme_id' => $conduct_grading_scheme_2->id
    ]);

    $students = Student::factory(3)->create();

    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $conduct_setting_homeroom_teacher = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem1->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,
    ]);

    $conduct_setting_teacher_sem1 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();

    $conduct_setting_homeroom_teacher_2 = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem2->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,
    ]);

    $conduct_setting_teacher_sem2 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();

    $cr_sem1_student_0 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher->id,
            'student_id' => $students[0]->id,
            'marks' => 82,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 94,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[4]->id,
            'student_id' => $students[0]->id,
            'marks' => 86,
            'status' => ConductRecordStatus::DRAFT->value,
        ]
    ))->create();

    $cr_sem1_student_1 = ConductRecord::factory(4)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher->id,
            'student_id' => $students[1]->id,
            'marks' => 93,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[0]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[1]->id,
            'student_id' => $students[1]->id,
            'marks' => 89,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem1[2]->id,
            'student_id' => $students[1]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
    ))->create();

    $cr_sem2_student_0 = ConductRecord::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_homeroom_teacher_2->id,
            'student_id' => $students[0]->id,
            'marks' => 95,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 74,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher_sem2[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::DRAFT->value,
        ],
    ))->create();

    $reward_punishments = RewardPunishment::factory(3)->state(new Sequence(
        ['name->en' => 'Cleaning'],
        ['name->en' => 'Charity'],
        ['name->en' => 'Stealing'],
    ))->create();

    $reward_punishment_records = RewardPunishmentRecord::factory(7)->state(new Sequence(
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[0]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => -2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2026-04-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'average_exam_marks' => 0.4,
            'conduct_marks' => 1,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.6,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::DRAFT->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[1]->id,
            'average_exam_marks' => 0.3,
            'conduct_marks' => 2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[2]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => -2,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
    ))->create();

    // testing sem 1, student 0, 1 homeroom, 5 non-homeroom
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[0])
        ->getConductScore('2025SEM1', '2025-01-01', '2025-06-31');

    expect($response)->toEqual(87.75);

    $student0_sem1_record_status = ConductRecord::where([
            'student_id' => $students[0]->id,
            'conduct_setting_id' => $conduct_setting_sem1->id
        ])
        ->get()
        ->groupBy('status')
        ->keys()
        ->all();

    expect($student0_sem1_record_status)->toHaveCount(1)
        ->and($student0_sem1_record_status)->toBe(['POSTED']);

    // make sure other semester is not posted
    $student0_sem1_record_status = ConductRecord::where([
        'student_id' => $students[0]->id,
        'conduct_setting_id' => $conduct_setting_sem2->id
        ])
        ->get()
        ->groupBy('status')
        ->keys()
        ->all();

    expect($student0_sem1_record_status)->toHaveCount(1)
        ->and($student0_sem1_record_status)->toBe(['DRAFT']);

    // testing sem 1, student 1, 1 homeroom, 3 non-homeroom
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[1])
        ->getConductScore('2025SEM1', '2025-01-01', '2025-06-31');
        
    expect($response)->toEqual(86.07);

    $student1_sem1_record_status = ConductRecord::where([
        'student_id' => $students[1]->id,
        'conduct_setting_id' => $conduct_setting_sem1->id
    ])
        ->get()
        ->groupBy('status')
        ->keys()
        ->all();

    expect($student1_sem1_record_status)->toHaveCount(1)
        ->and($student1_sem1_record_status)->toBe(['POSTED']);

    // testing sem 2, student 0, 1 homeroom, 4 non-homeroom
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[0])
        ->getConductScore('2025SEM2', '2025-07-01', '2025-12-31');

    expect($response)->toEqual(83.95);

    $student0_sem2_record_status = ConductRecord::where([
        'student_id' => $students[0]->id,
        'conduct_setting_id' => $conduct_setting_sem2->id
    ])
        ->get()
        ->groupBy('status')
        ->keys()
        ->all();

    expect($student0_sem2_record_status)->toHaveCount(1)
        ->and($student0_sem2_record_status)->toBe(['POSTED']);

    // testing sem 1, student 2, 1 homeroom, 4 non-homeroom
    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[2])
        ->getConductScore('2025SEM1', '2025-01-01', '2025-12-31');

    expect($response)->toEqual(0);

});

test('getPromotedRetained', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $class = ClassModel::factory()->create(['grade_id' => 7]);
    $senior_semester_class = SemesterClass::factory()->create(
        [
            'semester_setting_id' => $this->semester_setting_1->id,
            'class_id' => $class->id,
        ]
    );

    $promotion_marks = PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class_1->id,
            'conduct_mark_for_promotion' => 60.00,
            'net_average_for_promotion' => 56.60,
        ],
        [
            'semester_class_id' => $this->semester_class_2->id,
            'conduct_mark_for_promotion' => 90.00,
            'net_average_for_promotion' => 80.0,
        ],
        [
            'semester_class_id' => $senior_semester_class->id,
            'conduct_mark_for_promotion' => 90.00,
            'net_average_for_promotion' => 80.0,
        ]
    ))->create();

    StudentClass::factory()->create([
        'student_id' => $students[0]->id,
        'semester_class_id' => $this->semester_class_1->id,
        'semester_setting_id' => $this->semester_setting_1->id,
        'is_latest_class_in_semester' => true
    ]);

    StudentClass::factory()->create([
        'student_id' => $students[0]->id,
        'semester_class_id' => $senior_semester_class->id,
        'semester_setting_id' => $this->semester_setting_2->id,
        'is_latest_class_in_semester' => true
    ]);

    $output_values = [];

    // test promoted
    $output_values['FINALEXAM']['CONDUCT']['total'] = 70;
    $output_values['FINALEXAM']['SYS_NET_AVG']['total'] = 70;

    $response = $this->reportCardOutputService->setOutputValues($output_values)
        ->setStudentGradingFramework($sgfs[0])
        ->getPromotedRetained('2025SEM1', 'FINALEXAM');

    expect($response)->toBe('升');

    // test retained (not enough net average)
    $output_values['FINALEXAM']['CONDUCT']['total'] = 70;
    $output_values['FINALEXAM']['SYS_NET_AVG']['total'] = 50;

    $response = $this->reportCardOutputService->setOutputValues($output_values)
        ->setStudentGradingFramework($sgfs[0])
        ->getPromotedRetained('2025SEM1', 'FINALEXAM');

    expect($response)->toBe('留');

    // test retained (not enough conduct)
    $output_values['FINALEXAM']['CONDUCT']['total'] = 59.99;
    $output_values['FINALEXAM']['SYS_NET_AVG']['total'] = 80;

    $response = $this->reportCardOutputService->setOutputValues($output_values)
        ->setStudentGradingFramework($sgfs[0])
        ->getPromotedRetained('2025SEM1', 'FINALEXAM');

    expect($response)->toBe('留');

    // test for a different semester code
    $output_values['FINALEXAM']['CONDUCT']['total'] = 95;
    $output_values['FINALEXAM']['SYS_NET_AVG']['total'] = 81;

    $response = $this->reportCardOutputService->setOutputValues($output_values)
        ->setStudentGradingFramework($sgfs[0])
        ->getPromotedRetained('2025SEM2', 'FINALEXAM');

    expect($response)->toBe('毕业');
    

    // test for a different semester code
    $output_values['FINALEXAM']['CONDUCT']['total'] = 80;
    $output_values['FINALEXAM']['SYS_NET_AVG']['total'] = 60;

    $response = $this->reportCardOutputService->setOutputValues($output_values)
        ->setStudentGradingFramework($sgfs[0])
        ->getPromotedRetained('2025SEM2', 'FINALEXAM');

    expect($response)->toBe('结业');
});

test('getClassLeadershipPositions', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();


    $leadership_position_1 = LeadershipPosition::factory()->create([
        'name->en' => 'Class Leader',
        'is_active' => true,
    ]);

    $leadership_position_2 = LeadershipPosition::factory()->create([
        'name->en' => 'Prefect',
        'is_active' => true,
    ]);

    LeadershipPositionRecord::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class_1->id,
            'semester_setting_id' => $this->semester_setting_1->id,
            'leadership_position_id' => $leadership_position_1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $this->semester_class_2->id,
            'semester_setting_id' => $this->semester_setting_2->id,
            'leadership_position_id' => $leadership_position_2->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $this->semester_class_1->id,
            'semester_setting_id' => $this->semester_setting_1->id,
            'leadership_position_id' => $leadership_position_2->id,
            'student_id' => $students[1]->id,
        ],
    ))->create();

    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getClassLeadershipPositions('2025SEM1');

    expect($response)->toBe($leadership_position_1->name);

    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getClassLeadershipPositions('2025SEM2');

    expect($response)->toBe($leadership_position_2->name);

    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[1])
        ->getClassLeadershipPositions('2025SEM1');

    expect($response)->toBe($leadership_position_2->name);

    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[2])
        ->getClassLeadershipPositions('2025SEM1');

    expect($response)->toBe(null);

});

test('getSociety', function(){
    $scout_society = ClassModel::factory()->create([
        'name->en' => 'SCOUT',
        'type' => ClassType::SOCIETY,
    ]);

    $badminton_society = ClassModel::factory()->create([
        'name->en' => 'BADMINTON',
        'type' => ClassType::SOCIETY,
    ]);

    $scout_semester_1 = SemesterClass::factory() ->create([
        'semester_setting_id' => $this->semester_setting_1->id,
        'class_id' => $scout_society->id
    ]);

    $scout_semester_2 = SemesterClass::factory() ->create([
        'semester_setting_id' => $this->semester_setting_2->id,
        'class_id' => $scout_society->id
    ]);

    $badminton_semester_1 = SemesterClass::factory() ->create([
        'semester_setting_id' => $this->semester_setting_1->id,
        'class_id' => $badminton_society->id
    ]);

    $students = Student::factory(3)->create();

    StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $this->semester_setting_1->id,
            'semester_class_id' => $scout_semester_1->id,
            'student_id' => $students[0]->id
        ],
        [
            'semester_setting_id' => $this->semester_setting_1->id,
            'semester_class_id' => $badminton_semester_1->id,
            'student_id' => $students[1]->id
        ],
        [
            'semester_setting_id' => $this->semester_setting_2->id,
            'semester_class_id' => $scout_semester_2->id,
            'student_id' => $students[0]->id
        ],
        [
            'semester_setting_id' => $this->semester_setting_2->id,
            'semester_class_id' => $scout_semester_2->id,
            'student_id' => $students[1]->id
        ],
    ))->create();

    $player = SocietyPosition::factory()->create(['name' => 'Player']);
    $leader = SocietyPosition::factory()->create(['name' => 'Leader']);

    $student_society_position = StudentSocietyPosition::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $scout_semester_1->id,
            'student_id' => $students[0]->id,
            'society_position_id' => $leader->id
        ],
        [
            'semester_class_id' => $badminton_semester_1->id,
            'student_id' => $students[1]->id,
            'society_position_id' => $player->id
        ],
        [
            'semester_class_id' => $scout_semester_2->id,
            'student_id' => $students[0]->id,
            'society_position_id' => $leader->id
        ]
    ))->create();

    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    // student 0 join scout in sem1 2025
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getSociety('2025SEM1');

    expect($response)->toBe($scout_society->name." (".$leader->name.")");

    // student 0 only scout in sem 2 2025
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getSociety('2025SEM2');

    expect($response)->toBe($scout_society->name." (".$leader->name.")");

    // student 1 join badminton in sem 1 2025
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[1])
        ->getSociety('2025SEM1');

    expect($response)->toBe($badminton_society->name." (".$player->name.")");

    // student 1 join badminton in sem 2 2025
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[1])
        ->getSociety('2025SEM2');

    expect($response)->toBe($scout_society->name);

    // student 2 did not join any societies
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[2])
        ->getSociety('2025SEM1');

    expect($response)->toBe(null);

});

test('getAbsence', function(){
    Carbon::setTestNow('2025-06-01 00:00:00');
    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $leave_application_type_1 = LeaveApplicationType::factory()->create(['name->zh' => 'MC']);
    $leave_application_type_2 = LeaveApplicationType::factory()->create(['name->zh' => 'Emergency']);

    $leave_applications = LeaveApplication::factory(3)->state(new Sequence(
        [
            'leave_applicable_id' => $students[0],
            'leave_application_type_id' => $leave_application_type_1->id,
            'average_point_deduction' => 0.01,
            'status' => LeaveApplicationStatus::APPROVED->value,
        ],
        [
            'leave_applicable_id' => $students[0],
            'leave_application_type_id' => $leave_application_type_2->id,
            'average_point_deduction' => 0.0,
            'status' => LeaveApplicationStatus::APPROVED->value,
        ],
        [
            'leave_applicable_id' => $students[1],
            'leave_application_type_id' => $leave_application_type_1->id,
            'average_point_deduction' => 0.01,
            'status' => LeaveApplicationStatus::APPROVED->value,
        ]
    ))->create();

    $leave_application_period = LeaveApplicationPeriod::factory(8)->state(new Sequence(
        [
            'leave_application_id' => $leave_applications[0]->id,
            'date' => '2025-05-01',
        ],
    ))->create();

    $leave_application_period = LeaveApplicationPeriod::factory(5)->state(new Sequence(
        [
            'leave_application_id' => $leave_applications[1]->id,
            'date' => '2025-06-01',
        ],
    ))->create();

    $leave_application_period = LeaveApplicationPeriod::factory(8)->state(new Sequence(
        [
            'leave_application_id' => $leave_applications[2]->id,
            'date' => '2025-05-01',
        ],
    ))->create();

    // Creating ABSENT/LATE data for student 0
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-02-01',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'has_mark_deduction' => true
        ],
    ))->create();

    PeriodAttendance::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-03-01',
            'status' => PeriodAttendanceStatus::LATE->value,
            'leave_application_id' => null,
            'has_mark_deduction' => true
        ],
    ))->create();

    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[0]->id,
            'has_mark_deduction' => true
        ],
    ))->create();

    PeriodAttendance::factory(5)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-04-05',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[1]->id,
            'has_mark_deduction' => true
        ],
    ))->create();

    // Creating ABSENT/LATE data for student 1
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'date' => '2025-02-01',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'has_mark_deduction' => true
        ],
    ))->create();

    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[2]->id,
            'has_mark_deduction' => true
        ],
    ))->create();

    // Data that should be filtered out (out of date range)
    PeriodAttendance::factory(5)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2026-04-05',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'has_mark_deduction' => true
        ],
    ))->create();

    // Dummy Present Data, repeating eight times for each
    PeriodAttendance::factory(24)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ],
        [
            'student_id' => $students[1]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ],
        [
            'student_id' => $students[2]->id,
            'date' => '2025-02-02',
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'leave_application_id' => null,
        ]
    ))->create();

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[0])
        ->getAbsence('2025-01-01', '2025-06-01');

    expect($response)->toBe('旷课:8节; MC:8节; Emergency:5节');

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[1])
        ->getAbsence('2025-01-01', '2025-06-01');

    expect($response)->toBe('旷课:8节; MC:8节');

    $response = $this->reportCardOutputService
        ->setStudentGradingFramework($sgfs[2])
        ->getAbsence('2025-01-01', '2025-06-01');

    expect($response)->toBe(null);


});

test('getMerit', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $reward_punishments = RewardPunishment::factory(4)->state(new Sequence(
        ['name->en' => 'Cleaning'],
        ['name->en' => 'Charity'],
        ['name->en' => 'Stealing'],
        ['name->en' => 'Very Very Long reward punishment meant to exceed 50 characters so that we can test this thing haiya isit 50 characters already or not']
    ))->create();

    $merit_demerit = MeritDemeritSetting::factory(3)->state(new Sequence(
        ['name->en' => '1 Merit'],
        ['name->en' => '2 Merit'],
        ['name->en' => '1 Demerit']
    ))->create();

    MeritDemeritRewardPunishment::factory(5)->state(new Sequence(
        [
            'merit_demerit_id' => $merit_demerit[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[1]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[1]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[2]->id,
            'reward_punishment_id' => $reward_punishments[2]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[0]->id,
            'reward_punishment_id' => $reward_punishments[3]->id,
        ]
    ))->create();

    $reward_punishment_records = RewardPunishmentRecord::factory(10)->state(new Sequence(
        [
            'date' => '2025-02-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2026-04-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-05-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[2]->id,
            'display_in_report_card' => false,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::DRAFT->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[1]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-01-01',
            'student_id' => $students[1]->id,
            'reward_punishment_id' => $reward_punishments[3]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ],
        [
            'date' => '2025-07-01',
            'student_id' => $students[2]->id,
            'reward_punishment_id' => $reward_punishments[3]->id,
            'display_in_report_card' => true,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ]
    ))->create();

    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getMerit('2025-01-01', '2025-06-01');

    // only latest three records should be shown
    expect($response)->toBeString()
        ->toBe('2 Merit (Charity); 2 Merit (Charity); 1 Merit, 2 Merit (Cleaning)');

    // only fourth record should be shown
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getMerit('2026-01-01', '2026-06-01');

    expect($response)->toBeString()
        ->toBe('2 Merit (Charity)');

    // only fourth record should be shown, long record is ignored
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[1])
        ->getMerit('2025-01-01', '2025-06-01');

    expect($response)->toBeString()
        ->toBe('2 Merit (Charity)');

    // student 2 has no record in this period
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[2])
        ->getMerit('2025-01-01', '2025-06-01');

    expect($response)->toBe(null);

    // student 2 has no record in this period
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[2])
        ->getMerit('2025-06-30', '2025-12-01');

    expect($response)->toBe('请参阅成绩附件');


});

test('getExceptionalPerformance', function(){

    $students = Student::factory(3)->create();
    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        ['student_id' => $students[0]->id],
        ['student_id' => $students[1]->id],
        ['student_id' => $students[2]->id]
    ))->create();

    $competitions = Competition::factory(7)->state(new Sequence(
        [
            'name' => 'Football Match',
            'date' => '2025-06-01'
        ],
        [
            'name' => 'this is a very looooooooooooooooooooooooooooooooooooooong basketball tournament',
            'date' => '2025-02-01'
        ],
        [
            'name' => '2025 饮食比赛 - 最胖的人将赢得唐人街的自助餐!',
            'date' => '2025-05-01'
        ],
        [
            'name' => 'Swimming competition',
            'date' => '2025-04-01'
        ],
        [
            'name' => 'Eating competition',
            'date' => '2025-03-01'
        ],
        [
            'name' => 'Tennis Open',
            'date' => '2026-03-01'
        ],
        [
            'name' => 'this is a very looooooooooooooooooooooooooooooooooooooong football competition......',
            'date' => '2025-02-01'
        ],
    ))->create();

    $competition_records = CompetitionRecord::factory(7)->state(new Sequence(
        [
            'competition_id' => $competitions[0]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 1,
        ],
        [
            'competition_id' => $competitions[1]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'competition_id' => $competitions[2]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'competition_id' => $competitions[3]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'competition_id' => $competitions[4]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'competition_id' => $competitions[1]->id,
            'student_id' => $students[1]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
        [
            'competition_id' => $competitions[6]->id,
            'student_id' => $students[1]->id,
            'semester_class_id' => $this->semester_class_1->id,
            'mark' => 2,
        ],
    ))->create();

    // testing general logic
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getExceptionalPerformance('2025-01-01', '2025-06-01');

    $result = 'Football Match; 2025 饮食比赛 - 最胖的人将赢得唐人街的自助餐!; Swimming competition';

    expect($response)->toBeString()
        ->toBe($result);

    // testing date filter logic
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[0])
        ->getExceptionalPerformance('2025-03-01', '2025-05-01');

    $result = '2025 饮食比赛 - 最胖的人将赢得唐人街的自助餐!; Swimming competition; Eating competition';

    expect($response)->toBeString()
        ->toBe($result);

    // testing all items is more than 50 characters
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[1])
        ->getExceptionalPerformance('2025-01-01', '2025-06-01');

    expect($response)->toBeString()
        ->toBe('请参阅成绩附件');

    // testing when null / no competition record
    $response = $this->reportCardOutputService->setStudentGradingFramework($sgfs[2])
        ->getExceptionalPerformance('2025-01-01', '2025-06-01');

    expect($response)->toBe(null);

});
