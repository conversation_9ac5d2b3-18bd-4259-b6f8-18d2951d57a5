<?php

use App\Models\ClassModel;
use App\Models\Grade;
use App\Models\PromotionMark;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Repositories\PromotionMarkRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->promotionMarkRepository = app(PromotionMarkRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(PromotionMark::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->promotionMarkRepository->getModelClass();

    expect($response)->toEqual(PromotionMark::class);
});


test('getAll()', function () {

    $grade1 = Grade::factory()->create();
    $grade2 = Grade::factory()->create();

    $class1 = ClassModel::factory()->create(['grade_id' => $grade1->id]);
    $class2 = ClassModel::factory()->create(['grade_id' => $grade2->id]);
    $class3 = ClassModel::factory()->create(['grade_id' => $grade1->id]);

    $semester_setting1 = SemesterSetting::factory()->create();
    $semester_setting2 = SemesterSetting::factory()->create();

    $semester_classes = SemesterClass::factory(3)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class1->id
        ],
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class2->id
        ],
        [
            'semester_setting_id' => $semester_setting2->id,
            'class_id' => $class3->id
        ]

    ))->create();

    PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_classes[0]->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[1]->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[2]->id,
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();


    $result = $this->promotionMarkRepository->getAll()->toArray();
    expect($result)->toHaveCount(3)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_classes[0]->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result[1])->toMatchArray([
            "semester_class_id" => $semester_classes[1]->id,
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result[2])->toMatchArray([
            "semester_class_id" => $semester_classes[2]->id,
            "net_average_for_promotion"=> 90.00,
            "conduct_mark_for_promotion" => 80.00,     
        ]);
    

    // filter test
    $filters = [ 'semester_class_id' => $semester_classes[0]->id ];

    $result = $this->promotionMarkRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount(1)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_classes[0]->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);

    // filter relation test 
    $filters = [
        'grade_id' => $grade2->id,
        'semester_setting_id' => $semester_setting1->id
    ];

    $result = $this->promotionMarkRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount(1)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_classes[1]->id,
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);
});

test('getAllPaginated()', function () {

    $grade1 = Grade::factory()->create();
    $grade2 = Grade::factory()->create();

    $class1 = ClassModel::factory()->create(['grade_id' => $grade1->id]);
    $class2 = ClassModel::factory()->create(['grade_id' => $grade2->id]);
    $class3 = ClassModel::factory()->create(['grade_id' => $grade1->id]);

    $semester_setting1 = SemesterSetting::factory()->create();
    $semester_setting2 = SemesterSetting::factory()->create();

    $semester_classes = SemesterClass::factory(3)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class1->id
        ],
        [
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class2->id
        ],
        [
            'semester_setting_id' => $semester_setting2->id,
            'class_id' => $class3->id
        ]

    ))->create();

    PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_classes[0]->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[1]->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_classes[2]->id,
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();


    $result = $this->promotionMarkRepository->getAllPaginated()->toArray();
    expect($result['data'])->toHaveCount(3)
        ->and($result['data'][0])->toMatchArray([
            "semester_class_id" => $semester_classes[0]->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result['data'][1])->toMatchArray([
            "semester_class_id" => $semester_classes[1]->id,
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result['data'][2])->toMatchArray([
            "semester_class_id" => $semester_classes[2]->id,
            "net_average_for_promotion"=> 90.00,
            "conduct_mark_for_promotion" => 80.00,     
        ]);
    

    // filter test
    $filters = [ 'semester_class_id' => $semester_classes[0]->id ];

    $result = $this->promotionMarkRepository->getAllPaginated($filters)->toArray();
    expect($result['data'])->toHaveCount(1)
        ->and($result['data'][0])->toMatchArray([
            "semester_class_id" => $semester_classes[0]->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);

    $filters = [
        'grade_id' => $grade2->id,
        'semester_setting_id' => $semester_setting1->id
    ];

    // filter relation test
    $result = $this->promotionMarkRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount(1)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_classes[1]->id,
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);        
});
