<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\ResultsPostingLineItemHistory;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\StudentReportCard;
use App\Models\Subject;
use App\Models\User;
use App\Services\Exam\ExamResultsPostingService;
use App\Services\Exam\Output\GeneralReportCardOutputService;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {

    Cache::clear();
    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();


    $this->grade1 = Grade::factory()->create([
        'name->en' => 'J1',
    ]);
    $this->grade2 = Grade::factory()->create([
        'name->en' => 'J2',
    ]);

    $this->student1 = Student::factory()->create();
    $this->student2 = Student::factory()->create();
    $this->student3 = Student::factory()->create();

    $course = Course::factory()->uec()->create();

    $this->semester_setting1 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
    ]);
    $this->semester_setting2 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 2',
    ]);

    $this->class1 = ClassModel::factory()->create([
        'grade_id' => $this->grade1->id,
    ]);
    $this->class2 = ClassModel::factory()->create([
        'grade_id' => $this->grade2->id,
    ]);

    $this->semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'class_id' => $this->class1->id,
    ]);
    $this->semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'class_id' => $this->class2->id,
    ]);
    $this->semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'class_id' => $this->class1->id,
    ]);
    $this->semester_class4 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'class_id' => $this->class2->id,
    ]);

    // in sem 1, student 1 & 2 in class 1, student 3 in class 2
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_class1->id,
        'student_id' => $this->student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_class1->id,
        'student_id' => $this->student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_class2->id,
        'student_id' => $this->student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    // in sem 2, student 1 & 2 & 3 in class 2, no one in class 1
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_class4->id,
        'student_id' => $this->student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_class4->id,
        'student_id' => $this->student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_class4->id,
        'student_id' => $this->student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student1->id,
        'is_active' => true,
    ]);
    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student2->id,
        'is_active' => true,
    ]);
    $sgf3 = StudentGradingFramework::factory()->create([
        'student_id' => $this->student3->id,
        'is_active' => true,
    ]);

    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf1->id,
        'code' => 'output1',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf1->id,
        'code' => 'output2',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf2->id,
        'code' => 'output1',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf2->id,
        'code' => 'output2',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf3->id,
        'code' => 'output3',
    ]);
    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf3->id,
        'code' => 'output4',
    ]);

    $this->academic_year = '2024';
});

test('getEligibleMasterGradingFrameworks', function () {

    $semester_settings = SemesterSetting::factory(2)->create();
    $grades = Grade::factory(2)->create();
    $students = Student::factory(4)->create();

    $classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name->en' => 'Grade 0 Class A',
            'grade_id' => $grades[0]->id,
        ],
        [
            'name->en' => 'Grade 0 Class B',
            'grade_id' => $grades[0]->id,
        ],
        [
            'name->en' => 'Grade 1 Class A',
            'grade_id' => $grades[1]->id,
        ]
    ))->create();

    $semester_classes = SemesterClass::factory(6)->state(new Sequence(
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[0]->id
        ],
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[1]->id
        ],
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[2]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[0]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[1]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[2]->id
        ],
    ))->create();

    StudentClass::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_classes[0]->id
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_classes[0]->id
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_classes[1]->id
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_classes[2]->id
        ],
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_classes[3]->id
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_classes[3]->id
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_classes[4]->id
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_classes[5]->id
        ],
    ))->create();

    $grading_frameworks = GradingFramework::factory(3)->state(new Sequence(
        [
            'name->en' => 'Junior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Junior 2 High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Senior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ]
    ))->create();

    $sgfs = StudentGradingFramework::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[2]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[3]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[2]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[3]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => false
        ],
    ))->create();

    $payload = [
        'grade_id' => $grades[0]->id,
        'semester_setting_id' => $semester_settings[0]->id
    ];

    $response = app()->make(ExamResultsPostingService::class)
        ->getEligibleMasterGradingFrameworks($payload);

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'id' => $grading_frameworks[0]->id,
            'name' => [
                'en' => 'Junior High School Grading Framework',
                'zh' => '初中生成绩模板',
            ]
        ])
        ->and($response[1])->toMatchArray([
            'id' => $grading_frameworks[1]->id,
            'name' => [
                'en' => 'Junior 2 High School Grading Framework',
                'zh' => '初中生成绩模板',
            ]
        ]);

    $payload = [
        'grade_id' => $grades[1]->id,
        'semester_setting_id' => $semester_settings[1]->id
    ];

    $response = app()->make(ExamResultsPostingService::class)
        ->getEligibleMasterGradingFrameworks($payload);

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            'id' => $grading_frameworks[2]->id,
            'name' => [
                'en' => 'Senior High School Grading Framework',
                'zh' => '初中生成绩模板',
            ]
        ]);

});


test('getEligibleReportCardOutputCodes', function () {

    $students = Student::factory(4)->create();
    $grading_frameworks = GradingFramework::factory(3)->state(new Sequence(
        [
            'name->en' => 'Junior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Senior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Other High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ]
    ))->create();

    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => true
        ]
    ))->create();

    $report_card_outputs = ReportCardOutput::factory(6)->state(new Sequence(
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'SEM1RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'SEM2RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'FINALRESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWSEM1RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWSEM2RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWFINALRESULT'
        ]
    ))->create();

    $payload = ['grading_framework_id' => $grading_frameworks[0]->id];

    $response = app()->make(ExamResultsPostingService::class)
        ->getEligibleReportCardOutputCodes($payload);

    expect($response)->toHaveCount(3)
        ->and($response[0])->toMatchArray(['code' => $report_card_outputs[0]->code])
        ->and($response[1])->toMatchArray(['code' => $report_card_outputs[1]->code])
        ->and($response[2])->toMatchArray(['code' => $report_card_outputs[2]->code]);


    $payload = ['grading_framework_id' => $grading_frameworks[1]->id];

    $response = app()->make(ExamResultsPostingService::class)
        ->getEligibleReportCardOutputCodes($payload);

    expect($response)->toHaveCount(3)
        ->and($response[0])->toMatchArray(['code' => $report_card_outputs[3]->code])
        ->and($response[1])->toMatchArray(['code' => $report_card_outputs[4]->code])
        ->and($response[2])->toMatchArray(['code' => $report_card_outputs[5]->code]);


    $payload = ['grading_framework_id' => $grading_frameworks[2]->id];

    $response = app()->make(ExamResultsPostingService::class)
        ->getEligibleReportCardOutputCodes($payload);

    expect($response)->toHaveCount(0);
});

test('checkDataEntryCompleteness', function () {

    $student = Student::factory()->create();

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id
    ]);

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[4]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '03')->first()->id)
        ->first();
    $result_source_subject_4 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '04')->first()->id)
        ->first();
    $result_source_subject_5 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '70')->first()->id)
        ->first();

    $errors = app()->make(ExamResultsPostingService::class)
        ->setReportCardOutput($report_card_output)
        ->setReportCardOutputService($report_card_output->service_class)
        ->checkDataEntryCompleteness();

    expect($errors)->toHaveCount(6)->toContain(
        "No score entered for SEM1EXAM > {$this->subjects[0]->name} > FINAL",
        "No score entered for SEM1EXAM > {$this->subjects[1]->name} > FINAL",
        "No grade entered for SEM1EXAM > {$this->subjects[2]->name}",
        "No score entered for SEM1EXAM > {$this->subjects[3]->name} > HOMEWORK",
        "No score entered for SEM1EXAM > {$this->subjects[3]->name} > FINAL",
        "No score entered for SEM1EXAM > {$this->subjects[4]->name} > FINAL",
    );

    $result_source_subject_1->components()->update([
        'actual_score' => 100
    ]);
    $result_source_subject_2->components()->update([
        'actual_score' => 100
    ]);
    $result_source_subject_3->update([
        'actual_score_grade' => 'B'
    ]);
    $result_source_subject_4->components()->update([
        'actual_score' => 100
    ]);
    $result_source_subject_5->components()->update([
        'actual_score' => 100
    ]);

    $result_source->refresh();

    $errors = app()->make(ExamResultsPostingService::class)
        ->setReportCardOutput($report_card_output)
        ->setReportCardOutputService($report_card_output->service_class)
        ->checkDataEntryCompleteness();

    expect($errors)->toBeEmpty();
});


test('runPosting and savePostingOutput', function () {

    $student = Student::factory()->create();
    $grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
        'grade_id' => $grade->id,
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id,
        'semester_setting_id' => $semester_setting->id
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[4]->id
        ]
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '03')->first()->id)
        ->first();
    $result_source_subject_4 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '04')->first()->id)
        ->first();
    $result_source_subject_5 = ResultSourceSubject::where('result_source_id', $result_source->id)
        ->where('subject_id', $this->subjects->where('code', '70')->first()->id)
        ->first();

    $result_source_subject_1->components()->update([
        'actual_score' => 40
    ]);
    $result_source_subject_2->components()->update([
        'actual_score' => 75
    ]);
    $result_source_subject_3->update([
        'actual_score_grade' => 'B+'
    ]);
    $result_source_subject_4->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100
    ]);
    $result_source_subject_4->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 80
    ]);
    $result_source_subject_5->components()->update([
        'actual_score' => 100
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'code' => 'SEM1EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student->id],
        'metadata' => [
            'test_number' => 1458,          // can use metadata to specify things like attendance start/end date for results calculation
        ]
    ]);

    $header2 = ResultsPostingHeader::factory()->create([
        'code' => 'SEM2EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student->id],
    ]);

    $service = app()->make(ExamResultsPostingService::class);

    $service
        ->setReportCardOutput($report_card_output)
        ->setResultsPostingHeader($header1)
        ->setReportCardOutputService($report_card_output->service_class)
        ->runChecks()
        ->runPosting();

   // dd($service->getOutputValues()['SEM1RESULT']['01']);
    expect($service->getOutputValues())->toHaveKeys(['SEM1RESULT'])->toHaveCount(1)
        ->and($service->getOutputValues()['SEM1RESULT']['01'])->toMatchArray([
            'total' => 40.0,
            'label' => 'FAILED',
            'total_grade' => [
                'name' => 'dumb dumb',
                'display_as_name' => 'FAILED',
                'extra_marks' => 0,
            ],
            'weightage_multiplier' => 2.0000,
            'weightage_total' => 80.00,
            'subject_id' => $this->subjects[0]->id,
            'grading_scheme_id' => $this->gradingScheme->id,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
            'report_card_output_component' => $report_card_output->components->where('code', '01')->first(),
            'calculate_rank' => true
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['02'])->toMatchArray([
            'total' => 75.0,
            'label' => 'B',
            'total_grade' => [
                'name' => 'B',
                'display_as_name' => 'B',
                'extra_marks' => 0,
            ],
            'weightage_multiplier' => 2.0000,
            'weightage_total' => 150.00,
            'subject_id' => $this->subjects[1]->id,
            'grading_scheme_id' => $this->gradingScheme->id,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
            'report_card_output_component' => $report_card_output->components->where('code', '02')->first(),
            'calculate_rank' => true
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['03'])->toMatchArray([
            'total' => null,
            'label' => 'B+',
            'total_grade' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => $this->subjects[2]->id,
            'grading_scheme_id' => $this->gradingScheme->id,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
            'report_card_output_component' => $report_card_output->components->where('code', '03')->first(),
            'calculate_rank' => false
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['04'])->toMatchArray([
            'total' => 86.0,
            'label' => 'A',
            'total_grade' => [
                'name' => 'A',
                'display_as_name' => 'A',
                'extra_marks' => 1,
            ],
            'weightage_multiplier' => 1.0000,
            'weightage_total' => 86.00,
            'subject_id' => $this->subjects[3]->id,
            'grading_scheme_id' => $this->gradingScheme->id,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
            'report_card_output_component' => $report_card_output->components->where('code', '04')->first(),
            'calculate_rank' => true
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['70'])->toMatchArray([
            'total' => 100,
            'label' => 'A+',
            'total_grade' => [
                'name' => 'A+',
                'display_as_name' => 'A+',
                'extra_marks' => 2,
            ],
            'weightage_multiplier' => 1.0000,
            'weightage_total' => 100.00,
            'subject_id' => $this->subjects[4]->id,
            'grading_scheme_id' => $this->gradingScheme->id,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
            'report_card_output_component' => $report_card_output->components->where('code', '70')->first(),
            'calculate_rank' => true
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['GT'])->toMatchArray([
            'total' => 316,
            'label' => null,
            'total_grade' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'output_type' => null,
            'report_card_output_component' => $report_card_output->components->where('code', 'GT')->first(),
            'calculate_rank' => false
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['GW'])->toMatchArray([
            'total' => 5,
            'label' => null,
            'total_grade' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'output_type' => null,
            'report_card_output_component' => $report_card_output->components->where('code', 'GW')->first(),
            'calculate_rank' => false
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['GA'])->toMatchArray([
            'total' => 63.2,
            'label' => null,
            'total_grade' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'output_type' => null,
            'report_card_output_component' => $report_card_output->components->where('code', 'GA')->first(),
            'calculate_rank' => false
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['MS'])->toMatchArray([
            'total' => 1458,       // from metadata
            'label' => null,
            'total_grade' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'output_type' => null,
            'report_card_output_component' => $report_card_output->components->where('code', 'MS')->first(),
            'calculate_rank' => false
        ])
        ->and($service->getOutputValues()['SEM1RESULT']['MA']['total'])->toBeBetween(0, 100)
        ->and($service->getOutputValues()['SEM1RESULT']['MA']['total_grade'])->toBeNull()
        ->and($service->getOutputValues()['SEM1RESULT']['MS']['total'])->toEqual(1458)
        ->and($service->getOutputValues()['SEM1RESULT']['MS']['total_grade'])->toBeNull()
        ->and($service->getOutputValues()['SEM1RESULT']['SYS_NET_AVG']['total'])->toEqual(316 + $service->getOutputValues()['SEM1RESULT']['MA']['total'] - $service->getOutputValues()['SEM1RESULT']['MS']['total'])
        ->and($service->getOutputValues()['SEM1RESULT']['SYS_NET_AVG']['total_grade'])->toBeNull()
        ->and($service->getOutputValues()['SEM1RESULT']['SYS_GRADE']['label'])->toBe('custom-function-output-no-args')
        ->and($service->getOutputValues()['SEM1RESULT']['SYS_GRADE']['total_grade'])->toBeNull()
        ->and($service->getOutputValues()['SEM1RESULT']['SYS_GRADE']['total'])->toBeNull();

    $this->assertDatabaseEmpty(ResultsPostingLineItem::class);
    $this->assertDatabaseEmpty(ResultsPostingLineItemHistory::class);

    $service->savePostingOutput();

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 12);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 0);

    $output_values_v1 = $service->getOutputValues()['SEM1RESULT'];

    expect(count($output_values_v1))->toBe(12);

    foreach ($output_values_v1 as $output_component_code => $item) {

        $compare = [
            'header_id' => $header1->id,
            'report_card_output_component_id' => $item['report_card_output_component']->id,
            'report_card_output_component_code' => $output_component_code,
            'student_id' => $student->id,
            'subject_id' => $item['subject_id'],
            'grade_id' => $grade->id,
            'semester_class_id' => $first_semester_class->id,
            'grading_scheme_id' => $item['grading_scheme_id'],
            'grading_framework_id' => $sgf->grading_framework_id,
            'total' => $item['total'],
            'label' => $item['label'],
            'total_grade' => null,
            'calculate_rank' => $item['report_card_output_component']->calculate_rank,
            'weightage_multiplier' => $item['weightage_multiplier'],
            'weightage_total' => $item['weightage_total'],
        ];

        if ($item['total_grade'] !== null) {
            unset($compare['total_grade']);
            $compare['total_grade->name'] = $item['total_grade']['name'];
            $compare['total_grade->extra_marks'] = $item['total_grade']['extra_marks'];
            $compare['total_grade->display_as_name'] = $item['total_grade']['display_as_name'];
        }

        $this->assertDatabaseHas(ResultsPostingLineItem::class, $compare);
    }

    // let's do posting again, old data should be archived
    $service = app()->make(ExamResultsPostingService::class);

    $service
        ->setReportCardOutput($report_card_output)
        ->setResultsPostingHeader($header2)
        ->setReportCardOutputService($report_card_output->service_class)
        ->runChecks()
        ->runPosting()
        ->savePostingOutput();

    $output_values_v2 = $service->getOutputValues()['SEM1RESULT'];

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 12);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 12);

    expect(count($output_values_v2))->toBe(12);

    // check in line items table for latest entries
    foreach ($output_values_v2 as $output_component_code => $item) {

        $compare = [
            'header_id' => $header2->id,
            'report_card_output_component_id' => $item['report_card_output_component']->id,
            'report_card_output_component_code' => $output_component_code,
            'student_id' => $student->id,
            'subject_id' => $item['subject_id'],
            'grade_id' => $grade->id,
            'semester_class_id' => $first_semester_class->id,
            'grading_scheme_id' => $item['grading_scheme_id'],
            'grading_framework_id' => $sgf->grading_framework_id,
            'total' => $item['total'],
            'label' => $item['label'],
            'total_grade' => null,
            'calculate_rank' => $item['report_card_output_component']->calculate_rank,
            'weightage_multiplier' => $item['weightage_multiplier'],
            'weightage_total' => $item['weightage_total'],
        ];

        if ($item['total_grade'] !== null) {
            unset($compare['total_grade']);
            $compare['total_grade->name'] = $item['total_grade']['name'];
            $compare['total_grade->extra_marks'] = $item['total_grade']['extra_marks'];
            $compare['total_grade->display_as_name'] = $item['total_grade']['display_as_name'];
        }

        $this->assertDatabaseHas(ResultsPostingLineItem::class, $compare);

    }

    // check in history table
    foreach ($output_values_v1 as $output_component_code => $item) {

        $compare = [
            'header_id' => $header1->id,
            'report_card_output_component_id' => $item['report_card_output_component']->id,
            'report_card_output_component_code' => $output_component_code,
            'student_id' => $student->id,
            'subject_id' => $item['subject_id'],
            'grade_id' => $grade->id,
            'semester_class_id' => $first_semester_class->id,
            'grading_scheme_id' => $item['grading_scheme_id'],
            'grading_framework_id' => $sgf->grading_framework_id,
            'total' => $item['total'],
            'label' => $item['label'],
            'total_grade' => null,
            'calculate_rank' => $item['report_card_output_component']->calculate_rank,
            'output_type' => $item['report_card_output_component']->output_type,
            'weightage_multiplier' => $item['weightage_multiplier'],
            'weightage_total' => $item['weightage_total'],
        ];

        if ($item['total_grade'] !== null) {
            unset($compare['total_grade']);
            $compare['total_grade->name'] = $item['total_grade']['name'];
            $compare['total_grade->extra_marks'] = $item['total_grade']['extra_marks'];
            $compare['total_grade->display_as_name'] = $item['total_grade']['display_as_name'];
        }

        $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, $compare);

    }
});


test('validateReportCardOutputCode', function () {

    $service = app()->make(ExamResultsPostingService::class);

    $results = $service->validateReportCardOutputCode('INVALIDCODE', $this->grade1, $this->semester_setting1);
    expect($results)->toBeFalse();

    // grade 1, sem 1
    $results = $service->validateReportCardOutputCode('output1', $this->grade1, $this->semester_setting1);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output2', $this->grade1, $this->semester_setting1);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output3', $this->grade1, $this->semester_setting1);
    expect($results)->toBeFalse();

    $results = $service->validateReportCardOutputCode('output4', $this->grade1, $this->semester_setting1);
    expect($results)->toBeFalse();


    // grade 1, sem 2
    try {
        $results = $service->validateReportCardOutputCode('output1', $this->grade1, $this->semester_setting2);
        expect($results)->toBeFalse();
    } catch (Exception $e) {
        expect($e)->toBeInstanceOf(\App\Exceptions\ExamResultsPostingException::class)
            ->and($e->getMessage())->toBe('Students not found for Grade J1 in Semester 2025 Semester 2');
    }

    try {
        $results = $service->validateReportCardOutputCode('output2', $this->grade1, $this->semester_setting2);
        expect($results)->toBeFalse();
    } catch (Exception $e) {
        expect($e)->toBeInstanceOf(\App\Exceptions\ExamResultsPostingException::class)
            ->and($e->getMessage())->toBe('Students not found for Grade J1 in Semester 2025 Semester 2');
    }

    try {
        $results = $service->validateReportCardOutputCode('output3', $this->grade1, $this->semester_setting2);
        expect($results)->toBeFalse();
    } catch (Exception $e) {
        expect($e)->toBeInstanceOf(\App\Exceptions\ExamResultsPostingException::class)
            ->and($e->getMessage())->toBe('Students not found for Grade J1 in Semester 2025 Semester 2');
    }

    try {
        $results = $service->validateReportCardOutputCode('output4', $this->grade1, $this->semester_setting2);
        expect($results)->toBeFalse();
    } catch (Exception $e) {
        expect($e)->toBeInstanceOf(\App\Exceptions\ExamResultsPostingException::class)
            ->and($e->getMessage())->toBe('Students not found for Grade J1 in Semester 2025 Semester 2');
    }

    // grade 2, sem 1
    $results = $service->validateReportCardOutputCode('output1', $this->grade2, $this->semester_setting1);
    expect($results)->toBeFalse();

    $results = $service->validateReportCardOutputCode('output2', $this->grade2, $this->semester_setting1);
    expect($results)->toBeFalse();

    $results = $service->validateReportCardOutputCode('output3', $this->grade2, $this->semester_setting1);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output4', $this->grade2, $this->semester_setting1);
    expect($results)->toBeTrue();

    // grade 2, sem 2
    $results = $service->validateReportCardOutputCode('output1', $this->grade2, $this->semester_setting2);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output2', $this->grade2, $this->semester_setting2);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output3', $this->grade2, $this->semester_setting2);
    expect($results)->toBeTrue();

    $results = $service->validateReportCardOutputCode('output4', $this->grade2, $this->semester_setting2);
    expect($results)->toBeTrue();
});

test('createPostingSession', function () {

    $user = User::factory()->withEmployee()->create();
    Auth::onceUsingId($user->id);

    \Carbon\Carbon::setTestNow('2024-12-10 13:50:01.000');

    $service = app()->make(ExamResultsPostingService::class);
    $service->setGrade($this->grade1)
        ->setSemesterSetting($this->semester_setting1)
        ->setReportCardOutputCode('output1')
        ->setPublishDate(now()->toDateString())
        ->setReportCardTemplateServiceName('PinHwaDefaultReportCardTemplateService')
        ->createPostingSession();

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'code' => '20241210-1733838601000',
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'report_card_output_code' => 'output1',
        'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => json_encode([$this->student1->id, $this->student2->id]),
        'posted_by_employee_id' => $user->employee->id,
        'publish_date' => now()->toDateString(),
        'posted_at' => now(),
        'metadata' => null,
    ]);

    // with metadata
    $service = app()->make(ExamResultsPostingService::class);
    $service->setGrade($this->grade2)
        ->setSemesterSetting($this->semester_setting1)
        ->setPublishDate(now()->toDateString())
        ->setReportCardOutputCode('output3')
        ->setReportCardTemplateServiceName('PinHwaDefaultReportCardTemplateService')
        ->setMetadata(['attendance_from' => '2024-12-16', 'attendance_to' => '2024-12-31'])
        ->createPostingSession();

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'code' => '20241210-1733838601000',
        'grade_id' => $this->grade2->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'report_card_output_code' => 'output3',
        'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => json_encode([$this->student3->id]),
        'posted_by_employee_id' => $user->employee->id,
        'publish_date' => now()->toDateString(),
        'posted_at' => now(),
        'metadata->attendance_from' => '2024-12-16',
        'metadata->attendance_to' => '2024-12-31',
    ]);

});


test('updateClassAndGradeRankingsForPostingLineItems', function () {

    $students = Student::factory(6)->create();
    $grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
        'grade_id' => $grade->id,
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);

    $second_class = ClassModel::factory()->create([
        'name->en' => 'Class 2',
        'grade_id' => $grade->id,
    ]);
    $second_semester_class = SemesterClass::factory()->create([
        'class_id' => $second_class->id
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    // setup student's grading framework
    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => $students->pluck('id')->toArray(),
    ]);


    // students 0-2 in class 1, grade 1
    // students 3-5 in class 2, grade 1
    for ($i = 0; $i < 6; $i++) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $i <= 2 ? $first_semester_class->id : $second_semester_class->id,
            'student_id' => $students[$i]->id,
        ]);
        ResultsPostingLineItem::factory()->create([
            'header_id' => $header1->id,
            'student_id' => $students[$i]->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $i <= 2 ? $first_semester_class->id : $second_semester_class->id,
            'report_card_output_component_code' => 'BM',
            'calculate_rank' => false,      // wont calculate rank
            'total' => 100
        ]);
    }

    // mock marks
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 35,
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[1]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 65,
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[2]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 65,
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[3]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 70.063,
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[4]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 70.063,
    ]);
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[5]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'CHINESE',
        'calculate_rank' => true,
        'total' => 96,
    ]);

    // mock net average field
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 60
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[1]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 80
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[2]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $first_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 76
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[3]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 81.2
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[4]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 76
    ]);

    ResultsPostingLineItem::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $students[5]->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $second_semester_class->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'calculate_rank' => 1,
        'total' => 81.2
    ]);

    $service = app()->make(ExamResultsPostingService::class);

    $service
        ->setResultsPostingHeader($header1)
        ->updateClassAndGradeRankingsForPostingLineItems();

    // ensure all grade and class rankings are correct

    // SYS_NET_AVG rankings
    // class 1 ranking - student 1 (R1), 2 (R2), 0 (R3)
    // class 2 ranking - student 3, 5 (R1), 4 (R3)
    // grade ranking - student 3, 5 (R1), 1 (R3), 2, 4 (R4), 0 (R6)

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 3,
        'class_population' => 3,
        'grade_rank' => 6,
        'grade_population' => 6,
        'grade_percentile_rank' => 16.67,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[1]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 3,
        'grade_population' => 6,
        'grade_percentile_rank' => 66.67,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[2]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 2,
        'class_population' => 3,
        'grade_rank' => 4,
        'grade_population' => 6,
        'grade_percentile_rank' => 33.33,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[3]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 1,
        'grade_population' => 6,
        'grade_percentile_rank' => 83.33,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[4]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 3,
        'class_population' => 3,
        'grade_rank' => 4,
        'grade_population' => 6,
        'grade_percentile_rank' => 33.33,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[5]->id,
        'report_card_output_component_code' => 'SYS_NET_AVG',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 1,
        'grade_population' => 6,
        'grade_percentile_rank' => 83.33,
    ]);

    // CHINESE rankings
    // 5, 3+4 , 1+2 , 0
    // class 1 ranking - student 5 (R1), 3,4 (R2)
    // class 2 ranking - student 1+2 (R1), 0 (R3)
    // grade ranking - student 5 (R1), 3, 4 (R2), 1, 2 (R4), 0 (R6)

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[0]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 3,
        'class_population' => 3,
        'grade_rank' => 6,
        'grade_population' => 6,
        'grade_percentile_rank' => 16.67,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[1]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 4,
        'grade_population' => 6,
        'grade_percentile_rank' => 33.33,
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[2]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 4,
        'grade_population' => 6,
        'grade_percentile_rank' => 33.33,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[3]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 2,
        'class_population' => 3,
        'grade_rank' => 2,
        'grade_population' => 6,
        'grade_percentile_rank' => 66.67,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[4]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 2,
        'class_population' => 3,
        'grade_rank' => 2,
        'grade_population' => 6,
        'grade_percentile_rank' => 66.67,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $students[5]->id,
        'report_card_output_component_code' => 'CHINESE',
        'class_rank' => 1,
        'class_population' => 3,
        'grade_rank' => 1,
        'grade_population' => 6,
        'grade_percentile_rank' => 100.00,
    ]);

    // line item with calculate_rank = false should not be processed
    foreach ($students as $student) {
        $this->assertDatabaseHas(ResultsPostingLineItem::class, [
            'header_id' => $header1->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => 'BM',
            'class_rank' => null,
            'class_population' => null,
            'grade_rank' => null,
            'grade_population' => null,
            'grade_percentile_rank' => null,
        ]);
    }

});


test('setReportCardOutputService success', function () {

    $service = app()->make(ExamResultsPostingService::class);
    $service->setReportCardOutputService('GeneralReportCardOutputService');

    expect($service->getReportCardOutputService())->toBeInstanceOf(GeneralReportCardOutputService::class);

});


test('setReportCardOutputService failed', function () {

    $service = app()->make(ExamResultsPostingService::class);

    $this->expectExceptionMessage('Report Card Output class not found: InvalidClassName');
    $service->setReportCardOutputService('InvalidClassName');

});

test('allowReportCardVisibleByPostingHeader', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();
    $student3 = Student::factory()->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student1->id, $student2->id],
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student3->id],
    ]);

    $report_card1 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student1->id,
        'is_visible_to_student' => false,
    ]);

    $report_card2 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student2->id,
        'is_visible_to_student' => false,
    ]);

    $report_card3 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header2->id,
        'student_id' => $student3->id,
        'is_visible_to_student' => false,
    ]);

    expect($report_card1->is_visible_to_student)->toBeFalse()
        ->and($report_card2->is_visible_to_student)->toBeFalse()
        ->and($report_card3->is_visible_to_student)->toBeFalse();

    app()->make(ExamResultsPostingService::class)
        ->setResultsPostingHeader($header1)
        ->allowReportCardVisibleByPostingHeader();

    $report_card1->refresh();
    $report_card2->refresh();
    $report_card3->refresh();

    expect($report_card1->is_visible_to_student)->toBeTrue()
        ->and($report_card2->is_visible_to_student)->toBeTrue()
        ->and($report_card3->is_visible_to_student)->toBeFalse();


});

test('disallowReportCardVisibleByPostingHeader', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();
    $student3 = Student::factory()->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student1->id, $student2->id],
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student3->id],
    ]);

    $report_card1 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student1->id,
        'is_visible_to_student' => true,
    ]);

    $report_card2 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student2->id,
        'is_visible_to_student' => true,
    ]);

    $report_card3 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header2->id,
        'student_id' => $student3->id,
        'is_visible_to_student' => true,
    ]);

    expect($report_card1->is_visible_to_student)->toBeTrue()
        ->and($report_card2->is_visible_to_student)->toBeTrue()
        ->and($report_card3->is_visible_to_student)->toBeTrue();

    app()->make(ExamResultsPostingService::class)
        ->setResultsPostingHeader($header1)
        ->disallowReportCardVisibleByPostingHeader();

    $report_card1->refresh();
    $report_card2->refresh();
    $report_card3->refresh();

    expect($report_card1->is_visible_to_student)->toBeFalse()
        ->and($report_card2->is_visible_to_student)->toBeFalse()
        ->and($report_card3->is_visible_to_student)->toBeTrue();
});

test('allowReportCardVisibleByStudent', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();
    $student3 = Student::factory()->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student1->id, $student2->id],
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student3->id],
    ]);

    $report_card1 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student1->id,
        'is_visible_to_student' => false,
    ]);

    $report_card2 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student2->id,
        'is_visible_to_student' => false,
    ]);

    $report_card3 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header2->id,
        'student_id' => $student3->id,
        'is_visible_to_student' => false,
    ]);

    expect($report_card1->is_visible_to_student)->toBeFalse()
        ->and($report_card2->is_visible_to_student)->toBeFalse()
        ->and($report_card3->is_visible_to_student)->toBeFalse();

    app()->make(ExamResultsPostingService::class)
        ->setResultsPostingHeader($header1)
        ->allowReportCardVisibleByStudent($student2);

    $report_card1->refresh();
    $report_card2->refresh();
    $report_card3->refresh();

    expect($report_card1->is_visible_to_student)->toBeFalse()
        ->and($report_card2->is_visible_to_student)->toBeTrue()
        ->and($report_card3->is_visible_to_student)->toBeFalse();

});

test('disallowReportCardVisibleByStudent', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();
    $student3 = Student::factory()->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student1->id, $student2->id],
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$student3->id],
    ]);

    $report_card1 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student1->id,
        'is_visible_to_student' => true,
    ]);

    $report_card2 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header1->id,
        'student_id' => $student2->id,
        'is_visible_to_student' => true,
    ]);

    $report_card3 = StudentReportCard::factory()->create([
        'results_posting_header_id' => $header2->id,
        'student_id' => $student3->id,
        'is_visible_to_student' => true,
    ]);

    expect($report_card1->is_visible_to_student)->toBeTrue()
        ->and($report_card2->is_visible_to_student)->toBeTrue()
        ->and($report_card3->is_visible_to_student)->toBeTrue();

    app()->make(ExamResultsPostingService::class)
        ->setResultsPostingHeader($header1)
        ->disallowReportCardVisibleByStudent($student1);

    $report_card1->refresh();
    $report_card2->refresh();
    $report_card3->refresh();

    expect($report_card1->is_visible_to_student)->toBeFalse()
        ->and($report_card2->is_visible_to_student)->toBeTrue()
        ->and($report_card3->is_visible_to_student)->toBeTrue();
});


test('resetCurrentPostingToHeader', function () {

    $header1 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);
    $header2 = ResultsPostingHeader::factory()->create([
        'grade_id' => $this->grade1->id,
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => ResultsPostingHeader::STATUS_PENDING,
    ]);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $this->semester_class1->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $this->semester_class1->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $this->semester_class1->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $this->semester_class1->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $this->semester_class1->id,
            'subject_id' => $this->subjects[4]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $this->student1->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $this->student1->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $this->student1->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $this->student1->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $this->student1->id,
            'class_subject_id' => $class_subjects[4]->id
        ],
    ))->create();
    
    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student1)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source = ResultSource::where('code', 'SEM1EXAM')->first();
    $report_card_output = $sgf->outputs()->where('code', 'SEM1RESULT')->first();

    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);

    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    ResultsPostingLineItemHistory::factory()->create([
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);

    // assume current posting is header2. we wanna restore header2 to header1
    ResultsPostingLineItem::factory()->create([
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 1);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 4);

    app()->make(ExamResultsPostingService::class)
        ->setResultsPostingHeader($header2)
        ->resetCurrentPostingToHeader($header1);

    $this->assertDatabaseCount(ResultsPostingLineItem::class, 4);
    $this->assertDatabaseCount(ResultsPostingLineItemHistory::class, 1);

    $this->assertDatabaseHas(ResultsPostingLineItemHistory::class, [
        'header_id' => $header2->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 101
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[0]->id,
        'report_card_output_component_code' => $report_card_output->components[0]->code,
        'total' => 40
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[1]->id,
        'report_card_output_component_code' => $report_card_output->components[1]->code,
        'total' => 75
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[2]->id,
        'report_card_output_component_code' => $report_card_output->components[2]->code,
        'label' => 'B+'
    ]);
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $header1->id,
        'student_id' => $this->student1->id,
        'report_card_output_component_id' => $report_card_output->components[3]->id,
        'report_card_output_component_code' => $report_card_output->components[3]->code,
        'total' => 100
    ]);
});
