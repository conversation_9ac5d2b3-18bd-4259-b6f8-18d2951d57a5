<?php

use App\Jobs\ApplyGradingFrameworkJob;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Services\Exam\GradingFrameworkService;
use App\Services\Exam\Output\GeneralReportCardOutputService;
use App\Services\Exam\Output\PinHwaReportCardOutputV1Service;
use App\Services\Exam\StudentGradingFrameworkService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Mockery\MockInterface;

beforeEach(function () {
    Cache::clear();

    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
    $this->gradingFrameworkService = app(GradingFrameworkService::class);

    $this->gradingFrameworkTable = resolve(GradingFramework::class)->getTable();
    $this->studentGradingFrameworkTable = resolve(StudentGradingFramework::class)->getTable();

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();

    $this->config = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true);

});

test('getAllGradingFrameworks()', function (int $expected_count, array $filters, array $expected_model) {
    $keys = [
        'first' => GradingFramework::factory()->create([
            'code' => 'J001',
            'name->en' => 'J1DEFAULT',
            'name->zh' => 'J1号码',
            'is_active' => true
        ]),
        'second' => GradingFramework::factory()->create([
            'code' => 'J002',
            'name->en' => 'J2DEFAULT',
            'name->zh' => 'J2号码',
            'is_active' => true
        ]),
        'third' => GradingFramework::factory()->create([
            'code' => 'J003',
            'name->en' => 'J3DEFAULT',
            'name->zh' => 'J3号码',
            'is_active' => false
        ]),
    ];

    $result = $this->gradingFrameworkService->getAllGradingFrameworks($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }

})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = J1DEFAULT' => [1, ['name' => 'J1DEFAULT'], ['first']],
    'filter by code = J001' => [1, ['code' => 'J001'], ['first']],
    'filter by is_active = TRUE' => [2, ['is_active' => true], ['first', 'second']],
]);


test('getAllPaginatedGradingFrameworks()', function () {

    $keys = [
        'first' => GradingFramework::factory()->create([
            'code' => 'J001',
            'name->en' => 'J1DEFAULT',
            'name->zh' => 'J1号码',
            'is_active' => true
        ]),
        'second' => GradingFramework::factory()->create([
            'code' => 'J002',
            'name->en' => 'J2DEFAULT',
            'name->zh' => 'J2号码',
            'is_active' => true
        ]),
        'third' => GradingFramework::factory()->create([
            'code' => 'J003',
            'name->en' => 'J3DEFAULT',
            'name->zh' => 'J3号码',
            'is_active' => false
        ]),
    ];

    // Checking return type, as get logic is the same as getAllGradingFrameworks()
    $result = $this->gradingFrameworkService->getAllPaginatedGradingFrameworks([]);
    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);
});

test('createGradingFramework()', function () {
    // store success
    $this->assertDatabaseCount($this->gradingFrameworkTable, 0);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => false,
    ];

    $this->gradingFrameworkService
        ->setGradingFrameworkData($payload)
        ->createGradingFramework();

    $this->assertDatabaseCount($this->gradingFrameworkTable, 1);

    $this->assertDatabaseHas($this->gradingFrameworkTable, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'is_active' => $payload['is_active'],
        'configuration' => null
    ]);

});

test('createGradingFramework() - validation testing', function () {
    // Complete validation testing is done under studentGradingFrameworkTest
    // This test is to test exception throwing behaviour for validation

    $temp_config = $this->config;
    $temp_config['result_sources'] = [[]];
    $temp_config['output'] = [[]];

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J2DEFAULT',
        'is_active' => true,
        'configuration' => $temp_config
    ];

    $has_error = false;
    try {

        $this->gradingFrameworkService
            ->setGradingFrameworkData($payload)
            ->createGradingFramework();

    } catch (ValidationException $e) {
        $has_error = true;

        $errors = Arr::flatten($e->errors());
        expect($errors)
            ->toContain(
                'The result_sources.0.code field is required.',
                'The result_sources.0.name field is required.',
                'The result_sources.0.subjects field is required.',
                'The result_sources.0.exams field is required.',
                'The output.0.code field is required.',
                'The output.0.name field is required.',
                'The output.0.name.en field is required.',
                'The output.0.service field is required.',
                'The output.0.is_final field is required.',
                'The output.0.components field is required.'
            );
    }
    expect($has_error)->toBeTrue();
    $this->assertDatabaseCount($this->gradingFrameworkTable, 0);
});

test('updateGradingFramework() - is_active = true', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => null,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory(2)->state(new Sequence(
        [
            'student_id' => $student_1->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ],
        [
            'student_id' => $student_2->id,
            'semester_class_id' => $semester_class->id,
            'is_active' => true
        ]
    ))->create();

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(8)->state(new Sequence(
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    // existing student grading framework
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'effective_from' => '2024-02-02',
        'effective_to' => '2024-06-30'
    ]);

    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'effective_from' => '2024-03-02',
        'effective_to' => '2024-07-30',
        'configuration' => [
            'some_json' => 123,
            'other_json' => 'test',
        ]
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config
    ];

    $result = $this->gradingFrameworkService
        ->setGradingFrameworkData($payload)
        ->replaceExistingStudentGradingFramework(true)
        ->updateGradingFramework($grading_framework)->toArray();

    expect($result)->toMatchArray([
        'id' => $grading_framework->id,
        'name' => $payload['name'],
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $this->assertDatabaseCount($this->gradingFrameworkTable, 1);
    $this->assertDatabaseHas($this->gradingFrameworkTable, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'is_active' => $payload['is_active'],
        'configuration' => json_encode($this->config)
    ]);

    // check if all related ACTIVE student grading framework are updated
    // only update if student grading framework is active and grading framework payload is active
    $this->assertDatabaseCount($this->studentGradingFrameworkTable, 2);

    // Student is only taking 01, 02, 03, 04 subjects and not 70, so we check against the mapped configuration
    $sgf1 = StudentGradingFramework::with('gradingFramework')->where('id', $sgf1->id)->first();
    $sgf1_result = $sgf1->toArray();
    $sgf1_config = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student_1)
        ->setGradingFramework($sgf1->gradingFramework)
        ->setStudentGradingFramework($sgf1)
        ->mapConfigToValidSubjects();

    // sgf1 is active, hence expected to update
    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1_config
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($sgf1_config)
    ]);

    // sgf2 is not active, hence will not update
    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);

});

test('updateGradingFramework() - invalid relational update', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => null,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    // existing student grading framework
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
    ]);

    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => [
            'some_json' => 123,
            'other_json' => 'test',
        ]
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ];

    $result = $this->gradingFrameworkService
        ->setGradingFrameworkData($payload)
        ->updateGradingFramework($grading_framework)->toArray();

    expect($result)->toMatchArray([
        'id' => $grading_framework->id,
        'name' => $payload['name'],
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config,
    ]);

    $this->assertDatabaseCount($this->gradingFrameworkTable, 1);
    $this->assertDatabaseHas($this->gradingFrameworkTable, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'is_active' => $payload['is_active'],
        'configuration' => json_encode($this->config)
    ]);

    // is_active is set to false during grading framework update, all related student grading framework should NOT be updated
    $this->assertDatabaseCount($this->studentGradingFrameworkTable, 2);

    $sgf1_result = StudentGradingFramework::find($sgf1->id)->toArray();
    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1->configuration
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => "[]"
    ]);

    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config
    ];

    $this->gradingFrameworkService
        ->setGradingFrameworkData($payload)
        ->updateGradingFramework($grading_framework)->toArray();

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);

    // is_active = true, but replaceExistingStudentGradingFramework is false, so no updates to related student grading frameworks
    $this->assertDatabaseCount($this->studentGradingFrameworkTable, 2);

    $sgf1_result = StudentGradingFramework::find($sgf1->id)->toArray();
    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1->configuration
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => "[]"
    ]);

    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);
});

test('applyGradingFramework()', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $student_1 = Student::factory()->create();
    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student_1->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    $payload = [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-02-02',
        'effective_to' => '2024-06-30'
    ];

    $this->gradingFrameworkService->applyStudentGradingFramework($payload);

    // Student is only taking 01, 02, 03, 04 subjects and not 70, so we check against the mapped configuration
    $sgf = StudentGradingFramework::with('gradingFramework')->first();
    $config_output = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student_1)
        ->setGradingFramework($sgf->gradingFramework)
        ->setStudentGradingFramework($sgf)
        ->mapConfigToValidSubjects();

    $sgf1 = StudentGradingFramework::where('student_id', $student_1->id)->get()->toArray();
    expect($sgf1[0])->toMatchArray([
        'configuration' => $config_output,
        'is_active' => true,
        'effective_from' => $payload['effective_from'],
        'effective_to' => $payload['effective_to']
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($config_output)
    ]);
});

test('bulkApplyStudentGradingFrameworkByClass()', function () {
    // Testing dispatch to queue behaviour
    // For apply behaviour, check studentGradingFrameworkTest

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $semester_setting = SemesterSetting::factory()->create(['is_current_semester' => true]);

    $semester_class1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);
    $semester_class2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);
    $semester_class3 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);

    $students = Student::factory(4)->create();

    $student_class = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'student_id' => $students[3]->id,
        ]
    ))->create();

    $payload = [
        'semester_class_ids' => [$semester_class1->id, $semester_class2->id],
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-02-02',
        'effective_to' => '2024-06-30'
    ];
    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    Queue::fake();
    $this->gradingFrameworkService->bulkApplyStudentGradingFrameworkByClass($payload);

    Queue::assertPushed(ApplyGradingFrameworkJob::class, 3);

    // Student 0
    Queue::assertPushed(ApplyGradingFrameworkJob::class, function ($job) use ($students, $grading_framework, $payload) {
        $valid_student = $job->student->id === $students[0]->id;
        $valid_grading_framework = $job->gradingFramework->id === $grading_framework->id;
        $valid_effective_from = $job->input['effective_from'] === $payload['effective_from'];
        $valid_effective_to = $job->input['effective_to'] === $payload['effective_to'];

        return $valid_student && $valid_grading_framework && $valid_effective_from && $valid_effective_to;
    });

    // Student 1
    Queue::assertPushed(ApplyGradingFrameworkJob::class, function ($job) use ($students, $grading_framework, $payload) {
        $valid_student = $job->student->id === $students[1]->id;
        $valid_grading_framework = $job->gradingFramework->id === $grading_framework->id;
        $valid_effective_from = $job->input['effective_from'] === $payload['effective_from'];
        $valid_effective_to = $job->input['effective_to'] === $payload['effective_to'];

        return $valid_student && $valid_grading_framework && $valid_effective_from && $valid_effective_to;
    });

    // Student 2
    Queue::assertPushed(ApplyGradingFrameworkJob::class, function ($job) use ($students, $grading_framework, $payload) {
        $valid_student = $job->student->id === $students[2]->id;
        $valid_grading_framework = $job->gradingFramework->id === $grading_framework->id;
        $valid_effective_from = $job->input['effective_from'] === $payload['effective_from'];
        $valid_effective_to = $job->input['effective_to'] === $payload['effective_to'];

        return $valid_student && $valid_grading_framework && $valid_effective_from && $valid_effective_to;
    });

    /* Tentatively commenting out this code
    $this->assertDatabaseCount($this->studentGradingFrameworkTable, 3);

    $config_output = $this->config;
    $config_output['effective_from'] = '2024-02-02';
    $config_output['effective_to'] = '2024-06-30';
    $config_output['is_active'] = true;


    $sgf1 = StudentGradingFramework::where('student_id', $students[0]->id)->get()->toArray();
    expect($sgf1[0])->toMatchArray([
        'configuration' => $config_output
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $students[0]->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($config_output)
    ]);

    $sgf2 = StudentGradingFramework::where('student_id', $students[1]->id)->get()->toArray();
    expect($sgf2[0])->toMatchArray([
        'configuration' => $config_output
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $students[1]->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($config_output)
    ]);

    $sgf3 = StudentGradingFramework::where('student_id', $students[2]->id)->get()->toArray();
    expect($sgf2[0])->toMatchArray([
        'configuration' => $config_output
    ]);

    $this->assertDatabaseHas($this->studentGradingFrameworkTable, [
        'student_id' => $students[2]->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($config_output)
    ]);*/
});


test('getAllFixedFormulas', function () {
    $response = $this->gradingFrameworkService->getAllFixedFormulas();
    $service = resolve(PinHwaReportCardOutputV1Service::class);

    expect($response)->toHaveCount(count($service::FORMULA));

    foreach ($service::FORMULA as $key => $value) {
        expect($response)->toContain(
            [
                'label' => $key,
                'value' => $value['value'],
                'args' => isset($value['args']) ? $value['args'] : []
            ]
        );
    }
});

test('getAllResultSourceFormulas - no filters', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework);

    // 3 result source code * 5 subjects * 4 keywords = 60
    expect($response)->toHaveCount(60);

    $result_sources = $this->config['result_sources'];

    foreach ($result_sources as $result_source) {

        $code = $result_source['code'];
        $subjects = $result_source['subjects'];

        foreach ($subjects as $subject) {
            $subject_code = $subject['code'];

            foreach (array_keys(GeneralReportCardOutputService::TARGET_KEYWORDS) as $keyword) {
                expect($response)->toContain(
                    [
                        'label' => $code . '.' . $subject_code . '.' . $keyword,
                        'value' => "VAR(\"RESULTSOURCE[" . $code . "].SUBJECT[" . $subject_code . "].$keyword\")",
                    ]
                );
            }
        }
    }
});

test('getAllResultSourceFormulas - with filters', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $payload = [
        'result_source_code' => 'SEM1EXAM',
        'subject_code' => '70'
    ];

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework, $payload);

    // 1 result source code * 1 subjects * 4 keywords = 4
    expect($response)->toHaveCount(4);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1EXAM.70.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.70.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHTEDSCORE\")",
        ],
    ]);

    // filter result source code only
    $payload = [
        'result_source_code' => 'SEM1EXAM',
    ];

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework, $payload);

    // 1 result source code * 5 subjects * 4 keywords = 4
    expect($response)->toHaveCount(20);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1EXAM.01.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[01].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.01.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[01].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.01.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[01].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.01.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[01].WEIGHTEDSCORE\")",
        ],
        [
            'label' => "SEM1EXAM.02.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[02].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.02.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[02].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.02.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[02].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.02.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[02].WEIGHTEDSCORE\")",
        ],
        [
            'label' => "SEM1EXAM.03.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[03].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.03.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[03].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.03.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[03].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.03.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[03].WEIGHTEDSCORE\")",
        ],
        [
            'label' => "SEM1EXAM.04.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[04].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.04.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[04].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.04.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[04].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.04.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[04].WEIGHTEDSCORE\")",
        ],
        [
            'label' => "SEM1EXAM.70.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.70.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHTEDSCORE\")",
        ],
    ]);

    // filter subject code only
    $payload = [
        'subject_code' => '70',
    ];

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework, $payload);

    // 3 result source code * 1 subjects * 4 keywords = 4
    expect($response)->toHaveCount(12);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1EXAM.70.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].SCORE\")",
        ],
        [
            'label' => "SEM1EXAM.70.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].GRADE\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHT\")",
        ],
        [
            'label' => "SEM1EXAM.70.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHTEDSCORE\")",
        ],
        [
            'label' => "SEM2EXAM.70.SCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM2EXAM].SUBJECT[70].SCORE\")"
        ],
        [
            'label' => "SEM2EXAM.70.GRADE",
            'value' => "VAR(\"RESULTSOURCE[SEM2EXAM].SUBJECT[70].GRADE\")"
        ],
        [
            'label' => "SEM2EXAM.70.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[SEM2EXAM].SUBJECT[70].WEIGHT\")"
        ],
        [
            'label' => "SEM2EXAM.70.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[SEM2EXAM].SUBJECT[70].WEIGHTEDSCORE\")"
        ], [
            'label' => "FINALEXAM.70.SCORE",
            'value' => "VAR(\"RESULTSOURCE[FINALEXAM].SUBJECT[70].SCORE\")"
        ],
        [
            'label' => "FINALEXAM.70.GRADE",
            'value' => "VAR(\"RESULTSOURCE[FINALEXAM].SUBJECT[70].GRADE\")"
        ],
        [
            'label' => "FINALEXAM.70.WEIGHT",
            'value' => "VAR(\"RESULTSOURCE[FINALEXAM].SUBJECT[70].WEIGHT\")"
        ],
        [
            'label' => "FINALEXAM.70.WEIGHTEDSCORE",
            'value' => "VAR(\"RESULTSOURCE[FINALEXAM].SUBJECT[70].WEIGHTEDSCORE\")"
        ],
    ]);
});

test('getAllResultSourceFormulas - missing output/component', function () {

    $temp_config = $this->config;
    $temp_config['result_sources'] = [];

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $temp_config,
    ]);

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework);
    expect($response)->toBeInstanceOf(Collection::class)
        ->toBeEmpty();

    $temp_config = $this->config;
    foreach ($temp_config['result_sources'] as &$rs) {
        $rs['subjects'] = [];
    };

    $grading_framework->configuration = $temp_config;

    $response = $this->gradingFrameworkService->getAllResultSourceFormulas($grading_framework);
    expect($response)->toBeInstanceOf(Collection::class)
        ->toBeEmpty();

});

test('getAllOutputFormulas - no filters', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework);

    // 3 output codes * 12 component * 3 keywords = 108
    expect($response)->toHaveCount(108);

    $config = $grading_framework->configuration;
    $report_outputs = $config['output'];

    foreach ($report_outputs as $output) {

        $code = $output['code'];
        $components = $output['components'];

        foreach ($components as $component) {
            $component_code = $component['code'];

            foreach (array_keys(GeneralReportCardOutputService::OUTPUT_TARGET_KEYWORDS) as $keyword) {

                expect($response)->toContain(
                    [
                        'label' => $code . '.' . $component_code . '.' . $keyword,
                        'value' => "VAR(\"OUTPUT[" . $code . "].COMPONENT[" . $component_code . "].$keyword\")"
                    ]
                );
            }
        }
    }
});

test('getAllOutputFormulas - with filters', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $payload = [
        'output_code' => 'SEM1RESULT',
        'component_code' => 'GT',
    ];

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework, $payload);

    // 1 output codes * 1 component * 3 keywords = 3 response
    expect($response)->toHaveCount(3);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1RESULT.GT.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL_GRADE\")"
        ]
    ]);

    $payload = [
        'component_code' => 'GT',
    ];

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework, $payload);

    // 3 output codes * 1 component * 3 keywords = 9 responses
    expect($response)->toHaveCount(9);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1RESULT.GT.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM2RESULT.GT.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM2RESULT].COMPONENT[GT].TOTAL\")"
        ],
        [
            'label' => "SEM2RESULT.GT.LABEL",
            'value' => "VAR(\"OUTPUT[SEM2RESULT].COMPONENT[GT].LABEL\")"
        ],
        [
            'label' => "SEM2RESULT.GT.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM2RESULT].COMPONENT[GT].TOTAL_GRADE\")"
        ],
        [
            'label' => "FINALRESULT.GT.TOTAL",
            'value' => "VAR(\"OUTPUT[FINALRESULT].COMPONENT[GT].TOTAL\")"
        ],
        [
            'label' => "FINALRESULT.GT.LABEL",
            'value' => "VAR(\"OUTPUT[FINALRESULT].COMPONENT[GT].LABEL\")"
        ],
        [
            'label' => "FINALRESULT.GT.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[FINALRESULT].COMPONENT[GT].TOTAL_GRADE\")"
        ]
    ]);

    $payload = [
        'output_code' => 'SEM1RESULT',
    ];

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework, $payload);

    // 1 output codes * 12 component * 3 keywords = 36 responses
    expect($response)->toHaveCount(36);
    expect($response->toArray())->toBe([
        [
            'label' => "SEM1RESULT.01.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[01].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.01.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[01].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.01.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[01].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.02.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[02].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.02.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[02].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.02.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[02].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.03.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[03].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.03.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[03].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.03.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[03].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.04.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[04].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.04.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[04].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.04.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[04].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.70.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[70].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.70.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[70].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.70.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[70].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.GT.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.GT.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.GW.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GW].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.GW.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GW].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.GW.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GW].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.GA.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GA].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.GA.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GA].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.GA.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GA].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.MA.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MA].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.MA.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MA].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.MA.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MA].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.MS.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MS].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.MS.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MS].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.MS.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[MS].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_NET_AVG.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_NET_AVG].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_NET_AVG.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_NET_AVG].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_NET_AVG.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_NET_AVG].TOTAL_GRADE\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_GRADE.TOTAL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_GRADE].TOTAL\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_GRADE.LABEL",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_GRADE].LABEL\")"
        ],
        [
            'label' => "SEM1RESULT.SYS_GRADE.TOTAL_GRADE",
            'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[SYS_GRADE].TOTAL_GRADE\")"
        ],
    ]);
});

test('getAllOutputFormulas - missing output/component', function () {

    $temp_config = $this->config;
    $temp_config['output'] = [];

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $temp_config,
    ]);

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework);
    expect($response)->toBeInstanceOf(Collection::class)
        ->toBeEmpty();

    $temp_config = $this->config;
    foreach ($temp_config['output'] as &$out) {
        $out['components'] = [];
    };

    $grading_framework->configuration = $temp_config;

    $response = $this->gradingFrameworkService->getAllOutputFormulas($grading_framework);
    expect($response)->toBeInstanceOf(Collection::class)
        ->toBeEmpty();

});
