<?php

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Services\Exam\ExamResultsDataEntryService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function() {

    Cache::clear();

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM', 'results_entry_period_from' => '2024-11-20 16:00:00', 'results_entry_period_to' => '2024-11-30 15:59:59'],
        ['code' => 'SEM2EXAM', 'results_entry_period_from' => '2024-11-01 16:00:00', 'results_entry_period_to' => '2024-11-21 15:59:59'],
        ['code' => 'FINALEXAM', 'results_entry_period_from' => '2024-12-01 16:00:00', 'results_entry_period_to' => '2024-12-30 15:59:59'],
    ))->create();

});

test('getStudentGradingFramework', function () {

    $employee = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);

    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);
    $student3 = Student::factory()->create([]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' =>$first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student3->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2, $student3];
    foreach($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }



    expect($first_semester_class->students)->toHaveCount(3);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $other_sgf_2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $exam = $this->exams[0];

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($exam)
        ->setEmployee($employee)
        ->getStudentGradingFramework();

    // entire class gets cached
    expect($data)->toBeInstanceOf(\App\Models\StudentGradingFramework::class)
        ->and($data->id)->toBe($sgf->id)->not()->toBe($other_sgf->id)
        ->and(Cache::has('active-student-grading-framework-' . $student->id))->toBeTrue()
        ->and(Cache::has('active-student-grading-framework-' . $student2->id))->toBeTrue()
        ->and(Cache::has('active-student-grading-framework-' . $student3->id))->toBeTrue()
        ->and(Cache::get('active-student-grading-framework-' . $student->id))->toBeInstanceOf(\App\Models\StudentGradingFramework::class)
        ->and(Cache::get('active-student-grading-framework-' . $student2->id))->toBeInstanceOf(\App\Models\StudentGradingFramework::class)
        ->and(Cache::get('active-student-grading-framework-' . $student3->id))->toBeInstanceOf(\App\Models\StudentGradingFramework::class);

});


test('getResultSource', function () {

    $employee = Employee::factory()->create([]);

    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);
    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2];
    foreach ($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->getResultSource();

    expect($data->code)
        ->toBe('SEM1EXAM')
        ->and($data->student_grading_framework_id)->toBe($sgf->id);

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[2])
        ->setEmployee($employee)
        ->getResultSource();

    expect($data->code)
        ->toBe('FINALEXAM')
        ->and($data->student_grading_framework_id)->toBe($sgf->id);

});


test('determineResultSourceSubject', function () {

    $employee = Employee::factory()->create([]);

    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);
    StudentClass::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);
    StudentClass::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2];
    foreach ($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[0])
        ->determineResultSourceSubject(ResultSource::where('student_grading_framework_id', $sgf->id)->first());

    expect($data->subject_id)
        ->toBe($this->subjects[0]->id)
        ->and($data->resultSource->studentGradingFramework->id)->toBe($sgf->id);


    $data = app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[1])
        ->determineResultSourceSubject(ResultSource::where('student_grading_framework_id', $sgf->id)->first());

    expect($data->subject_id)
        ->toBe($this->subjects[1]->id)
        ->and($data->resultSource->studentGradingFramework->id)->toBe($sgf->id);

});



test('save', function () {

    $employee = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);

    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' =>$first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2];
    foreach ($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    //score - 1 component
    app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[0])
        ->setScore(56.66)
        ->setSubjectComponentCode('FINAL')
        ->save();

    $component = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first()
        ->components()->where('code', 'FINAL')->first();

    expect($component->actual_score)->toEqual(56.66)
        ->and($component->actual_score_grade)->toBeNull()
        ->and($component->data_entry_employee_id)->toBe($employee->id)
        ->and($component->data_entry_at)->not()->toBeNull();

    // score - multi component
    app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[3])
        ->setScore(89.93)
        ->setSubjectComponentCode('HOMEWORK')
        ->save();

    app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[3])
        ->setScore(65.1)
        ->setSubjectComponentCode('FINAL')
        ->save();

    // component 1
    $component = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[3]->id)->first()
        ->components()->where('code', 'HOMEWORK')->first();

    expect($component->actual_score)->toEqual(89.93)
        ->and($component->actual_score_grade)->toBeNull()
        ->and($component->data_entry_employee_id)->toBe($employee->id)
        ->and($component->data_entry_at)->not()->toBeNull();

    // component 2
    $component = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[3]->id)->first()
        ->components()->where('code', 'FINAL')->first();

    expect($component->actual_score)->toEqual(65.1)
        ->and($component->actual_score_grade)->toBeNull()
        ->and($component->data_entry_employee_id)->toBe($employee->id)
        ->and($component->data_entry_at)->not()->toBeNull();

    // grade
    app()->make(ExamResultsDataEntryService::class)
        ->setStudent($student)
        ->setExam($this->exams[0])
        ->setEmployee($employee)
        ->setSubject($this->subjects[2])
        ->setGrade('A-')
        ->save();

    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[2]->id)->first();

    expect($result_source_subject->actual_score_grade)->toBe('A-')
        ->and($result_source_subject->actual_score)->toBeNull()
        ->and($result_source_subject->data_entry_at)->not()->toBeNull()
        ->and($result_source_subject->data_entry_employee_id)->toBe($employee->id);

});


test('saveUsingResultSourceSubject', function () {

    $employee = Employee::factory()->create([]);
    $student = Student::factory()->create([]);
    $student2 = Student::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);


    StudentClass::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $student->id,
    ]);

    StudentClass::factory()->create([
        'semester_class_id' =>$first_semester_class->id,
        'student_id' => $student2->id,
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $first_semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    $students = [$student, $student2];
    foreach ($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ]
        ))->create();
    }

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework (create multiple)
    $other_sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first();

    $result_source_subject_component = $result_source_subject->components()->where('code', 'FINAL')->first();

    //score - 1 component
    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($result_source_subject)
        ->setResultSourceSubjectComponent($result_source_subject_component)
        ->setScore(56.66)
        ->setSubjectComponentCode('FINAL')
        ->saveUsingResultSourceSubject();

    $result_source_subject_component->refresh();

    expect($result_source_subject_component->actual_score)->toEqual(56.66)
        ->and($result_source_subject_component->actual_score_grade)->toBeNull()
        ->and($result_source_subject_component->data_entry_employee_id)->toBe($employee->id)
        ->and($result_source_subject_component->data_entry_at)->not()->toBeNull();

    // score - multi component
    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[3]->id)->first();

    $result_source_subject_component1 = $result_source_subject->components()->where('code', 'HOMEWORK')->first();
    $result_source_subject_component2 = $result_source_subject->components()->where('code', 'FINAL')->first();

    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($result_source_subject)
        ->setResultSourceSubjectComponent($result_source_subject_component1)
        ->setScore(89.93)
        ->saveUsingResultSourceSubject();

    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($result_source_subject)
        ->setResultSourceSubjectComponent($result_source_subject_component2)
        ->setScore(65.1)
        ->saveUsingResultSourceSubject();

    $result_source_subject_component1->refresh();
    $result_source_subject_component2->refresh();

    // component 1
    expect($result_source_subject_component1->actual_score)->toEqual(89.93)
        ->and($result_source_subject_component1->actual_score_grade)->toBeNull()
        ->and($result_source_subject_component1->data_entry_employee_id)->toBe($employee->id)
        ->and($result_source_subject_component1->data_entry_at)->not()->toBeNull();

    // component 2
    expect($result_source_subject_component2->actual_score)->toEqual(65.1)
        ->and($result_source_subject_component2->actual_score_grade)->toBeNull()
        ->and($result_source_subject_component2->data_entry_employee_id)->toBe($employee->id)
        ->and($result_source_subject_component2->data_entry_at)->not()->toBeNull();

    // grade
    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[2]->id)->first();

    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($result_source_subject)
        ->setGrade('A-')
        ->saveUsingResultSourceSubject();

    $result_source_subject->refresh();

    expect($result_source_subject->actual_score_grade)->toBe('A-')
        ->and($result_source_subject->actual_score)->toBeNull()
        ->and($result_source_subject->data_entry_at)->not()->toBeNull()
        ->and($result_source_subject->data_entry_employee_id)->toBe($employee->id);

    // unable to do data entry if Result Source subject is Posted
    $result_source_subject = $sgf->resultSources()->where('code', $this->exams[0]->code)->first()
        ->subjects()->where('subject_id', $this->subjects[0]->id)->first();

    $result_source_subject->data_entry_status = ResultSourceSubject::DATA_ENTRY_STATUS_POSTED;
    $result_source_subject->save();

    $result_source_subject_component = $result_source_subject->components()->where('code', 'FINAL')->first();

    $this->expectExceptionMessage('Unable to perform data entry as data status is Posted for subject ' . $this->subjects[0]->name);

    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($result_source_subject)
        ->setResultSourceSubjectComponent($result_source_subject_component)
        ->setScore(56.66)
        ->setSubjectComponentCode('FINAL')
        ->saveUsingResultSourceSubject();

});

test('getEligibleExams', function() {

    Carbon::setTestNow('2024-11-20 16:00:00');

    $data = app()->make(ExamResultsDataEntryService::class)
        ->getEligibleExamsForDataEntry();

    expect($data)->toHaveCount(2)
        ->and($data->pluck('code'))->toContain('SEM1EXAM', 'SEM2EXAM');

    Carbon::setTestNow('2024-12-06 16:00:00');

    $data = app()->make(ExamResultsDataEntryService::class)
        ->getEligibleExamsForDataEntry();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('code'))->toContain('FINALEXAM');

});


test('getEligibleSubjectsForTeacher', function() {

    $employee = Employee::factory()->create([]);
    $employee2 = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);

    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->getEligibleSubjectsForTeacher();

    expect($data)->toHaveCount(2)
        ->and($data->pluck('id'))->toContain($this->subjects[0]->id, $this->subjects[1]->id);


    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee2)
        ->getEligibleSubjectsForTeacher();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('id'))->toContain($this->subjects[2]->id);

});


test('getEligibleSubjectsForTeacherAndSubject', function() {

    $employee = Employee::factory()->create([]);
    $employee2 = Employee::factory()->create([]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $second_class = ClassModel::factory()->create([
        'name->en' => 'Class 2',
    ]);
    $third_class = ClassModel::factory()->create([
        'name->en' => 'Class 3',
    ]);


    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);
    $first_semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $second_class->id
    ]);
    $first_semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $third_class->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);
    $class_subject5 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject6 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject7 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class3->id,
        'subject_id' => $this->subjects[0]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject5->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject6->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject7->id
    ]);

    // employee 1, subject 0
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setSubject($this->subjects[0])
        ->getEligibleClassesForTeacherAndSubject();

    expect($data)->toHaveCount(2)
        ->and($data->pluck('id'))->toContain($first_class->id, $second_class->id);


    // employee 1, subject 1
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setSubject($this->subjects[1])
        ->getEligibleClassesForTeacherAndSubject();

    expect($data)->toHaveCount(2)
        ->and($data->pluck('id'))->toContain($first_class->id, $second_class->id);


    // employee 2, subject 0
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee2)
        ->setSubject($this->subjects[0])
        ->getEligibleClassesForTeacherAndSubject();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('id'))->toContain($third_class->id);

    // employee 2, subject 2
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee2)
        ->setSubject($this->subjects[2])
        ->getEligibleClassesForTeacherAndSubject();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('id'))->toContain($first_class->id);


    // employee 2, subject 3
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee2)
        ->setSubject($this->subjects[3])
        ->getEligibleClassesForTeacherAndSubject();

    expect($data)->toHaveCount(0);


});



test('getEligibleStudentsAndScores', function() {

    $employee = Employee::factory()->create([]);
    $employee2 = Employee::factory()->create([]);

    /**
     *
     * Class 1
     *
     * - subject 0 (Employee)
     *   - student 1
     *   - student 2
     * - subject 1 (Employee)
     *   - student 1
     * - subject 2 (Employee2)
     *   - student 1
     *
     * Class 2
     *
     * - subject 0 (Employee)
     *   - student 3
     * - subject 1 (Employee)
     *   - student 3
     *
     * Class 3
     *
     * - subject 0 (Employee2)
 */

    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);
    $second_class = ClassModel::factory()->create([
        'name->en' => 'Class 2',
    ]);
    $third_class = ClassModel::factory()->create([
        'name->en' => 'Class 3',
    ]);


    Carbon::setTestNow('2024-11-21');

    $old_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-30',
    ]);
    $first_semester = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id
    ]);
    $old_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $old_semester->id,
        'class_id' => $first_class->id
    ]);
    $first_semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $second_class->id
    ]);
    $first_semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $third_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id
    ]);

    StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $first_semester_class2->id
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject3 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class->id,
        'subject_id' => $this->subjects[2]->id,
    ]);
    $class_subject4 = ClassSubject::factory()->create([
        'semester_class_id' => $old_semester_class->id,
        'subject_id' => $this->subjects[3]->id,
    ]);
    $class_subject5 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[0]->id,
    ]);
    $class_subject6 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class2->id,
        'subject_id' => $this->subjects[1]->id,
    ]);
    $class_subject7 = ClassSubject::factory()->create([
        'semester_class_id' => $first_semester_class3->id,
        'subject_id' => $this->subjects[0]->id,
    ]);

    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject2->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject3->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject4->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject5->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee->id,
        'class_subject_id' => $class_subject6->id
    ]);
    ClassSubjectTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'class_subject_id' => $class_subject7->id
    ]);

   ClassSubjectStudent::factory()->create([
       'class_subject_id' => $class_subject->id,
       'student_id' => $student->id,
   ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject2->id,
        'student_id' => $student->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject3->id,
        'student_id' => $student->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject->id,
        'student_id' => $student2->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject5->id,
        'student_id' => $student3->id,
    ]);
    ClassSubjectStudent::factory()->create([
        'class_subject_id' => $class_subject6->id,
        'student_id' => $student3->id,
    ]);

    // setup student's grading framework
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();


    // employee 1, subject 0, class 1
    // Update score for 1 component to assume data was entered before
    $sgf1->resultSources()->where('code', $this->exams[0]->code)->first()->subjects()
        ->where('subject_id', $this->subjects[0]->id)->first()->components()->where('code', 'FINAL')
        ->update(['actual_score' => 99.65]);

    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setSubject($this->subjects[0])
        ->setClass($first_class)
        ->setExam($this->exams[0])
        ->getEligibleStudentsAndScores();

    expect($data)->toHaveCount(2)
        ->and($data->pluck('student_id'))->toContain($student->id, $student2->id)
        ->and($data->toArray()[0])->toMatchArray([
            'student_id' => $student->id,
            'student_number' => $student->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf1->id,
        ])
        ->and($data->toArray()[0]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => 99.65,      // previously entered score pre-populated
            'component_weightage_percent' => 100,
        ])
        ->and($data->toArray()[1])->toMatchArray([
            'student_id' => $student2->id,
            'student_number' => $student2->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf2->id,
        ])
        ->and($data->toArray()[1]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => null,
            'component_weightage_percent' => 100,
        ]);

    // employee 1, subject 1, class 1
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setSubject($this->subjects[1])
        ->setClass($first_class)
        ->setExam($this->exams[0])
        ->getEligibleStudentsAndScores();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('student_id'))->toContain($student->id)
        ->and($data->toArray()[0])->toMatchArray([
            'student_id' => $student->id,
            'student_number' => $student->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf1->id,
        ])
        ->and($data->toArray()[0]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => null,
            'component_weightage_percent' => 100,
        ]);

    // employee 1, subject 1, class 2
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setSubject($this->subjects[1])
        ->setClass($second_class)
        ->setExam($this->exams[0])
        ->getEligibleStudentsAndScores();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('student_id'))->toContain($student3->id)
        ->and($data->toArray()[0])->toMatchArray([
            'student_id' => $student3->id,
            'student_number' => $student3->student_number,
            'grading_type' => 'SCORE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf3->id,
        ])
        ->and($data->toArray()[0]['components'][0])->toMatchArray([
            'component_code' => 'FINAL',
            'component_actual_score' => null,
            'component_weightage_percent' => 100,
        ]);

    // employee 2, subject 2, class 1 (GRADE)
    $data = app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee2)
        ->setSubject($this->subjects[2])
        ->setClass($first_class)
        ->setExam($this->exams[0])
        ->getEligibleStudentsAndScores();

    expect($data)->toHaveCount(1)
        ->and($data->pluck('student_id'))->toContain($student->id)
        ->and($data->toArray()[0])->toMatchArray([
            'student_id' => $student->id,
            'student_number' => $student->student_number,
            'grading_type' => 'GRADE',
            'is_exempted' => false,
            'actual_score_grade' => null,
            'student_grading_framework_id' => $sgf1->id,
        ])
        ->and($data->toArray()[0]['components'])->toBeEmpty();

});


test('reopenPostedEntriesByResultSourceSubject', function() {

    $employee = Employee::factory()->create([]);
    $rss = ResultSourceSubject::factory()->create([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-25 00:00:00',
        'actual_score' => 100,
    ]);

    $rss2 = ResultSourceSubject::factory()->create([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-20 00:00:00',
        'actual_score' => 99,
    ]);

    app()->make(ExamResultsDataEntryService::class)
        ->setEmployee($employee)
        ->setResultSourceSubject($rss)
        ->reopenPostedEntriesByResultSourceSubject();

    $this->assertDatabaseHas('result_source_subjects', [
        'id' => $rss->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
        'posted_at' => null,
        'actual_score' => null,
    ]);
    $this->assertDatabaseHas('result_source_subjects', [
        'id' => $rss2->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
        'posted_at' => '2024-11-20 00:00:00',
        'actual_score' => 99,
    ]);

});
