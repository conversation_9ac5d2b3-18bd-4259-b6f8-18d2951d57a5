<?php

use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Collection;

beforeEach(function () {

    $this->config = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true);
    $this->studentGradingFrameworkService = app()->make(StudentGradingFrameworkService::class);

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    $this->subjects = Subject::factory(6)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
        ['code' => 'CODE-1']
    ))->create();

    $this->exams = Exam::factory(4)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'SEM3EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();

    $this->academic_year = '2024';
});


test('validateConfiguration basic success and failure', function () {

    // success case
    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($this->config);

    expect($value)->toBeTrue()->not()->toBeInstanceOf(Collection::class);
    /*
        // fail case, level 1 key fields missing
        $value = app()->make(StudentGradingFrameworkService::class)
            ->validateConfiguration([]);

        expect($value)->toBeArray()
            ->toMatchArray([
                'grading_framework_code' => ['The grading framework code field is required.'],
                'output_system_components' => ['The output system components field is required.'],
                'result_sources' => ['The result sources field is required.'],
                'output' => ['The output field is required.']
            ]);*/


    // fail case, level 2 key fields missing
    $temp_config = $this->config;
    $temp_config['result_sources'] = [[]];
    $temp_config['output'] = [[]];

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeArray()
        ->toMatchArray([
            'result_sources' => [
                'The result_sources.0.code field is required.',
                'The result_sources.0.name field is required.',
                'The result_sources.0.subjects field is required.',
                'The result_sources.0.exams field is required.',
            ],
            'output' => [
                'The output.0.code field is required.',
                'The output.0.name field is required.',
                'The output.0.name.en field is required.',
                'The output.0.service field is required.',
                'The output.0.is_final field is required.',
                'The output.0.components field is required.'
            ]
        ]);


    // fail case, level 3 key fields missing
    $temp_config = $this->config;
    foreach ($temp_config['result_sources'] as &$rs) {
        $rs['subjects'] = [];
        $rs['exams'] = [];
    }
    foreach ($temp_config['output'] as &$out) {
        $out['components'] = [];
    }

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeArray()
        ->toMatchArray([
            'result_sources' => [
                'The result_sources.0.subjects field is required.',
                'The result_sources.1.subjects field is required.',
                'The result_sources.2.subjects field is required.',
                'The result_sources.0.exams field is required.',
                'The result_sources.1.exams field is required.',
                'The result_sources.2.exams field is required.',
            ],
            'output' => [
                'The output.0.components field is required.',
                'The output.1.components field is required.',
                'The output.2.components field is required.',
            ]
        ]);

    // fail case, level 4 key fields missing
    $temp_config = $this->config;
    foreach ($temp_config['result_sources'] as &$rs) {
        foreach ($rs['subjects'] as &$subject) {
            $subject['components'] = [];
        }
    }

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeArray()
        ->toMatchArray([
            'result_sources' => [
                "The result_sources.0.subjects.0.components field is required when result_sources.0.subjects.0.grading_type is SCORE.",
                "The result_sources.0.subjects.1.components field is required when result_sources.0.subjects.1.grading_type is SCORE.",
                "The result_sources.0.subjects.3.components field is required when result_sources.0.subjects.3.grading_type is SCORE.",
                "The result_sources.0.subjects.4.components field is required when result_sources.0.subjects.4.grading_type is SCORE.",
                "The result_sources.1.subjects.0.components field is required when result_sources.1.subjects.0.grading_type is SCORE.",
                "The result_sources.1.subjects.1.components field is required when result_sources.1.subjects.1.grading_type is SCORE.",
                "The result_sources.1.subjects.3.components field is required when result_sources.1.subjects.3.grading_type is SCORE.",
                "The result_sources.1.subjects.4.components field is required when result_sources.1.subjects.4.grading_type is SCORE.",
                "The result_sources.2.subjects.0.components field is required when result_sources.2.subjects.0.grading_type is SCORE.",
                "The result_sources.2.subjects.1.components field is required when result_sources.2.subjects.1.grading_type is SCORE.",
                "The result_sources.2.subjects.3.components field is required when result_sources.2.subjects.3.grading_type is SCORE.",
                "The result_sources.2.subjects.4.components field is required when result_sources.2.subjects.4.grading_type is SCORE.",
            ]
        ]);


    // fail case, must provide either one name / subject_code
    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0] = [
        'code' => 'CHINESE',
        'grading_scheme_code' => 'DEFAULT',
        'total_formula' => 'XXX'
    ];

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeArray()
        ->toMatchArray([
            "output" => [
                "The output.0.components.0.subject_code field is required when output.0.components.0.name is not present.",
                "The output.0.components.0.name field is required when output.0.components.0.subject_code is not present.",
            ]
        ]);
});


test('validateConfiguration advanced - all result source subject components must sum up to 100', function () {

    $temp_config = $this->config;

    $temp_config['result_sources'][0]['subjects'][1]['components'][0]['weightage_percent'] = 0;
    $temp_config['result_sources'][0]['subjects'][3]['components'][0]['weightage_percent'] = 30;
    $temp_config['result_sources'][0]['subjects'][3]['components'][1]['weightage_percent'] = 69;

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(2)
        ->toContain(
            "Components' weightage_percent must sum up to 100 for subject code 02",
            "Components' weightage_percent must sum up to 100 for subject code 04"
        );
});


test('validateConfiguration advanced - all result source subject components must have unique codes', function () {

    $temp_config = $this->config;
    $temp_config['result_sources'][0]['subjects'][3]['components'][0]['code'] = 'homework';
    $temp_config['result_sources'][0]['subjects'][3]['components'][1]['code'] = 'HOMEWORK';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated component code "HOMEWORK". All components\' code must be unique for subject code 04'
        );
});


test('validateConfiguration advanced - all result source subject code can support dash character', function () {

    $temp_config = $this->config;

    $temp_config['result_sources'][0]['subjects'][0]['code'] = 'CODE-1';

    // remove the use of the original subject code to prevent unrelated validation errors
    $temp_config['output'][0]['components'][0]['total_formula'] = 'VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[CODE-1].score")';
    $temp_config['output'][0]['components'][5]['total_formula'] = '1';
    $temp_config['output'][0]['components'][6]['total_formula'] = '1';
    $temp_config['output'][1]['components'][0]['total_formula'] = '1';
    $temp_config['output'][2]['components'][0]['total_formula'] = '1';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeTrue();
});


test('validateConfiguration advanced - all output component code can support dash character', function () {

    $temp_config = $this->config;

    $temp_config['output'][0]['components'][0]['subject_code'] = 'CODE-1';
    $temp_config['output'][0]['components'][0]['code'] = 'CODE-1';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeTrue();
});

test('validateConfiguration advanced - all result source subjects must have unique codes', function () {

    $temp_config = $this->config;

    $temp_config['result_sources'][0]['subjects'][1]['code'] = '01';

    // drop off unnecessary data to prevent unrelated errors
    unset($temp_config['output'][0]);
    unset($temp_config['output'][1]);
    unset($temp_config['output'][2]['components'][1]);

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated subject code "01". All subjects\' code must be unique for result source SEM1EXAM'
        );
});


test('validateConfiguration advanced - all result source subjects must have valid code', function () {

    $temp_config = $this->config;

    // drop off unnecessary data to prevent unrelated errors
    $temp_config['output'][0]['is_final'] = 1;
    unset($temp_config['output'][1]);
    unset($temp_config['output'][2]);
    unset($temp_config['output'][0]['components'][2]);

    $temp_config['result_sources'][0]['subjects'][2]['code'] = '0333';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Subject code "0333" is invalid in result source SEM1EXAM'
        );
});


test('validateConfiguration advanced - all result source exams must have valid code', function () {

    Exam::truncate();

    Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAMAAAA'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();

    $temp_config = $this->config;
    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Exam code "SEM1EXAM" is invalid in result source SEM1EXAM'
        );
});


test('validateConfiguration advanced - result sources must have unique code within the same grading framework', function () {

    $temp_config = $this->config;
    $temp_config['result_sources'][0]['code'] = 'SEM1EXAM';
    $temp_config['result_sources'][1]['code'] = 'SEM1EXAM';
    $temp_config['result_sources'][2]['code'] = 'SEM2EXAM';

    // drop off unnecessary data to prevent unrelated errors
    $temp_config['output'][0]['is_final'] = 1;
    unset($temp_config['output'][1]);
    unset($temp_config['output'][2]);

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated result source code "SEM1EXAM". All result sources\' code must be unique for grading framework J1DEFAULT'
        );
});


test('validateConfiguration advanced - each report card output must have unique code within the same grading framework', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['code'] = 'SAMECODE';
    $temp_config['output'][1]['code'] = 'DIFFCODE';
    $temp_config['output'][2]['code'] = 'SAMECODE';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated grading framework output code "SAMECODE". All output codes must be unique for grading framework J1DEFAULT'
        );
});


test('validateConfiguration advanced - each report card output component must have unique code within the same report card output', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['components'][5]['code'] = 'SAMECODE';
    $temp_config['output'][0]['components'][6]['code'] = 'DIFFCODE';
    $temp_config['output'][0]['components'][7]['code'] = 'SAMECODE';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated grading framework output component code "SAMECODE". All component codes must be unique for Output code ' . $temp_config['output'][0]['code']
        );
});

test('validateConfiguration advanced - each report card output component grading scheme code must be valid if provided', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0]['grading_scheme_code'] = 'INVALIDCODE';
    $temp_config['output'][1]['components'][2]['grading_scheme_code'] = 'ANOTHERCODE';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(2)
        ->toContain(
            'Grading Scheme "INVALIDCODE" is invalid in Output Component ' . $temp_config['output'][0]['code'] . ' > ' . $temp_config['output'][0]['components'][0]['code'],
            'Grading Scheme "ANOTHERCODE" is invalid in Output Component ' . $temp_config['output'][1]['code'] . ' > ' . $temp_config['output'][1]['components'][2]['code'],
        );
});

test('validateConfiguration advanced - each report card output must contain all compulsory components declared at Grading framework output_system_components', function () {

    $temp_config = $this->config;
    $temp_config['output_system_components'] = ['NEWCOMPONENT'];

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(3)
        ->toContain(
            'Grading Framework Output "SEM1RESULT" components must contain all mandatory components from "output_system_components"',
            'Grading Framework Output "SEM2RESULT" components must contain all mandatory components from "output_system_components"',
            'Grading Framework Output "FINALRESULT" components must contain all mandatory components from "output_system_components"',
        );

});

test('validateConfiguration advanced - report card output specified service must exists', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['service'] = 'InvalidService';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Grading Framework Output "SEM1RESULT" service "InvalidService" is invalid.',
        );

});


test('validateConfiguration advanced - each report card output component with subject must have valid subject code', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0]['subject_code'] = 'INVALIDCODE';
    $temp_config['output'][1]['components'][1]['subject_code'] = 'ANOTHERCODE';
    $temp_config['output'][0]['components'][0]['code'] = 'INVALIDCODE';
    $temp_config['output'][1]['components'][1]['code'] = 'ANOTHERCODE';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(2)
        ->toContain(
            'Subject code "INVALIDCODE" is invalid in Output Component ' . $temp_config['output'][0]['code'] . ' > ' . $temp_config['output'][0]['components'][0]['code'],
            'Subject code "ANOTHERCODE" is invalid in Output Component ' . $temp_config['output'][1]['code'] . ' > ' . $temp_config['output'][1]['components'][1]['code'],
        );

});


test('validateConfiguration advanced - each report card output component with subject must have same code as subject code', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0]['subject_code'] = '01';
    $temp_config['output'][1]['components'][1]['subject_code'] = '01';
    $temp_config['output'][0]['components'][0]['code'] = '01A';
    $temp_config['output'][1]['components'][1]['code'] = '02A';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(2)
        ->toContain(
            'Output component code must be same as subject code if subject code is provided in Output Component SEM1RESULT > ' . $temp_config['output'][0]['components'][0]['code'],
            'Output component code must be same as subject code if subject code is provided in Output Component SEM2RESULT > ' . $temp_config['output'][1]['components'][1]['code'],
        );

});

test('validateConfiguration advanced - each exam can only be used once per student grading framework', function () {

    $temp_config = $this->config;
    $temp_config['result_sources'][0]['exams'] = ['SEM1EXAM'];
    $temp_config['result_sources'][1]['exams'] = ['SEM1EXAM'];
    $temp_config['result_sources'][2]['exams'] = ['SEM2EXAM'];

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Duplicated exam code "SEM1EXAM" used. Each exam code can only be assigned once.',
        );

});


test('validateConfiguration advanced - can only have 1 is_final report card output within a Grading framework', function () {

    // more than one is_final = 1
    $temp_config = $this->config;
    $temp_config['output'][0]['is_final'] = 1;
    $temp_config['output'][1]['is_final'] = 1;
    $temp_config['output'][2]['is_final'] = 1;

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Must have only 1 Output with is_final = 1 setting for Grading Framework J1DEFAULT',
        );


    // no is_final = 1
    $temp_config = $this->config;
    $temp_config['output'][0]['is_final'] = 0;
    $temp_config['output'][1]['is_final'] = 0;
    $temp_config['output'][2]['is_final'] = 0;

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Must have at least 1 Output with is_final = 1 setting for Grading Framework J1DEFAULT',
        );

});


test('validateConfiguration advanced - invalid expression in formula fields', function () {

    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0]['total_formula'] = 'VAR("ResultSource[SEM1EXAM].SUBJECT[01].score")';

    // bad syntax
    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Bad total_formula (Unable to parse expression "ResultSource[SEM1EXAM].SUBJECT[01].score" part "ResultSource[SEM1EXAM]") in Output Component SEM1RESULT > 01'
        );


    // bad target (total_formula)
    $temp_config['output'][0]['components'][0]['total_formula'] = 'VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01].xxx")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Bad total_formula (Unable to parse expression "RESULTSOURCE[SEM1EXAM].SUBJECT[01].xxx" part "xxx") in Output Component SEM1RESULT > 01'
        );


    // bad result source reference (total_formula)
    $temp_config['output'][0]['components'][0]['total_formula'] = 'VAR("RESULTSOURCE[XXXX].SUBJECT[01].score")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Invalid total_formula result source code XXXX in Output Component SEM1RESULT > 01'
        );

    // bad subject reference (total_formula)
    $temp_config['output'][0]['components'][0]['total_formula'] = 'VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01A].score")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Invalid total_formula subject code 01A in Output Component SEM1RESULT > 01'
        );


    // bad target (label_formula)
    $temp_config = $this->config;
    $temp_config['output'][0]['components'][0]['label_formula'] = 'VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01].xxx")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Bad label_formula (Unable to parse expression "RESULTSOURCE[SEM1EXAM].SUBJECT[01].xxx" part "xxx") in Output Component SEM1RESULT > 01'
        );


    // bad result source reference (label_formula)
    $temp_config['output'][0]['components'][0]['label_formula'] = 'VAR("RESULTSOURCE[XXXX].SUBJECT[01].score")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Invalid label_formula result source code XXXX in Output Component SEM1RESULT > 01'
        );

    // bad subject reference (label_formula)
    $temp_config['output'][0]['components'][0]['label_formula'] = 'VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01A].score")';

    $value = app()->make(StudentGradingFrameworkService::class)
        ->validateConfiguration($temp_config);

    expect($value)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1)
        ->toContain(
            'Invalid label_formula subject code 01A in Output Component SEM1RESULT > 01'
        );

});


test('applyGradingFrameworkForPeriod()', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $student = Student::factory()->create();

    // create several old settings, should be deactivated
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'grading_framework_id' => 100,
        'is_active' => true,
        'academic_year' => '2024'
    ]);
    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'grading_framework_id' => 101,
        'is_active' => true,
        'academic_year' => '2023'
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[4]->id
        ]
    ))->create();


    $this->assertDatabaseCount(StudentGradingFramework::class, 2);

    app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year);

    $config_output =  app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->mapConfigToValidSubjects();

    // Old 2024 grading framework is deleted and new one is created
    $this->assertDatabaseCount(StudentGradingFramework::class, 2);
    $this->assertDatabaseHas(StudentGradingFramework::class, [
        'student_id' => $student->id,
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => true,
        'configuration' => $this->castAsJson($config_output),
    ]);


    // same academic year deleted
    $this->assertDatabaseMissing(StudentGradingFramework::class, [
        'student_id' => $student->id,
        'grading_framework_id' => 100,
        'is_active' => true,
        'academic_year' => '2024'
    ]);

    // previous years deactivated
    $this->assertDatabaseHas(StudentGradingFramework::class, [
        'id' => $sgf2->id,
        'is_active' => false,
        'academic_year' => '2023'
    ]);


    $student_grading_framework = StudentGradingFramework::orderBy('id', 'DESC')->first();

    expect($student->gradingFrameworks)->toHaveCount(2)
        ->and($student->activeGradingFramework->id)->toBe($student_grading_framework->id);

    // result sources
    $this->assertDatabaseCount(ResultSource::class, 3);
    $this->assertDatabaseCount(ResultSourceExam::class, 3);
    $this->assertDatabaseCount(ResultSourceSubject::class, 15);
    $this->assertDatabaseCount(ResultSourceSubjectComponent::class, 15);
    $this->assertDatabaseCount(ReportCardOutput::class, 3);
    $this->assertDatabaseCount(ReportCardOutputComponent::class, 36);

    expect($student_grading_framework->resultSources)->toHaveCount(3)
        ->and($student_grading_framework->outputs)->toHaveCount(3);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);

        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {

                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }

    }


    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }


            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);

        }

    }
});

test('applyGradingFrameworkForPeriod() - elective behaviour', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $student = Student::factory()->create();

    // create several old settings, should be deactivated
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'grading_framework_id' => 100,
        'is_active' => true,
        'academic_year' => '2024'
    ]);
    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'grading_framework_id' => 101,
        'is_active' => true,
        'academic_year' => '2023'
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ]
    ))->create();


    $this->assertDatabaseCount(StudentGradingFramework::class, 2);

    app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year);

    $config_output =  app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->mapConfigToValidSubjects();

    $this->assertDatabaseCount(StudentGradingFramework::class, 2);
    $this->assertDatabaseHas(StudentGradingFramework::class, [
        'student_id' => $student->id,
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => true,
        'configuration' => $this->castAsJson($config_output),
    ]);

    // same academic year deleted
    $this->assertDatabaseMissing(StudentGradingFramework::class, [
        'student_id' => $student->id,
        'grading_framework_id' => 100,
        'is_active' => true,
        'academic_year' => '2024'
    ]);

    // previous years deactivated
    $this->assertDatabaseHas(StudentGradingFramework::class, [
        'id' => $sgf2->id,
        'is_active' => false,
        'academic_year' => '2023'
    ]);


    $student_grading_framework = StudentGradingFramework::orderBy('id', 'DESC')->first();

    expect($student->gradingFrameworks)->toHaveCount(2)
        ->and($student->activeGradingFramework->id)->toBe($student_grading_framework->id);
    // result sources
    $this->assertDatabaseCount(ResultSource::class, 3);
    $this->assertDatabaseCount(ResultSourceExam::class, 3);
    $this->assertDatabaseCount(ResultSourceSubject::class, 9);
    $this->assertDatabaseCount(ResultSourceSubjectComponent::class, 9);
    $this->assertDatabaseCount(ReportCardOutput::class, 3);
    $this->assertDatabaseCount(ReportCardOutputComponent::class, 30);

    expect($student_grading_framework->resultSources)->toHaveCount(3)
        ->and($student_grading_framework->outputs)->toHaveCount(3);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);

        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {

                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }

    }

    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }


            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);

        }
    }

    // Check that subject 03 and subject 04 is not created
    $this->assertDatabaseMissing(ReportCardOutputComponent::class, [
        'subject_id' => $this->subjects[2]->id
    ]);
    $this->assertDatabaseMissing(ReportCardOutputComponent::class, [
        'subject_id' => $this->subjects[3]->id
    ]);

    $this->assertDatabaseMissing(ReportCardOutputComponent::class, [
        'subject_id' => $this->subjects[2]->id
    ]);
    $this->assertDatabaseMissing(ReportCardOutputComponent::class, [
        'subject_id' => $this->subjects[3]->id
    ]);
});

test('applyGradingFrameworkForPeriod() - has existing exam results', function () {

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $student = Student::factory()->create();

    // create several old settings, should be deactivated
    $sgf_old = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'grading_framework_id' => 101,
        'is_active' => true,
        'academic_year' => '2023'
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ]
    ))->create();


    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();
    
    $this->assertDatabaseCount(StudentGradingFramework::class, 2);
    $rs_sem1_student0 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf->id)->first();

    $rs_sub1_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $rs_sub2_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    
    $rs_sub1_sem1_student0->components()->update([
        'actual_score' => 85
    ]);
    $rs_sub2_sem1_student0->components()->update([
        'actual_score' => 80
    ]);

    $this->assertDatabaseCount(StudentGradingFramework::class, 2);
    
    $this->expectExceptionMessage('Unable to apply grading framework, student has existing marks. Please check exam posting pre-checks and remove exam marks related to the student.');
    $this->expectExceptionCode(60002);
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

});


test('updateGradingFramework', function (){

    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);
    $effective_from = \Carbon\Carbon::parse('2024-01-01');
    $effective_to = \Carbon\Carbon::parse('2024-12-31');

    $student = Student::factory()->create();

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[4]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[4]->id
        ]
    ))->create();

    $config_output = $this->config;

    $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod($effective_from, $effective_to, $this->academic_year);

    // validate all result source and output component created
    $student_grading_framework = StudentGradingFramework::orderBy('id', 'DESC')->first();
    // result sources
    $this->assertDatabaseCount(ResultSource::class, 3);
    $this->assertDatabaseCount(ResultSourceExam::class, 3);
    $this->assertDatabaseCount(ResultSourceSubject::class, 15);
    $this->assertDatabaseCount(ResultSourceSubjectComponent::class, 15);
    $this->assertDatabaseCount(ReportCardOutput::class, 3);
    $this->assertDatabaseCount(ReportCardOutputComponent::class, 36);

    expect($student_grading_framework->resultSources)->toHaveCount(3)
        ->and($student_grading_framework->outputs)->toHaveCount(3);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);

        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {

                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }

    }


    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }

            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);
        }
    }

    // Delete SEM1EXAM and SEM1RESULT 
    // Delete subject 01 from both result source and output component from all result source and outputs
    $config_output = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1-deleted-test.json')), true);
    $grading_framework->update(['configuration' => $config_output]);
    
    $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->setStudentGradingFramework($student_grading_framework)
        ->updateGradingFramework($effective_from, $effective_to);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);
    
        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);
        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);
    
            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {
                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }
    }
    
    
    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }

            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);
        }
    }
 
    // modify subject 02 from both result source and output component
    $config_output = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1-modified-test.json')), true);
    $grading_framework->update(['configuration' => $config_output]);

    $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->setStudentGradingFramework($student_grading_framework)
        ->updateGradingFramework($effective_from, $effective_to);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);
    
        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);
    
            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {
                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }
    }
    
    
    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }

            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);
        }
    }

    // add subject 01 back to result source and output component
    $config_output = $this->config;
    $grading_framework->update(['configuration' => $config_output]);

    $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->setStudentGradingFramework($student_grading_framework)
        ->updateGradingFramework($effective_from, $effective_to);

    // validate all result source and output component created
    $student_grading_framework = StudentGradingFramework::orderBy('id', 'DESC')->first();
    // result sources
    $this->assertDatabaseCount(ResultSource::class, 3);
    $this->assertDatabaseCount(ResultSourceExam::class, 3);
    $this->assertDatabaseCount(ResultSourceSubject::class, 15);
    $this->assertDatabaseCount(ResultSourceSubjectComponent::class, 15);
    $this->assertDatabaseCount(ReportCardOutput::class, 3);
    $this->assertDatabaseCount(ReportCardOutputComponent::class, 36);

    expect($student_grading_framework->resultSources)->toHaveCount(3)
        ->and($student_grading_framework->outputs)->toHaveCount(3);

    foreach ($config_output['result_sources'] as $result_source_config) {

        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);

        $result_source = ResultSource::where('code', $result_source_config['code'])->first();

        expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));

        foreach ($result_source_config['exams'] as $exam_code) {

            $this->assertDatabaseHas(ResultSourceExam::class, [
                'result_source_id' => $result_source->id,
                'exam_id' => Exam::where('code', $exam_code)->first()->id,
            ]);

        }

        expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));

        foreach ($result_source_config['subjects'] as $subject_config) {

            $subject = Subject::where('code', $subject_config['code'])->first();

            $this->assertDatabaseHas(ResultSourceSubject::class, [
                'result_source_id' => $result_source->id,
                'subject_id' => $subject->id,
                'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
                'is_exempted' => $subject_config['is_exempted'],
                'grading_type' => $subject_config['grading_type'],
                'actual_score' => null,
                'actual_score_grade' => null,
            ]);

            $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
                ->where('result_source_id', $result_source->id)
                ->first();

            expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

            foreach ($subject_config['components'] as $subject_component) {
                $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                    'result_source_subject_id' => $result_source_subject->id,
                    'code' => $subject_component['code'],
                    'name->en' => $subject_component['name']['en'],
                    'name->zh' => $subject_component['name']['zh'],
                    'weightage_percent' => $subject_component['weightage_percent'],
                    'actual_score' => null,
                ]);
            }
        }

    }

    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student_grading_framework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student_grading_framework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }

            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);
        }
    }
});

test('mapConfigToValidSubjects', function(){
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $student = Student::factory()->create();

    $semester_setting = SemesterSetting::factory()->create([
        'is_current_semester' => true
    ]);
    
    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();
    
    $effective_from = \Carbon\Carbon::parse('2024-01-01');
    $effective_to = \Carbon\Carbon::parse('2024-12-31');

    $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod($effective_from, $effective_to, $this->academic_year);


    $config_output = $this->studentGradingFrameworkService
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->mapConfigToValidSubjects();

    foreach ($config_output['result_sources'] as $result_source_config) {
        $this->assertDatabaseHas(ResultSource::class, [
            'student_grading_framework_id' => $student->activeGradingFramework->id,
            'code' => $result_source_config['code'],
            'name->en' => $result_source_config['name']['en'],
            'name->zh' => $result_source_config['name']['zh'],
        ]);
    }

    $result_source = ResultSource::where([
        'code' => $result_source_config['code'],
        'student_grading_framework_id' => $student->activeGradingFramework->id
    ])->first();

    expect($result_source->exams)->toHaveCount(count($result_source_config['exams']));
    foreach ($result_source_config['exams'] as $exam_code) {

        $this->assertDatabaseHas(ResultSourceExam::class, [
            'result_source_id' => $result_source->id,
            'exam_id' => Exam::where('code', $exam_code)->first()->id,
        ]);

    }

    expect($result_source->subjects)->toHaveCount(count($result_source_config['subjects']));
    foreach ($result_source_config['subjects'] as $subject_config) {
        $subject = Subject::where('code', $subject_config['code'])->first();

        $this->assertDatabaseHas(ResultSourceSubject::class, [
            'result_source_id' => $result_source->id,
            'subject_id' => $subject->id,
            'weightage_multiplier' => $subject_config['weightage_multiplier'] ?? null,
            'is_exempted' => $subject_config['is_exempted'],
            'grading_type' => $subject_config['grading_type'],
            'actual_score' => null,
            'actual_score_grade' => null,
        ]);

        $result_source_subject = ResultSourceSubject::where('subject_id', $subject->id)
            ->where('result_source_id', $result_source->id)
            ->first();

        expect($result_source_subject->components)->toHaveCount(count($subject_config['components']));

        foreach ($subject_config['components'] as $subject_component) {

            $this->assertDatabaseHas(ResultSourceSubjectComponent::class, [
                'result_source_subject_id' => $result_source_subject->id,
                'code' => $subject_component['code'],
                'name->en' => $subject_component['name']['en'],
                'name->zh' => $subject_component['name']['zh'],
                'weightage_percent' => $subject_component['weightage_percent'],
                'actual_score' => null,
            ]);
        }
    }


    foreach ($config_output['output'] as $output_config) {

        $this->assertDatabaseHas(ReportCardOutput::class, [
            'student_grading_framework_id' => $student->activeGradingFramework->id,
            'code' => $output_config['code'],
            'name->en' => $output_config['name']['en'],
            'name->zh' => $output_config['name']['zh'],
            'service_class' => $output_config['service'],
            'is_final' => $output_config['is_final'],
        ]);

        $output = ReportCardOutput::where('code', $output_config['code'])->where('student_grading_framework_id', $student->activeGradingFramework->id)->first();

        expect($output)->not()->toBeNull()
            ->and($output->components)->toHaveCount(count($output_config['components']));

        foreach ($output_config['components'] as $component_config) {

            $subject = null;
            $grading_scheme = null;

            if (isset($component_config['subject_code'])) {
                $subject = Subject::where('code', $component_config['subject_code'])->first();
            }
            if (isset($component_config['grading_scheme_code'])) {
                $grading_scheme = GradingScheme::where('code', $component_config['grading_scheme_code'])->first();
            }


            // use name if provided, else fall back to subject name if subject_code is provided.
            $name = $component_config['name'] ?? null;

            if ($name === null && isset($component_config['subject_code'])) {
                $name = $subject->getTranslations('name');
            }

            $this->assertDatabaseHas(ReportCardOutputComponent::class, [
                'report_card_output_id' => $output->id,
                'code' => $component_config['code'],
                'name->en' => $name['en'],
                'name->zh' => $name['zh'],
                'subject_id' => $subject->id ?? null,
                'grading_scheme_id' => $grading_scheme->id ?? null,
                'total_formula' => $component_config['total_formula'] ?? null,
                'label_formula' => $component_config['label_formula'] ?? null,
                'resolve_priority' => $component_config['priority'] ?? 0,
                'calculate_rank' => $component_config['calculate_rank'] ?? 0,
                'output_type' => $component_config['output_type'] ?? null,
                'weightage_multiplier' => $component_config['weightage_multiplier'] ?? null,
            ]);
        }  
    }

});