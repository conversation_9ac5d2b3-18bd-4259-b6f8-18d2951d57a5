<?php

use App\Models\PromotionMark;
use App\Models\SemesterClass;
use App\Services\Exam\PromotionMarkService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
    $this->promotionMarkService = app(PromotionMarkService::class);

    $this->table = resolve(PromotionMark::class)->getTable();
});

test('getAllPromotionMarks()', function () {

    $semester_class = SemesterClass::factory()->create();

    PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();


    $result = $this->promotionMarkService->getAllPromotionMarks();
    expect($result)->toBeInstanceOf(Collection::class);

    expect($result->toArray())
        ->toHaveCount(3)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_class->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result[1])->toMatchArray([
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result[2])->toMatchArray([
            "net_average_for_promotion"=> 90.00,
            "conduct_mark_for_promotion" => 80.00,     
        ]);
    

    // filter test
    $filters = [ 'semester_class_id' => $semester_class->id ];

    $result = $this->promotionMarkService->getAllPromotionMarks($filters);
    
    expect($result)->toBeInstanceOf(Collection::class);

    expect($result->toArray())
        ->toHaveCount(1)
        ->and($result[0])->toMatchArray([
            "semester_class_id" => $semester_class->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);
});

test('getAllPaginatedPromotionMarks()', function () {

    $semester_class = SemesterClass::factory()->create();

    PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();


    $result = $this->promotionMarkService->getAllPaginatedPromotionMarks();
    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);

    $result = $result->toArray();
    
    expect($result['data'])
        ->toHaveCount(3)
        ->and($result['data'][0])->toMatchArray([
            "semester_class_id" => $semester_class->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result['data'][1])->toMatchArray([
            "net_average_for_promotion"=> 70.00,
            "conduct_mark_for_promotion" => 60.00,     
        ])
        ->and($result['data'][2])->toMatchArray([
            "net_average_for_promotion"=> 90.00,
            "conduct_mark_for_promotion" => 80.00,     
        ]);
    

    // filter test
    $filters = [ 'semester_class_id' => $semester_class->id ];

    $result = $this->promotionMarkService->getAllPaginatedPromotionMarks($filters);
    
    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);

    $result = $result->toArray();

    expect($result['data'])
        ->toHaveCount(1)
        ->and($result['data'][0])->toMatchArray([
            "semester_class_id" => $semester_class->id,
            "net_average_for_promotion"=> 56.00,
            "conduct_mark_for_promotion" => 60.00,     
        ]);
});

test('bulkCreateOrUpdatePromotionMark() - bulk create', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $semester_class = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();
    $semester_class3 = SemesterClass::factory()->create();

    $payload = [
        [
            'semester_class_id' => $semester_class->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 46.00,
            'conduct_mark_for_promotion' => 50.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 66.00,
            'conduct_mark_for_promotion' => 70.00,
        ]
    ];

    $this->promotionMarkService->bulkCreateOrUpdatePromotionMark($payload);
    $this->assertDatabaseCount($this->table, 3);

    foreach($payload as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_class_id' => $input['semester_class_id'],
            'net_average_for_promotion' => $input['net_average_for_promotion'],
            'conduct_mark_for_promotion' => $input['conduct_mark_for_promotion']
        ]);
    }
});


test('bulkCreateOrUpdatePromotionMark() - bulkUpdate', function(){
    $semester_class1 = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();
    $semester_class3 = SemesterClass::factory()->create();

    $promotion_marks = PromotionMark::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class1->id,
            'net_average_for_promotion' => 56.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 70.00,
            'conduct_mark_for_promotion' => 60.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 90.00,
            'conduct_mark_for_promotion' => 80.00,
        ]
    ))->create();

    $payload = [
        [
            'semester_class_id' => $semester_class1->id,
            'net_average_for_promotion' => 80.00,
            'conduct_mark_for_promotion' => 70.00,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'net_average_for_promotion' => 80.00,
            'conduct_mark_for_promotion' => 50.00,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'net_average_for_promotion' => 26.00,
            'conduct_mark_for_promotion' => 30.00,
        ]
    ];

    $this->promotionMarkService->bulkCreateOrUpdatePromotionMark($payload);
    $this->assertDatabaseCount($this->table, 3);

    foreach($payload as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_class_id' => $input['semester_class_id'],
            'net_average_for_promotion' => $input['net_average_for_promotion'],
            'conduct_mark_for_promotion' => $input['conduct_mark_for_promotion']
        ]);
    }
});