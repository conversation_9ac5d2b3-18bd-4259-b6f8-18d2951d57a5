<?php

use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentReportCard;
use App\Services\Exam\ReportCard\StudentReportCardService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->table = resolve(ResultsPostingHeader::class)->getTable();

    $this->studentReportCardService = app(StudentReportCardService::class);
});

test('getAllReportCard', function (){
    $grades = Grade::factory(3)->create();
    $semester_settings = SemesterSetting::factory(3)->create();
    $semester_class = SemesterClass::factory(3)->create();
    $students = Student::factory(2)->create();

    $headers = ResultsPostingHeader::factory(3)->state(new Sequence(
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT ALT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ]
    ))->create();


    $report_cards = StudentReportCard::factory(4)->state(new Sequence(
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[0]->id,
            'is_active' => false,
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[1]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[1]->id,
            'semester_class_id' => $semester_class[1]->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
        ]
    ))->create();

    // filter for result_posting_header_id
    // semester_class_id
    // is_active true

    $payload = [
        'results_posting_header_id' => $headers[0]->id,
        'semester_class_id' => $semester_class[0]->id,
        'is_active' => true
    ];

    $response = $this->studentReportCardService->getAllReportCard($payload);

    expect($response)->toBeInstanceOf(Collection::class)
        ->toHaveCount(2);
    expect($response->toArray())
        ->and($response[0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'results_posting_header_id' => $report_cards[0]->results_posting_header_id,
            'student_id' => $report_cards[0]->student_id,
            'file_url' => $report_cards[0]->file_url,
            'semester_class_id' => $report_cards[0]->semester_class_id,
            'is_active' => true
        ])
        ->and($response[1])->toMatchArray([
            'id' => $report_cards[2]->id,
            'results_posting_header_id' => $report_cards[2]->results_posting_header_id,
            'student_id' => $report_cards[2]->student_id,
            'file_url' => $report_cards[2]->file_url,
            'semester_class_id' => $report_cards[2]->semester_class_id,
            'is_active' => true
        ]);  

    // filter for result_posting_header_id
    // student_id
    // is_active true
    $payload = [
        'results_posting_header_id' => $headers[0]->id,
        'student_id' => $students[0]->id,
        'is_active' => true
    ];

    $response = $this->studentReportCardService->getAllReportCard($payload);

    expect($response)->toBeInstanceOf(Collection::class)
        ->toHaveCount(1);
    expect($response->toArray())
        ->and($response[0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'results_posting_header_id' => $report_cards[0]->results_posting_header_id,
            'student_id' => $report_cards[0]->student_id,
            'file_url' => $report_cards[0]->file_url,
            'semester_class_id' => $report_cards[0]->semester_class_id,
            'is_active' => true
        ]); 
});

test('getAllPaginatedReportCards', function (){
    $grades = Grade::factory(3)->create();
    $semester_settings = SemesterSetting::factory(3)->create();
    $semester_class = SemesterClass::factory(3)->create();
    $students = Student::factory(2)->create();

    $headers = ResultsPostingHeader::factory(3)->state(new Sequence(
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT ALT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ]
    ))->create();


    $report_cards = StudentReportCard::factory(4)->state(new Sequence(
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[0]->id,
            'is_active' => false,
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'student_id' => $students[1]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[1]->id,
            'semester_class_id' => $semester_class[1]->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
        ]
    ))->create();

    // filter for result_posting_header_id
    // semester_class_id
    // is_active true

    $payload = [
        'results_posting_header_id' => $headers[0]->id,
        'semester_class_id' => $semester_class[0]->id,
        'is_active' => true
    ];

    $response = $this->studentReportCardService->getAllPaginatedReportCards($payload);

    expect($response)->toBeInstanceOf(LengthAwarePaginator::class)
        ->toHaveCount(2);
    
    expect($response->toArray())
        ->and($response[0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'results_posting_header_id' => $report_cards[0]->results_posting_header_id,
            'student_id' => $report_cards[0]->student_id,
            'file_url' => $report_cards[0]->file_url,
            'semester_class_id' => $report_cards[0]->semester_class_id,
            'is_active' => true
        ])
        ->and($response[1])->toMatchArray([
            'id' => $report_cards[2]->id,
            'results_posting_header_id' => $report_cards[2]->results_posting_header_id,
            'student_id' => $report_cards[2]->student_id,
            'file_url' => $report_cards[2]->file_url,
            'semester_class_id' => $report_cards[2]->semester_class_id,
            'is_active' => true
        ]);   
    // filter for result_posting_header_id
    // student_id
    // is_active true

    $payload = [
        'results_posting_header_id' => $headers[0]->id,
        'student_id' => $students[0]->id,
        'is_active' => true
    ];

    $response = $this->studentReportCardService->getAllPaginatedReportCards($payload);

    expect($response)->toBeInstanceOf(LengthAwarePaginator::class)
        ->toHaveCount(1);
    expect($response->toArray())
        ->and($response[0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'results_posting_header_id' => $report_cards[0]->results_posting_header_id,
            'student_id' => $report_cards[0]->student_id,
            'file_url' => $report_cards[0]->file_url,
            'semester_class_id' => $report_cards[0]->semester_class_id,
            'is_active' => true
        ]); 
});