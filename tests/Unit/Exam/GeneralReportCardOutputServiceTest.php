<?php


use App\Exceptions\BadExpressionException;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Services\Exam\Output\GeneralReportCardOutputService;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Cache;
use Mockery\MockInterface;

beforeEach(function() {

    Cache::clear();
    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $this->gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);


    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();

});

test('evaluate simple arithmetic', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    // addition
    $expression = '1 + 10 + 2';
    $result = $helper->evaluate($expression);

    expect($result)->toEqual(13);

    // subtraction
    $expression = '1-4';
    $result = $helper->evaluate($expression);

    expect($result)->toEqual(-3);

    // multiplication
    $expression = '2.5*4';
    $result = $helper->evaluate($expression);

    expect($result)->toEqual(10);

    // division
    $expression = '10/3';
    $result = $helper->evaluate($expression);

    expect(round($result, 2))->toEqual(3.33);

    // mixture
    $expression = '1 + 2 * 4 - 2';
    $result = $helper->evaluate($expression);

    expect($result)->toEqual(7);

    // mixture with priority
    $expression = '(1 + 2) * (4 - 2)';
    $result = $helper->evaluate($expression);

    expect($result)->toEqual(6);

});


test('evaluate bad expressions 1', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $this->expectException(BadExpressionException::class);
    $helper->evaluate('1 x 3 b');

});
test('evaluate bad expressions 2', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $this->expectException(BadExpressionException::class);
    $helper->evaluate('Unknown(1200)');

});

test('evaluate custom function - sum', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $result = $helper->evaluate('SUM(1,2,3,4)');
    expect($result)->toEqual(10);

});

test('evaluate custom function - min', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $result = $helper->evaluate('MIN(5,3,8)');
    expect($result)->toEqual(3);

});

test('evaluate custom function - max', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $result = $helper->evaluate('MAX(5,3,8)');
    expect($result)->toEqual(8);

});

test('evaluate custom function - round', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $result = $helper->evaluate('ROUND(14.346667, 3)');
    expect($result)->toEqual(14.347);

});


test('evaluate custom function - testFunction', function () {

    $helper = app()->make(GeneralReportCardOutputService::class);

    $result = $helper->evaluate('CUSTOMFUNCTIONNOARGS()');
    expect($result)->toEqual('custom-function-output-no-args');

    $result = $helper->evaluate('CUSTOMFUNCTION(1)');
    expect($result)->toEqual('custom-function-output-1');

    $result = $helper->evaluate('CUSTOMFUNCTION(1, "ab")');
    expect($result)->toEqual('custom-function-output-1-ab');

    $result = $helper->evaluate('CUSTOMFUNCTIONTWOARGS(2, 3)');
    expect($result)->toEqual(5);

});

test('evaluate dynamic variables', function () {

    $student = Student::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();
    
    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // set marks for test purposes
    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM2EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_4 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '04')->first()->id)
        ->first();

    $result_source_subject_1->update([
        'actual_score' => 95,
        'weightage_multiplier' => 2,
    ]);
    $result_source_subject_2->update([
        'actual_score' => 67,
        'weightage_multiplier' => 1,
    ]);
    $result_source_subject_3->update([
        'actual_score' => 82,
        'weightage_multiplier' => 2,
    ]);
    $result_source_subject_4->update([
        'actual_score_grade' => 'A-'
    ]);

    ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject_1->id)->where('code', 'FINAL')->update([
        'actual_score' => 95.55
    ]);

    $helper = app()->make(GeneralReportCardOutputService::class);
    $helper->setStudentGradingFramework($sgf);

    // scores
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[02].score")');
    expect($result)->toEqual(95);

    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[02].COMPONENT[FINAL].score")');
    expect($result)->toEqual(95.55);

    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01].score")');
    expect($result)->toEqual(67);

    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM2EXAM].SUBJECT[02].score")');
    expect($result)->toEqual(82);

    // arithmetic across different result sources
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[02].score") * 0.6 + VAR("RESULTSOURCE[SEM2EXAM].SUBJECT[02].score") * 0.4');
    expect($result)->toEqual(89.8);

    // grades
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[04].grade")');
    expect($result)->toEqual('A-');

    // weight
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[02].weight")');
    expect($result)->toEqual(2);

    // WEIGHTEDSCORE
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[02].weightedscore")');
    expect($result)->toEqual(190);

    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01].weightedscore")');
    expect($result)->toEqual(67);

    // no value
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[04].weightedscore")');
    expect($result)->toBeNull();

    // no value
    $result = $helper->evaluate('VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[03].score")');
    expect($result)->toBeNull();


});

test('resolveVariable fail no target element', function() {

    $service = app()->make(GeneralReportCardOutputService::class);

    $this->expectException(BadExpressionException::class);
    $this->expectExceptionMessage('Expression RESULTSOURCE[SEM1EXAM].SUBJECT[02] does not contain Target element.');
    $service->resolveVariable('RESULTSOURCE[SEM1EXAM].SUBJECT[02]');

});


test('resolveVariable fail invalid target keyword', function() {

    $service = app()->make(GeneralReportCardOutputService::class);

    $this->expectException(BadExpressionException::class);
    $this->expectExceptionMessage('Unable to parse expression "RESULTSOURCE[SEM1EXAM].SUBJECT[02].invalid" part "invalid"');
    $service->resolveVariable('RESULTSOURCE[SEM1EXAM].SUBJECT[02].invalid');

});


test('resolveVariable success', function() {

    // mock resolveResultSourceSubject
    $this->partialMock(GeneralReportCardOutputService::class, function (MockInterface $mock) {
        $mock->shouldReceive('resolveResultSourceSubject')->with('SEM1EXAM', '02', 'actual_score')->once()->andReturn(100);
    });

    $service = app()->make(GeneralReportCardOutputService::class);
    $value = $service->resolveVariable('RESULTSOURCE[SEM1EXAM].SUBJECT[02].score');
    expect($value)->toEqual(100);

    // mock resolveResultSourceSubjectComponent
    $this->partialMock(GeneralReportCardOutputService::class, function (MockInterface $mock) {
        $mock->shouldReceive('resolveResultSourceSubjectComponent')->with('SEM1EXAM', '02', 'MEH', 'actual_score')->once()->andReturn(101);
    });

    $service = app()->make(GeneralReportCardOutputService::class);
    $value = $service->resolveVariable('RESULTSOURCE[SEM1EXAM].SUBJECT[02].COMPONENT[MEH].score');
    expect($value)->toEqual(101);

});

test('resolveResultSourceSubject', function() {

    $student = Student::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();
    

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // set marks for test purposes
    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();

    $result_source_subject_1->update([
        'actual_score' => 69.69
    ]);

    $value = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->resolveResultSourceSubject('SEM1EXAM', '02', 'actual_score');

    expect($value)->toEqual(69.69);

    $value = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->resolveResultSourceSubject('SEM2EXAM', '02', 'actual_score');

    expect($value)->toBeNull();

});


test('resolveResultSourceSubjectComponent', function() {

    $student = Student::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();
    


    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // set marks for test purposes
    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();

    ResultSourceSubjectComponent::where('result_source_subject_id', $result_source_subject_1->id)->where('code', 'FINAL')->update([
        'actual_score' => 98.56
    ]);

    $value = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->resolveResultSourceSubjectComponent('SEM1EXAM', '02', 'FINAL','actual_score');

    expect($value)->toEqual(98.56);

    $value = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->resolveResultSourceSubjectComponent('SEM1EXAM', '01', 'FINAL','actual_score');

    expect($value)->toBeNull();

});


test('evaluateForOutputComponent', function() {

    $student = Student::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();
    

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // set marks for test purposes
    // total
    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM1EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', ResultSource::where('code', 'SEM2EXAM')->first()->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();

    $result_source_subject_1->update([
        'actual_score' => 70
    ]);
    $result_source_subject_2->update([
        'actual_score' => 92
    ]);


    // with total_formula only = VAR("RESULTSOURCE[SEM1EXAM].SUBJECT[01].score")
    $report_card_output = ReportCardOutput::where('code', 'SEM1RESULT')->where('student_grading_framework_id', $sgf->id)->first();
    $target_component = ReportCardOutputComponent::where('report_card_output_id', $report_card_output->id)->where('code', '01')->first();

    $output = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->evaluateOutputComponent($target_component);

    expect($output)->toMatchArray([
        'total' => 70,
        'total_grade' => [
            'name' => 'B',
            'display_as_name' => 'B',
            'extra_marks' => 0.0
        ],
        'label' => 'B',
        'weightage_multiplier' => 2,
        'weightage_total' => 140,
        'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE
    ]);


    // total_formula with arithmetic
    $report_card_output = ReportCardOutput::where('code', 'FINALRESULT')->where('student_grading_framework_id', $sgf->id)->first();
    $target_component = ReportCardOutputComponent::where('report_card_output_id', $report_card_output->id)->where('code', '01')->first();

    $output = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->evaluateOutputComponent($target_component);

    expect($output)->toMatchArray([
        'total' => 83.2,
        'total_grade' => [
            'name' => 'A',
            'display_as_name' => 'A',
            'extra_marks' => 1
        ],
        'label' => 'A',
        'weightage_multiplier' => 2,
        'weightage_total' => 166.4,
        'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE
    ]);
});


test('resolveReportCardOutputComponent', function() {

    $student = Student::factory()->create();
    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();


    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // set marks for test purposes
    // total
    $result_source_sem1 = ResultSource::where('code', 'SEM1EXAM')->first();
    $output_sem1 = ReportCardOutput::where('code', 'SEM1RESULT')->first();

    $result_source_subject_1 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '01')->first()->id)
        ->first();
    $result_source_subject_2 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '02')->first()->id)
        ->first();
    $result_source_subject_3 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '03')->first()->id)
        ->first();
    $result_source_subject_4 = ResultSourceSubject::where('result_source_id', $result_source_sem1->id)
        ->where('subject_id', $this->subjects->where('code', '04')->first()->id)
        ->first();

    $result_source_subject_1->components->first()->update([
        'actual_score' => 70
    ]);
    $result_source_subject_2->components->first()->update([
        'actual_score' => 92
    ]);
    $result_source_subject_3->update([
        'actual_score_grade' => 'A++'
    ]);
    $result_source_subject_4->components->first()->update([
        'actual_score' => 80
    ]);
    $result_source_subject_4->components->last()->update([
        'actual_score' => 60
    ]);

    $service = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->compileResultSourceScoresUsedInOutput($output_sem1)
        ->evaluateAllComponentsForOutput($output_sem1, 0);

    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '01', 'total');
    expect($value)->toEqual(70);

    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '01', 'total_grade');
    expect($value)->toMatchArray([
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0
    ]);

    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '02', 'total');
    expect($value)->toEqual(92);

    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '04', 'total');
    expect($value)->toEqual(66);

    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '03', 'label');
    expect($value)->toBe('A++');

    // Subject 70 is not taken by the student, so eventho we added 85 marks to result source
    // The evaluator does not add subject 70 to the output list, hence returning null
    $value = $service->resolveReportCardOutputComponent('SEM1RESULT', '70', 'total');
    expect($value)->toBe(null);

    // test evaluate
    $result = $service->evaluate('VAR("OUTPUT[SEM1RESULT].COMPONENT[02].total")');
    expect($result)->toEqual(92);

    $result = $service->evaluate('VAR("OUTPUT[SEM1RESULT].COMPONENT[04].total")');
    expect($result)->toEqual(66);

    $result = $service->evaluate('VAR("OUTPUT[SEM1RESULT].COMPONENT[03].label")');
    expect($result)->toBe('A++');

    // evaluate priority = 1
    $service->evaluateAllComponentsForOutput($output_sem1, 1);

    expect($service->getOutputValues()['SEM1RESULT'])->toMatchArray([
        'GT' => [
            'total' => 390,
            'label' => null,
            'total_grade' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'output_type' => null,
            'report_card_output_component' => $output_sem1->components->where('code', 'GT')->first(),
        ],
        'GW' =>  [
            'total' => 5,
            'label' => null,
            'total_grade' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'output_type' => null,
            'report_card_output_component' => $output_sem1->components->where('code', 'GW')->first(),
        ],
    ]);

    // evaluate priority 2
    $service->evaluateAllComponentsForOutput($output_sem1, 2);

    expect($service->getOutputValues()['SEM1RESULT'])->toMatchArray([
        'GA' => [
            'total' => 78,
            'label' => null,
            'total_grade' => null,
            'subject_id' => null,
            'grading_scheme_id' => null,
            'weightage_multiplier' => null,
            'weightage_total' => null,
            'output_type' => null,
            'report_card_output_component' => $output_sem1->components->where('code', 'GA')->first(),
        ],
    ]);

});


test('validateResultSourcesInOutputBeforeCompilation', function() {

    $student = Student::factory()->create();
    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // make sure the result source under compilation must have scores all entered.
    $errors = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->validateResultSourcesInOutputBeforeCompilation($sgf->outputs->where('code', 'SEM1RESULT')->first(), false);

    // Errors only expected for subjects 01, 02, 03, 04 as 70 is excluded (not in student class)
    expect($errors)->toMatchArray([
        'No score entered for SEM1EXAM > ' . $this->subjects[0]->name . ' > ' . 'FINAL',
        'No score entered for SEM1EXAM > ' . $this->subjects[1]->name . ' > ' . 'FINAL',
        'No grade entered for SEM1EXAM > ' . $this->subjects[2]->name,     // for grading_type = grade
        'No score entered for SEM1EXAM > ' . $this->subjects[3]->name . ' > ' . 'HOMEWORK',
        'No score entered for SEM1EXAM > ' . $this->subjects[3]->name . ' > ' . 'FINAL',
    ]);

});


test('getResultSourceSubjectsFromOutput', function() {

    $student = Student::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'is_active' => true
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    $results = app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->getResultSourceSubjectsFromOutput($sgf->outputs->where('code', 'SEM1RESULT')->first());

    expect($results)->toHaveKeys(['SEM1EXAM'])
        ->and($results['SEM1EXAM'])->toMatchArray([
            '01','02','03','04'
        ]);

});

test('compileResultSourceScoresUsedInOutput', function() {

    $student = Student::factory()->create();
    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();

    ClassSubjectStudent::factory(4)->state(new Sequence(
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    // setup student's grading framework
    $sgf = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'))
        ->getStudentGradingFramework();

    // insert random scores
    foreach ( ResultSourceSubjectComponent::all() as $component ) {
        $component->actual_score = rand(0, 100);
        $component->save();
    }

    $grades = ['A', 'B', 'C', 'D', 'F'];

    foreach ( ResultSourceSubject::where('grading_type', ResultSourceSubject::GRADING_TYPE_GRADE)->get() as $result_source_subject ) {
        $result_source_subject->actual_score_grade = $grades[rand(0, 4)];
        $result_source_subject->save();
    }

    // assuming we are generating report card for output SEM1RESULT
    $output = $sgf->outputs->where('code', 'SEM1RESULT')->first();
    app()->make(GeneralReportCardOutputService::class)
        ->setStudentGradingFramework($sgf)
        ->compileResultSourceScoresUsedInOutput($output);

    // Subject Code 70 is excluded (student not taking)
    foreach ( $sgf->resultSources->where('code', 'SEM1EXAM')->first()->subjects as $result_source_subject ) {
        
        if ($result_source_subject->subject_id == $this->subjects[4]->id){
            continue;
        }

        if ( $result_source_subject->grading_type === ResultSourceSubject::GRADING_TYPE_GRADE ) {
            expect($result_source_subject->actual_score_grade)->not()->toBeNull()
                ->and($result_source_subject->actual_score)->toBeNull();
        }
        else{
            $subject_score = 0;

            foreach ( $result_source_subject->components as $component ) {
                $component_score = bcmul($component->actual_score, ($component->weightage_percent / 100), 2);
                $subject_score = bcadd($subject_score, $component_score, 2);
            }
            expect($result_source_subject->actual_score_grade)->toBeNull()
                ->and($result_source_subject->actual_score)->toEqual($subject_score);
        }
    }

});
