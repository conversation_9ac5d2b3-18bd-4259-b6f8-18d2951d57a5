<?php

use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Enums\ExportType;
use App\Enums\MerchantType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Http\Resources\CurrencyResource;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\ClassModel;
use App\Models\Currency;
use App\Models\EcommerceOrder;
use App\Models\EcommerceOrderItem;
use App\Models\EcommerceProduct;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\Merchant;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Repositories\CurrencyRepository;
use App\Services\DocumentPrintService;
use App\Services\Report\EcommerceCanteenReportService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\CurrencySeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

beforeEach(function () {
    $this->seed([
        CurrencySeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->canteenReportService = app(EcommerceCanteenReportService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    $this->user = User::factory()->create();

    //prepare data
    $this->grades = Grade::factory(2)
        ->state(new Sequence(
            [
                'sequence' => 1
            ],
            [
                'sequence' => 2
            ],
        ))
        ->create();
    $this->students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Student 1',
        ],
        [
            'name->en' => 'Student 2',
        ],
    ))->create();

    $this->classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name' => 'Class 1',
            'grade_id' => $this->grades[0]->id
        ],
        [
            'name' => 'Class 2',
            'grade_id' => $this->grades[1]->id
        ],
    ))->create();

    $this->semester_settings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'Semester 1'
            ],
            [
                'name' => 'Semester 2'
            ],
        ))
        ->create();
    $this->semester_classes = SemesterClass::factory(2)
        ->state(new Sequence(
            [
                'class_id' => $this->classes[0]->id,
                'semester_setting_id' => $this->semester_settings[0]->id,
            ],
            [
                'class_id' => $this->classes[1]->id,
                'semester_setting_id' => $this->semester_settings[1]->id,
            ],
        ))
        ->create();

    $this->student_classes = StudentClass::factory(2)->state(new Sequence(
        [
            'student_id' => $this->students[0]->id,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
        ],
        [
            'student_id' => $this->students[1]->id,
            'semester_setting_id' => $this->semester_settings[1]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
        ]
    ))->create();

    $this->orders = EcommerceOrder::factory(6)->state(new Sequence(
        [
            'merchant_type' => MerchantType::BOOKSHOP,
            'order_reference_number' => '123',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[0]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PENDING,
            'recipient_student_class_id' => $this->student_classes[0]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '456',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[0]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[0]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '789',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[1]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[1]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '7891',
            'buyer_userable_type' => Guardian::class,
            'buyer_userable_id' => 3,
            'status' => EcommerceOrderStatus::PROCESSING,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => null,
            'created_at' => Carbon::parse('2024-02-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '7892',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[1]->id,
            'status' => EcommerceOrderStatus::PROCESSING,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[1]->id,
            'created_at' => Carbon::parse('2024-02-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '789-1',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[0]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[0]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
    ))->create();

    $this->merchants = Merchant::factory(2)->state(new Sequence(
        [
            'name' => 'Merchant 1',
            'type' => MerchantType::BOOKSHOP
        ],
        [
            'name' => 'Merchant 2',
            'type' => MerchantType::CANTEEN
        ]
    ))->create();

    $this->order_items = EcommerceOrderItem::factory(7)->state(new Sequence(
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 1,
            'amount_before_tax' => 1,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[0]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 2,
            'amount_before_tax' => 4,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[0]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 1,
            'amount_before_tax' => 1,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[1]->id,
            'product_delivery_date' => Carbon::parse('2024-01-02 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 3,
            'amount_before_tax' => 6,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[2]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 5,
            'amount_before_tax' => 10,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[3]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 5,
            'amount_before_tax' => 5,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[4]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 3,
            'amount_before_tax' => 6,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[5]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
    ))->create();

    SnappyPdf::fake();
    Excel::fake();

    Carbon::setTestNow(Carbon::parse('2024-01-01 00:00:00'));
});

test('getReportByClass()', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClass($filters);

    foreach ($response as $date) {
        expect($date['date']->isBetween($filters['start_product_delivery_date'], $filters['end_product_delivery_date']))->toBeTrue()
            ->and($date['classes'][0])->toMatchArray(
                [
                    'class_name' => $this->classes[0]->name,
                    'quantity' => 1,
                    'order_items' => [
                        [
                            'product_name' => $this->order_items[0]->product_name,
                            'quantity' => 1,
                            'students' => [
                                [
                                    'name' => $this->students[0]->getFormattedTranslations('name'),
                                    'quantity' => 1,
                                ]
                            ]
                        ]
                    ]
                ]
            );
    }
});

test('getReportByClass() check is sort by grade sequence and class code', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-03',
        'end_product_delivery_date' => '2024-01-03',
    ];

    $response = $this->canteenReportService->getReportByClass($filters);

    expect($response[0]['classes'][0]['grade_sequence'])->toBe(2)
        ->and($response[0]['classes'][1]['grade_sequence'])->toBe(1);
});

test('getReportByClass() only get status completed and payment paid', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-01',
        'end_product_delivery_date' => '2024-02-03',
    ];

    $response = $this->canteenReportService->getReportByClass($filters);

    expect($response[0]['classes'][0])->toMatchArray([
        'class_name' => $this->classes[1]->name,
        'class_code' => $this->classes[1]->code,
        'grade_sequence' => 2,
        'quantity' => 3,
        'order_items' => [
            [
                'product_name' => 'product 2',
                'quantity' => 3,
                'students' => [
                    [
                        'name' => $this->students[1]->getFormattedTranslations('name'),
                        'quantity' => 3,
                    ]
                ]
            ]
        ]
    ])->and($response[1]['classes'][0])->toMatchArray([
        'class_name' => $this->classes[0]->name,
        'class_code' => $this->classes[0]->code,
        'grade_sequence' => 1,
        'quantity' => 1,
        'order_items' => [
            [
                'product_name' => 'product 1',
                'quantity' => 1,
                'students' => [
                    [
                        'name' => $this->students[0]->getFormattedTranslations('name'),
                        'quantity' => 1,
                    ]
                ]
            ]
        ]
    ]);

});

test('getReportByClass() export pdf', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClass($filters);

    $report_data = ['data' => $response];
    $report_view_name = 'reports.ecommerce.canteens.by-classes';
    $file_name = 'ecommerce-canteen-report-by-classes';

    $export_type = ExportType::from(ExportType::PDF->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();


    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    foreach ($report_data['data'] as $date) {
        SnappyPdf::assertSee('Canteen Report By Class');
        SnappyPdf::assertSee('Order For : ' . $date['date']->format('d M Y'));
        SnappyPdf::assertSee('Print Date : ' . now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A'));

        foreach ($date['classes'] as $class) {
            SnappyPdf::assertSee('Class : ' . $class['class_name'] . ' (' . $class['quantity'] . ' Unit)');

            foreach ($class['order_items'] as $item) {
                SnappyPdf::assertSee($item['product_name'] . ' (' . $item['quantity'] . ' Unit)');

                foreach (array_chunk($item['students'], 2) as $students) {
                    SnappyPdf::assertSee($students[0]['name'] . ' (' . $students[0]['quantity'] . ' Unit)');

                    if (isset($students[1])) {
                        SnappyPdf::assertSee($students[1]['name'] . ' (' . $students[1]['quantity'] . ' Unit)');
                    }
                }
            }
        }
    }
});

test('getReportByClass() export excel', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClass($filters);

    $report_data = ['data' => $response];
    $report_view_name = 'reports.ecommerce.canteens.by-classes';
    $file_name = 'ecommerce-canteen-report-by-classes';

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($report_data['data'] as $date) {
                $view->assertSee('Canteen Report By Class');
                $view->assertSee('Order For : ' . $date['date']->format('d M Y'));
                $view->assertSee('Print Date : ' . now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A'));

                foreach ($date['classes'] as $class) {
                    $view->assertSee('Class : ' . $class['class_name'] . ' (' . $class['quantity'] . ' Unit)');
                    foreach ($class['order_items'] as $item) {
                        $view->assertSee($item['product_name'] . ' (' . $item['quantity'] . ' Unit)');

                        foreach (array_chunk($item['students'], 2) as $students) {
                            $view->assertSee($students[0]['name'] . ' (' . $students[0]['quantity'] . ' Unit)');

                            if (isset($students[1])) {
                                $view->assertSee($students[1]['name'] . ' (' . $students[1]['quantity'] . ' Unit)');
                            }
                        }
                    }
                }
            }

            return true;
        }
    );
});

test('getReportByClassWeekly()', function () {
    $filters = [
        'semester_class_ids' => [$this->semester_classes[0]->id],
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClassWeekly($filters);
    $response = collect($response)->sortBy('class_name');

    expect($response['classes'])->toMatchArray(
        [
            [
                'class_name' => $this->classes[0]->name,
                'students' => [
                    [
                        'name' => $this->students[0]->getFormattedTranslations('name'),
                        'dates' => [
                            [
                                'date' => Carbon::parse('2024-01-02', 'UTC'),
                                'order_items' => [
                                    [
                                        'product_name' => $this->order_items[0]->product_name,
                                        'quantity' => 1,
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    );
});

test('getReportByClassWeekly() only get status completed and payment paid ', function () {
    $filters = [
        'semester_class_ids' => [$this->semester_classes[0]->id, $this->semester_classes[1]->id],
        'start_product_delivery_date' => '2024-01-01',
        'end_product_delivery_date' => '2024-01-03',
    ];

    $response = $this->canteenReportService->getReportByClassWeekly($filters);

    expect($response['classes'])->toMatchArray(
        [
            [
                'class_name' => $this->classes[0]->name,
                'students' => [
                    [
                        'name' => $this->students[0]->getFormattedTranslations('name'),
                        'dates' => [
                            [
                                'date' => Carbon::parse('2024-01-01', 'UTC'),
                                'order_items' => []
                            ],
                            [
                                'date' => Carbon::parse('2024-01-02', 'UTC'),
                                'order_items' => [
                                    [
                                        'product_name' => 'product 1',
                                        'quantity' => 1,
                                    ]
                                ]
                            ],
                            [
                                'date' => Carbon::parse('2024-01-03', 'UTC'),
                                'order_items' => [
                                    [
                                        'product_name' => 'product 2',
                                        'quantity' => 3,
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'class_name' => $this->classes[1]->name,
                'students' => [
                    [
                        'name' => $this->students[1]->getFormattedTranslations('name'),
                        'dates' => [
                            [
                                'date' => Carbon::parse('2024-01-01', 'UTC'),
                                'order_items' => []
                            ],
                            [
                                'date' => Carbon::parse('2024-01-02', 'UTC'),
                                'order_items' => []
                            ],
                            [
                                'date' => Carbon::parse('2024-01-03', 'UTC'),
                                'order_items' => [
                                    [
                                        'product_name' => 'product 2',
                                        'quantity' => 3,
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    );
});

test('getReportByClassWeekly() export pdf', function () {
    $filters = [
        'semester_class_ids' => [$this->semester_classes[0]->id],
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClassWeekly($filters);

    $start_date = Carbon::parse($filters['start_product_delivery_date']);
    $end_date = Carbon::parse($filters['end_product_delivery_date']);

    $report_data = ['data' => $response, 'start_date' => $start_date, 'end_date' => $end_date];
    $report_view_name = 'reports.ecommerce.canteens.by-classes-weekly';
    $file_name = 'ecommerce-canteen-report-by-classes-weekly';

    $export_type = ExportType::from(ExportType::PDF->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();


    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    $expected_headers = ['S/N', 'Student Name'];

    foreach ($report_data['data']['dates'] as $period) {
        $expected_headers[] = $period->format('D') . ' (' . $period->format('d M Y') . ')';
    }

    foreach ($report_data['data']['classes'] as $class) {
        SnappyPdf::assertSee('Canteen Report By Class ' . $class['class_name']);
        SnappyPdf::assertSee('Order For : ' . $report_data['start_date']->format('d M Y') . ' - ' . $report_data['end_date']->format('d M Y'));
        SnappyPdf::assertSee('Print Date : ' . now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A'));

        foreach ($expected_headers as $header) {
            SnappyPdf::assertSee($header);
        }
        $i = 1;
        foreach ($class['students'] as $student) {
            SnappyPdf::assertSee($i);
            SnappyPdf::assertSee($student['name']);
            foreach ($student['dates'] as $date) {
                foreach ($date['order_items'] as $item) {
                    SnappyPdf::assertSee($item['product_name'] . ' (x' . $item['quantity'] . ')');
                }
            }

            $i++;
        }
    }
});

test('getReportByClassWeekly() export excel', function () {
    $filters = [
        'semester_class_ids' => [$this->semester_classes[0]->id],
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
    ];

    $response = $this->canteenReportService->getReportByClassWeekly($filters);

    $start_date = Carbon::parse($filters['start_product_delivery_date']);
    $end_date = Carbon::parse($filters['end_product_delivery_date']);

    $report_data = ['data' => $response, 'start_date' => $start_date, 'end_date' => $end_date];
    $report_view_name = 'reports.ecommerce.canteens.by-classes-weekly';
    $file_name = 'ecommerce-canteen-report-by-classes-weekly';

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();


    $expected_headers = ['S/N', 'Student Name'];

    foreach ($report_data['data']['dates'] as $period) {
        $expected_headers[] = $period->format('D') . ' (' . $period->format('d M Y') . ')';
    }

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($expected_headers, $report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($report_data['data']['classes'] as $class) {
                $view->assertSee('Canteen Report By Class ' . $class['class_name']);
                $view->assertSee('Order For : ' . $report_data['start_date']->format('d M Y') . ' - ' . $report_data['end_date']->format('d M Y'));
                $view->assertSee('Print Date : ' . now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A'));

                foreach ($expected_headers as $header) {
                    $view->assertSee($header);
                }
                $i = 1;
                foreach ($class['students'] as $student) {
                    $view->assertSee($i);
                    $view->assertSee($student['name']);
                    foreach ($student['dates'] as $date) {
                        foreach ($date['order_items'] as $item) {
                            $view->assertSee($item['product_name'] . ' (x' . $item['quantity'] . ')');
                        }
                    }

                    $i++;
                }
            }

            return true;
        }
    );
});

test('getReportByMerchant()', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->canteenReportService->getReportByMerchant($filters);

    foreach ($response as $date) {
        $expected_result = [
            [
                'name' => $this->merchants[1]->name,
                'grades' => [
                    [
                        'order_items' => [
                            [
                                'product_name' => $this->order_items[0]->product_name,
                                'quantity' => 1,
                                'data' => [
                                    [
                                        'class_name' => $this->classes[0]->name,
                                        'quantity' => 1,
                                    ]
                                ]
                            ]
                        ],
                        'semester_classes' => [
                            [
                                'class_name' => $this->classes[0]->name,
                            ]
                        ]
                    ]
                ],
                'order_items' => [
                    [
                        'product_name' => $this->order_items[0]->product_name,
                        'quantity' => 1,
                    ]
                ]
            ]
        ];

        expect($date['date']->isBetween($filters['start_product_delivery_date'], $filters['end_product_delivery_date']))->toBeTrue()
            ->and($date['merchants'])->toMatchArray($expected_result);
    }
});

test('getReportByMerchant() only get status completed and payment paid', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-01',
        'end_product_delivery_date' => '2024-01-03',
        'semester_setting_id' => $this->semester_settings[1]->id,
    ];

    $response = $this->canteenReportService->getReportByMerchant($filters);

    foreach ($response as $date) {
        $expected_result = [
            [
                'name' => $this->merchants[1]->name,
                'grades' => [
                    [
                        'order_items' => [
                            [
                                'product_name' => 'product 2',
                                'quantity' => 3,
                                'data' => [
                                    [
                                        'class_name' => $this->classes[1]->name,
                                        'quantity' => 3,
                                    ]
                                ]
                            ]
                        ],
                        'semester_classes' => [
                            [
                                'class_name' => $this->classes[1]->name,
                            ]
                        ]
                    ]
                ],
                'order_items' => [
                    [
                        "product_name" => 'product 2',
                        "quantity" => 3

                    ]
                ]
            ]
        ];

        expect($date['merchants'])
            ->toMatchArray($expected_result);
    }
});


test('getReportByMerchant() export pdf', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->canteenReportService->getReportByMerchant($filters);

    $start_date = Carbon::parse($filters['start_product_delivery_date']);
    $end_date = Carbon::parse($filters['end_product_delivery_date']);

    $report_data = [
        'data' => $response,
        'title' => 'Canteen Report By Merchant',
        'subtitle' => 'Order For' . ' : ' . $start_date->format('d M Y') . ' - ' . $end_date->format('d M Y'),
    ];
    $report_view_name = 'reports.ecommerce.canteens.by-merchants';
    $file_name = 'ecommerce-canteen-report-by-merchants';

    $export_type = ExportType::from(ExportType::PDF->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();


    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);
    SnappyPdf::assertSee($report_data['subtitle']);

    foreach ($report_data['data'] as $date) {
        SnappyPdf::assertSee($date['date']->format('d M Y'));

        foreach ($date['merchants'] as $merchant) {
            SnappyPdf::assertSee($merchant['name']);

            foreach ($merchant['grades'] as $grade) {
                SnappyPdf::assertSee('Item Name');

                foreach ($grade['semester_classes'] as $semester_class) {
                    SnappyPdf::assertSee($semester_class['class_name']);
                }

                SnappyPdf::assertSee('Total');

                foreach ($grade['order_items'] as $item) {
                    SnappyPdf::assertSee($item['product_name']);

                    foreach ($item['data'] as $data) {
                        SnappyPdf::assertSee($data['quantity'] ?: '-');
                    }

                    SnappyPdf::assertSee($item['quantity']);
                }
            }

            SnappyPdf::assertSee('Item Name');
            SnappyPdf::assertSee('Total');

            foreach ($merchant['order_items'] as $item) {
                SnappyPdf::assertSee($item['product_name']);
                SnappyPdf::assertSee($item['quantity']);
            }
        }
    }
});

test('getReportByMerchant() export excel', function () {
    $filters = [
        'start_product_delivery_date' => '2024-01-02',
        'end_product_delivery_date' => '2024-01-02',
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->canteenReportService->getReportByMerchant($filters);

    $start_date = Carbon::parse($filters['start_product_delivery_date']);
    $end_date = Carbon::parse($filters['end_product_delivery_date']);

    $report_data = [
        'data' => $response,
        'title' => 'Canteen Report By Merchant',
        'subtitle' => 'Order For' . ' : ' . $start_date->format('d M Y') . ' - ' . $end_date->format('d M Y'),
    ];

    $report_view_name = 'reports.ecommerce.canteens.by-merchants';
    $file_name = 'ecommerce-canteen-report-by-merchants';

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($report_data['title']);
            $view->assertSee($report_data['subtitle']);

            foreach ($report_data['data'] as $date) {
                $view->assertSee($date['date']->format('d M Y'));

                foreach ($date['merchants'] as $merchant) {
                    $view->assertSee($merchant['name']);

                    foreach ($merchant['grades'] as $grade) {
                        $view->assertSee('Item Name');

                        foreach ($grade['semester_classes'] as $semester_class) {
                            $view->assertSee($semester_class['class_name']);
                        }

                        $view->assertSee('Total');

                        foreach ($grade['order_items'] as $item) {
                            $view->assertSee($item['product_name']);

                            foreach ($item['data'] as $data) {
                                $view->assertSee($data['quantity'] ?: '-');
                            }

                            $view->assertSee($item['quantity']);
                        }
                    }

                    $view->assertSee('Item Name');
                    $view->assertSee('Total');

                    foreach ($merchant['order_items'] as $item) {
                        $view->assertSee($item['product_name']);
                        $view->assertSee($item['quantity']);
                    }
                }
            }

            return true;
        }
    );
});


test('getOrderListByStudent()', function () {
    $student = Student::factory()->create();

    $semester_class = SemesterClass::factory()->create();

    $student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => $semester_class->classModel->type,
        'student_id' => $student->id,
    ]);

    Carbon::setTestNow('2024-09-01 09:10:00');

    // $student has 2 orders
    $order_1_meal_date = '2024-09-01';
    $order_1 = EcommerceOrder::factory()->withOrderItems(product_delivery_date: $order_1_meal_date)->create([
        'merchant_type' => MerchantType::CANTEEN,
        'user_id' => $this->user->id,
        'buyer_userable_id' => $student->id,
        'recipient_student_class_id' => $student_class->id,
        'created_at' => $order_1_meal_date,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
    ]);
    $order_1_item = $order_1->items->first();

    $invoice_item_1 = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
    ]);

    UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => $invoice_item_1->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice_item_1->id,
        'billable_item_type' => EcommerceOrderItem::class,
        'billable_item_id' => $order_1_item->id,
    ]);

    $order_2_meal_date = '2024-09-02';
    $order_2 = EcommerceOrder::factory()->withOrderItems(product_delivery_date: $order_2_meal_date)->create([
        'merchant_type' => MerchantType::CANTEEN,
        'user_id' => $this->user->id,
        'buyer_userable_id' => $student->id,
        'recipient_student_class_id' => $student_class->id,
        'created_at' => $order_2_meal_date,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
    ]);

    $order_2_item = $order_2->items->first();

    $invoice_item_2 = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000002',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
    ]);

    UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => $invoice_item_2->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice_item_2->id,
        'billable_item_type' => EcommerceOrderItem::class,
        'billable_item_id' => $order_2_item->id,
    ]);

    $payload = [
        'from_date' => '2024-09-01', // filter by product_delivery date
        'to_date' => '2024-09-30',
        'student_id' => $student->id,
    ];

    $response = $this->canteenReportService->getOrderListByStudent($payload);

    expect($response['orders'])->toEqual([
        [
            'receipt_number' => $invoice_item_2->reference_no,
            'meal_date' => $order_2_meal_date,
            'items' => $order_2_item->product_name,
            'items_quantity' => $order_2_item->quantity,
        ],
        [
            'receipt_number' => $invoice_item_1->reference_no,
            'meal_date' => $order_1_meal_date,
            'items' => $order_1_item->product_name,
            'items_quantity' => $order_1_item->quantity,
        ],
    ]);

    expect($response['student'])
        ->toHaveKey('id', $student->id)
        ->toHaveKey('student_number', $student->student_number);
});

test('getOrderByMerchantDailySales()', function () {
    /**
     * 2 order on same days
     * 1 order on next day
     */
    $merchant = Merchant::factory()->create(['type' => MerchantType::CANTEEN->value]);

    Carbon::setTestNow('2024-09-01 09:10:00');

    // 2 order for $merchant on same days
    $meal_date_1 = '2024-09-01';

    $ecommerce_product_1 = EcommerceProduct::factory()->create([
        'merchant_id' => $merchant->id,
    ]);

    $order_1 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_1_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_1,
        'merchant_id' => $merchant->id,
        'order_id' => $order_1->id,
        'product_id' => $ecommerce_product_1->id,
    ]);


    $ecommerce_product_2 = EcommerceProduct::factory()->create([
        'merchant_id' => $merchant->id,
    ]);

    $order_2 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_2_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_1,
        'merchant_id' => $merchant->id,
        'order_id' => $order_2->id,
        'product_id' => $ecommerce_product_2->id,
    ]);


    $meal_date_1_total_sales =
        round(($order_1_item->amount_before_tax +
            $order_1_item->tax_amount +
            $order_2_item->amount_before_tax +
            $order_2_item->tax_amount), 2);

    $meal_date_1_total_item_sold = $order_1_item->quantity + $order_2_item->quantity;

    $meal_date_1_avg_per_item = round((($meal_date_1_total_sales) / ($meal_date_1_total_item_sold)), 2);
    $meal_date_1_avg_per_receipt = round((($meal_date_1_total_sales) / (2)), 2);


    // 1 order for $merchant on next day
    $meal_date_3 = '2024-09-02';

    $order_3 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_3,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_3_item = EcommerceOrderItem::factory()->create([ // sales for $ecommerce_product_2->id
        'product_delivery_date' => $meal_date_3,
        'merchant_id' => $merchant->id,
        'order_id' => $order_3->id,
        'product_id' => $ecommerce_product_2->id,
    ]);


    $meal_date_3_total_sales = round(($order_3_item->amount_before_tax + $order_3_item->tax_amount), 2);
    $meal_date_3_total_item_sold = $order_3_item->quantity;
    $meal_date_3_avg_per_item = round((($meal_date_3_total_sales) / ($meal_date_3_total_item_sold)), 2);
    $meal_date_3_avg_per_receipt = round((($meal_date_3_total_sales) / 1), 2);


    // merchant total
    $merchant_total_sales = $meal_date_1_total_sales + $meal_date_3_total_sales;
    $merchant_total_item_sold = $meal_date_1_total_item_sold + $meal_date_3_total_item_sold;
    $merchant_total_receipt = 3; // 3 orders
    $merchant_avg_per_item = round((($merchant_total_sales) / $merchant_total_item_sold), 2);
    $merchant_avg_per_receipt = round((($merchant_total_sales) / $merchant_total_receipt), 2);


    $payload = [
        'from_date' => '2024-09-01', // filter by product_delivery date
        'to_date' => '2024-09-04',
        'merchant_id' => $merchant->id,
    ];


    $response = $this->canteenReportService->getOrderByMerchantDailySales($payload);

    expect($response['orders'])->toEqual([
        [
            'meal_date' => '2024-09-04',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => '2024-09-03',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => $meal_date_3,
            'total_sales' => $meal_date_3_total_sales,
            'total_item_sold' => $meal_date_3_total_item_sold,
            'total_receipt' => 1, // 1 order
            'avg_per_item' => $meal_date_3_avg_per_item,
            'avg_per_receipt' => $meal_date_3_avg_per_receipt,
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => $meal_date_1,
            'total_sales' => $meal_date_1_total_sales,
            'total_item_sold' => $meal_date_1_total_item_sold,
            'total_receipt' => 2, // 2 order
            'avg_per_item' => $meal_date_1_avg_per_item,
            'avg_per_receipt' => $meal_date_1_avg_per_receipt,
            'merchant_name' => $merchant->name,
        ],
    ])
        ->and($response['merchant_total_sales'])->toEqual($merchant_total_sales)
        ->and($response['merchant_total_item_sold'])->toEqual($merchant_total_item_sold)
        ->and($response['merchant_total_receipt'])->toEqual($merchant_total_receipt)
        ->and($response['merchant_avg_per_item'])->toEqual($merchant_avg_per_item)
        ->and($response['merchant_avg_per_receipt'])->toEqual($merchant_avg_per_receipt)
        ->and($response['merchant']->toArray())->toEqual($merchant->toArray())
        ->and($response['currency'])->toEqual(new CurrencyResource(Currency::first()));


    /**
     * GETTING EMPTY DATA FOR $merchant->id
     */
    $payload = [
        'from_date' => '2025-09-01', // filter by product_delivery date
        'to_date' => '2025-09-04',
        'merchant_id' => $merchant->id,
    ];

    $response = $this->canteenReportService->getOrderByMerchantDailySales($payload);

    expect($response['orders'])->toEqual([
        [
            'meal_date' => '2025-09-04',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => '2025-09-03',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => '2025-09-02',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
        [
            'meal_date' => '2025-09-01',
            'total_sales' => '0.00',
            'total_item_sold' => 0,
            'total_receipt' => 0,
            'avg_per_item' => '0.00',
            'avg_per_receipt' => '0.00',
            'merchant_name' => $merchant->name,
        ],
    ])
        ->and($response['merchant_total_sales'])->toEqual(0)
        ->and($response['merchant_total_item_sold'])->toEqual(0)
        ->and($response['merchant_total_receipt'])->toEqual(0)
        ->and($response['merchant_avg_per_item'])->toEqual(0)
        ->and($response['merchant_avg_per_receipt'])->toEqual(0)
        ->and($response['merchant']->toArray())->toEqual($merchant->toArray())
        ->and($response['currency'])->toEqual(new CurrencyResource(Currency::first()));
});

test('getOrderByDailySalesGroupByMerchant()', function () {
    /**
     * 3 order on for 1 merchant, 2 order on same day, 1 order in another day
     * 1 order on for 1 merchant
     */

    DB::table('merchants')->truncate();

    $merchant_1 = Merchant::factory()->create(['name' => 'AA Jon', 'type' => MerchantType::CANTEEN->value]);

    Carbon::setTestNow('2024-09-01 09:10:00');

    // 2 order for $merchant_1 on same day
    $meal_date_1 = '2024-09-01';

    $ecommerce_product_1 = EcommerceProduct::factory()->create([
        'merchant_id' => $merchant_1->id,
    ]);

    $order_1 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_1_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_1,
        'merchant_id' => $merchant_1->id,
        'order_id' => $order_1->id,
        'product_id' => $ecommerce_product_1->id,
    ]);

    $order_2 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_2_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_1,
        'merchant_id' => $merchant_1->id,
        'order_id' => $order_2->id,
        'product_id' => $ecommerce_product_1->id,
    ]);


    $meal_date_1_total_sales =
        round(($order_1_item->amount_before_tax +
            $order_1_item->tax_amount +
            $order_2_item->amount_before_tax +
            $order_2_item->tax_amount), 2);

    $meal_date_1_total_item_sold = $order_1_item->quantity + $order_2_item->quantity;
    $meal_date_1_avg_per_item = round((($meal_date_1_total_sales) / ($meal_date_1_total_item_sold)), 2);
    $meal_date_1_avg_per_receipt = round((($meal_date_1_total_sales) / (2)), 2);


    // 1 order for $merchant_1 on another day
    $meal_date_2 = '2024-09-02';

    $ecommerce_product_2 = EcommerceProduct::factory()->create([
        'merchant_id' => $merchant_1->id,
    ]);

    $another_day_order = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_2,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $another_day_order_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_2,
        'merchant_id' => $merchant_1->id,
        'order_id' => $another_day_order->id,
        'product_id' => $ecommerce_product_2->id,
    ]);


    $another_day_total_sales = round(($another_day_order_item->amount_before_tax + $another_day_order_item->tax_amount), 2);
    $another_day_total_item_sold = $another_day_order_item->quantity;
    $another_day_avg_per_item = round((($another_day_total_sales) / ($another_day_total_item_sold)), 2);
    $another_day_avg_per_receipt = round((($another_day_total_sales) / (1)), 2);


    $merchant_AA_Jon_total_sales = $meal_date_1_total_sales + $another_day_total_sales;
    $merchant_AA_Jon_total_item_sold = $meal_date_1_total_item_sold + $another_day_total_item_sold;
    $merchant_AA_Jon_total_receipt = 3; // 3 orders
    $merchant_AA_Jon_avg_per_item = round((($merchant_AA_Jon_total_sales) / $merchant_AA_Jon_total_item_sold), 2);
    $merchant_AA_Jon_avg_per_receipt = round((($merchant_AA_Jon_total_sales) / $merchant_AA_Jon_total_receipt), 2);


    // 1 order for $merchant_2
    $merchant_2 = Merchant::factory()->create(['name' => 'ZZ Koko', 'type' => MerchantType::CANTEEN->value]);

    $meal_date_3 = '2024-09-03';

    $ecommerce_product_3 = EcommerceProduct::factory()->create([
        'merchant_id' => $merchant_2->id,
    ]);

    $order_3 = EcommerceOrder::factory()->create([
        'created_at' => $meal_date_3,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ]);

    $order_3_item = EcommerceOrderItem::factory()->create([
        'product_delivery_date' => $meal_date_3,
        'merchant_id' => $merchant_2->id,
        'order_id' => $order_3->id,
        'product_id' => $ecommerce_product_3->id,
    ]);


    $meal_date_3_total_sales = round(($order_3_item->amount_before_tax + $order_3_item->tax_amount), 2);
    $meal_date_3_total_item_sold = $order_3_item->quantity;
    $meal_date_3_avg_per_item = round((($meal_date_3_total_sales) / ($meal_date_3_total_item_sold)), 2);
    $meal_date_3_avg_per_receipt = round((($meal_date_3_total_sales) / (1)), 2);


    $merchant_ZZ_Koko_total_sales = $meal_date_3_total_sales;
    $merchant_ZZ_Koko_total_item_sold = $meal_date_3_total_item_sold;
    $merchant_ZZ_Koko_total_receipt = 1; // 1 order
    $merchant_ZZ_Koko_avg_per_item = round((($merchant_ZZ_Koko_total_sales) / $merchant_ZZ_Koko_total_item_sold), 2);
    $merchant_ZZ_Koko_avg_per_receipt = round((($merchant_ZZ_Koko_total_sales) / $merchant_ZZ_Koko_total_receipt), 2);


    $payload = [
        'from_date' => '2024-09-01', // filter by product_delivery date
        'to_date' => '2024-09-04',
    ];

    $response = $this->canteenReportService->getOrderByDailySalesGroupByMerchant($payload);

    expect($response['orders'])
        ->toEqual([
            'AA Jon' => [
                'orders' => [
                    [
                        'meal_date' => '2024-09-04',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => '2024-09-03',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => $meal_date_2,
                        'total_sales' => $another_day_total_sales,
                        'total_item_sold' => $another_day_total_item_sold,
                        'total_receipt' => 1, // 1 order
                        'avg_per_item' => $another_day_avg_per_item,
                        'avg_per_receipt' => $another_day_avg_per_receipt,
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => $meal_date_1,
                        'total_sales' => $meal_date_1_total_sales,
                        'total_item_sold' => $meal_date_1_total_item_sold,
                        'total_receipt' => 2, // 2 order
                        'avg_per_item' => $meal_date_1_avg_per_item,
                        'avg_per_receipt' => $meal_date_1_avg_per_receipt,
                        'merchant_name' => 'AA Jon',
                    ],
                ],
                'merchant_total_sales' => $merchant_AA_Jon_total_sales,
                'merchant_total_item_sold' => $merchant_AA_Jon_total_item_sold,
                'merchant_total_receipt' => $merchant_AA_Jon_total_receipt,
                'merchant_avg_per_item' => $merchant_AA_Jon_avg_per_item,
                'merchant_avg_per_receipt' => $merchant_AA_Jon_avg_per_receipt,
            ],
            'ZZ Koko' => [
                'orders' => [
                    [
                        'meal_date' => '2024-09-04',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => $meal_date_3,
                        'total_sales' => $meal_date_3_total_sales,
                        'total_item_sold' => $meal_date_3_total_item_sold,
                        'total_receipt' => 1, // 1 order
                        'avg_per_item' => $meal_date_3_avg_per_item,
                        'avg_per_receipt' => $meal_date_3_avg_per_receipt,
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => '2024-09-02',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => '2024-09-01',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                ],
                'merchant_total_sales' => $merchant_ZZ_Koko_total_sales,
                'merchant_total_item_sold' => $merchant_ZZ_Koko_total_item_sold,
                'merchant_total_receipt' => $merchant_ZZ_Koko_total_receipt,
                'merchant_avg_per_item' => $merchant_ZZ_Koko_avg_per_item,
                'merchant_avg_per_receipt' => $merchant_ZZ_Koko_avg_per_receipt,
            ],
        ])
        ->and($response['currency'])->toEqual(new CurrencyResource(Currency::first()));


    /**
     * GETTING EMPTY DATA FOR ALL MERCHANT
     */
    $payload = [
        'from_date' => '2025-09-01', // filter by product_delivery date
        'to_date' => '2025-09-04',
    ];

    $response = $this->canteenReportService->getOrderByDailySalesGroupByMerchant($payload);

    expect($response['orders'])
        ->toEqual([
            'AA Jon' => [
                'orders' => [
                    [
                        'meal_date' => '2025-09-04',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => '2025-09-03',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => '2025-09-02',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                    [
                        'meal_date' => '2025-09-01',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'AA Jon',
                    ],
                ],
                'merchant_total_sales' => 0,
                'merchant_total_item_sold' => 0,
                'merchant_total_receipt' => 0,
                'merchant_avg_per_item' => 0,
                'merchant_avg_per_receipt' => 0,
            ],
            'ZZ Koko' => [
                'orders' => [
                    [
                        'meal_date' => '2025-09-04',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => '2025-09-03',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => '2025-09-02',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                    [
                        'meal_date' => '2025-09-01',
                        'total_sales' => '0.00',
                        'total_item_sold' => 0,
                        'total_receipt' => 0,
                        'avg_per_item' => '0.00',
                        'avg_per_receipt' => '0.00',
                        'merchant_name' => 'ZZ Koko',
                    ],
                ],
                'merchant_total_sales' => 0,
                'merchant_total_item_sold' => 0,
                'merchant_total_receipt' => 0,
                'merchant_avg_per_item' => 0,
                'merchant_avg_per_receipt' => 0,
            ],
        ])
        ->and($response['currency'])->toEqual(new CurrencyResource(Currency::first()));

});

test('getOrderByDailyCollection()', function () {
    Carbon::setTestNow('2024-09-01 09:10:00');

    $meal_date_1 = '2024-09-01';

    $created_orders = EcommerceOrder::factory(2)->withOrderItems(product_delivery_date: $meal_date_1)->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
    ])->loadMissing('items.merchant');

    $irrelevant_order = EcommerceOrder::factory()->withOrderItems(product_delivery_date: $meal_date_1)->create([
        'created_at' => $meal_date_1,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::BOOKSHOP,
    ])->loadMissing('items.merchant');


    $item_1 = $created_orders[0]->items[0];
    $item_2 = $created_orders[1]->items[0];

    $merchant_1 = $item_1->merchant;
    $merchant_2 = $item_2->merchant;

    $total_collection = 0;
    $total_collection += ($item_1->quantity * $item_1->product_unit_price);
    $total_collection += ($item_2->quantity * $item_2->product_unit_price);

    $expected_orders_payload = [
        "{$merchant_1->getFormattedTranslations('name')}" => [
            'total_group_quantity' => $item_1->quantity,
            'total_group_price' => ($item_1->quantity * $item_1->product_unit_price),
            'orders' => [
                [
                    'product_id' => $item_1->product_id,
                    'product_name' => $item_1->product_name,
                    'unit_price' => $item_1->product_unit_price,
                    'quantity' => $item_1->quantity,
                    'total_price' => ($item_1->quantity * $item_1->product_unit_price),
                    'merchant_name' => $merchant_1->getFormattedTranslations('name'),
                ]
            ]
        ],

        "{$merchant_2->getFormattedTranslations('name')}" => [
            'total_group_quantity' => $item_2->quantity,
            'total_group_price' => ($item_2->quantity * $item_2->product_unit_price),
            'orders' => [
                [
                    'product_id' => $item_2->product_id,
                    'product_name' => $item_2->product_name,
                    'unit_price' => $item_2->product_unit_price,
                    'quantity' => $item_2->quantity,
                    'total_price' => ($item_2->quantity * $item_2->product_unit_price),
                    'merchant_name' => $merchant_2->getFormattedTranslations('name'),
                ]
            ]
        ],
    ];

    $currency = (new CurrencyRepository)->getCurrencyByCode(config('school.currency_code'));


    $payload = [
        'date' => $meal_date_1,
    ];


    $response = $this->canteenReportService->getOrderByDailyCollection($payload);

    expect($response)->toEqual([
        'currency' => new CurrencyResource($currency),
        'total_collection' => $total_collection,
        'orders' => $expected_orders_payload,
    ]);
});
