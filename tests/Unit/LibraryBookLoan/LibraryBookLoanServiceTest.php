<?php

use App\Enums\BookStatus;
use App\Enums\LibraryBookLoanPaymentStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Enums\LibraryPenaltyPaymentMethod;
use App\Enums\PushNotificationClickAction;
use App\Enums\PushNotificationPlatform;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Models\Book;
use App\Models\Config;
use App\Models\Currency;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\LibraryPaymentTransaction;
use App\Models\Student;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\AdHocNotificationService;
use App\Services\LibraryBookLoanService;
use App\Services\PushNotification\AndroidPushNotificationService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Mo<PERSON>y\MockInterface;

beforeEach(function () {
    $this->bookLoanService = app(LibraryBookLoanService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(LibraryBookLoan::class)->getTable();
    $this->book_table = resolve(Book::class)->getTable();
    $this->library_payment_transaction_table = resolve(LibraryPaymentTransaction::class)->getTable();
    $this->wallet_table = resolve(Wallet::class)->getTable();
    $this->wallet_transaction_table = resolve(WalletTransaction::class)->getTable();

    $config = [
        Config::LIBRARY_BORROW_LIMIT_EMPLOYEE => 3,
        Config::LIBRARY_BORROW_LIMIT_STUDENT => 2,
        Config::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        Config::LIBRARY_BORROW_LIMIT_OTHER => 4,

        Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT => true,
        Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_OTHER => false,

        Config::LIBRARY_FINE_PER_DAY_EMPLOYEE => 0.1,
        Config::LIBRARY_FINE_PER_DAY_STUDENT => 0.2,
        Config::LIBRARY_FINE_PER_DAY_LIBRARIAN => 0.1,
        Config::LIBRARY_FINE_PER_DAY_OTHER => 0,
    ];

    foreach ($config as $key => $value) {
        Config::factory()->create([
            'key' => $key,
            'value' => $value
        ]);
    }

    $this->book = Book::factory()->create([
        'status' => BookStatus::AVAILABLE,
        'book_no' => 'A1234'
    ]);

    $this->user_student = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);
    $this->student = Student::factory()->create([
        'user_id' => $this->user_student->id,
    ]);

    $this->currency = Currency::factory()->create([
        'code' => config('school.currency_code'),
    ]);

    $this->wallet = Wallet::factory()->create([
        'user_id' => $this->user_student->id,
        'balance' => 0,
        'currency_id' => $this->currency->id
    ]);

    $this->member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'userable_type' => Student::class,
        'userable_id' => $this->student->id,
    ]);
});

test('getAllBookLoans()', function () {
    $books = Book::factory(2)->create();
    $members = LibraryMember::factory(2)->create();

    $book_loans = LibraryBookLoan::factory(3)->state(new Sequence(
        [
            'member_id' => $members[0]->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(3),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::LOST,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(4),
        ]
    ))->create();

    //Filter by ids
    $payload = [
        'ids' => [$book_loans[1]->id, $book_loans[2]->id],
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[1]->id);
            },
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[2]->id);
            },
        );

    //Filter by is_overdue = true
    $payload = [
        'is_overdue' => true
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[0]->id);

                $data->due_date->toBeLessThan(now());
            }
        );

    //Filter by is_overdue = false
    $payload = [
        'is_overdue' => false
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->due_date->toBeGreaterThanOrEqual(now());
            },
            function ($data) use ($book_loans) {
                $data->due_date->toBeGreaterThanOrEqual(now());
            }
        );

    //Filter by loan_status = Borrow
    $payload = [
        'loan_status' => LibraryBookLoanStatus::BORROWED->value
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('loan_status', LibraryBookLoanStatus::BORROWED->value);
            }
        );

    //Filter by book_id
    $payload = [
        'book_id' => $books[0]->id
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($books) {
                $data->toHaveKey('book_id', $books[0]->id);
            }
        );

    //Filter by member id
    $payload = [
        'member_id' => $members[0]->id
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($members) {
                $data->toHaveKey('member_id', $members[0]->id);
            }
        );

    //Filter by payment status
    $payload = [
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($members) {
                $data->toHaveKey('penalty_payment_status', LibraryBookLoanPaymentStatus::UNPAID->value);
            }
        );

    //Filter by period loan date
    $start_loan_date = now()->subDays(4)->format('Y-m-d');
    $end_loan_date = now()->format('Y-m-d');
    $payload = [
        'period_loan_date_from' => $start_loan_date,
        'period_loan_date_to' => $end_loan_date,
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            function ($data) use ($members, $start_loan_date, $end_loan_date) {
                $data->loan_date->toBeBetween(Carbon::parse($start_loan_date), Carbon::parse($end_loan_date));
            },
            function ($data) use ($members, $start_loan_date, $end_loan_date) {
                $data->loan_date->toBeBetween(Carbon::parse($start_loan_date), Carbon::parse($end_loan_date));
            }
        );

    //Filter by period due date
    $start_due_date = now()->addDays(2)->format('Y-m-d');
    $end_due_date = now()->addDays(4)->format('Y-m-d');
    $payload = [
        'period_due_date_from' => $start_due_date,
        'period_due_date_to' => $end_due_date,
    ];
    $response = $this->bookLoanService->getAllBookLoans($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            function ($data) use ($members, $start_due_date, $end_due_date) {
                $data->due_date->toBeBetween(Carbon::parse($start_due_date), Carbon::parse($end_due_date));
            },
            function ($data) use ($members, $start_due_date, $end_due_date) {
                $data->due_date->toBeBetween(Carbon::parse($start_due_date), Carbon::parse($end_due_date));
            }
        );
});

test('getAllPaginatedBookLoans()', function () {
    $books = Book::factory(2)->create();
    $members = LibraryMember::factory(2)->create();

    $book_loans = LibraryBookLoan::factory(3)->state(new Sequence(
        [
            'member_id' => $members[0]->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(3),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::LOST,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(4),
        ]
    ))->create();

    //Filter by ids
    $payload = [
        'ids' => [$book_loans[1]->id, $book_loans[2]->id],
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[1]->id);
            },
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[2]->id);
            },
        );

    //Filter by is_overdue = true
    $payload = [
        'is_overdue' => true
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[0]->id);

                $data->due_date->toBeLessThan(now());
            }
        );

    //Filter by is_overdue = false
    $payload = [
        'is_overdue' => false
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->due_date->toBeGreaterThanOrEqual(now());
            },
            function ($data) use ($book_loans) {
                $data->due_date->toBeGreaterThanOrEqual(now());
            }
        );

    //Filter by status = Borrow
    $payload = [
        'loan_status' => LibraryBookLoanStatus::BORROWED->value
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('loan_status', LibraryBookLoanStatus::BORROWED->value);
            }
        );

    //Filter by book_id
    $payload = [
        'book_id' => $books[0]->id
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($books) {
                $data->toHaveKey('book_id', $books[0]->id);
            }
        );

    //Filter by member id
    $payload = [
        'member_id' => $members[0]->id
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($members) {
                $data->toHaveKey('member_id', $members[0]->id);
            }
        );

    //Filter by penalty payment status
    $payload = [
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($members) {
                $data->toHaveKey('penalty_payment_status', LibraryBookLoanPaymentStatus::UNPAID->value);
            }
        );

    //Filter by period loan date
    $start_loan_date = now()->subDays(4)->format('Y-m-d');
    $end_loan_date = now()->format('Y-m-d');
    $payload = [
        'period_loan_date_from' => $start_loan_date,
        'period_loan_date_to' => $end_loan_date,
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($members, $start_loan_date, $end_loan_date) {
                $data->loan_date->toBeBetween(Carbon::parse($start_loan_date), Carbon::parse($end_loan_date));
            },
            function ($data) use ($members, $start_loan_date, $end_loan_date) {
                $data->loan_date->toBeBetween(Carbon::parse($start_loan_date), Carbon::parse($end_loan_date));
            }
        );

    //Filter by period due date
    $start_due_date = now()->addDays(2)->format('Y-m-d');
    $end_due_date = now()->addDays(4)->format('Y-m-d');
    $payload = [
        'period_due_date_from' => $start_due_date,
        'period_due_date_to' => $end_due_date,
    ];
    $response = $this->bookLoanService->getAllPaginatedBookLoans($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($members, $start_due_date, $end_due_date) {
                $data->due_date->toBeBetween(Carbon::parse($start_due_date), Carbon::parse($end_due_date));
            },
            function ($data) use ($members, $start_due_date, $end_due_date) {
                $data->due_date->toBeBetween(Carbon::parse($start_due_date), Carbon::parse($end_due_date));
            }
        );
});

test('createBookLoan()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(3)->format('Y-m-d'),
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
    ];

    $response = $this->bookLoanService->setMember($this->member_student)
        ->setBook($this->book)
        ->setDueDate(now()->addDays(3)->format('Y-m-d'))
        ->createBookLoan()
        ->toArray();

    expect($response)->toMatchArray([
        'member_id' => $payload['member_id'],
        'book_id' => $payload['book_id'],
        'loan_status' => $payload['loan_status'],
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
    ])->and(Carbon::parse($response['loan_date'])->format('Y-m-d'))->toBe($payload['loan_date'])
        ->and(Carbon::parse($response['due_date'])->format('Y-m-d'))->toBe($payload['due_date']);


    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'member_id' => $payload['member_id'],
        'book_id' => $payload['book_id'],
        'loan_status' => $payload['loan_status'],
        'loan_date' => $payload['loan_date'],
        'due_date' => $payload['due_date'],
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
    ]);
});

test('findBookLoan()', function () {
    $book_loans = LibraryBookLoan::factory(3)->create();

    $response = $this->bookLoanService->findBookLoan($book_loans[0]->id)->toArray();

    expect($response['id'])->toBe($book_loans[0]->id);
});

test('getOverdueBookLoans()', function () {
    $books = Book::factory(2)->create();
    $members = LibraryMember::factory(2)->create();

    $book_loans = LibraryBookLoan::factory(3)->state(new Sequence(
        [
            'member_id' => $members[0]->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(3),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::LOST,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(4),
        ]
    ))->create();

    $response = $this->bookLoanService->getOverdueBookLoans()->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            function ($data) use ($book_loans) {
                $data->toHaveKey('id', $book_loans[0]->id);
            }
        );
});

test('getFinePerDay()', function () {
    //employee
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE,
    ]);

    $response = $this->bookLoanService->getFinePerDay($member_employee);
    expect($response)->toBe(0.1);

    //student
    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
    ]);

    $response = $this->bookLoanService->getFinePerDay($member_student);
    expect($response)->toBe(0.2);

    //librarian
    $member_librarian = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'is_librarian' => 1
    ]);
    $response = $this->bookLoanService->getFinePerDay($member_librarian);
    expect($response)->toBe(0.1);

    //other
    $member_other = LibraryMember::factory()->create([
        'type' => LibraryMemberType::OTHERS,
    ]);

    $response = $this->bookLoanService->getFinePerDay($member_other);
    expect($response)->toBe(0.0);
});

test('configCanBorrowWithUnreturnedBooks()', function () {
    //employee
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE,
    ]);

    $response = $this->bookLoanService->configCanBorrowWithUnreturnedBooks($member_employee);
    expect($response)->toBeFalse();

    //student
    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
    ]);

    $response = $this->bookLoanService->configCanBorrowWithUnreturnedBooks($member_student);
    expect($response)->toBeTrue();

    //librarian
    $member_librarian = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'is_librarian' => 1
    ]);
    $response = $this->bookLoanService->configCanBorrowWithUnreturnedBooks($member_librarian);
    expect($response)->toBeFalse();

    //other
    $member_other = LibraryMember::factory()->create([
        'type' => LibraryMemberType::OTHERS,
    ]);

    $response = $this->bookLoanService->configCanBorrowWithUnreturnedBooks($member_other);
    expect($response)->toBeFalse();
});

test('calculateOverdueAmount()', function () {
    $books = Book::factory(2)->create();
    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT
    ]);
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    Carbon::setTestNow('2025-05-01 16:01:00'); // UTC+0

    $book_loans = LibraryBookLoan::factory(3)->state(new Sequence(
        [
            'member_id' => $member_student->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(3),
        ],
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->subYear(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subYear(),
        ]
    ))->create();

    //student fine per day = 0.2
    $response = $this->bookLoanService->calculateOverdueAmount($book_loans[0]);

    // 0.2 * 2days = 0.4
    expect($response)->toEqual(0.4);

    //employee fine per day = 0.1
    $response = $this->bookLoanService->calculateOverdueAmount($book_loans[1]);

    // 0.1 * 2days = 0.4
    expect($response)->toEqual(0.2);

//    //max fine = 20
//    $response = $this->bookLoanService->calculateOverdueAmount($book_loans[2]);
//    expect($response)->toEqual(20);
});

test('isMemberAllowToBorrowBooks()', function () {
    $books = Book::factory(2)->create();
    $books_to_be_borrowed = Book::factory(5)->create();

    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'borrow_limit' => 2
    ]);

    LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'member_id' => $member_student->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $member_student->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ]
    ))->create();

    //student borrow limit = 2, can borrow with unreturned book = true
    //current borrow book over 2
    $response = $this->bookLoanService->isMemberAllowToBorrowBooks($member_student, 3);

    expect($response)->toBeFalse();

    //can borrow with unreturned book
    $response = $this->bookLoanService->isMemberAllowToBorrowBooks($member_student, 2);

    expect($response)->toBeTrue();

    //employee borrow limit = 3, can borrow with unreturned book = false
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE,
        'borrow_limit' => 3
    ]);

    LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ]
    ))->create();

    //cant borrow as over limit
    $response = $this->bookLoanService->isMemberAllowToBorrowBooks($member_employee, 2);

    expect($response)->toBeFalse();

    //can borrow without over limit
    $response = $this->bookLoanService->isMemberAllowToBorrowBooks($member_employee, 1);

    expect($response)->toBeTrue();
});

test('validateExtend()', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
    ]);

    $days = 3;
    $due_date = now()->subDays($days);

    $this->expectExceptionMessage("New due date {$due_date} must be after old due date {$book_loan->due_date->format('Y-m-d')}.");
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->validateExtend($due_date);
});

test('extend()', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
    ]);

    $days = 3;
    $due_date = now()->addDays($days);
    $response = $this->bookLoanService
        ->setBookLoan($book_loan)
        ->extend($due_date)->toArray();

    expect($response)->toMatchArray([
        'id' => $book_loan->id,
    ])->and(Carbon::parse($response['due_date'])->format('Y-m-d'))->toBe($due_date->format('Y-m-d'));

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'due_date' => $due_date->format('Y-m-d'),
    ]);
});


test('extend() with invalid due date', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => '2024-09-01',
    ]);

    $due_date = '2024-08-31';

    $this->expectExceptionMessage("New due date 2024-08-31 must be after old due date 2024-09-01.");

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->extend($due_date);
});

test('checkIsBookIsBorrowed()', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->expectExceptionMessage('Book [No:A1234] is not borrowed.');
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->checkIsBookIsBorrowed();
});

test('processWalletPayment() payment method is cash', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setPenaltyPaidAmount(10)
        ->processWalletPayment();

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);
});

test('processWalletPayment() penalty paid amount is 0', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::WALLET->value)
        ->setPenaltyPaidAmount(0)
        ->processWalletPayment();

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);
});

test('processWalletPayment() insufficient balance', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $this->expectExceptionMessage("User has insufficient balance.");
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::WALLET->value)
        ->setPenaltyPaidAmount(10)
        ->processWalletPayment();
});

test('processWalletPayment()', function () {
    $this->wallet->balance = 10;
    $this->wallet->save();
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::WALLET->value)
        ->setPenaltyPaidAmount(10)
        ->processWalletPayment();

    $this->assertDatabaseCount($this->wallet_transaction_table, 1);
    $this->assertDatabaseHas($this->wallet_transaction_table, [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'wallet_transactable_id' => $book_loan->id,
        'wallet_transactable_type' => get_class($book_loan),
        'userable_type' => get_class($this->member_student->userable),
        'userable_id' => $this->member_student->userable->id,
        'total_amount' => -10,
        'amount_before_tax' => -10,
        'amount_after_tax' => -10,
        'balance_before' => 10,
        'balance_after' => 0,
    ]);
    $this->assertDatabaseHas($this->wallet, [
        'id' => $this->wallet->id,
        'balance' => 0
    ]);
});

test('createPaymentTransaction()', function () {
    $this->assertDatabaseCount($this->library_payment_transaction_table, 0);

    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id
    ]);
    $wallet_transaction = WalletTransaction::factory()->create();
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::WALLET->value)
        ->setPenaltyPaidAmount(10)
        ->setRemarks('Test')
        ->setPaymentWalletTransaction($wallet_transaction)
        ->createPaymentTransaction();

    $this->assertDatabaseCount($this->library_payment_transaction_table, 1);
    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'wallet_transaction_id' => $wallet_transaction->id,
        'member_id' => $this->member_student->id,
        'book_loan_id' => $book_loan->id,
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'payment_amount' => 10,
        'description' => 'Test'
    ]);
});

test('save()', function () {
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
        'penalty_lost_amount' => 0,
        'penalty_overdue_amount' => 10,
        'penalty_total_fine_amount' => 0
    ]);

    //payment method = cash, without penaltyPaidAmount, will mark payment status =  paid
    $this->bookLoanService->setBookLoan($book_loan)
        ->checkIsBookIsBorrowed()
        ->setIsLost(1)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setPenaltyLostAmount(10)
        ->setPenaltyOverdueAmount(10)
        ->calcPenaltyTotalFineAmount()
        ->setPenaltyPaidAmount(20)
        ->payPenalty()
        ->processBookReturn()
        ->save();

    //check db record
    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
        'penalty_lost_amount' => 10,
        'penalty_total_fine_amount' => 20
    ]);
});

test('payPenalty()', function () {
    //No notification send
    $this->partialMock(AndroidPushNotificationService::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('queuedSend');
    });

    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
        'penalty_lost_amount' => 0,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    //payment method = cash, without penaltyPaidAmount, will mark payment status =  paid
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setPenaltyPaidAmount(0)
        ->setPenaltyOverdueAmount(0)
        ->calcPenaltyTotalFineAmount()
        ->payPenalty()
        ->save();

    //check db record
    $this->assertDatabaseCount($this->library_payment_transaction_table, 0);
    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
    ]);

    //payment method = cash, will create payment transaction, and mark payment status = paid
    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
        'penalty_lost_amount' => 0,
        'penalty_overdue_amount' => 10,
        'penalty_total_fine_amount' => 0
    ]);

    // no notification
    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($book_loan) {
        $mock->shouldNotReceive('setUserable');
        $mock->shouldNotReceive('setTitle');
        $mock->shouldNotReceive('setMessage');
        $mock->shouldNotReceive('determineRecipients');
        $mock->shouldNotReceive('send');
    });

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setPenaltyPaidAmount(10)
        ->setPenaltyOverdueAmount(10)
        ->calcPenaltyTotalFineAmount()
        ->payPenalty()
        ->save();

    //check db record
    $this->assertDatabaseCount($this->library_payment_transaction_table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
    ]);

    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'member_id' => $this->member_student->id,
        'book_loan_id' => $book_loan->id,
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'payment_amount' => 10,
        'wallet_transaction_id' => null,
    ]);
});

test('payPenalty() wallet', function () {

    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $this->member_student->id,
        'book_id' => $this->book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
        'penalty_lost_amount' => 0,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    //payment method = wallet, will create payment transaction, mark payment status = paid, create wallet transaction, deduct wallet
    $this->assertDatabaseCount($this->library_payment_transaction_table, 0);
    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $this->wallet->balance = 10;
    $this->wallet->save();

    //Notification send
    $this->mock(AndroidPushNotificationService::class, function (MockInterface $mock) {
        $mock->shouldReceive('setTitle')->times(1)->with('Library Book Loan Penalty Paid')->andReturnSelf();
        $mock->shouldReceive('setBody')->times(1)->with('Total MYR 9.00 is deducted from your wallet for library book loan penalty')->andReturnSelf();
        $mock->shouldReceive('setClickAction')->times(1)->with(PushNotificationClickAction::FLUTTER_NOTIFICATION_CLICK)->andReturnSelf();
        $mock->shouldReceive('setUser')->times(1)->andReturnSelf();
        $mock->shouldReceive('setToken')->times(1)->with('123456')->andReturnSelf();
        $mock->shouldReceive('queuedSend')->times(1)->andReturnSelf();
    });
    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($book_loan) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($book_loan) {
            return $value->id === $book_loan->member->userable->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with('Wallet Charge Transaction')->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with('Payment of MYR 9.00 for Library Book Loan (Ref: #' . $book_loan->id . ') is successful')->once()->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::WALLET->value)
        ->setPenaltyPaidAmount(9)
        ->setPenaltyOverdueAmount(9)
        ->calcPenaltyTotalFineAmount()
        ->payPenalty()
        ->save();

    //check db record
    $this->assertDatabaseCount($this->library_payment_transaction_table, 1);
    $this->assertDatabaseCount($this->wallet_transaction_table, 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
    ]);

    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'member_id' => $this->member_student->id,
        'book_loan_id' => $book_loan->id,
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'payment_amount' => 9,
        'wallet_transaction_id' => WalletTransaction::orderBy('id', 'desc')->first()->id,
    ]);

    $this->assertDatabaseHas($this->wallet_transaction_table, [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => LibraryBookLoan::class,
        'wallet_transactable_id' => $book_loan->id,
        'total_amount' => -9,
        'amount_before_tax' => -9,
        'amount_after_tax' => -9,
        'balance_before' => 10,
        'balance_after' => 1,
        'description' => 'Library book loan penalty'
    ]);

    $this->assertDatabaseHas($this->wallet_table, [
        'id' => $this->wallet->id,
        'balance' => 1
    ]);
});

test('processBookReturn()', function () {
    $book = Book::factory()->create([
        'status' => BookStatus::AVAILABLE,
        'book_no' => 'A1234'
    ]);

    $user = User::factory()->create();
    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'userable_id' => $student->id,
        'userable_type' => Student::class,
    ]);

    $book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $member_student->id,
        'book_id' => $book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
        'penalty_lost_amount' => 0,
        'penalty_overdue_amount' => 10,
        'penalty_total_fine_amount' => 0
    ]);

    //is lost = true
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setIsLost(true)
        ->setPenaltyOverdueAmount(10)
        ->setPenaltyLostAmount(10)
        ->calcPenaltyTotalFineAmount()
        ->payPenalty()
        ->processBookReturn()
        ->save();

    //check db record
    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_total_fine_amount' => 20,
        'loan_status' => LibraryBookLoanStatus::LOST,
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book->id,
        'status' => BookStatus::LOST,
    ]);

    //is lost = false
    $this->bookLoanService
        ->setBookLoan($book_loan)
        ->setPaymentMethod(LibraryPenaltyPaymentMethod::CASH->value)
        ->setIsLost(false)
        ->setPenaltyLostAmount(0)
        ->calcPenaltyTotalFineAmount()
        ->payPenalty()
        ->processBookReturn()
        ->save();

    //check db record
    $this->assertDatabaseHas($this->table, [
        'id' => $book_loan->id,
        'penalty_total_fine_amount' => 10,
        'loan_status' => LibraryBookLoanStatus::RETURNED
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);
});

test('checkWalletHasSufficientBalance()', function () {
    $this->wallet->balance = 10;
    $this->wallet->save();

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $this->member_student->id,
            'due_date' => now()->subDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 5,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 5,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $this->member_student->id,
            'due_date' => now()->subDays(2)->format('Y-m-d'),
            'penalty_overdue_amount' => 6,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 6,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID
        ]
    ))->create();

    // sufficient balance (less than wallet balance)
    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 3,
                'remarks' => 'remark 1',
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 6,
                'remarks' => 'remark 2',
            ],
        ]
    ];

    $value = $this->bookLoanService->checkWalletHasSufficientBalance($payload['payment_method'], $payload['book_loans']);
    $this->assertTrue($value);

    // sufficient balance (same as wallet balance)
    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 3.6,
                'remarks' => 'remark 1',
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 6.4,
                'remarks' => 'remark 2',
            ],
        ]
    ];

    $value = $this->bookLoanService->checkWalletHasSufficientBalance($payload['payment_method'], $payload['book_loans']);
    $this->assertTrue($value);

    // insufficient balance
    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 4.01,
                'remarks' => 'remark 1',
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 6,
                'remarks' => 'remark 2',
            ],
        ]
    ];

    $this->expectExceptionCode(1002);
    $this->expectExceptionMessage('User has insufficient balance.');
    $this->bookLoanService->checkWalletHasSufficientBalance($payload['payment_method'], $payload['book_loans']);

});

test('getDescription', function () {

    $loan = LibraryBookLoan::factory()->create([
        'id' => 3434,
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'due_date' => now(),
    ]);

    expect($loan->getDescription())->toBe('Library Book Loan (Ref: #3434)');

});


