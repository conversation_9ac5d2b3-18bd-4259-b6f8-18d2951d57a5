<?php

use App\Enums\ClassType;
use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Enums\MerchantType;
use App\Enums\PushNotificationPage;
use App\Enums\PushNotificationPlatform;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Helpers\SystemHelper;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Config;
use App\Models\Course;
use App\Models\EcommerceOrder;
use App\Models\EcommerceOrderItem;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\Guardian;
use App\Models\Merchant;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentTerm;
use App\Models\RunningNumber;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Tax;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\AdHocNotificationService;
use App\Services\EcommerceOrderService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {

    $this->seedAccountingData();
    $this->orderService = app(EcommerceOrderService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(EcommerceOrder::class)->getTable();
    $this->configTable = resolve(Config::class)->getTable();

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->employee = Employee::factory()->create([
        'user_id' => $this->user->id,
    ]);

    Sanctum::actingAs($this->user);

    $student_user = User::factory()->create();

    $this->student = Student::factory()->create([
        'user_id' => $student_user->id,
    ]);

    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->tax2 = Tax::factory()->create([
        'percentage' => 8,
        'name' => 'SST 8%',
    ]);
});

test('getAllPaginatedEcommerceOrders()', function () {
    $orders = EcommerceOrder::factory(3)->state(new Sequence(
        [
            'order_reference_number' => '123',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => 3,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        ],
        [
            'order_reference_number' => '456',
            'buyer_userable_type' => Guardian::class,
            'buyer_userable_id' => 3,
            'status' => EcommerceOrderStatus::PROCESSING,
            'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        ],
        [
            'order_reference_number' => '789',
            'buyer_userable_type' => Guardian::class,
            'buyer_userable_id' => 3,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
        ]
    ))->create();

    //Check relationship is loaded
    $response = $this->orderService->getAllPaginatedEcommerceOrders([
        'includes' => ['items.billingDocumentLineItem.billingDocument.payments.paymentMethod']
    ]);
    foreach ($response as $data) {
        expect($data->relationLoaded('items'))->toBeTrue();

        // Check the nested relationship for each item
        foreach ($data->items as $item) {
            expect($item->relationLoaded('billingDocumentLineItem'))->toBeTrue()
                ->and($item->billingDocumentLineItem->relationLoaded('billingDocument'))->toBeTrue()
                ->and($item->billingDocumentLineItem->billingDocument->relationLoaded('payments'))->toBeTrue();

            foreach ($item->billingDocumentLineItem->billingDocument->payments as $payment) {
                expect($payment->relationLoaded('paymentMethod'))->toBeTrue();
            }
        }
    }

    //Filter by id
    $payload = [
        'id' => $orders[0]->id,
    ];
    $response = $this->orderService->getAllPaginatedEcommerceOrders($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.id", $orders[0]->id);

    //Filter non-existing id = 9999
    $payload = [
        'id' => 9999
    ];
    $response = $this->orderService->getAllPaginatedEcommerceOrders($payload)->toArray();

    expect($response['data'])->toHaveCount(0);
});

test('createEcommerceOrder()', function () {

    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_DAY,
        "value" => 'Friday',
        "category" => Config::CATEGORY_CANTEEN,
    ]);
    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_TIME,
        "value" => '23:59',
        "category" => Config::CATEGORY_CANTEEN,
    ]);

    //store success
    $this->assertDatabaseCount($this->table, 0);

    $student = Student::factory()->create();

    $semester_class = SemesterClass::factory()
        ->forSemesterSetting([
            'from' => '2024-06-01',
            'to' => '2024-12-31',
        ])
        ->create();

    $student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY->value,
        'is_active' => true
    ]);

    \Carbon\Carbon::setTestNow('2024-09-22 08:10:00');

    $response = $this->orderService
        ->setUser($student->user)
        ->setStatus(EcommerceOrderStatus::NEW)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PENDING)
        ->setBillTo($student)
        ->setAmountBeforeTax(10)
        ->setAmountAfterTax(12)
        ->setTaxAmount(2)
        ->setMerchantType(MerchantType::CANTEEN->value)
        ->createEcommerceOrder()
        ->toArray();

    expect($response)->toMatchArray([
        'merchant_type' => MerchantType::CANTEEN->value,
        'user_id' => $student->user->id,
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $student->id,
        'currency_code' => config('school.currency_code'),
        'recipient_student_class_id' => $student_class->id,
        'cancel_before_datetime' => '2024-09-23T08:10:00.000000Z',
        'status' => EcommerceOrderStatus::NEW->value,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING->value,
        'amount_before_tax' => 10,
        'amount_after_tax' => 12,
        'tax_amount' => 2,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'merchant_type' => MerchantType::CANTEEN->value,
        'user_id' => $student->user->id,
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $student->id,
        'recipient_student_class_id' => $student_class->id,
        'currency_code' => config('school.currency_code'),
        'status' => EcommerceOrderStatus::NEW->value,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING->value,
        'cancel_before_datetime' => '2024-09-23T08:10:00.000000Z',
        'amount_before_tax' => 10,
        'amount_after_tax' => 12,
        'tax_amount' => 2,
        'tax_breakdown' => '[]'
    ]);
});

test('generateReferenceNumber()', function () {

    \App\Models\RunningNumber::factory()->create([
        'document_type' => \App\Models\EcommerceOrder::class,
        'identifier1' => null,
        'identifier2' => null,
        'year' => '2024',
        'month' => null,
        'next_number' => 102,
        'max_number' => null
    ]);

    $response = $this->orderService->generateReferenceNumber();

    expect($response)->toBe('ORD202400102');

    $this->assertDatabaseHas(RunningNumber::class, [
        'document_type' => \App\Models\EcommerceOrder::class,
        'identifier1' => null,
        'identifier2' => null,
        'year' => '2024',
        'month' => null,
        'next_number' => 103,
        'max_number' => null
    ]);
});

test('sendCompletedNotification()', function () {
    $user_student_android = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);
    $student_android = Student::factory()->create([
        'user_id' => $user_student_android->id,
    ]);
    $past_order = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $student_android->id,
        'merchant_type' => MerchantType::CANTEEN,
        'status' => EcommerceOrderStatus::PROCESSING,
        'cancel_before_datetime' => now(),
    ]);

    //Notification send
    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($student_android, $past_order) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($student_android) {
            return $value->id === $student_android->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with("Order #{$past_order->order_reference_number} Confirmed.")->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with("Order #{$past_order->order_reference_number} is confirmed.")->once()->andReturnSelf();
        $mock->shouldReceive('setData')->times(1)->with(['id' => $past_order->id, 'page' => PushNotificationPage::ECOMMERCE_ORDER->value])->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $this->orderService
        ->setOrder($past_order)
        ->sendCompletedNotification();
});

test('getCurrentSemesterStudentClassId()', function () {
    $student = Student::factory()->create();

    $course = Course::factory()->create();

    $semester_classes_2024_sem_1 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2024-01-01',
            'to' => '2024-06-30',
        ])
        ->create();

    $semester_classes_2024_sem_2 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2024-07-01',
            'to' => '2024-08-31',
        ])
        ->create();

    $semester_classes_2025_sem_1 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2025-01-01',
            'to' => '2025-06-30',
        ])
        ->create();

    Carbon::setTestNow('2024-08-01'); // Set current time to sem 2

    $student_classes = StudentClass::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2024_sem_1->semester_setting_id,
            'semester_class_id' => $semester_classes_2024_sem_1->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2024_sem_2->semester_setting_id,
            'semester_class_id' => $semester_classes_2024_sem_2->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2025_sem_1->semester_setting_id,
            'semester_class_id' => $semester_classes_2025_sem_1->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
    ))->create();

    $response = $this->orderService->getCurrentSemesterStudentClassId(Student::class, $student->id);
    expect($response)->toBe($student_classes[1]->id);
});

test('updateEcommerceOrder() status only', function () {
    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    $response = $this->orderService
        ->setOrder($order)
        ->setStatus(EcommerceOrderStatus::PROCESSING)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PAID)
        ->updateEcommerceOrder()
        ->toArray();

    expect($response)->toMatchArray([
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
    ]);
});


test('updateEcommerceOrder() with amounts', function () {

    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'amount_before_tax' => 200,
        'amount_after_tax' => 210,
        'tax_amount' => 10,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    $response = $this->orderService
        ->setOrder($order)
        ->setStatus(EcommerceOrderStatus::PROCESSING)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PAID)
        ->setAmountBeforeTax(100)
        ->setAmountAfterTax(100)
        ->setTaxAmount(0)
        ->updateEcommerceOrder()
        ->toArray();

    expect($response)->toMatchArray([
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'tax_amount' => 0,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'tax_amount' => 0,
    ]);
});


test('recalculateAmountsFromItems() multiple tax codes', function () {

    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'amount_before_tax' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0,
    ]);

    // 2 items with tax 8%, 2 items with tax 0%
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 100,
        'tax_amount' => 100 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 200,
        'tax_amount' => 200 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 50,
        'tax_amount' => 50 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 60,
        'tax_amount' => 60 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount(EcommerceOrderItem::class, 4);

    $response = $this->orderService
        ->setOrder($order)
        ->setStatus(EcommerceOrderStatus::PROCESSING)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PAID)
        ->recalculateAmountsFromItems()
        ->updateEcommerceOrder()
        ->toArray();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'amount_before_tax' => 410,
        'amount_after_tax' => 418.8,
        'tax_amount' => 8.8,
    ]);

    $tax_breakdown = EcommerceOrder::orderBy('id', 'desc')->first()->tax_breakdown;

    expect(count($tax_breakdown))->toEqual(2);

    expect($tax_breakdown[$this->tax->id]['tax_amount'])->toEqual(0)
        ->and($tax_breakdown[$this->tax->id]['tax_percentage'])->toEqual(0)
        ->and($tax_breakdown[$this->tax->id]['taxable_amount'])->toEqual(300)
        ->and($tax_breakdown[$this->tax2->id]['tax_amount'])->toEqual(8.8)
        ->and($tax_breakdown[$this->tax2->id]['tax_percentage'])->toEqual(8)
        ->and($tax_breakdown[$this->tax2->id]['taxable_amount'])->toEqual(110);

});

test('recalculateAmountsFromItems() single tax 0%', function () {

    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'amount_before_tax' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0,
    ]);

    // 4 items with tax 0%
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 100,
        'tax_amount' => 100 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 200,
        'tax_amount' => 200 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 50,
        'tax_amount' => 50 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 60,
        'tax_amount' => 60 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount(EcommerceOrderItem::class, 4);

    $response = $this->orderService
        ->setOrder($order)
        ->setStatus(EcommerceOrderStatus::PROCESSING)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PAID)
        ->recalculateAmountsFromItems()
        ->updateEcommerceOrder()
        ->toArray();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'amount_before_tax' => 410,
        'amount_after_tax' => 410,
        'tax_amount' => 0,
    ]);

    $tax_breakdown = EcommerceOrder::orderBy('id', 'desc')->first()->tax_breakdown;

    expect(count($tax_breakdown))->toEqual(1);

    expect($tax_breakdown[$this->tax->id]['tax_amount'])->toEqual(0)
        ->and($tax_breakdown[$this->tax->id]['tax_percentage'])->toEqual(0)
        ->and($tax_breakdown[$this->tax->id]['taxable_amount'])->toEqual(410);

});


test('recalculateAmountsFromItems() single tax 8%', function () {

    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'amount_before_tax' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0,
    ]);

    // 4 items with tax 8%
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 100,
        'tax_amount' => 100 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 200,
        'tax_amount' => 200 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 50,
        'tax_amount' => 50 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 60,
        'tax_amount' => 60 * $this->tax2->percentage,
        'tax_percentage' => $this->tax2->percentage,
        'tax_id' => $this->tax2->id,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount(EcommerceOrderItem::class, 4);

    $response = $this->orderService
        ->setOrder($order)
        ->setStatus(EcommerceOrderStatus::PROCESSING)
        ->setPaymentStatus(EcommerceOrderPaymentStatus::PAID)
        ->recalculateAmountsFromItems()
        ->updateEcommerceOrder()
        ->toArray();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::PROCESSING->value,
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'amount_before_tax' => 410,
        'amount_after_tax' => 442.8,
        'tax_amount' => 32.8,
    ]);

    $tax_breakdown = EcommerceOrder::orderBy('id', 'desc')->first()->tax_breakdown;

    expect(count($tax_breakdown))->toEqual(1);

    expect($tax_breakdown[$this->tax2->id]['tax_amount'])->toEqual(32.8)
        ->and($tax_breakdown[$this->tax2->id]['tax_percentage'])->toEqual(8)
        ->and($tax_breakdown[$this->tax2->id]['taxable_amount'])->toEqual(410);

});


test('validateOrderCancellation() over cancel date', function () {
    $order_over_cancel_before_datetime = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $this->student->id,
        'cancel_before_datetime' => now()->subDays()
    ]);

    $this->expectExceptionMessage("Order cannot be cancelled. Cancellation cut-off time has passed.");
    $this->orderService
        ->setBillTo($this->student)
        ->setOrder($order_over_cancel_before_datetime)
        ->validateOrderCancellation();
});

test('validateOrderCancellation() order status not equal processing', function () {
    $order_status_canceled = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $this->student->id,
        'status' => EcommerceOrderStatus::CANCELED->value,
        'cancel_before_datetime' => now()->addDay(),
    ]);

    $this->expectExceptionMessage("Order cannot be cancelled. Invalid order status or payment status.");
    $this->orderService
        ->setBillTo($this->student)
        ->setOrder($order_status_canceled)
        ->validateOrderCancellation();
});

test('validateOrderCancellation() order payment status not equal paid', function () {
    $order_payment_status_pending = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $this->student->id,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING->value,
        'cancel_before_datetime' => now()->addDay(),
    ]);

    $this->expectExceptionMessage("Order cannot be cancelled. Invalid order status or payment status.");
    $this->orderService
        ->setBillTo($this->student)
        ->setOrder($order_payment_status_pending)
        ->validateOrderCancellation();
});

test('validateOrderCancellation() not able to cancel other people order', function () {
    $user_other_student = User::factory()->create();
    $other_student = Student::factory()->create([
        'user_id' => $user_other_student->id,
    ]);
    $order_other_student = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $other_student->id,
        'status' => EcommerceOrderStatus::PROCESSING,
        'payment_status' => EcommerceOrderPaymentStatus::PAID,
        'cancel_before_datetime' => now()->endOfDay(),
        'amount_before_tax' => 5,
        'amount_after_tax' => 5,
        'tax_amount' => 0
    ]);

    $this->expectExceptionMessage("Order cannot be cancelled. You are not authorized to cancel this order.");
    $this->orderService
        ->setBillTo($this->student)
        ->setOrder($order_other_student)
        ->validateOrderCancellation();
});

test('isAuthorizeToCancel()', function () {
    $user_student = User::factory()->create();
    $user_employee = User::factory()->create();

    $student = Student::factory()->create([
        'user_id' => $user_student->id
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user_employee->id
    ]);
    $student2 = Student::factory()->create();

    $order_student = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $student->id,
    ]);

    $order_student2 = EcommerceOrder::factory()->create([
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $student2->id,
    ]);

    //Employee is authorized to cancel all order
//    Sanctum::actingAs($user_employee);
    $response = $this->orderService->setOrder($order_student)->isAuthorizeToCancel(Employee::class, $employee->id);

    expect($response)->toBeTrue();

    $response = $this->orderService->setOrder($order_student2)->isAuthorizeToCancel(Employee::class, $employee->id);

    expect($response)->toBeTrue();

    //Non Employee is authorized to cancel his own order only
//    Sanctum::actingAs($user_student);

    $response = $this->orderService->setOrder($order_student)->isAuthorizeToCancel(Student::class, $student->id);

    expect($response)->toBeTrue();

    $response = $this->orderService->setOrder($order_student2)->isAuthorizeToCancel(Student::class, $student->id);

    expect($response)->toBeFalse();
});


test('EcommerceOrder getMerchantsInOrder()', function () {

    $merchants = Merchant::factory(2)->create();

    $order = EcommerceOrder::factory()->create([
        'order_reference_number' => '123',
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $this->student->id,
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'recipient_student_class_id' => 1,
        'cancel_before_datetime' => now(),
        'amount_before_tax' => 10,
        'amount_after_tax' => 12,
        'tax_amount' => 2,
    ]);

    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'merchant_id' => $merchants[0]->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'merchant_id' => $merchants[0]->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'merchant_id' => $merchants[0]->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'merchant_id' => $merchants[1]->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'merchant_id' => $merchants[1]->id,
    ]);

    $merchants = $order->getMerchantsInOrder();

    expect($merchants)->toHaveCount(2)
        ->and($merchants->pluck('name')->toArray())->toMatchArray([$merchants[0]->name, $merchants[1]->name]);

});


test('createPaidInvoice()', function () {

    \Carbon\Carbon::setTestNow('2024-09-23 07:00:00');

    $order = EcommerceOrder::factory()->create([
        'order_reference_number' => '123',
        'buyer_userable_type' => Student::class,
        'buyer_userable_id' => $this->student->id,
        'status' => EcommerceOrderStatus::NEW,
        'payment_status' => EcommerceOrderPaymentStatus::PENDING,
        'recipient_student_class_id' => 1,
        'cancel_before_datetime' => now(),
        'amount_before_tax' => 30,
        'amount_after_tax' => 30,
        'tax_amount' => 0,
    ]);

    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 10,
        'tax_amount' => 10 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);
    EcommerceOrderItem::factory()->create([
        'order_id' => $order->id,
        'amount_before_tax' => 20,
        'tax_amount' => 20 * $this->tax->percentage,
        'tax_percentage' => $this->tax->percentage,
        'tax_id' => $this->tax->id,
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $this->student->id,
        'balance' => 0,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'reference_no' => $order->order_reference_number,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => $order->amount_after_tax * -1,
        'amount_before_tax' => $order->amount_after_tax * -1,
        'amount_after_tax' => $order->amount_after_tax * -1,
    ]);

    Event::fake();

    $service = app()->make(EcommerceOrderService::class);
    $service
        ->setBillTo($this->student)
        ->setOrder($order)
        ->setWalletTransaction($wallet_transaction)
        ->createPaidInvoice();

    $this->assertDatabaseHas(BillingDocument::class, [
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date' => '2024-09-23',
        'paid_at' => '2024-09-23 07:00:00',
        'posting_date' => null,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'tax_code' => $this->tax->code,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'payment_term_id' => PaymentTerm::where('code', PaymentTerm::CODE_DUE_NOW)->firstOrFail()->id,
        'currency_code' => 'MYR',
        'amount_before_tax' => 30,
        'amount_before_tax_after_less_advance' => 30,
        'tax_amount' => 0,
        'amount_after_tax' => 30,
    ]);

    $billing_document = BillingDocument::orderBy('id', 'DESC')->first();

    foreach ($order->items as $item) {
        $description = $item->product_delivery_date->toDateString() . ': ' . $item->product_name . "\n";
        $description .= "SELLER: " . $item->merchant->name;
        $this->assertDatabaseHas(BillingDocumentLineItem::class, [
            'billing_document_id' => $billing_document->id,
            'currency_code' => $billing_document->currency_code,
            'amount_before_tax' => $item->amount_before_tax,
            'product_id' => null,
            'gl_account_code' => GlAccount::CODE_ECOMMERCE,
            'offset_billing_document_id' => null,
            'description' => $description,
            'is_discount' => false,
            'billable_item_id' => $item->id,
            'billable_item_type' => get_class($item),
        ]);
    }

    // check payment
    $this->assertDatabaseHas(Payment::class, [
        'billing_document_id' => $billing_document->id,
        'payment_method_id' => PaymentMethod::where('code', PaymentMethod::CODE_SKLEARN_WALLET)->firstOrFail()->id,
        'payment_reference_no' => $order->order_reference_number,
        'payment_reference_no_2' => null,
        'amount_received' => 30,
        'paid_at' => '2024-09-23 07:00:00',
        'bank_id' => null,
        'remarks' => null,
        'created_by_employee_id' => Employee::where('employee_number', Employee::SYSTEM_ID)->first()->id,
        'payment_source_id' => $wallet_transaction->id,
        'payment_source_type' => get_class($wallet_transaction),
    ]);

    // post payment events
    Event::assertDispatched(\App\Events\InvoicePaidEvent::class);
    Event::assertDispatched(\App\Events\PaymentCompletedEvent::class);

});

test('markAsComplete()', function () {
    $order = EcommerceOrder::factory()->create([
        'status' => EcommerceOrderStatus::PROCESSING,
        'payment_status' => EcommerceOrderPaymentStatus::PAID,
    ]);

    $this->orderService
        ->setOrder($order)
        ->markAsComplete();

    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'status' => EcommerceOrderStatus::COMPLETED->value,
    ]);
});


test('Canteen getAllowedCancellationDate() cutoff date is earlier', function () {

    \Carbon\Carbon::setTestNow('2024-11-22 14:00:00');

    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_DAY,
        "value" => 'Friday',
        "category" => Config::CATEGORY_CANTEEN,
    ]);
    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_TIME,
        "value" => '23:59',
        "category" => Config::CATEGORY_CANTEEN,
    ]);

    $date = $this->orderService
        ->setMerchantType(MerchantType::CANTEEN->value)
        ->getAllowedCancellationDate();

    // should take cutoff date as it's earlier
    expect($date->toDateTimeString())->toBe('2024-11-22 15:59:00');


});


test('Canteen getAllowedCancellationDate() default +1 day is earlier', function () {

    \Carbon\Carbon::setTestNow('2024-11-19 14:00:00');

    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_DAY,
        "value" => 'Friday',
        "category" => Config::CATEGORY_CANTEEN,
    ]);
    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_TIME,
        "value" => '23:59',
        "category" => Config::CATEGORY_CANTEEN,
    ]);

    $date = $this->orderService
        ->setMerchantType(MerchantType::CANTEEN->value)
        ->getAllowedCancellationDate();

    // should take cutoff date as it's earlier
    expect($date->toDateTimeString())->toBe('2024-11-20 14:00:00');


});


test('Non-Canteen getAllowedCancellationDate()', function () {

    \Carbon\Carbon::setTestNow('2024-11-19 13:00:00');

    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_DAY,
        "value" => 'Friday',
        "category" => Config::CATEGORY_CANTEEN,
    ]);
    Config::factory()->create([
        "key" => Config::CANTEEN_CUTOFF_TIME,
        "value" => '23:59',
        "category" => Config::CATEGORY_CANTEEN,
    ]);

    $date = $this->orderService
        ->setMerchantType(MerchantType::BOOKSHOP->value)
        ->getAllowedCancellationDate();

    // should take cutoff date as it's earlier
    expect($date->toDateTimeString())->toBe('2024-11-19 13:00:00');


});


test('EcommerceOrder getDescription', function () {

    $order = EcommerceOrder::factory()->create([
        'order_reference_number' => 'ORD20241111',
        'payment_status' => EcommerceOrderPaymentStatus::PAID->value,
        'status' => EcommerceOrderStatus::COMPLETED->value,
        'merchant_type' => MerchantType::CANTEEN,
        'amount_after_tax' => 23554.55,
    ]);

    expect($order->getDescription())->toBe('[ORD20241111] E-commerce purchase at canteen. Amount MYR 23,554.55');

});
