<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\ExportType;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\ConfigHelper;
use App\Models\Attendance;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Employee;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationType;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Timeslot;
use App\Models\Timetable;
use App\Repositories\AttendanceRepository;
use App\Services\DocumentPrintService;
use App\Services\Report\AttendanceReportService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PeriodSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->attendanceReportService = app(AttendanceReportService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    $this->student_late = Student::factory()->create([
        'name->en' => 'Student Late'
    ]);
    $this->student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_late->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 00:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);
    $this->student_on_time = Student::factory()->create([
        'name->en' => 'Student On time'
    ]);
    $this->student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_on_time->id,
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);
    $this->student_absent = Student::factory()->create([
        'name->en' => 'Student Absent'
    ]);
    $this->student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);
    $this->teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 07:00:00', // UTC + 0 Time
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);
    $this->semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $this->semester_setting1_semester_class = SemesterClass::factory()->create([
        'id' => 100,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);
    $this->semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'id' => 200,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);
    $this->semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);
    $this->semester_setting2_semester_class = SemesterClass::factory()->create([
        'id' => 300,
        'semester_setting_id' => $this->semester_setting2->id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class->id,
        'student_id' => $this->student_late->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'student_id' => $this->student_on_time->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $this->student_absent->id,
        'is_active' => true,
    ]);

    $this->employee1 = Employee::factory()->create([
        'name->en' => 'Employee 1'
    ]);
    $this->employee2 = Employee::factory()->create([
        'name->en' => 'Employee 2'
    ]);

    $period_group = PeriodGroup::factory()->create([
        'name' => 'Period Group 1',
    ]);
    $period_group2 = PeriodGroup::factory()->create([
        'name' => 'Period Group 2',
    ]);

    PeriodLabel::factory(2)
        ->state(new Sequence(
            [
                'period_group_id' => $period_group->id,
                'is_attendance_required' => true,
                'name->en' => 'Label 1',
                'period' => 1
            ],
            [
                'period_group_id' => $period_group2->id,
                'is_attendance_required' => true,
                'name->en' => 'Label 2',
                'period' => 1
            ],
        ))
        ->create();


    $class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
    ]);

    $class2 = ClassModel::factory()->create([
        'name->en' => 'Senior Class 1',
    ]);

    $semester = SemesterSetting::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester->id,
        'class_id' => $class->id,
    ]);

    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester->id,
        'class_id' => $class2->id,
    ]);

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
        'semester_class_id' => $semester_class->id,
    ]);
    $timetable2 = Timetable::factory()->create([
        'period_group_id' => $period_group2->id,
        'semester_class_id' => $semester_class2->id,
    ]);

    $subject = Subject::factory()->create([
        'name->en' => 'Subject 1'
    ]);

    $class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class->id,
        'subject_id' => $subject->id,
    ]);

    $class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class2->id,
        'subject_id' => $subject->id,
    ]);

    $periods = Period::factory(3)
        ->state(new Sequence(
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group->id,
                'from_time' => '07:00:00',
                'to_time' => '07:30:00',
                'period' => 1,
            ],
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group->id,
                'from_time' => '07:31:00',
                'to_time' => '08:00:00',
                'period' => 2,
            ],
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group->id,
                'from_time' => '08:01:00',
                'to_time' => '08:30:00',
                'period' => 3,
            ]
        ))
        ->create();

    $periods2 = Period::factory(3)
        ->state(new Sequence(
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group2->id,
                'from_time' => '07:00:00',
                'to_time' => '07:30:00',
                'period' => 1,
            ],
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group2->id,
                'from_time' => '07:31:00',
                'to_time' => '08:00:00',
                'period' => 2,
            ],
            [
                'day' => Day::MONDAY,
                'period_group_id' => $period_group2->id,
                'from_time' => '08:01:00',
                'to_time' => '08:30:00',
                'period' => 3,
            ]
        ))
        ->create();

    $this->timeslots = Timeslot::factory(3)
        ->state(new Sequence(
            [
                'day' => Day::MONDAY,
                'timetable_id' => $timetable->id,
                'period_id' => $periods[0]->id,
                'class_subject_id' => $class_subject->id,
                'attendance_from' => '07:00:00',
                'attendance_to' => '07:30:00',
                'placeholder' => null,
            ],
            [
                'day' => Day::MONDAY,
                'timetable_id' => $timetable->id,
                'period_id' => $periods[1]->id,
                'class_subject_id' => $class_subject->id,
                'attendance_from' => '07:31:00',
                'attendance_to' => '08:00:00',
                'placeholder' => null,
            ],
            [
                'day' => Day::MONDAY,
                'timetable_id' => $timetable2->id,
                'period_id' => $periods2[2]->id,
                'class_subject_id' => $class_subject2->id,
                'attendance_from' => '08:01:00',
                'attendance_to' => '08:31:00',
                'placeholder' => null,
            ],
        ))
        ->create();

    $this->period_attendances = PeriodAttendance::factory(3)
        ->state(new Sequence(
            [
                'date' => '2024-01-01',
                'student_id' => $this->student_on_time->id,
                'timeslot_id' => $this->timeslots[0]->id,
                'status' => AttendanceStatus::PRESENT,
                'updated_by_employee_id' => $this->employee1->id,
                'period' => 1,
                'updated_at' => '2024-01-01 10:10:10',
            ],
            [
                'date' => '2024-01-01',
                'student_id' => $this->student_absent->id,
                'timeslot_id' => $this->timeslots[1]->id,
                'status' => AttendanceStatus::PRESENT,
                'updated_by_employee_id' => $this->employee2->id,
                'period' => 2,
                'updated_at' => '2024-01-01 10:10:10',
            ],
            [
                'date' => '2024-01-01',
                'student_id' => $this->student_late->id,
                'timeslot_id' => $this->timeslots[2]->id,
                'status' => AttendanceStatus::PRESENT,
                'updated_by_employee_id' => $this->employee2->id,
                'period' => 3,
                'updated_at' => '2024-01-01 10:10:10',
            ],
        ))
        ->create();


    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);
    SnappyPdf::fake();
});

test('getReportByAttendanceSummaryData, preview data', function () {
    $expected_result = [
        '2024-01-01' => [
            [
                'date' => '2024-01-01',
                'id' => 1,
                'name' => [
                    'en' => 'Class A',
                    'zh' => '班级A',
                ],
                'records' => [
                    'LATE' => 0,
                    'PRESENT' => 0,
                    'ABSENT' => 2,
                ],
            ]
        ],
        '2024-01-02' => [
            [
                'date' => '2024-01-02',
                'id' => 2,
                'name' => [
                    'en' => 'Class B',
                    'zh' => '班级B',
                ],
                'records' => [
                    'LATE' => 0,
                    'PRESENT' => 2,
                    'ABSENT' => 0,
                ],
            ]
        ],
    ];

    $payload = [
        'semester_class_ids' => [1, 2],
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-02',
    ];


    $this->mock(AttendanceRepository::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getAttendanceSummaryData')
            ->with($payload)
            ->andReturn($expected_result);
    });


    $response = resolve(AttendanceReportService::class)->getReportByAttendanceSummaryData($payload);

    expect($response)->toBe($expected_result);
});

test('getReportByAttendanceSummaryData, download excel / pdf', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);
    $j22_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J22',
    ]);

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $j11_class->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $j22_class->id,
        ],
    ))->create();

    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
        [
            'name->en' => 'Charlie', // J22
        ],
        [
            'name->en' => 'David', // J22
        ],
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[3]->id,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);


    $attendances = Attendance::factory(16)->state(new Sequence(
    /**
     * 2024-06-01
     */

    // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[3]->id, // David J22 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],


        /**
         * 2024-06-02
         */

        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[3]->id, // David J22 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],

        /**
         * 2024-06-03
         */

        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[3]->id, // David J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],


        /**
         * 2024-06-04
         */

        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[3]->id, // David J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
    ))->create();

    $mocked_result = resolve(AttendanceRepository::class)->getAttendanceSummaryData([
        'semester_class_ids' => [
            $semester_classes[0]->id,
            $semester_classes[1]->id,
        ],
        'date_from' => '2024-06-01',
        'date_to' => '2024-06-04',
    ]);

    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $attendanceReportService = resolve(AttendanceReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_class_ids' => [
            $semester_classes[0]->id,
            $semester_classes[1]->id,
        ],
        'date_from' => '2024-06-01',
        'date_to' => '2024-06-04',
    ];

    $title = __('attendance.student_daily_arrival');

    $file_name = 'report-by-attendance-summary-data';

    $expected_headers = [
        __('general.no'),
        __('general.class'),
        __('general.attend'),
        __('general.late'),
        __('general.absent'),
        __('general.total_student'),
        __('general.total'),
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.attendances.by-attendances-summary';

    $attendanceReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByAttendanceSummaryData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $semester_classes,
            $mocked_result,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title, false);

            foreach ($expected_headers as $header) {
                $view->assertSee($header, false);
            }

            $view->assertSee($semester_classes[0]->classModel->getTranslation('name', 'en'));
            $view->assertSee($semester_classes[0]->classModel->getTranslation('name', 'zh'));
            $view->assertSee($semester_classes[1]->classModel->getTranslation('name', 'en'));
            $view->assertSee($semester_classes[1]->classModel->getTranslation('name', 'zh'));

            $view->assertSee(Carbon::parse('2024-06-01')->format('F j, Y'));
            $view->assertSee(Carbon::parse('2024-06-02')->format('F j, Y'));
            $view->assertSee(Carbon::parse('2024-06-03')->format('F j, Y'));
            $view->assertSee(Carbon::parse('2024-06-04')->format('F j, Y'));

            foreach ($mocked_result as $row) {
                $records = collect($row)->pluck('records')->flatten(1);

                foreach ($records as $record) {
                    $view->assertSee($record); // count for LATE, PRESENT, ABSENT
                }
            }

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $attendanceReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByAttendanceSummaryData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.attendances.by-attendances-summary');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($title);

    SnappyPdf::assertSee($semester_classes[0]->classModel->getTranslation('name', 'en'));
    SnappyPdf::assertSee($semester_classes[0]->classModel->getTranslation('name', 'zh'));
    SnappyPdf::assertSee($semester_classes[1]->classModel->getTranslation('name', 'en'));
    SnappyPdf::assertSee($semester_classes[1]->classModel->getTranslation('name', 'zh'));

    SnappyPdf::assertSee(Carbon::parse('2024-06-01')->format('F j, Y'));
    SnappyPdf::assertSee(Carbon::parse('2024-06-02')->format('F j, Y'));
    SnappyPdf::assertSee(Carbon::parse('2024-06-03')->format('F j, Y'));
    SnappyPdf::assertSee(Carbon::parse('2024-06-04')->format('F j, Y'));

    foreach ($mocked_result as $row) {
        $records = collect($row)->pluck('records')->flatten(1);

        foreach ($records as $record) {
            SnappyPdf::assertSee($record); // count for LATE, PRESENT, ABSENT
        }
    }
});

test('getReportByClassAttendanceTakingData, preview data', function () {
    $expected_result = [
        'class' => [
            'id' => 1,
            'name' => [
                'en' => 'Class A',
                'zh' => '班级A',
            ],
        ],
        'headers' => [
            [
                'from_time' => '07:00:00',
                'to_time' => '07:30:00',
                'period' => 1,
                'name' => [
                    'en' => 'First period',
                    'zh' => '第一节',
                ],
            ],
            [
                'from_time' => '07:30:00',
                'to_time' => '08:00:00',
                'period' => 2,
                'name' => [
                    'en' => 'Second period',
                    'zh' => '第二节',
                ],
            ],
            [
                'from_time' => '08:00:00',
                'to_time' => '08:30:00',
                'period' => 3,
                'name' => [
                    'en' => 'Third period',
                    'zh' => '第三节',
                ],
            ],
            [
                'from_time' => '08:30:00',
                'to_time' => '09:00:00',
                'period' => 4,
                'name' => [
                    'en' => 'Fourth period',
                    'zh' => '第四节',
                ],
            ],
            [
                'from_time' => '09:00:00',
                'to_time' => '09:30:00',
                'period' => 5,
                'name' => [
                    'en' => 'Fifth period',
                    'zh' => '第五节',
                ],
            ],
            [
                'from_time' => '09:30:00',
                'to_time' => '10:00:00',
                'period' => 6,
                'name' => [
                    'en' => 'Sixth period',
                    'zh' => '第六节',
                ],
            ],
            [
                'from_time' => '10:00:00',
                'to_time' => '10:30:00',
                'period' => 7,
                'name' => [
                    'en' => 'Seventh period',
                    'zh' => '第七节',
                ],
            ],
            [
                'from_time' => '10:30:00',
                'to_time' => '11:00:00',
                'period' => 8,
                'name' => [
                    'en' => 'Eighth period',
                    'zh' => '第八节',
                ],
            ],
            [
                'from_time' => '11:00:00',
                'to_time' => '11:30:00',
                'period' => 9,
                'name' => [
                    'en' => 'Ninth period',
                    'zh' => '第九节',
                ],
            ],
        ],
        'attendance_data' => [
            [
                'student_id' => 50,
                'student_name' => [
                    'en' => 'Albert',
                    'zh' => '丛龙',
                ],
                'student_number' => '8272554729',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 51,
                'student_name' => [
                    'en' => 'Bob',
                    'zh' => '尤娟',
                ],
                'student_number' => '5102835683',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 52,
                'student_name' => [
                    'en' => 'Charlie',
                    'zh' => '欧阳梅',
                ],
                'student_number' => '5079128467',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
            [
                'student_id' => 53,
                'student_name' => [
                    'en' => 'David',
                    'zh' => '周强',
                ],
                'student_number' => '1296499080',
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => 'PRESENT',
                    2 => 'PRESENT',
                    3 => 'PRESENT',
                    4 => 'PRESENT',
                    5 => 'PRESENT',
                    6 => 'PRESENT',
                    7 => 'PRESENT',
                    8 => 'PRESENT',
                    9 => 'PRESENT',
                ],
            ],
        ]
    ];

    $payload = [
        'semester_class_id' => 1,
        'date' => '2024-01-01',
    ];

    $this->mock(AttendanceRepository::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getClassAttendanceTakingData')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = resolve(AttendanceReportService::class)->getReportByClassAttendanceTakingData($payload);

    expect($response)->toBe($expected_result);
});


test('getReportByClassAttendanceTakingData, download excel / pdf', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
        [
            'name->en' => 'Charlie', // J11
        ],
        [
            'name->en' => 'David', // J11
        ],
    ))->create();


    Attendance::factory(4)->state(new Sequence(
        [
            'attendance_recordable_id' => $students[0]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[1]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[2]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[3]->id,
            'status' => AttendanceStatus::ABSENT->value,
            'date' => '2025-04-10',
        ]
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[3]->id,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $this->seed([PeriodSeeder::class]);

    $period_group = PeriodGroup::first();


    // make period 10, 11, 12  dont require attendance, these periods will not appear in headers and attendance data
    $period_group->periodLabels()
        ->whereIn('period', [10, 11, 12])
        ->update(['is_attendance_required' => false]);


    $time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class->id,
        'period_group_id' => $period_group->id, // default period group
        'is_active' => true,
    ]);

    $timeslots = collect();

    $thursday_periods = $period_group->periods()->where('day', Day::THURSDAY)->orderBy('period', 'asc')->get();

    foreach ($thursday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $time_table->id,
            'day' => Day::THURSDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $timeslots->push($time_slot);
    }


    // All student PRESENT at all period
    foreach ($timeslots as $timeslot) {

        /**
         * Albert
         */
        PeriodAttendance::factory()->create([
            'student_id' => $students[0]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        /**
         * Bob
         */
        PeriodAttendance::factory()->create([
            'student_id' => $students[1]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        /**
         * Charlie
         */
        PeriodAttendance::factory()->create([
            'student_id' => $students[2]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        /**
         * David
         */
        PeriodAttendance::factory()->create([
            'student_id' => $students[3]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);
    }

    $mocked_result = resolve(AttendanceRepository::class)->getClassAttendanceTakingData([
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10',
    ]);

    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $attendanceReportService = resolve(AttendanceReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10',
    ];

    $title = __('attendance.class_taking_attendance_report');

    $file_name = 'report-by-class-taking-attendance';

    $expected_headers = [
        __('general.student'),
        __('general.class'),
    ];

    $available_locales = ConfigHelper::getAvailableLocales();


    // Test Excel
    Excel::fake();

    $view_name = 'reports.attendances.class-attendance-report';

    $attendanceReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByClassAttendanceTakingData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $mocked_result,
            $available_locales,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title, false);

            foreach ($expected_headers as $header) {
                $view->assertSee($header, false);
            }

            foreach ($mocked_result['headers'] as $header) {
                $view->assertSee(Carbon::parse($header['from_time'])->format('H:i'));
                $view->assertSee(Carbon::parse($header['to_time'])->format('H:i'));

                $view->assertSee($header['name'][app()->getLocale()]);
            }

            foreach ($mocked_result['attendance_data'] as $row) {
                foreach ($available_locales as $locale) {
                    $view->assertSee($row['student_name'][$locale]);
                }

                $view->assertSee($row['student_number']);

                $period_attendances_status = [];
                foreach ($row['period_attendances'] as $status) {
                    $period_attendances_status[] = PeriodAttendanceStatus::getStatusForReport($status, $row['school_attendance_status']);
                }

                $view->assertSeeInorder($period_attendances_status);
            }

            return true;
        }
    );


    // Test PDF
    SnappyPdf::fake();

    $attendanceReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByClassAttendanceTakingData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.attendances.class-attendance-report');

    SnappyPdf::assertSee($title, false);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header, false);
    }

    foreach ($mocked_result['headers'] as $header) {
        SnappyPdf::assertSee(Carbon::parse($header['from_time'])->format('H:i'));
        SnappyPdf::assertSee(Carbon::parse($header['to_time'])->format('H:i'));

        SnappyPdf::assertSee($header['name'][app()->getLocale()]);
    }

    foreach ($mocked_result['attendance_data'] as $row) {
        foreach ($available_locales as $locale) {
            SnappyPdf::assertSee($row['student_name'][$locale]);
        }

        SnappyPdf::assertSee($row['student_number']);

        foreach ($row['period_attendances'] as $status) {
            SnappyPdf::assertSee(PeriodAttendanceStatus::getStatusForReport($status, $row['school_attendance_status']));
        }
    }
});


test('getReportByAttendanceMarkDeductionData, preview data', function () {
    $expected_result = [
        [
            "class_name" => "J11",
            "students" => [
                [
                    "student_name" => [
                        "en" => "Albert",
                        "zh" => "石楠",
                    ],
                    "student_number" => "3771335427",
                    "class" => [
                        "en" => "J11",
                        "zh" => "周芬",
                    ],
                    "attendances" => [
                        [
                            "date" => "2025-04-11",
                            "type" => "LATE",
                            "base_deduct_average_point" => 0.01,
                            "periods" => 12,
                            "total_deduct_average_point" => 0.12,
                            "reason" => null,
                        ],
                    ],
                ],
                [
                    "student_name" => [
                        "en" => "Bob",
                        "zh" => "吕金凤",
                    ],
                    "student_number" => "1848070925",
                    "class" => [
                        "en" => "J11",
                        "zh" => "周芬",
                    ],
                    "attendances" => [
                        [
                            "date" => "2025-04-11",
                            "type" => "ABSENT",
                            "base_deduct_average_point" => 0.02,
                            "periods" => 12,
                            "total_deduct_average_point" => 0.24,
                            "reason" => null,
                        ],
                    ],
                ],
            ],
        ],
    ];

    $payload = [
        'semester_class_ids' => [
            1,
        ],
        'date_from' => '2025-04-11',
        'date_to' => '2025-04-11',
    ];

    $this->mock(AttendanceRepository::class, function (MockInterface $mock) use ($expected_result, $payload) {
        $mock
            ->shouldReceive('getStudentAttendanceMarkDeductionData')
            ->with($payload)
            ->andReturn($expected_result);
    });

    $response = resolve(AttendanceReportService::class)->getReportByAttendanceMarkDeductionData($payload);

    expect($response)->toBe($expected_result);
});


test('getReportByAttendanceMarkDeductionData, download excel / pdf', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
    ))->create();

    $student_classes = StudentClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_1->id,
            'student_id' => $students[1]->id,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $this->seed([PeriodSeeder::class]);

    $period_group = PeriodGroup::first();


    $time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class_1->id,
        'period_group_id' => $period_group->id, // default period group
        'is_active' => true,
    ]);

    $timeslots = collect();

    $thursday_periods = $period_group->periods()->where('day', Day::THURSDAY)->orderBy('period', 'asc')->get();

    foreach ($thursday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $time_table->id,
            'day' => Day::THURSDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $timeslots->push($time_slot);
    }

    $J11_period_attendances = collect();

    foreach ($timeslots as $timeslot) {
        /**
         * Albert is LATE (initially) for all period, THURSDAY
         */
        $period_attendance_1 = PeriodAttendance::factory()->create([
            'student_id' => $students[0]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $J11_period_attendances->push($period_attendance_1);


        /**
         * Bob is ABSENT for all period, THURSDAY
         */
        $period_attendance_2 = PeriodAttendance::factory()->create([
            'student_id' => $students[1]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $J11_period_attendances->push($period_attendance_2);
    }

    //  updating ALBERT LATE attendances for period 1, 2, 3
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [1, 2, 3])
        ->each(function ($period_attendance) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::LATE->value,
            ]);
        });

    // updating ALBERT ABSENT attendances for period 4, 5
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [4, 5])
        ->each(function ($period_attendance) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::ABSENT->value,
            ]);
        });

    $mc_leave_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Medical Certificate',
        'is_present' => true,
        'average_point_deduction' => 0,
    ]);

    $albert_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $students[0]->id, // Albert
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $mc_leave_type->id,
        'reason' => 'Stomachache',
    ]);

    // updating ALBERT MC attendances for period 6, 7, 8, 9, 10, 11, 12
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [6, 7, 8, 9, 10, 11, 12])
        ->each(function ($period_attendance) use ($albert_leave_application) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::ABSENT->value,
                'leave_application_id' => $albert_leave_application->id,
            ]);
        });

    $mocked_result = resolve(AttendanceRepository::class)->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'date_from' => '2025-04-10',
        'date_to' => '2025-04-10',
    ]);


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $attendanceReportService = resolve(AttendanceReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'date_from' => '2025-04-10',
        'date_to' => '2025-04-10',
    ];

    $locale = app()->getLocale();

    $title = __('attendance.mark_deduct_report', [
        'date_from' => \Carbon\Carbon::parse('2025-04-10')->locale($locale)->translatedFormat('F j, Y'),
        'date_to' => \Carbon\Carbon::parse('2025-04-10')->locale($locale)->translatedFormat('F j, Y'),
    ]);

    $file_name = 'report-by-student-attendance-mark-deduction';

    $expected_headers = [
        __('general.class'),
        __('general.student_no'),
        __('general.student_name'),
        __('general.date'),
        __('general.attendance'),
        __('general.category'),
        __('general.period'),
        __('general.deduct_average_point'),
        __('general.remarks'),
    ];


    // Test Excel
    Excel::fake();

    $view_name = 'reports.attendances.by-student-attendance-mark-deduction';

    $attendanceReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByAttendanceMarkDeductionData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $mocked_result,
            $locale,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title, false);

            foreach ($expected_headers as $header) {
                $view->assertSee($header, false);
            }

            $view->assertSee($mocked_result[0]['class_name']);

            foreach ($mocked_result[0]['students'] as $row) {
                $view->assertSee($row['student_name'][$locale]);
                $view->assertSee($row['student_number']);
                $view->assertSee($row['class'][$locale]);

                foreach ($row['attendances'] as $attendance) {
                    $view->assertSee($attendance['date']);
                    $view->assertSee($attendance['type']);
                    $view->assertSee($attendance['periods']);
                    $view->assertSee($attendance['total_deduct_average_point']);
                    $view->assertSee($attendance['reason'] ?? '');
                }
            }

            return true;
        }
    );


    // Test PDF
    SnappyPdf::fake();

    $attendanceReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getReportByAttendanceMarkDeductionData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.attendances.by-student-attendance-mark-deduction');

    SnappyPdf::assertSee($title, false);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header, false);
    }

    SnappyPdf::assertSee($mocked_result[0]['class_name']);

    foreach ($mocked_result[0]['students'] as $row) {
        SnappyPdf::assertSee($row['student_name'][$locale]);
        SnappyPdf::assertSee($row['student_number']);
        SnappyPdf::assertSee($row['class'][$locale]);

        foreach ($row['attendances'] as $attendance) {
            SnappyPdf::assertSee($attendance['date']);
            SnappyPdf::assertSee($attendance['type']);
            SnappyPdf::assertSee($attendance['periods']);
            SnappyPdf::assertSee($attendance['total_deduct_average_point']);
            SnappyPdf::assertSee($attendance['reason'] ?? '');
        }
    }
});

test('getClassAttendanceTakingStatus() preview data', function () {
    $filters = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-01',
        'employee_id' => $this->employee2->id,
    ];

    $response = $this->attendanceReportService->getClassAttendanceTakingStatus($filters);

    expect($response)
        ->toMatchArray([
            'period_groups' => [
                [
                    'period_group_name' => 'Period Group 1',
                    'periods' => [
                        [
                            'period' => 1,
                            "from_time" => "07:00",
                            "to_time" => "07:30",
                            'period_label_name' => 'Label 1',
                        ],
                        [
                            'period' => 2,
                            "from_time" => "07:31",
                            "to_time" => "08:00",
                            'period_label_name' => null,
                        ],
                        [
                            'period' => 3,
                            "from_time" => "08:01",
                            "to_time" => "08:30",
                            'period_label_name' => null,
                        ],
                    ]
                ],
                [
                    'period_group_name' => 'Period Group 2',
                    'periods' => [
                        [
                            'period' => 1,
                            "from_time" => "07:00",
                            "to_time" => "07:30",
                            'period_label_name' => 'Label 2',
                        ],
                        [
                            'period' => 2,
                            "from_time" => "07:31",
                            "to_time" => "08:00",
                            'period_label_name' => null,
                        ],
                        [
                            'period' => 3,
                            "from_time" => "08:01",
                            "to_time" => "08:30",
                            'period_label_name' => null,
                        ],
                    ]
                ],
            ],
            'dates' => [
                [
                    'date' => '2024-01-01',
                    'day' => 'Monday',
                    'attendances' => [
                        null,
                        [
                            'period_attendance_id' => $this->period_attendances[1]->id,
                            'timeslot_id' => $this->period_attendances[1]->timeslot_id,
                            'subject_name' => 'Subject 1',
                            'class_name' => 'Class 1',
                            'period' => 2,
                            'time_taken_at' => '18:10'
                        ],
                        [
                            'period_attendance_id' => $this->period_attendances[2]->id,
                            'timeslot_id' => $this->period_attendances[2]->timeslot_id,
                            'subject_name' => 'Subject 1',
                            'class_name' => 'Senior Class 1',
                            'period' => 3,
                            'time_taken_at' => '18:10'
                        ],
                    ]
                ]
            ],
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-01',
            'employee' => [
                'number' => $this->employee2->employee_number,
                'name' => $this->employee2->name,
            ]
        ]);
});

test('getClassAttendanceTakingStatus() generate pdf', function () {
    $filters = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-01-01',
        'employee_id' => $this->employee2->id,
    ];

    $response = $this->attendanceReportService->getClassAttendanceTakingStatus($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Class Teacher Attendance Report',
    ];
    $report_view_name = 'reports.attendances.class-attendance-taking-status-report';
    $file_name = 'class-attendance-taking-status-report';
    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);
    SnappyPdf::assertSee($report_data['title']);

    SnappyPdf::assertSee('Employee Number : ' . $this->employee2->employee_number);
    SnappyPdf::assertSee('Teacher : ' . $this->employee2->name);
    SnappyPdf::assertSee('Date : 2024-01-01 - 2024-01-01');

    SnappyPdf::assertSee('Date');

    foreach ($response['period_groups'] as $period_group) {
        SnappyPdf::assertSee($period_group['period_group_name']);

        foreach ($period_group['periods'] as $period) {
            if ($period['period_label_name']) {
                SnappyPdf::assertSee($period['period_label_name']);
            }

            SnappyPdf::assertSee($period['from_time']);
            SnappyPdf::assertSee($period['to_time']);
        }
    }

    foreach ($response['dates'] as $date) {
        SnappyPdf::assertSee($date['date']);
        SnappyPdf::assertSee($date['day']);

        foreach ($date['attendances'] as $attendance) {
            if (!$attendance) {
                continue;
            }
            SnappyPdf::assertSee($attendance['subject_name']);
            SnappyPdf::assertSee($attendance['class_name']);
            SnappyPdf::assertSee($attendance['time_taken_at']);
        }
    }
});
