<?php

use App\Enums\ConductReportSettingCategory;
use App\Models\ConductReportSetting;
use App\Models\Grade;
use App\Models\SemesterSetting;
use App\Repositories\ConductReportSettingRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->conductReportSettingRepository = app(ConductReportSettingRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(ConductReportSetting::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->conductReportSettingRepository->getModelClass();

    expect($response)->toEqual(ConductReportSetting::class);
});

test('getAll()', function (int $expected_count, array $filters, array $expected_model) {;

    $grade_1 = Grade::factory()->create(['id' => 1]);
    $grade_2 = Grade::factory()->create(['id' => 2]);

    $semester_setting_1 = SemesterSetting::factory()->create(['id' => 1]);
    $semester_setting_2 = SemesterSetting::factory()->create(['id' => 2]);

    $keys = [
        'first' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::ATTENDANCE->value,
            'year' => '2024',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id
        ]),
        'second' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_2->id,
            'semester_setting_id' => $semester_setting_1->id
        ]),
        'third' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::MARK_DEDUCT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_2->id
        ]),
    ];

    $result = $this->conductReportSettingRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by category = Attendance' => [1, ['category' => 'Attendance'], ['first']],
    'filter by category = Mark Deduct' => [1, ['category' => 'Mark Deduct'], ['third']],
    'filter by year = 2024' => [1, ['year' => '2024'], ['first']],
    'filter by year = 2025' => [2, ['year' => '2025'], ['second', 'third']],
    'filter by grade_id = 1' => [2, ['grade_id' => 1], ['first', 'third']],
    'filter by grade_id = 2' => [1, ['grade_id' => 2], ['second']],
    'filter by semester_setting_id = 1' => [2, ['semester_setting_id' => 1], ['first', 'second']],
    'filter by semester_setting_id = 2' => [1, ['semester_setting_id' => 2], ['third']],
]);

test('getAllPaginated()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $grade_1 = Grade::factory()->create(['id' => 1]);
    $grade_2 = Grade::factory()->create(['id' => 2]);

    $semester_setting_1 = SemesterSetting::factory()->create(['id' => 1]);
    $semester_setting_2 = SemesterSetting::factory()->create(['id' => 2]);

    $keys = [
        'first' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::ATTENDANCE->value,
            'year' => '2024',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id
        ]),
        'second' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_2->id,
            'semester_setting_id' => $semester_setting_1->id
        ]),
        'third' => ConductReportSetting::factory()->create([
            'category' => ConductReportSettingCategory::MARK_DEDUCT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_2->id
        ]),
    ];

    $result = $this->conductReportSettingRepository->getAllPaginated($filters)->toArray();
    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by category = Attendance' => [1, ['category' => 'Attendance'], ['first']],
    'filter by category = Mark Deduct' => [1, ['category' => 'Mark Deduct'], ['third']],
    'filter by year = 2024' => [1, ['year' => '2024'], ['first']],
    'filter by year = 2025' => [2, ['year' => '2025'], ['second', 'third']],
    'filter by grade_id = 1' => [2, ['grade_id' => 1], ['first', 'third']],
    'filter by grade_id = 2' => [1, ['grade_id' => 2], ['second']],
    'filter by semester_setting_id = 1' => [2, ['semester_setting_id' => 1], ['first', 'second']],
    'filter by semester_setting_id = 2' => [1, ['semester_setting_id' => 2], ['third']],
]);