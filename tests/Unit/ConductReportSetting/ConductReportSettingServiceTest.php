<?php

use App\Enums\ConductReportSettingCategory;
use App\Models\ConductReportSetting;
use App\Models\Grade;
use App\Models\SemesterSetting;
use App\Services\ConductReportSettingService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
    $this->conductReportSettingService = app(ConductReportSettingService::class);

    $this->table = resolve(ConductReportSetting::class)->getTable();
});

test('getAllConductReportSettings()', function () {

    $grade_1 = Grade::factory()->create();
    $grade_2 = Grade::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();


    $conduct_report_settings = ConductReportSetting::factory(3)->state(new Sequence(
        [
            'category' => ConductReportSettingCategory::ATTENDANCE->value,
            'year' => '2024',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_2->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MARK_DEDUCT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_2->id
        ]
    ))->create();

    $result = $this->conductReportSettingService->getAllConductReportSettings([]);
    expect($result)->toBeInstanceOf(Collection::class);

    expect($result->toArray())
        ->toHaveCount(3)
        ->and($result[0])->toMatchArray([    
            'category' => $conduct_report_settings[0]->category->value,
            'year' => $conduct_report_settings[0]->year,
            'from_date' => $conduct_report_settings[0]->from_date,
            'to_date' => $conduct_report_settings[0]->to_date,
            'grade_id' => $conduct_report_settings[0]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[0]->semester_setting_id
        ])
        ->and($result[1])->toMatchArray([  
            'category' => $conduct_report_settings[1]->category->value,
            'year' => $conduct_report_settings[1]->year,
            'from_date' => $conduct_report_settings[1]->from_date,
            'to_date' => $conduct_report_settings[1]->to_date,
            'grade_id' => $conduct_report_settings[1]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[1]->semester_setting_id
        ])
        ->and($result[2])->toMatchArray([   
            'category' => $conduct_report_settings[2]->category->value,
            'year' => $conduct_report_settings[2]->year,
            'from_date' => $conduct_report_settings[2]->from_date,
            'to_date' => $conduct_report_settings[2]->to_date,
            'grade_id' => $conduct_report_settings[2]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[2]->semester_setting_id
        ]);
    

    // filter test
    $filters = [ 'semester_setting_id' => $semester_setting_2->id ];

    $result = $this->conductReportSettingService->getAllConductReportSettings($filters);

    expect($result)->toBeInstanceOf(Collection::class);
    expect($result->toArray())
        ->toHaveCount(1)
        ->and($result[0])->toMatchArray([ 
            'category' => $conduct_report_settings[2]->category->value,
            'year' => $conduct_report_settings[2]->year,
            'from_date' => $conduct_report_settings[2]->from_date,
            'to_date' => $conduct_report_settings[2]->to_date,
            'grade_id' => $conduct_report_settings[2]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[2]->semester_setting_id
        ]);
});

test('getAllPaginatedConductReportSettings()', function () {

    $grade_1 = Grade::factory()->create();
    $grade_2 = Grade::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();


    $conduct_report_settings = ConductReportSetting::factory(3)->state(new Sequence(
        [
            'category' => ConductReportSettingCategory::ATTENDANCE->value,
            'year' => '2024',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MERIT_DEMERIT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_2->id,
            'semester_setting_id' => $semester_setting_1->id
        ],
        [
            'category' => ConductReportSettingCategory::MARK_DEDUCT->value,
            'year' => '2025',
            'from_date' => Carbon::parse('2025-03-01')->toDateString(),
            'to_date' => Carbon::parse('2025-04-30')->toDateString(),
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_2->id
        ]
    ))->create();

    $result = $this->conductReportSettingService->getAllPaginatedConductReportSettings();
    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);

    $result = $result->toArray();

    expect($result['data'])->toHaveCount(3)
        ->and($result['data'][0])->toMatchArray([    
            'category' => $conduct_report_settings[0]->category->value,
            'year' => $conduct_report_settings[0]->year,
            'from_date' => $conduct_report_settings[0]->from_date,
            'to_date' => $conduct_report_settings[0]->to_date,
            'grade_id' => $conduct_report_settings[0]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[0]->semester_setting_id
        ])
        ->and($result['data'][1])->toMatchArray([  
            'category' => $conduct_report_settings[1]->category->value,
            'year' => $conduct_report_settings[1]->year,
            'from_date' => $conduct_report_settings[1]->from_date,
            'to_date' => $conduct_report_settings[1]->to_date,
            'grade_id' => $conduct_report_settings[1]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[1]->semester_setting_id
        ])
        ->and($result['data'][2])->toMatchArray([   
            'category' => $conduct_report_settings[2]->category->value,
            'year' => $conduct_report_settings[2]->year,
            'from_date' => $conduct_report_settings[2]->from_date,
            'to_date' => $conduct_report_settings[2]->to_date,
            'grade_id' => $conduct_report_settings[2]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[2]->semester_setting_id
        ]);
    

    // filter test
    $filters = [ 'semester_setting_id' => $semester_setting_2->id ];

    $result = $this->conductReportSettingService->getAllPaginatedConductReportSettings($filters);

    expect($result)->toBeInstanceOf(LengthAwarePaginator::class);
    $result = $result->toArray();

    expect($result['data'])->toHaveCount(1)
        ->and($result['data'][0])->toMatchArray([ 
            'category' => $conduct_report_settings[2]->category->value,
            'year' => $conduct_report_settings[2]->year,
            'from_date' => $conduct_report_settings[2]->from_date,
            'to_date' => $conduct_report_settings[2]->to_date,
            'grade_id' => $conduct_report_settings[2]->grade_id,
            'semester_setting_id' =>$conduct_report_settings[2]->semester_setting_id
    ]);
});

test('bulkCreateOrUpdateConductReportSettings', function (){
    //bulk create
    $this->assertDatabaseCount($this->table, 0);

    $grade_1 = Grade::factory()->create();
    $grade_2 = Grade::factory()->create();
    $semester_setting = SemesterSetting::factory()->create();

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'conduct_report_settings' => [
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-03-01')->toDateString(),
                'to_date' => Carbon::parse('2025-04-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
        ]
    ];
    $this->conductReportSettingService->bulkCreateOrUpdateConductReportSettings($payload);
    $this->assertDatabaseCount($this->table, 6);

    foreach($payload['conduct_report_settings'] as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_setting_id' => $semester_setting->id,
            'category' => $input['category'],
            'from_date' => $input['from_date'],
            'to_date' => $input['to_date'],
            'year' => $input['year'],
            'grade_id' => $input['grade_id']
        ]);
    }

    // bulk update 
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'conduct_report_settings' => [
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-05-01')->toDateString(),
                'to_date' => Carbon::parse('2025-05-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-06-01')->toDateString(),
                'to_date' => Carbon::parse('2025-10-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-08-01')->toDateString(),
                'to_date' => Carbon::parse('2025-09-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_1->id,
            ],
            [
                'category' => ConductReportSettingCategory::ATTENDANCE->value ,
                'from_date' => Carbon::parse('2025-10-01')->toDateString(),
                'to_date' => Carbon::parse('2025-11-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT->value ,
                'from_date' => Carbon::parse('2025-11-01')->toDateString(),
                'to_date' => Carbon::parse('2025-11-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
            [
                'category' => ConductReportSettingCategory::MARK_DEDUCT_RETAIN->value ,
                'from_date' => Carbon::parse('2025-01-01')->toDateString(),
                'to_date' => Carbon::parse('2025-12-30')->toDateString(),
                'year' => '2025',
                'grade_id' => $grade_2->id,
            ],
        ]
    ];

    $this->conductReportSettingService->bulkCreateOrUpdateConductReportSettings($payload);
    $this->assertDatabaseCount($this->table, 6);
    
    foreach($payload['conduct_report_settings'] as $input){
        $this->assertDatabaseHas($this->table, [
            'semester_setting_id' => $semester_setting->id,
            'category' => $input['category'],
            'from_date' => $input['from_date'],
            'to_date' => $input['to_date'],
            'year' => $input['year'],
            'grade_id' => $input['grade_id']
        ]);
    }
});