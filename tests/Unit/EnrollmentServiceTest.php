<?php

use App\Enums\EnrollmentAction;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\MarriedStatus;
use App\Enums\PaymentType;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Config;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Education;
use App\Models\Enrollment;
use App\Models\GlAccount;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Media;
use App\Models\PaymentMethod;
use App\Models\Race;
use App\Models\Religion;
use App\Models\User;
use App\Services\EnrollmentService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->seedAccountingData();

    $this->enrollmentService = app(EnrollmentService::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();

    $this->enrollment_table_name = resolve(Enrollment::class)->getTable();
    $this->guardian_student_table_name = resolve(GuardianStudent::class)->getTable();
});

test('getAllPaginatedEnrollments()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'John Jones',
        'student_name->zh' => 'zh John Jones'
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'BBO Name'
    ]);

    // filter by student_name
    $result = $this->enrollmentService->getAllPaginatedEnrollments(['student_name' => 'John Jones'])->toArray()['data'];

    expect($result)->toHaveCount(1)
        ->and($result[0])
        ->toMatchArray([
            'id' => $first_enrollment->id,
            'admission_year' => $first_enrollment->admission_year,
            'admission_grade_id' => $first_enrollment->admission_grade_id,
            'student_name' => [
                'en' => 'John Jones',
                'zh' => 'zh John Jones'
            ],
            'birthplace_id' => $first_enrollment->birthplace_id,
            'nationality_id' => $first_enrollment->nationality_id,
            'gender' => $first_enrollment->gender->value,
            'birth_cert_no' => $first_enrollment->birth_cert_no,
            'nric_no' => $first_enrollment->nric_no,
            'passport_no' => $first_enrollment->passport_no,
            'race_id' => $first_enrollment->race_id,
            'religion_id' => $first_enrollment->religion_id,
            'phone_no' => $first_enrollment->phone_no,
            'email' => $first_enrollment->email,
            'address' => $first_enrollment->address,
            'status' => $first_enrollment->status->value,
            'step' => $first_enrollment->step,
            'created_by' => $first_enrollment->created_by,
        ]);

    // Sort by student_name asc
    $result = $this->enrollmentService->getAllPaginatedEnrollments([
        'order_by' => ['student_name' => 'asc'],
    ])->toArray();

    expect($result['data'])->sequence(
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('AA Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('BBO Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('John Jones'),
    );

    // Sort by student_name desc
    $result = $this->enrollmentService->getAllPaginatedEnrollments([
        'order_by' => ['student_name' => 'desc'],
    ])->toArray();

    expect($result['data'])->sequence(
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('John Jones'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('BBO Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('AA Name'),
    );

    // Sort by id asc
    $result = $this->enrollmentService->getAllPaginatedEnrollments([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($result['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
    );

    // Sort by id asc
    $result = $this->enrollmentService->getAllPaginatedEnrollments([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($result['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );
});

test('getAllEnrollments()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'John Jones',
        'student_name->zh' => 'zh John Jones'
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'BBO Name'
    ]);

    // filter by student_name
    $result = $this->enrollmentService->getAllEnrollments(['student_name' => 'John Jones'])->toArray();

    expect($result)->toHaveCount(1)
        ->and($result[0])
        ->toMatchArray([
            'id' => $first_enrollment->id,
            'admission_year' => $first_enrollment->admission_year,
            'admission_grade_id' => $first_enrollment->admission_grade_id,
            'student_name' => [
                'en' => 'John Jones',
                'zh' => 'zh John Jones'
            ],
            'birthplace_id' => $first_enrollment->birthplace_id,
            'nationality_id' => $first_enrollment->nationality_id,
            'gender' => $first_enrollment->gender->value,
            'birth_cert_no' => $first_enrollment->birth_cert_no,
            'nric_no' => $first_enrollment->nric_no,
            'passport_no' => $first_enrollment->passport_no,
            'race_id' => $first_enrollment->race_id,
            'religion_id' => $first_enrollment->religion_id,
            'phone_no' => $first_enrollment->phone_no,
            'email' => $first_enrollment->email,
            'address' => $first_enrollment->address,
            'status' => $first_enrollment->status->value,
            'step' => $first_enrollment->step,
            'created_by' => $first_enrollment->created_by,
        ]);

    // Sort by student_name asc
    $result = $this->enrollmentService->getAllEnrollments([
        'order_by' => ['student_name' => 'asc'],
    ])->toArray();

    expect($result)->sequence(
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('AA Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('BBO Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('John Jones'),
    );

    // Sort by student_name desc
    $result = $this->enrollmentService->getAllEnrollments([
        'order_by' => ['student_name' => 'desc'],
    ])->toArray();

    expect($result)->sequence(
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('John Jones'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('BBO Name'),
        fn($enrollment) => $enrollment->student_name->{$this->locale}->toBe('AA Name'),
    );

    // Sort by id asc
    $result = $this->enrollmentService->getAllEnrollments([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($result)->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
    );

    // Sort by id asc
    $result = $this->enrollmentService->getAllEnrollments([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($result)->sequence(
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );
});


test('createEnrollment()', function () {
    $user = User::factory()->create();
    $grade = Grade::factory()->create();
    $nationality = Country::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $birthplace = Country::factory()->create();

    $payload = [
        'admission_year' => 2024,
        'admission_grade_id' => $grade->id, // key to MasterGrade
        'student_name' => [
            'en' => 'John Doe',
        ],
        'nationality_id' => $nationality->id, // key to MasterCountry
        'nric_no' => '11110010nric_no',
        'birth_cert_no' => '312321321birth_cert_no',
        'gender' => Gender::FEMALE->value,
        'religion_id' => $religion->id, // key to MasterReligion
        'race_id' => $race->id, // key to MasterRace
        'address' => 'Long Address',
        'birthplace_id' => $birthplace->id, // key to MasterCountry
        'date_of_birth' => '01/12/2024',
        'passport_no' => '11110010passport',
        'phone_no' => '+21381093870',
        'email' => '<EMAIL>',
        'status' => EnrollmentStatus::DRAFT->value,
        'step' => 1,
        'created_by' => $user->id,
    ];

    $result = $this->enrollmentService->createEnrollment($payload);

    expect($result)->toBeInstanceOf(Enrollment::class)
        ->and($result->admission_year)->toEqual($payload['admission_year'])
        ->and($result->admission_grade_id)->toEqual($payload['admission_grade_id'])
        ->and($result->student_name)->toEqual($payload['student_name']['en'])
        ->and($result->nationality_id)->toEqual($payload['nationality_id'])
        ->and($result->nric_no)->toEqual($payload['nric_no'])
        ->and($result->birth_cert_no)->toEqual($payload['birth_cert_no'])
        ->and($result->gender->value)->toEqual($payload['gender'])
        ->and($result->religion_id)->toEqual($payload['religion_id'])
        ->and($result->race_id)->toEqual($payload['race_id'])
        ->and($result->address)->toEqual($payload['address'])
        ->and($result->birthplace_id)->toEqual($payload['birthplace_id'])
        ->and($result->date_of_birth)->toEqual($payload['date_of_birth'])
        ->and($result->passport_no)->toEqual($payload['passport_no'])
        ->and($result->phone_no)->toEqual($payload['phone_no'])
        ->and($result->email)->toEqual($payload['email'])
        ->and($result->status->value)->toEqual($payload['status'])
        ->and($result->step)->toEqual($payload['step'])
        ->and($result->created_by)->toEqual($payload['created_by']);
});

test('updateEnrollment() : update enrollment without guardian', function () {
    $enrollment = Enrollment::factory()->create();

    $user = User::factory()->create();
    $grade = Grade::factory()->create();
    $nationality = Country::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $birthplace = Country::factory()->create();

    $current_step = Enrollment::STEP_STUDENT_PROFILE;
    $next_step = Enrollment::STEP_GUARDIAN_PROFILE;

    $payload = [
        'admission_year' => 2024,
        'admission_grade_id' => $grade->id, // key to MasterGrade
        'student_name' => [
            'en' => 'John Doe',
        ],
        'nationality_id' => $nationality->id, // key to MasterCountry
        'nric_no' => '11110010nric_no',
        'birth_cert_no' => '312321321birth_cert_no',
        'gender' => Gender::FEMALE->value,
        'religion_id' => $religion->id, // key to MasterReligion
        'race_id' => $race->id, // key to MasterRace
        'address' => 'Long Address',
        'birthplace_id' => $birthplace->id, // key to MasterCountry
        'date_of_birth' => '01/10/2024',
        'passport_no' => '11110010passport',
        'phone_no' => '+21381093870',
        'email' => '<EMAIL>',
        'status' => EnrollmentStatus::DRAFT->value,
        'step' => $current_step,
        'action' => EnrollmentAction::NEXT->value,
        'created_by' => $user->id,
    ];

    $result = $this->enrollmentService->updateEnrollment($enrollment, $payload);

    expect($result)->toBeInstanceOf(Enrollment::class)
        ->and($result->admission_year)->toEqual($payload['admission_year'])
        ->and($result->admission_grade_id)->toEqual($payload['admission_grade_id'])
        ->and($result->student_name)->toEqual($payload['student_name']['en'])
        ->and($result->nationality_id)->toEqual($payload['nationality_id'])
        ->and($result->nric_no)->toEqual($payload['nric_no'])
        ->and($result->birth_cert_no)->toEqual($payload['birth_cert_no'])
        ->and($result->gender->value)->toEqual($payload['gender'])
        ->and($result->religion_id)->toEqual($payload['religion_id'])
        ->and($result->race_id)->toEqual($payload['race_id'])
        ->and($result->address)->toEqual($payload['address'])
        ->and($result->birthplace_id)->toEqual($payload['birthplace_id'])
        ->and($result->passport_no)->toEqual($payload['passport_no'])
        ->and($result->phone_no)->toEqual($payload['phone_no'])
        ->and($result->email)->toEqual($payload['email'])
        ->and($result->status->value)->toEqual($payload['status'])
        ->and($result->step)->toEqual($next_step)
        ->and($result->created_by)->toEqual($payload['created_by']);
});

test('updateEnrollment() : update enrollment with guardian', function () {
    $enrollment = Enrollment::factory()->create();

    $guardian_nationality = Country::factory()->create();
    $guardian_religion = Religion::factory()->create();
    $guardian_race = Race::factory()->create();

    $current_step = Enrollment::STEP_GUARDIAN_PROFILE;
    $next_step = Enrollment::STEP_DOCUMENT_UPLOAD;

    $education = Education::factory()->create();

    $payload = [
        'student_name' => [
            'en' => 'John Doe',
        ],
        'status' => EnrollmentStatus::DRAFT->value,
        'action' => EnrollmentAction::NEXT->value,
        'step' => $current_step,
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Cool Paul',
                ],
                'nric' => fake()->uuid(),
                'passport_number' => fake()->uuid(),
                'phone_number' => fake()->phoneNumber(),
                'email' => fake()->email(),
                'nationality_id' => $guardian_nationality->id,
                'race_id' => $guardian_race->id,
                'religion_id' => $guardian_religion->id,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test'
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Momo',
                ],
                'nric' => fake()->uuid(),
                'passport_number' => fake()->uuid(),
                'phone_number' => fake()->phoneNumber(),
                'email' => fake()->email(),
                'nationality_id' => $guardian_nationality->id,
                'race_id' => $guardian_race->id,
                'religion_id' => $guardian_religion->id,
            ],
        ]
    ];

    $result = $this->enrollmentService->updateEnrollment($enrollment, $payload);

    expect($result)->toBeInstanceOf(Enrollment::class)
        ->and($result->student_name)->toEqual($payload['student_name']['en'])
        ->and($result->status->value)->toEqual($payload['status'])
        ->and($result->step)->toEqual($next_step);

    foreach ($payload['guardians'] as $guardian_data) {
        $this->assertDatabaseHas('guardians', [
            'name->en' => $guardian_data['name']['en'],
            'nric' => $guardian_data['nric'],
            'passport_number' => $guardian_data['passport_number'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nationality_id' => $guardian_data['nationality_id'],
            'race_id' => $guardian_data['race_id'],
            'religion_id' => $guardian_data['religion_id'],
            'education_id' => Arr::get($guardian_data,'education_id'),
            'married_status' => Arr::get($guardian_data,'married_status'),
            'occupation' => Arr::get($guardian_data,'occupation'),
            'occupation_description' => Arr::get($guardian_data,'occupation_description'),
        ]);

        $this->assertDatabaseHas('guardian_student', [
            'studenable_id' => $result->id,
            'studenable_type' => get_class($result),
            'type' => $guardian_data['type'],
        ]);
    }

    //old linking will be removed
    $payload2 = [
        'student_name' => [
            'en' => 'John Doe',
        ],
        'status' => EnrollmentStatus::DRAFT->value,
        'action' => EnrollmentAction::NEXT->value,
        'step' => $current_step,
        'guardians' => [
            [
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Cool Paul',
                ],
                'nric' => fake()->uuid(),
                'passport_number' => fake()->uuid(),
                'phone_number' => fake()->phoneNumber(),
                'email' => fake()->email(),
                'nationality_id' => $guardian_nationality->id,
                'race_id' => $guardian_race->id,
                'religion_id' => $guardian_religion->id,
            ]
        ]
    ];

    $result = $this->enrollmentService->updateEnrollment($enrollment, $payload2);

    foreach ($payload2['guardians'] as $guardian_data) {
        $this->assertDatabaseHas('guardians', [
            'name->en' => $guardian_data['name']['en'],
            'nric' => $guardian_data['nric'],
            'passport_number' => $guardian_data['passport_number'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nationality_id' => $guardian_data['nationality_id'],
            'race_id' => $guardian_data['race_id'],
            'religion_id' => $guardian_data['religion_id'],
        ]);

        $this->assertDatabaseHas('guardian_student', [
            'studenable_id' => $result->id,
            'studenable_type' => get_class($result),
            'type' => $guardian_data['type'],
        ]);
    }

    foreach ($payload['guardians'] as $guardian_data) {
        $this->assertDatabaseMissing('guardian_student', [
            'studenable_id' => $result->id,
            'studenable_type' => get_class($result),
            'type' => $guardian_data['type'],
        ]);
    }
});

test('upload()', function () {
    $enrollment = Enrollment::factory()->create();

    // nric file
    $nric_file = UploadedFile::fake()->create('nric.png');

    $payload = [
        'file' => $nric_file,
        'type' => Enrollment::TYPE_FILE_NRIC,
    ];

    $result = $this->enrollmentService->upload($enrollment, $payload);

    expect($result)->toBeInstanceOf(Media::class)
        ->and($enrollment->getMedia(Enrollment::TYPE_FILE_NRIC)->count())->toEqual(1);
});


test('getUploadableFilesOptions()', function () {
    $result = $this->enrollmentService->getUploadableFilesOptions();

    $expected_data = [
        [
            "value" => "file_nric",
            "label" => "NRIC"
        ],
        [
            "value" => "file_passport",
            "label" => "Passport"
        ]
    ];
    expect($result)->toBe($expected_data);

    Config::create([
        'key' => 'ENROLLMENT_DOCUMENT_TYPE',
        'category' => Config::CATEGORY_GENERAL,
        'value' => ['file_ic_front']
    ]);

    $result = $this->enrollmentService->getUploadableFilesOptions();

    $expected_data[] = ['value' => 'file_ic_front', 'label' => 'IC Front'];
    expect($result)->toBe($expected_data);
});

test('generatePaymentUrl()', function () {
    $currency = Currency::factory()->malaysiaCurrency()->create();
    $guardian = Guardian::factory()->hasUser()->create();
    $enrollment = Enrollment::factory()->create(['created_by' => $guardian->user->id]);
    Config::create([
        'key' => Config::ENROLLMENT_FEES,
        'category' => Config::CATEGORY_GENERAL,
        'value' => 500
    ]);

    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'category' => Config::CATEGORY_GENERAL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'category' => Config::CATEGORY_GENERAL,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $result = $this->enrollmentService->generatePaymentUrl($guardian->user, $enrollment, [
        'payment_type' => PaymentMethod::CODE_FPX,
        'customer_email' => '<EMAIL>',
        'customer_name' => 'Test name'
    ])->toArray();

    expect($result)->toMatchArray([
        "type" => PaymentType::ENROLLMENT_PAYMENT->value,
        "provider" => "PAYEX",
        "transaction_loggable_type" => Enrollment::class,
        "transaction_loggable_id" => $enrollment->id,
        "currency_id" => $currency->id,
        "currency_code" => $currency->code,
        "currency_name" => $currency->name,
        "amount" => 500,
        "status" => "PENDING",
        "description" => "Enrollment Payment",
        "payment_url" => "VALID_PAYMENT_URL",
    ]);

    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 1);

    $billing_document = BillingDocument::first();

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'type' => BillingDocument::TYPE_INVOICE,
        'paid_at' => null,
        'amount_before_tax' => 500,
        'bill_to_id' => $enrollment->id,
        'bill_to_type' => get_class($enrollment),
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $billing_document->id,
        'currency_code' => config('school.currency_code'),
        'description' => 'Enrollment Entrance Exam Fees',
        'amount_before_tax' => 500,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT_ADMISSION,
        'offset_billing_document_id' => null,
        'billable_item_id' => $enrollment->id,
        'billable_item_type' => get_class($enrollment),
    ]);
});

test('linkGuardian()', function () {
    $guardian = Guardian::factory()->create();
    $enrollment = Enrollment::factory()->create();

    $this->enrollmentService->linkGuardian($enrollment, $guardian, 'FATHER');

    $this->assertDatabaseHas($this->guardian_student_table_name, [
        'studenable_id' => $enrollment->id,
        'studenable_type' => Enrollment::class,
        'guardian_id' => $guardian->id,
    ]);
});


test('billingDocumentVoidedActionCallback', function() {

    // pending -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // failed -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::PAYMENT_FAILED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // SUBMITTED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::SUBMITTED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::SUBMITTED,
    ]);


    // APPROVED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::APPROVED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::APPROVED,
    ]);


    // DRAFT -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::DRAFT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::DRAFT,
    ]);


    // ENROLLED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'status' => EnrollmentStatus::ENROLLED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'status' => EnrollmentStatus::ENROLLED,
    ]);


});
