<?php

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\SchoolLevel;
use App\Models\Enrollment;
use App\Models\EnrollmentExam;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\GuardianStudent;
use App\Models\HealthConcern;
use App\Models\Religion;
use App\Models\School;
use App\Models\Student;
use App\Models\Subject;
use App\Services\EnrollmentService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;

beforeEach(function () {
    $this->seedAccountingData();

    $this->enrollmentService = app(EnrollmentService::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();

    $this->enrollment_table_name = resolve(Enrollment::class)->getTable();
    $this->guardian_student_table_name = resolve(GuardianStudent::class)->getTable();
});

test('getAllPaginatedEnrollments()', function () {
    $enrollments = Enrollment::factory(2)->create();

    $this->mock(EnrollmentService::class, function (\Mockery\MockInterface $mock) use ($enrollments) {
        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->once()
            ->andReturn(new LengthAwarePaginator($enrollments, 2, 1));
    });

    $response = app()->make(EnrollmentService::class)
        ->getAllPaginatedEnrollments()
        ->toArray();

    expect($response['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $enrollments[0]->id)
        ->toHaveKey('1.id', $enrollments[1]->id);
});

test('getAllEnrollments()', function () {
    $enrollments = Enrollment::factory(2)->create();

    $this->mock(EnrollmentService::class, function (\Mockery\MockInterface $mock) use ($enrollments) {
        $mock->shouldReceive('getAllEnrollments')
            ->once()
            ->andReturn($enrollments);
    });

    $response = app()->make(EnrollmentService::class)
        ->getAllEnrollments()
        ->toArray();

    expect($response)->toHaveCount(2)
        ->toHaveKey('0.id', $enrollments[0]->id)
        ->toHaveKey('1.id', $enrollments[1]->id);
});

test('billingDocumentVoidedActionCallback', function () {

    // pending -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // failed -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::PAYMENT_FAILED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // SUBMITTED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::SUBMITTED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::SUBMITTED,
    ]);


    // APPROVED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::APPROVED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::APPROVED,
    ]);


    // DRAFT -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::DRAFT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::DRAFT,
    ]);


    // ENROLLED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::ENROLLED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::ENROLLED,
    ]);
});

test('getTemplateData() - for post and pre payment', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(4)->state(new Sequence(
        [
            'code' => 'ENGLISH',
            'name->en' => 'English',
        ],
        [
            'code' => 'MATHS',
            'name->en' => 'Mathematics',
        ],
        [
            'code' => 'SCIENCE',
            'name->en' => 'Science',
        ],
        [
            'code' => 'ART',
            'name->en' => 'Art',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    /**
     * invalid enrollment session id
     */

    $response = $this->enrollmentService->getTemplateData(110000, false);

    expect($response)->toBeArray()->toHaveCount(0);

    /**
     * get full template data
     */

    $expected_response = [
        'number' => '1',
        'exam_slip_number' => 'R6566 - SAMPLEDATA',
        'student_name_en' => 'John Doe - SAMPLEDATA',
        'student_name_zh' => '约翰·多 - SAMPLEDATA',
        'nric' => '110113101982 - SAMPLEDATA',
        'passport_number' => 'A1234567 - SAMPLEDATA',
        'religion' => 'Christian - SAMPLEDATA',
        'gender' => 'MALE - SAMPLEDATA',
        'guardian_phone_number' => '********** - SAMPLEDATA',
        'guardian_email' => '<EMAIL> - SAMPLEDATA',
        'guardian_name' => 'Jane Doe - SAMPLEDATA',
        'guardian_type' => 'MOTHER - SAMPLEDATA',
        'total_average' => '80.05',
        'status' => 'APPROVED - SAMPLEDATA',
        'address' => '123 Sample Street - SAMPLEDATA',
        'primary_school' => 'Pin Hwa - SAMPLEDATA',
        'hostel' => 'TRUE - SAMPLEDATA',
        'have_siblings' => 'FALSE - SAMPLEDATA',
        'dietary_restriction' => 'NONE - SAMPLEDATA',
        'health_concern' => 'NONE - SAMPLEDATA',
        'foreigner' => 'FALSE - SAMPLEDATA',
        'conduct' => 'A+ - SAMPLEDATA',
        'remarks' => 'remarks - SAMPLEDATA',
    ];

    expect($enrollment_session->examSubjects)->toHaveCount(4);

    foreach ($enrollment_session->examSubjects->sortBy('code') as $subject) {
        $expected_response[$subject->code] = '80.05';
    }

    // get full template
    $response = $this->enrollmentService->getTemplateData($enrollment_session->id, false);

    expect($response['data'])->toEqual([$expected_response]);

    foreach ($enrollment_session->examSubjects as $subject) {
        expect($response['subject_lists'])->toHaveKey($subject->code);
    }


    /**
     * get lesser template data
     */

    $expected_response_2 = [
        'number' => '1',
        'student_name_en' => 'John Doe - SAMPLEDATA',
        'nric' => '110113101982 - SAMPLEDATA',
        'passport_number' => 'A1234567 - SAMPLEDATA',
        'hostel' => 'TRUE - SAMPLEDATA',
        'total_average' => '80.05',
        'status' => 'APPROVED - SAMPLEDATA',
    ];

    foreach ($enrollment_session->examSubjects->sortBy('code') as $subject) {
        $expected_response_2[$subject->code] = '80.05';
    }

    // get post payment template
    $response_2 = $this->enrollmentService->getTemplateData($enrollment_session->id, true);

    expect($response_2['data'])->toEqual([$expected_response_2]);

    foreach ($enrollment_session->examSubjects as $subject) {
        expect($response_2['subject_lists'])->toHaveKey($subject->code);
    }
});


test('transformExcelToCollection()', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => '002',
            'name->en' => 'English',
        ],
        [
            'code' => '001',
            'name->en' => 'Mathematics',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    // convert Enrollment Template Excel to collection
    $row1 = [
        '1', 'R6566', 'John Doe', '约翰·多', '110113101982', 'A1234567', 'Christian', 'MALE', '6**********', '<EMAIL>', 'Frank Reynold', 'FATHER',
        '82.05', '80.05', '81.05', 'APPROVED', '123 Sample Street', 'Pin Hwa', 'TRUE', 'TRUE', 'VEGETARIAN', 'Adhd', 'TRUE', 'A+', 'remarks',
    ];

    $row2 = [
        '2', 'R6566', 'John Cena', '约翰·多', '110113101982', 'A1234568', 'Buddhism', 'MALE', '60123456790', '<EMAIL>', 'Dee Reynold', 'MOTHER',
        '83.05', '81.05', '82.05', 'REJECTED', '555 Sample Street', 'SK KB', 'TRUE', 'TRUE', 'VEGETARIAN', 'Adhd', 'TRUE', 'B', 'yohoo',
    ];

    $header = [
        'No', '考生编号 / Exam Slip', 'Student Name', '学生姓名', '身份证号码 / IC Number', 'Passport Number', 'Religion', '性别 / Gender (MALE, FEMALE)',
        '监护人电话号码 / Guardian Phone Number', 'Guardian Email', '监护人姓名 / Guardian Name', '监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)',
        '总平均 / Total Average', 'English Language', 'Mathematics', 'Status (APPROVED, REJECTED, SHORLISTED)', '地址 / Address',
        '就读小学 / Primary School', '宿舍 / Hostel (TRUE, FALSE)', '兄弟姐妹 / Have Siblings (TRUE, FALSE)', '膳食 / Dietary Restrictions (NONE, VEGETARIAN)',
        '健康问题 / Health Concern', '外藉生/Foreigner (TRUE, FALSE)', '操行 / Conduct', '备注 /Remarks',
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $response = $this->enrollmentService
        ->setImportFile($test_excel)
        ->setEnrollmentSession($enrollment_session)
        ->transformExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => $row1[0],
            'exam_slip_number' => $row1[1],
            'student_name_en' => $row1[2],
            'student_name_zh' => $row1[3],
            'nric' => $row1[4],
            'passport_number' => $row1[5],
            'religion' => $row1[6],
            'gender' => $row1[7],
            'guardian_phone_number' => $row1[8],
            'guardian_email' => $row1[9],
            'guardian_name' => $row1[10],
            'guardian_type' => $row1[11],
            'total_average' => $row1[12],
            "001" => $row1[13], // sorted by code
            "002" => $row1[14],
            'status' => $row1[15],
            'address' => $row1[16],
            'primary_school' => $row1[17],
            'hostel' => true,
            'have_siblings' => true,
            'dietary_restriction' => $row1[20],
            'health_concern' => $row1[21],
            'foreigner' => true,
            'conduct' => $row1[23],
            'remarks' => $row1[24],
        ],
        [
            'number' => $row2[0],
            'exam_slip_number' => $row2[1],
            'student_name_en' => $row2[2],
            'student_name_zh' => $row2[3],
            'nric' => $row2[4],
            'passport_number' => $row2[5],
            'religion' => $row2[6],
            'gender' => $row2[7],
            'guardian_phone_number' => $row2[8],
            'guardian_email' => $row2[9],
            'guardian_name' => $row2[10],
            'guardian_type' => $row2[11],
            'total_average' => $row2[12],
            "001" => $row2[13], // sorted by code
            "002" => $row2[14],
            'status' => $row2[15],
            'address' => $row2[16],
            'primary_school' => $row2[17],
            'hostel' => true,
            'have_siblings' => true,
            'dietary_restriction' => $row2[20],
            'health_concern' => $row2[21],
            'foreigner' => true,
            'conduct' => $row2[23],
            'remarks' => $row2[24],
        ]
    ]);


    /**
     * Throw error because the row has more columns than expected
     */

    // convert Enrollment Template Excel to collection
    $row1 = [
        '1', 'R6566', 'John Doe', '约翰·多', '110113101982', 'A1234567', 'Christian', 'MALE', '6**********', '<EMAIL>', 'Frank Reynold', 'FATHER',
        '82.05', '80.05', '81.05', 'APPROVED', '123 Sample Street', 'Pin Hwa', 'TRUE', 'TRUE', 'VEGETARIAN', 'Adhd', 'TRUE', 'A+', 'remarks', 'END OF ROW',
    ];

    $header = [
        'No', '考生编号 / Exam Slip', 'Student Name', '学生姓名', '身份证号码 / IC Number', 'Passport Number', 'Religion', '性别 / Gender (MALE, FEMALE)',
        '监护人电话号码 / Guardian Phone Number', 'Guardian Email', '监护人姓名 / Guardian Name', '监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)',
        '总平均 / Total Average', 'English Language', 'Mathematics', 'Status (APPROVED, REJECTED, SHORLISTED)', '地址 / Address',
        '就读小学 / Primary School', '宿舍 / Hostel (TRUE, FALSE)', '兄弟姐妹 / Have Siblings (TRUE, FALSE)', '膳食 / Dietary Restrictions (NONE, VEGETARIAN)',
        '健康问题 / Health Concern', '外藉生/Foreigner (TRUE, FALSE)', '操行 / Conduct', '备注 /Remarks',
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
    ]);

    expect(function () use ($test_excel, $enrollment_session) {
        $response = $this->enrollmentService
            ->setImportFile($test_excel)
            ->setEnrollmentSession($enrollment_session)
            ->transformExcelToCollection();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(5017);
    }, 'Column counts do not match.');
});

test('validateBaseForImport', function () {
    $service = app(EnrollmentService::class);

    // Valid data with all required fields
    $valid_data = [
        [
            'number' => '1',
            'exam_slip_number' => 'E1234',
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰',
            'nric' => '123456789012',
            'passport_number' => 'A1234567',
            'status' => 'SHORTLISTED',
            'religion' => 'Christian',
            'gender' => 'MALE',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'guardian_phone_number' => '12345678',
            'guardian_email' => '<EMAIL>',
            'address' => '123 Street',
            'primary_school' => 'ABC School',
            'total_average' => '85.5',
            'hostel' => true,
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'health_concern' => 'None',
            'foreigner' => false,
            'conduct' => 'A',
            'remarks' => 'Good student'
        ]
    ];

    // Test valid data - shouldn't have errors
    $service->setData($valid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0])->not->toHaveKey('errors');

    // Test missing required field
    $invalid_data = $valid_data;
    $invalid_data[0]['student_name_en'] = '';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['student_name_en'][0])->toBe('The Student Name En field is required.');

    // Test missing both NRIC and passport
    $invalid_data = $valid_data;
    $invalid_data[0]['nric'] = '';
    $invalid_data[0]['passport_number'] = '';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['nric'][0])->toBe('Either NRIC or Passport number must be filled.');

    // Test invalid status
    $invalid_data = $valid_data;
    $invalid_data[0]['status'] = 'INVALID_STATUS';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['status'][0])->toBe('Status must be SHORTLISTED, REJECTED or APPROVED.');

    // Test NRIC not 12 digits
    $invalid_data = $valid_data;
    $invalid_data[0]['nric'] = '12345';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['nric'][0])->toBe('NRIC must be 12 digits.');

    // Test duplicate NRIC
    $duplicateData = [
        $valid_data[0],
        array_merge($valid_data[0], ['number' => '2', 'nric' => '123456789012'])
    ];

    $service->setData($duplicateData);
    $result = $service->validateBaseForImport()->getData();
    expect($result[1]['errors']['nric'][0])->toBe('NRIC is duplicated.');

    // Test duplicate passport number
    $duplicatePassportData = [
        $valid_data[0],
        array_merge($valid_data[0], ['number' => '2', 'nric' => '987654321098', 'passport_number' => 'A1234567'])
    ];

    $service->setData($duplicatePassportData);
    $result = $service->validateBaseForImport()->getData();
    expect($result[1]['errors']['passport_number'][0])->toBe('Passport number is duplicated.');

    // Test non-boolean hostel
    $invalid_data = $valid_data;
    $invalid_data[0]['hostel'] = 'not-a-boolean';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['hostel'][0])->toBe('Hostel must be boolean.');
});

test('validatePrePaymentInfoForImport', function () {
    $service = app(EnrollmentService::class);

    // Valid data with all requirements met
    $valid_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ]
    ];

    // Test valid data - should have no errors
    $service->setData($valid_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0])->not->toHaveKey('errors');

    // Test duplicate exam slip numbers
    $duplicate_exam_slip_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ],
        [
            'exam_slip_number' => 'E12345', // Duplicate
            'guardian_type' => 'MOTHER',
            'have_siblings' => false,
            'dietary_restriction' => 'VEGETARIAN',
            'foreigner' => true,
        ]
    ];

    $service->setData($duplicate_exam_slip_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[1]['errors']['exam_slip_number'][0])->toBe('Exam slip number is duplicated.');

    // Test invalid guardian type
    $invalid_guardian_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'INVALID_TYPE', // Invalid
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_guardian_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['guardian_type'][0])->toBe('Guardian type must be GUARDIAN, FATHER or MOTHER.');

    // Test non-boolean for have_siblings field
    $invalid_siblings_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => 'not-a-boolean', // Invalid
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_siblings_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['have_siblings'][0])->toBe('Have siblings must be boolean.');

    // Test invalid dietary restriction
    $invalid_dietary_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'INVALID_DIET', // Invalid
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_dietary_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['dietary_restriction'][0])->toBe('Dietary restriction must be ' . implode(', ', DietaryRestriction::values()));

    // Test non-boolean foreigner
    $invalid_foreigner_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => 'not-a-boolean', // Invalid
        ]
    ];

    $service->setData($invalid_foreigner_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['foreigner'][0])->toBe('Foreigner must be boolean.');
});

test('validateReligionForImport', function () {
    Religion::factory(2)->state(new Sequence(
        [
            'name->en' => 'Christianity',
            'name->zh' => '基督教',
        ],
        [
            'name->en' => 'Buddhism',
            'name->zh' => '佛教',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing religions
    $test_data = [
        [
            'religion' => 'Christianity', // Exists
            'other_field' => 'value'
        ],
        [
            'religion' => 'Islam', // Doesn't exist
            'other_field' => 'value'
        ],
        [
            'religion' => 'Islam', // Doesn't exist
            'other_field' => 'value'
        ],
        [
            'religion' => '佛教', // Exists (in Chinese)
            'other_field' => 'value'
        ],
        [
            'religion' => 'Hinduism', // Doesn't exist
            'other_field' => 'value'
        ]
    ];

    $service->setData($test_data);
    $result = $service->validateReligionForImport()->getData();

    // Existing religion should not have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[3])->not->toHaveKey('warnings');

    // Non-existing religions should have warnings and be added to creation list
    expect($result[1]['warnings']['religion'][0])
        ->toBe('Religion Islam does not exist in the database. It will be created.');
    expect($result[2]['warnings']['religion'][0])
        ->toBe('Religion Islam does not exist in the database. It will be created.');
    expect($result[4]['warnings']['religion'][0])
        ->toBe('Religion Hinduism does not exist in the database. It will be created.');

    // Check that they were added to the to-be-created list
    expect($service->getToBeCreatedReligions())->toContain('Islam');
    expect($service->getToBeCreatedReligions())->toContain('Hinduism');
    expect(count($service->getToBeCreatedReligions()))->toBe(2);
});

test('validateNRICForImport', function () {
    Student::factory(2)->state(new Sequence(
        [
            'nric' => '123456789012',
        ],
        [
            'nric' => '987654321098',
        ]
    ))->create();

    // Create test data with both existing and non-existing NRICs
    $test_data = [
        [
            'nric' => '123456789012', // Exists in DB
            'other_field' => 'value1'
        ],
        [
            'nric' => '111111111111', // Doesn't exist in DB
            'other_field' => 'value2'
        ],
        [
            'nric' => '987654321098', // Exists in DB
            'other_field' => 'value3'
        ],
        [
            'nric' => '222222222222', // Doesn't exist in DB
            'other_field' => 'value4'
        ]
    ];

    $service = app(EnrollmentService::class);
    $service->setData($test_data);

    // Run the validation
    $result = $service->validateNRICForImport()->getData();

    // Check that existing NRICs have errors
    expect($result[0]['errors']['nric'][0])
        ->toBe('NRIC 123456789012 already exists in the database.');
    expect($result[2]['errors']['nric'][0])
        ->toBe('NRIC 987654321098 already exists in the database.');

    // Check that non-existing NRICs don't have errors
    expect($result[1])->not->toHaveKey('errors');
    expect($result[3])->not->toHaveKey('errors');
});

test('validatePassportNumberForImport', function () {
    Student::factory(2)->state(new Sequence(
        [
            'passport_number' => 'AAA1212121',
        ],
        [
            'passport_number' => 'B02910902',
        ]
    ))->create();

    // Create test data with both existing and non-existing passport_numbers
    $test_data = [
        [
            'passport_number' => 'AAA1212121', // Exists in DB
            'other_field' => 'value1'
        ],
        [
            'passport_number' => '111111111111', // Doesn't exist in DB
            'other_field' => 'value2'
        ],
        [
            'passport_number' => 'B02910902', // Exists in DB
            'other_field' => 'value3'
        ],
        [
            'passport_number' => '222222222222', // Doesn't exist in DB
            'other_field' => 'value4'
        ]
    ];

    $service = app(EnrollmentService::class);
    $service->setData($test_data);

    // Run the validation
    $result = $service->validatePassportNumberForImport()->getData();

    // Check that existing passport_numbers have errors
    expect($result[0]['errors']['passport_number'][0])
        ->toBe('Passport number AAA1212121 already exists in the database.');
    expect($result[2]['errors']['passport_number'][0])
        ->toBe('Passport number B02910902 already exists in the database.');

    // Check that non-existing passport_numbers don't have errors
    expect($result[1])->not->toHaveKey('errors');
    expect($result[3])->not->toHaveKey('errors');
});

test('validatePrimarySchoolForImport', function () {
    School::factory(2)->state(new Sequence(
        [
            'name->en' => 'Sekolah Jenis Kebangsaan',
            'name->zh' => '国民型中学',
        ],
        [
            'name->en' => 'Pin Hwa',
            'name->zh' => '品华',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing primary schools
    $test_data = [
        [
            'primary_school' => 'Sekolah Jenis Kebangsaan', // Exists in English
            'other_field' => 'value1'
        ],
        [
            'primary_school' => 'Sekolah Menengah', // Doesn't exist
            'other_field' => 'value2'
        ],
        [
            'primary_school' => '品华', // Exists in Chinese
            'other_field' => 'value3'
        ],
        [
            'primary_school' => 'International School', // Doesn't exist
            'other_field' => 'value4'
        ],
        [
            'primary_school' => 'International School', // Doesn't exist
            'other_field' => 'value4'
        ],
    ];

    $service->setData($test_data);
    $result = $service->validatePrimarySchoolForImport()->getData();

    // Check that existing schools don't have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[2])->not->toHaveKey('warnings');

    // Check that non-existing schools have appropriate warnings
    expect($result[1]['warnings']['primary_school'][0])
        ->toBe('School Sekolah Menengah does not exist in the database. It will be created.');
    expect($result[3]['warnings']['primary_school'][0])
        ->toBe('School International School does not exist in the database. It will be created.');
    expect($result[4]['warnings']['primary_school'][0])
        ->toBe('School International School does not exist in the database. It will be created.');

    // Check that non-existing schools were added to the to-be-created list
    $to_be_created = $service->getToBeCreatedPrimarySchools();
    expect($to_be_created)->toContain('Sekolah Menengah');
    expect($to_be_created)->toContain('International School');
    expect(count($to_be_created))->toBe(2);
});

test('validateHealthConcernForImport', function () {
    HealthConcern::factory(2)->state(new Sequence(
        [
            'name->en' => 'Asthma',
            'name->zh' => '哮喘',
        ],
        [
            'name->en' => 'ADHD',
            'name->zh' => '注意力不足过动症',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing health concerns
    $test_data = [
        [
            'health_concern' => 'Asthma', // Exists in English
            'other_field' => 'value1'
        ],
        [
            'health_concern' => 'Allergies', // Doesn't exist
            'other_field' => 'value2'
        ],
        [
            'health_concern' => '哮喘', // Exists in Chinese
            'other_field' => 'value3'
        ],
        [
            'health_concern' => 'Diabetes', // Doesn't exist
            'other_field' => 'value4'
        ],
        [
            'health_concern' => 'Diabetes', // Doesn't exist
            'other_field' => 'value4'
        ],
    ];

    $service->setData($test_data);
    $result = $service->validateHealthConcernForImport()->getData();

    // Existing health concerns should not have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[2])->not->toHaveKey('warnings');

    // Non-existing health concerns should have warnings
    expect($result[1]['warnings']['health_concern'][0])
        ->toBe('Health concern Allergies does not exist in the database. It will be created.');
    expect($result[3]['warnings']['health_concern'][0])
        ->toBe('Health concern Diabetes does not exist in the database. It will be created.');
    expect($result[4]['warnings']['health_concern'][0])
        ->toBe('Health concern Diabetes does not exist in the database. It will be created.');

    // Check that non-existing health concerns were added to the to-be-created list
    $to_be_created = $service->getToBeCreatedHealthConcerns();
    expect($to_be_created)->toContain('Allergies');
    expect($to_be_created)->toContain('Diabetes');
    expect(count($to_be_created))->toBe(2);
});

test('validateSubjectForImport', function () {
    Subject::factory(3)->state(new Sequence(
        [
            'code' => 'ENG001',
            'name->en' => 'English',
        ],
        [
            'code' => 'MATH001',
            'name->en' => 'Mathematics',
        ],
        [
            'code' => 'SCI001',
            'name->en' => 'Science',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $enrollment_session->examSubjects()->attach(Subject::all());

    $service = app(EnrollmentService::class);
    $service->setEnrollmentSession($enrollment_session);

    // Test data with all required subjects
    $complete_data = [
        [
            'student_name_en' => 'John Doe',
            'ENG001' => '85.5',
            'MATH001' => '90.0',
            'SCI001' => '78.5',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'ENG001' => '92.0',
            'MATH001' => '88.5',
            'SCI001' => '95.0',
        ]
    ];

    $service->setData($complete_data);
    $result = $service->validateSubjectForImport()->getData();

    // No errors should exist when all subjects are present
    expect($result[0])->not->toHaveKey('errors');
    expect($result[1])->not->toHaveKey('errors');

    // Test data with missing subjects
    $incomplete_data = [
        [
            'student_name_en' => 'John Doe',
            'ENG001' => '85.5',
            // Missing MATH001
            'SCI001' => '78.5',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'ENG001' => '92.0',
            'MATH001' => '88.5',
            // Missing SCI001
        ]
    ];

    $service->setData($incomplete_data);
    $result = $service->validateSubjectForImport()->getData();

    // First row should have error for missing MATH001
    expect($result[0]['errors']['MATH001'][0])
        ->toBe('Subject MATH001 is missing in the import file.');

    // Second row should have error for missing SCI001
    expect($result[1]['errors']['SCI001'][0])
        ->toBe('Subject SCI001 is missing in the import file.');
});

test('validatePhoneNumberForImport', function () {
    $data = [
        [
            'guardian_phone_number' => '+6**********', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '**********', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '123456787', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => 'invalid',
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '+601234567', // invalid number - not enough digits
            'errors' => [],
        ],
    ];

    $this->enrollmentService->setData($data);

    $result = $this->enrollmentService->validatePhoneNumberForImport();

    $validated = $result->getData();

    expect($result->getImportErrorCount())->toBe(2);

    expect($validated[0]['guardian_phone_number'])->toBe('+6**********'); // Should be normalized to include country code
    expect($validated[0]['errors'])->toBeEmpty();

    expect($validated[1]['guardian_phone_number'])->toBe('+6**********'); // Should be normalized to include country code
    expect($validated[1]['errors'])->toBeEmpty();

    expect($validated[2]['guardian_phone_number'])->toBe('+60123456787'); // Should be normalized to include country code
    expect($validated[2]['errors'])->toBeEmpty();

    expect($validated[3]['guardian_phone_number'])->toBe('invalid');
    expect($validated[3]['errors']['guardian_phone_number'])->toBe([
        'Phone number without country code must start with Malaysian number: 601, 01, 1',
    ]);

    expect($validated[4]['guardian_phone_number'])->toBe('+601234567');
    expect($validated[4]['errors']['guardian_phone_number'])->toBe([
        'Invalid phone number.',
    ]);
});

test('validatePhoneNumberAndEmailUniquenessForImport', function () {
    $existing_user_1 = EnrollmentUser::factory()->create([
        'phone_number' => '+6**********',
        'email' => '<EMAIL>',
    ]);

    $existing_user_2 = EnrollmentUser::factory()->create([
        'phone_number' => '+60134567890',
        'email' => '<EMAIL>',
    ]);

    $data = [
        // Scenario 1: Valid - New phone number and email
        [
            'guardian_phone_number' => '+60145678901',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 2: Invalid - Existing phone number with different email
        [
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 3: Invalid - Existing email with different phone number
        [
            'guardian_phone_number' => '+60144444444',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 4: Valid - Existing phone number and email (same as existing_user_1)
        [
            'guardian_phone_number' => $existing_user_1->phone_number,
            'guardian_email' => $existing_user_1->email,
            'errors' => [],
        ],
        // Scenario 5: Valid - Existing phone number and email (same as existing_user_2)
        [
            'guardian_phone_number' => $existing_user_2->phone_number,
            'guardian_email' => $existing_user_2->email,
            'errors' => [],
        ],
    ];

    $this->enrollmentService->setData($data);

    $result = $this->enrollmentService->validatePhoneNumberAndEmailUniquenessForImport();

    $validated = $result->getData();

    // Check error count
    expect($result->getImportErrorCount())->toBe(2);

    // Scenario 1: Valid - No errors - new phone number and email
    expect($validated[0]['errors'])->toBeEmpty();

    // Scenario 2: Invalid - Existing phone number with different email
    expect($validated[1]['errors']['guardian_phone_number'])->toBe([
        'Phone number and email does not match with registered user.',
    ]);

    // Scenario 3: Invalid - Existing email with different phone number
    expect($validated[2]['errors']['guardian_email'])->toBe([
        'Phone number and email does not match with registered user.',
    ]);

    // Scenario 4: Valid - Existing phone number and email (same as existing_user_1)
    expect($validated[3]['errors'])->toBeEmpty();

    // Scenario 5: Valid - Existing phone number and email (same as existing_user_2)
    expect($validated[4]['errors'])->toBeEmpty();
});

test('getValidatedData', function () {
    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => 'ENG001',
            'name->en' => 'English',
            'name->zh' => '英语',
        ],
        [
            'code' => 'MATH001',
            'name->en' => 'Mathematics',
            'name->zh' => '数学',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $enrollment_session->examSubjects()->attach(Subject::all());

    // Test data
    $test_data = [
        [
            'student_name_en' => 'John Doe',
            'nric' => '123456789012',
            'ENG001' => '85',
            'MATH001' => '90',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'nric' => '987654321098',
            'ENG001' => '92',
            'MATH001' => '88',
        ]
    ];

    $service = app(EnrollmentService::class);
    $service->setData($test_data);
    $service->setEnrollmentSession($enrollment_session);

    // Get validated data
    $result = $service->getValidatedData();

    // Check structure
    expect($result)->toBeArray();
    expect($result)->toHaveKeys(['data', 'subject_lists']);

    expect($result['data'])->toBe($test_data);
    expect($result['subject_lists'])->toHaveCount(2);
});


test('fetchAllMapping', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    $religion = Religion::factory()->create([
        'name' => [
            'en' => 'Buddhism',
            'zh' => '佛教',
        ],
    ]);

    $health_concern = HealthConcern::factory()->create([
        'name' => [
            'en' => 'Asthma',
            'zh' => '哮喘',
        ],
    ]);

    $primary_school = School::factory()->create([
        'name' => [
            'en' => 'Test Primary School',
            'zh' => '测试小学',
        ],
        'level' => SchoolLevel::PRIMARY,
    ]);

    $guardian = EnrollmentUser::factory()->create([
        'name' => [
            'en' => 'John Parent',
            'zh' => '约翰家长',
        ],
        'phone_number' => '+6**********',
        'email' => '<EMAIL>',
    ]);

    $test_data = [
        [
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'Test Primary School',
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
        ],
        [
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'Test Primary School',
            'guardian_phone_number' => '01234', // Partial match
            'guardian_email' => '<EMAIL>',
        ],
    ];

    $enrollment_service->setData($test_data);

    $mappings = $enrollment_service->fetchAllMapping();

    // Verify all mappings are returned correctly
    expect($mappings)->toBeArray()
        ->toHaveKeys(['religion_map', 'health_concern_map', 'primary_school_map', 'guardian_by_phone_map', 'guardian_by_email_map']);

    // Test religion mappings
    expect($mappings['religion_map'])->toHaveKey('Buddhism');
    expect($mappings['religion_map']['Buddhism']->id)->toBe($religion->id);

    // Test health concern mappings
    expect($mappings['health_concern_map'])->toHaveKey('Asthma');
    expect($mappings['health_concern_map']['Asthma']->id)->toBe($health_concern->id);

    // Test primary school mappings
    expect($mappings['primary_school_map'])->toHaveKey('Test Primary School');
    expect($mappings['primary_school_map']['Test Primary School']->id)->toBe($primary_school->id);

    // Test guardian mappings by phone
    expect($mappings['guardian_by_phone_map'])->toHaveKey('+6**********');
    expect($mappings['guardian_by_phone_map']['+6**********']->id)->toBe($guardian->id);

    // Test guardian mappings by email
    expect($mappings['guardian_by_email_map'])->toHaveKey('<EMAIL>');
    expect($mappings['guardian_by_email_map']['<EMAIL>']->id)->toBe($guardian->id);


    /**
     * test empty data
     *
     */

    $enrollment_service->setData([]);

    $mappings = $enrollment_service->fetchAllMapping();

    expect($mappings)->toBeArray()
        ->toHaveKeys(['religion_map', 'health_concern_map', 'primary_school_map', 'guardian_by_phone_map', 'guardian_by_email_map']);

    expect($mappings['religion_map'])->toBeArray()->toBeEmpty();
    expect($mappings['health_concern_map'])->toBeArray()->toBeEmpty();
    expect($mappings['primary_school_map'])->toBeArray()->toBeEmpty();
    expect($mappings['guardian_by_phone_map'])->toBeArray()->toBeEmpty();
    expect($mappings['guardian_by_email_map'])->toBeArray()->toBeEmpty();


    /**
     * test multiple translation
     *
     */

    $religion = Religion::factory()->create([
        'name' => [
            'en' => 'Christianity',
            'zh' => '基督教',
        ],
    ]);

    $test_data = [
        [
            'religion' => 'Christianity',
            'health_concern' => '',
            'primary_school' => '',
            'guardian_phone_number' => '',
            'guardian_email' => '',
        ],
        [
            'religion' => '基督教', // Chinese translation
            'health_concern' => '',
            'primary_school' => '',
            'guardian_phone_number' => '',
            'guardian_email' => '',
        ],
    ];

    $enrollment_service->setData($test_data);

    $mappings = $enrollment_service->fetchAllMapping();

    expect($mappings['religion_map'])->toHaveKey('Christianity');
    expect($mappings['religion_map'])->toHaveKey('基督教');
    expect($mappings['religion_map']['Christianity']->id)->toBe($religion->id);
    expect($mappings['religion_map']['基督教']->id)->toBe($religion->id);
});

test('createReligionInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // Create nothing
    $enrollment_service->setToBeCreatedReligions([]);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 0);

    // Create 3 religions
    $religions = ['Buddhism', 'Sikhism', 'Jainism'];

    $enrollment_service->setToBeCreatedReligions($religions);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 3);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Buddhism',
        'name->zh' => 'Buddhism',
        'sequence' => 0,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Sikhism',
        'name->zh' => 'Sikhism',
        'sequence' => 1,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Jainism',
        'name->zh' => 'Jainism',
        'sequence' => 2,
    ]);

    // create 2 more
    $more_religions = ['Hinduism', 'Judaism'];

    $enrollment_service->setToBeCreatedReligions($more_religions);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 5);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Hinduism',
        'name->zh' => 'Hinduism',
        'sequence' => 3,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Judaism',
        'name->zh' => 'Judaism',
        'sequence' => 4,
    ]);
});

test('createPrimarySchoolInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // create nothing
    $enrollment_service->setToBeCreatedPrimarySchools([]);

    $enrollment_service->createPrimarySchoolInBulk();

    $this->assertDatabaseCount('master_schools', 0);


    // create 3 primary schools
    $primary_schools = ['SK Bukit Jalil', 'SK Seri Petaling', 'SJKC Kung Man'];

    $enrollment_service->setToBeCreatedPrimarySchools($primary_schools);

    $enrollment_service->createPrimarySchoolInBulk();

    $this->assertDatabaseCount('master_schools', 3);

    foreach ($primary_schools as $primary_school) {
        $this->assertDatabaseHas('master_schools', [
            'name->en' => $primary_school,
            'name->zh' => $primary_school,
            'level' => SchoolLevel::PRIMARY,
        ]);
    }
});

test('createHealthConcernInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // create nothing
    $enrollment_service->setToBeCreatedHealthConcerns([]);

    $enrollment_service->createHealthConcernInBulk();

    $this->assertDatabaseCount('master_health_concerns', 0);


    // create 3 health concerns
    $health_concerns = ['Asthma', 'Diabetes', 'Allergies'];

    $enrollment_service->setToBeCreatedHealthConcerns($health_concerns);

    $enrollment_service->createHealthConcernInBulk();

    $this->assertDatabaseCount('master_health_concerns', 3);

    foreach ($health_concerns as $health_concern) {
        $this->assertDatabaseHas('master_health_concerns', [
            'name->en' => $health_concern,
            'name->zh' => $health_concern,
        ]);
    }
});

test('saveImportedData - successful', function () {
    $enrollment_session = EnrollmentSession::factory()->create();
    $subject_1 = Subject::factory()->create(['code' => 'MATH']);
    $subject_2 = Subject::factory()->create(['code' => 'ENG']);

    $enrollment_session->examSubjects()->attach([
        $subject_1->id,
        $subject_2->id
    ]);

    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);
    $enrollment_service->setEnrollmentSession($enrollment_session);

    // all ok
    $import_data = [
        [
            'exam_slip_number' => 'E12345',
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰·多',
            'nric' => '990101123456',
            'passport_number' => 'A1234567',
            'religion' => 'Buddhism',
            'gender' => 'MALE',
            'guardian_phone_number' => '**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'total_average' => 85.5,
            'MATH' => 90,
            'ENG' => 81,
            'status' => 'ACCEPTED',
            'address' => '123 Main St',
            'primary_school' => 'Primary School ABC',
            'hostel' => true,
            'have_siblings' => false,
            'dietary_restriction' => 'NONE',
            'health_concern' => 'Asthma',
            'foreigner' => false,
            'conduct' => 'A',
            'remarks' => 'Good student',
        ],
        [
            'exam_slip_number' => 'E12346',
            'student_name_en' => 'Alice Smith',
            'student_name_zh' => '爱丽丝·史密斯',
            'nric' => '990202123456',
            'passport_number' => 'B1234567',
            'religion' => 'Christianity',
            'gender' => 'FEMALE',
            'guardian_phone_number' => '**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'John Smith',
            'guardian_type' => 'FATHER',
            'total_average' => 92.5,
            'MATH' => 95,
            'ENG' => 90,
            'status' => 'ACCEPTED',
            'address' => '456 Side St',
            'primary_school' => 'Primary School XYZ',
            'hostel' => false,
            'have_siblings' => true,
            'dietary_restriction' => 'VEGETARIAN',
            'health_concern' => 'None',
            'foreigner' => false,
            'conduct' => 'A',
            'remarks' => null,
        ],
    ];

    $enrollment_service->setData($import_data);

    $buddhism = Religion::factory()->create(['name' => ['en' => 'Buddhism', 'zh' => '佛教']]);
    $christianity = Religion::factory()->create(['name' => ['en' => 'Christianity', 'zh' => '基督教']]);
    $asthma = HealthConcern::factory()->create(['name' => ['en' => 'Asthma', 'zh' => '哮喘']]);
    $none_health_concern = HealthConcern::factory()->create(['name' => ['en' => 'None', 'zh' => '无']]);
    $abc = School::factory()->create(['name' => ['en' => 'Primary School ABC', 'zh' => '小学ABC']]);
    $xyz = School::factory()->create(['name' => ['en' => 'Primary School XYZ', 'zh' => '小学XYZ']]);

    $enrollment_service->saveImportedData();

    // ENROLLMENTS
    $this->assertDatabaseCount('enrollments', 2);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => 'John Doe',
        'name->zh' => '约翰·多',
        'nric' => '990101123456',
        'passport_number' => 'A1234567',
        'religion_id' => $buddhism->id,
        'gender' => 'MALE',
        'address' => '123 Main St',
        'primary_school_id' => $abc->id,
        'is_hostel' => true,
        'have_siblings' => false,
        'dietary_restriction' => 'NONE',
        'health_concern_id' => $asthma->id,
        'is_foreigner' => false,
        'conduct' => 'A',
        'remarks' => 'Good student',
        'enrollment_status' => 'ACCEPTED',
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => 'Alice Smith',
        'name->zh' => '爱丽丝·史密斯',
        'nric' => '990202123456',
        'passport_number' => 'B1234567',
        'religion_id' => $christianity->id,
        'gender' => 'FEMALE',
        'address' => '456 Side St',
        'primary_school_id' => $xyz->id,
        'is_hostel' => false,
        'have_siblings' => true,
        'dietary_restriction' => 'VEGETARIAN',
        'health_concern_id' => $none_health_concern->id,
        'is_foreigner' => false,
        'conduct' => 'A',
        'remarks' => null,
        'enrollment_status' => 'ACCEPTED',
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    // ENROLLMENT USERS
    $this->assertDatabaseCount('enrollment_users', 2);

    $this->assertDatabaseHas('enrollment_users', [
        'name->en' => 'Jane Doe',
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ]);

    $this->assertDatabaseHas('enrollment_users', [
        'name->en' => 'John Smith',
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ]);

    $first_guardian = EnrollmentUser::where([
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ])->first();

    $second_guardian = EnrollmentUser::where([
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ])->first();

    $enrollment = Enrollment::where('nric', '990101123456')->first();
    $enrollment_2 = Enrollment::where('nric', '990202123456')->first();

    expect($enrollment->enrollment_user_id)->toBe($first_guardian->id)
        ->and($enrollment_2->enrollment_user_id)->toBe($second_guardian->id);

    // ENROLLMENT EXAMS
    $this->assertDatabaseCount('enrollment_exams', 2);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment->id,
        'exam_slip_number' => 'E12345',
        'total_average' => 85.5,
    ]);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment_2->id,
        'exam_slip_number' => 'E12346',
        'total_average' => 92.5,
    ]);

    // ENROLLMENT EXAM MARKS
    $this->assertDatabaseCount('enrollment_exam_marks', 4); // 2 subjects × 2 students

    $enrollment_exam = EnrollmentExam::where('enrollment_id', $enrollment->id)->first();

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam->id,
        'subject_id' => $subject_1->id,
        'mark' => 90,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam->id,
        'subject_id' => $subject_2->id,
        'mark' => 81,
    ]);

    $enrollment_exam_2 = EnrollmentExam::where('enrollment_id', $enrollment_2->id)->first();

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_2->id,
        'subject_id' => $subject_1->id,
        'mark' => 95,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_2->id,
        'subject_id' => $subject_2->id,
        'mark' => 90,
    ]);
});
