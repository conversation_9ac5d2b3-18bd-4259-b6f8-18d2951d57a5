<?php

use App\Enums\MerchantType;
use App\Models\EcommerceProductCategory;
use App\Models\EcommerceProductSubCategory;
use App\Services\EcommerceProductCategoryService;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->productCategoryService = app()->make(EcommerceProductCategoryService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(EcommerceProductCategory::class)->getTable();
});

test('getAllPaginatedEcommerceProductCategories()', function () {
    $product_categories = EcommerceProductCategory::factory(3)->state(new Sequence(
        [
            'name' => 'English'
        ],
        [
            'name' => 'English 2'
        ],
        [
            'name' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name", 'English 2');

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'Tamil'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $product_categories[0]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->productCategoryService->getAllPaginatedEcommerceProductCategories($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $product_categories[2]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[0]->id),
        );
});

test('getAllEcommerceProductCategories()', function () {
    $product_categories = EcommerceProductCategory::factory(3)->state(new Sequence(
        [
            'name' => 'English'
        ],
        [
            'name' => 'English 2'
        ],
        [
            'name' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toHaveKey("0.name", 'English 2');

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'Tamil'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $product_categories[0]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->productCategoryService->getAllEcommerceProductCategories($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $product_categories[2]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $product_categories[0]->id),
        );
});

test('createEcommerceProductCategory()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => fake()->name,
        'type' => MerchantType::BOOKSHOP->value,
    ];

    $response = $this->productCategoryService->createEcommerceProductCategory($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'type' => $payload['type'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'type' => $payload['type']
    ]);
});

test('updateEcommerceProductCategory()', function () {
    $product_category = EcommerceProductCategory::factory()->create([
        'name' => 'Test EN',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => 'Test 2',
        'type' => MerchantType::CANTEEN->value,
    ];

    $response = $this->productCategoryService->updateEcommerceProductCategory($product_category, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $product_category->id,
        'name' => $payload['name'],
        'type' => $payload['type'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'type' => $payload['type']
    ]);

});

test('deleteEcommerceProductCategory() success', function () {
    $product_category = EcommerceProductCategory::factory()->create();
    $other_product_categories = EcommerceProductCategory::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->productCategoryService->deleteEcommerceProductCategory($product_category);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $product_category->id]);

    foreach ($other_product_categories as $other_product_category) {
        $this->assertDatabaseHas($this->table, ['id' => $other_product_category->id]);
    }

});

test('deleteEcommerceProductCategory() failed', function () {
    $product_category_with_sub = EcommerceProductCategory::factory()->create();
    EcommerceProductSubCategory::factory()->create([
        'product_category_id' => $product_category_with_sub->id,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->expectExceptionCode('13001');
    $this->expectExceptionMessage('Cannot delete product category that has subcategories.');
    $this->productCategoryService->deleteEcommerceProductCategory($product_category_with_sub);
});
