<?php

use App\Enums\PeriodAttendanceStatus;
use App\Models\Employee;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\Student;
use App\Models\Timeslot;
use App\Models\Timetable;
use App\Repositories\PeriodAttendanceRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    $this->attendancePeriodRepository = resolve(PeriodAttendanceRepository::class);
    app()->setLocale('en');
    $this->table = resolve(PeriodAttendance::class)->getTable();
});

test('getModelClass()', function () {
    $model_class = $this->attendancePeriodRepository->getModelClass();
    expect($model_class)->toEqual(PeriodAttendance::class);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_model) {
    $student1 = Student::factory()->create(['id' => 1]);
    $student2 = Student::factory()->create(['id' => 2]);

    $employee1 = Employee::factory()->create(['id' => 9991]);
    $employee2 = Employee::factory()->create(['id' => 10091]);

    $period_group = PeriodGroup::factory()->create();
    PeriodLabel::factory()
        ->create([
            'period_group_id' => $period_group->id,
            'is_attendance_required' => true
        ]);

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
    ]);

    $timeslot = Timeslot::factory()->create([
        'id' => 1,
        'timetable_id' => $timetable->id,
    ]);

    $first_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student1->id,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee1->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'date' => '2024-12-01',
        'id' => 1,
    ]);
    $second_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_id' => Timeslot::factory()->create(['id' => 2]),
        'updated_by_employee_id' => $employee1->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'date' => '2024-12-02',
        'id' => 2,
    ]);
    $third_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_id' => Timeslot::factory()->create(['id' => 3]),
        'updated_by_employee_id' => $employee2->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'date' => '2024-12-01',
        'id' => 3,
    ]);
    $attendance_periods = [
        'first_attendance_period' => $first_attendance_period,
        'second_attendance_period' => $second_attendance_period,
        'third_attendance_period' => $third_attendance_period,
    ];

    $result_paginated = $this->attendancePeriodRepository->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $attendance_periods[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->attendancePeriodRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $attendance_periods[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [3, [], ['first_attendance_period', 'second_attendance_period', 'third_attendance_period']],
    'filter by is_attendance_required' => [1, ['is_attendance_required' => true], ['first_attendance_period']],
    'filter by employee_id' => [1, ['employee_id' => 10091], ['third_attendance_period']],
    'filter by id' => [1, ['id' => 1], ['first_attendance_period']],
    'filter by array id' => [2, ['id' => [1, 2]], ['first_attendance_period', 'second_attendance_period']],
    'filter by student_id' => [2, ['student_id' => 2], ['second_attendance_period', 'third_attendance_period']],
    'filter by array student_id' => [3, ['student_id' => [1, 2]], ['first_attendance_period', 'second_attendance_period', 'third_attendance_period']],
    'filter by status' => [2, ['status' => PeriodAttendanceStatus::PRESENT->value], ['first_attendance_period', 'second_attendance_period']],
    'filter by date' => [2, ['date' => '2024-12-01'], ['first_attendance_period', 'third_attendance_period']],
    'filter by timeslot_id' => [1, ['timeslot_id' => 3], ['third_attendance_period']],
    'filter by array timeslot_id' => [2, ['timeslot_id' => [1, 3]], ['first_attendance_period', 'third_attendance_period']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first_attendance_period', 'second_attendance_period', 'third_attendance_period']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third_attendance_period', 'second_attendance_period', 'first_attendance_period']],
]);

test('batchDeleteById', function () {


    $student1 = Student::factory()->create(['id' => 1]);

    $first_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student1->id,
        'timeslot_id' => Timeslot::factory()->create(['id' => 1]),
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'date' => '2024-12-01',
        'id' => 1,
    ]);
    $second_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student1->id,
        'timeslot_id' => Timeslot::factory()->create(['id' => 2]),
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'date' => '2024-12-02',
        'id' => 2,
    ]);
    $third_attendance_period = PeriodAttendance::factory()->create([
        'student_id' => $student1->id,
        'timeslot_id' => Timeslot::factory()->create(['id' => 3]),
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'date' => '2024-12-01',
        'id' => 3,
    ]);

    // nothing deleted
    $this->assertDatabaseCount(PeriodAttendance::class, 3);
    app(PeriodAttendanceRepository::class)->batchDeleteByIds([]);
    $this->assertDatabaseCount(PeriodAttendance::class, 3);

    // 2 deleted
    $this->assertDatabaseCount(PeriodAttendance::class, 3);
    app(PeriodAttendanceRepository::class)->batchDeleteByIds([$first_attendance_period->id, $second_attendance_period->id]);
    $this->assertDatabaseCount(PeriodAttendance::class, 1);

    $this->assertDatabaseHas(PeriodAttendance::class, [
        'id' => $third_attendance_period->id,
    ]);
    $this->assertDatabaseMissing(PeriodAttendance::class, [
        'id' => $first_attendance_period->id,
    ]);
    $this->assertDatabaseMissing(PeriodAttendance::class, [
        'id' => $second_attendance_period->id,
    ]);

});
