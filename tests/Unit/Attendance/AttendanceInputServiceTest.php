<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Helpers\ConfigHelper;
use App\Models\Attendance;
use App\Models\AttendanceInput;
use App\Models\AttendanceInputErrorLog;
use App\Models\Calendar;
use App\Models\CalendarSetting;
use App\Models\CalendarTarget;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\Config;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\PosTerminalKey;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\AttendanceInputService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    $this->attendanceInputService = app()->make(AttendanceInputService::class);
    app()->setLocale('en');
    $this->table = resolve(AttendanceInput::class)->getTable();
    Employee::factory()->create(['employee_number' => Employee::SYSTEM_ID]);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_models) {
    $student = Student::factory()->create();
    $student->id = 1;
    $student->save();

    $first_attendance_input = AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => '2024-12-01',
    ]);

    $second_attendance_input = AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => Student::factory(),
        'date' => '2024-12-02',
    ]);

    $third_attendance_input = AttendanceInput::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-02',
    ]);

    $attendance_inputs = [
        'first_attendance_input' => $first_attendance_input,
        'second_attendance_input' => $second_attendance_input,
        'third_attendance_input' => $third_attendance_input,
    ];

    $result_paginated = $this->attendanceInputService->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendance_inputs[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->attendanceInputService->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendance_inputs[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [3, [], ['first_attendance_input', 'second_attendance_input', 'third_attendance_input']],
    'filter by attendance_recordable_type' => [2, ['attendance_recordable_type' => Student::class], ['first_attendance_input', 'second_attendance_input']],
    'filter by attendance_recordable_type attendance_recordable_id' => [1, ['attendance_recordable_type' => Student::class, 'attendance_recordable_id' => 1], ['first_attendance_input']],
    'filter by date' => [2, ['date' => '2024-12-02'], ['third_attendance_input', 'second_attendance_input']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first_attendance_input', 'second_attendance_input', 'third_attendance_input']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third_attendance_input', 'second_attendance_input', 'first_attendance_input']],
]);

test('create', function () {
    $this->assertDatabaseCount($this->table, 0);

    $employee = Employee::factory()->create();
    $student = Student::factory()->create();
    $card = Card::factory()->create();

    // with card
    $this->attendanceInputService
        ->setUserable($student)
        ->setRemarks('manual create attendance input')
        ->setCard($card)
        ->setDate('2024-12-01')
        ->setRecordDatetime('2024-12-01 01:00:00')
        ->setEmployee($employee)
        ->setRepostSchoolAttendance(false)
        ->create();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $card->id,
        'remarks' => 'manual create attendance input',
        'date' => '2024-12-01',
        'record_datetime' => '2024-12-01 01:00:00',
        'is_manual' => true,
        'updated_by_employee_id' => $employee->id,
    ]);

    // without card
    $this->attendanceInputService
        ->setCard(null)
        ->setUserable($student)
        ->setRemarks('manual create attendance input 2')
        ->setDate('2024-12-02')
        ->setRecordDatetime('2024-12-01 17:00:00')
        ->setEmployee($employee)
        ->setRepostSchoolAttendance(false)
        ->create();

    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => null,
        'remarks' => 'manual create attendance input 2',
        'date' => '2024-12-02',
        'record_datetime' => '2024-12-01 17:00:00',
        'is_manual' => true,
        'updated_by_employee_id' => $employee->id,
    ]);
});

test('update', function () {
    $attendance_input = AttendanceInput::factory()->create();
    $this->assertDatabaseCount($this->table, 1);

    $employee = Employee::factory()->create();
    $card = Card::factory()->create();

    $this->attendanceInputService
        ->setRemarks('manual create attendance input')
        ->setCard($card)
        ->setDate('2024-12-01')
        ->setRecordDatetime('2024-12-01 01:00:00')
        ->setEmployee($employee)
        ->update($attendance_input);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $attendance_input->id,
        'card_id' => $card->id,
        'remarks' => 'manual create attendance input',
        'date' => '2024-12-01',
        'record_datetime' => '2024-12-01 01:00:00',
        'is_manual' => true,
        'updated_by_employee_id' => $employee->id,
    ]);
});

test('delete', function () {
    $attendance_input = AttendanceInput::factory()->create();
    $this->assertDatabaseCount($this->table, 1);
    $this->attendanceInputService->delete($attendance_input);
    $this->assertDatabaseCount($this->table, 0);
});


test('bulkCreateAttendanceInputWithCard', function () {
    $student = Student::factory()->create();
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id
    ]);

    $student2 = Student::factory()->create();
    $card2 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id
    ]);

    $student3 = Student::factory()->create();
    $card3 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student3->id
    ]);

    $student4 = Student::factory()->create();
    $card4 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student4->id
    ]);

    $student5 = Student::factory()->create();
    $card5 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student5->id
    ]);

    $active_calendar_2024 = Calendar::factory()->create([
        'year' => '2024',
        'name->en' => 'calendar 2024',
        'name->zh' => '日历 2024',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2024->id,
        'date' => '2024-12-15',
        'is_attendance_required' => true,
        'description' => null,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2024->id,
        'date' => '2024-12-16',
        'is_attendance_required' => true,
        'description' => null,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2024->id,
        'date' => '2024-12-17',
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $calendar_targets = [];
    foreach ([$student->id, $student2->id, $student3->id, $student4->id, $student5->id] as $student_id) {
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2024->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $student_id,
        ];
    }
    CalendarTarget::insert($calendar_targets);

    $contractor = Contractor::factory()->create();
    $contractor_card = Card::factory()->create([
        'userable_type' => Contractor::class,
        'userable_id' => $contractor->id
    ]);

    $employee = Employee::factory()->create();
    $employee_card = Card::factory()->create([
        'userable_type' => Employee::class,
        'userable_id' => $employee->id
    ]);

    $employee_with_inactive_card = Employee::factory()->create();
    $employee_inactive_card = Card::factory()->create([
        'userable_type' => Employee::class,
        'userable_id' => $employee_with_inactive_card->id,
        'status' => CardStatus::INACTIVE,
    ]);

    $payload = [
        'card_numbers_with_datetime' => [
            [
                'card_number' => $card->card_number,
                'datetime' => '2024-12-16 05:00:00', // UTC+0
            ],
            [
                'card_number' => $card2->card_number,
                'datetime' => '2024-12-15 23:59:00', // UTC+0
            ],
            [
                'card_number' => $card3->card_number,
                'datetime' => '2024-12-16 00:00:00', // UTC+0
            ],
            [
                'card_number' => $card4->card_number,
                'datetime' => '2024-12-14 23:59:00', // UTC+0
            ],
            [
                'card_number' => $card5->card_number,
                'datetime' => '2024-12-16 04:00:00', // UTC+0
            ],
            [
                'card_number' => '123',
                'datetime' => '2024-12-15 23:00:00', // UTC+0
            ],
            [
                'card_number' => $card5->card_number,
                'datetime' => '2024-12-16 16:00:00', // UTC+0
            ],
            [
                'card_number' => $contractor_card->card_number,
                'datetime' => '2024-12-16 16:00:00', // UTC+0
            ],
            [
                'card_number' => $employee_card->card_number,
                'datetime' => '2024-12-16 16:00:00', // UTC+0
            ],
            [
                'card_number' => $employee_inactive_card->card_number,
                'datetime' => '2024-12-15 23:30:00', // UTC+0
            ],
        ]
    ];

    $pos_terminal_key = PosTerminalKey::factory()->create();
    $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->bulkCreateAttendanceInputWithCard($payload);

    // created attendance error log for invalid card number or inactive card
    expect(AttendanceInputErrorLog::count())->toBe(2);
    $this->assertDatabaseHas('attendance_input_error_logs', [
        "card_number" => "123",
        'time' => '2024-12-15 23:00:00',
        'error_message' => 'Invalid card number',
    ]);
    $this->assertDatabaseHas('attendance_input_error_logs', [
        "card_number" => $employee_inactive_card->card_number,
        'time' => '2024-12-15 23:30:00',
        'error_message' => 'Inactive card',
    ]);

    expect(Attendance::count())->toBe(8);
    expect(AttendanceInput::count())->toBe(8);

    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student),
        "attendance_recordable_id" => $student->id,
        "card_id" => $card->id,
        "remarks" => null,
        "date" => "2024-12-16",
        "record_datetime" => '2024-12-16 05:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student->id,
        "date" => "2024-12-16",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student2),
        "attendance_recordable_id" => $student2->id,
        "card_id" => $card2->id,
        "remarks" => null,
        "date" => "2024-12-16",
        "record_datetime" => '2024-12-15 23:59:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student2->id,
        "date" => "2024-12-16",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student3),
        "attendance_recordable_id" => $student3->id,
        "card_id" => $card3->id,
        "remarks" => null,
        "date" => "2024-12-16",
        "record_datetime" => '2024-12-16 00:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student3->id,
        "date" => "2024-12-16",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student4),
        "attendance_recordable_id" => $student4->id,
        "card_id" => $card4->id,
        "remarks" => null,
        "date" => "2024-12-15",
        "record_datetime" => '2024-12-14 23:59:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student4->id,
        "date" => "2024-12-15",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student5),
        "attendance_recordable_id" => $student5->id,
        "card_id" => $card5->id,
        "remarks" => null,
        "date" => "2024-12-16",
        "record_datetime" => '2024-12-16 04:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student5->id,
        "date" => "2024-12-16",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($student5),
        "attendance_recordable_id" => $student5->id,
        "card_id" => $card5->id,
        "remarks" => null,
        "date" => "2024-12-17",
        "record_datetime" => '2024-12-16 16:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student5->id,
        "date" => "2024-12-17",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($contractor),
        "attendance_recordable_id" => $contractor->id,
        "card_id" => $contractor_card->id,
        "remarks" => null,
        "date" => "2024-12-17",
        "record_datetime" => '2024-12-16 16:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Contractor::class,
        "attendance_recordable_id" => $contractor->id,
        "date" => "2024-12-17",
        "status" => AttendanceStatus::PRESENT,
    ]);
    $this->assertDatabaseHas($this->table, [
        "attendance_recordable_type" => get_class($employee),
        "attendance_recordable_id" => $employee->id,
        "card_id" => $employee_card->id,
        "remarks" => null,
        "date" => "2024-12-17",
        "record_datetime" => '2024-12-16 16:00:00',
        "is_manual" => false,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Employee::class,
        "attendance_recordable_id" => $employee->id,
        "date" => "2024-12-17",
        "status" => AttendanceStatus::PRESENT,
    ]);
});

test('createAttendanceInputWithCard', function () {
    // Set to UTC 2025-03-16 23:55:00
    // Which equals to UTC+8 2025-03-17 07:55:00
    Carbon::setTestNow(Carbon::parse('2025-03-16 23:55:00'));
    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    foreach (Day::cases() as $day) {
        $period1 = Period::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => 1,
            'from_time' => '08:00:00',
            'to_time' => '08:30:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period2 = Period::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => 2,
            'from_time' => '08:30:00',
            'to_time' => '09:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);
    }
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);

    $student = Student::factory()->create(['is_active' => true]);
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id
    ]);
    $student2 = Student::factory()->create(['is_active' => true]);
    $card2 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id
    ]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => '2025-03-17',
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $calendar_targets = [];
    foreach ([$student->id, $student2->id] as $student_id) {
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $student_id,
        ];
    }
    CalendarTarget::insert($calendar_targets);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    foreach (Day::cases() as $day) {
        $timeslot1 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'period_id' => $period1->id,
            'placeholder' => null,
            'attendance_from' => '08:00',
            'attendance_to' => '08:30',
            'day' => $day,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot1->id,
        ]);
        $timeslot2 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'period_id' => $period2->id,
            'placeholder' => null,
            'attendance_from' => '08:30',
            'attendance_to' => '09:00',
            'day' => $day,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot2->id,
        ]);
    }

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    StudentTimetable::refreshViewTable(false);
    $pos_terminal_key = PosTerminalKey::factory()->create();

    Cache::clear();
    ConfigHelper::put(Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND, 3, Config::CATEGORY_GENERAL);

    expect(Attendance::count())->toBe(0);
    // Scenario 1: Student came on time
    $datetime = Carbon::parse('2025-03-16 23:55:00')->toDateTimeString();
    // student1 tap card
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card->card_number, $datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckInStatus::ON_TIME->value)
        ->and(Cache::has('card-lock-' . $card->card_number))->toBeTrue();

    expect(Attendance::count())->toBe(1);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Student::class,
        "attendance_recordable_id" => $student->id,
        "date" => "2025-03-17",
        "check_in_datetime" => Carbon::parse('2025-03-16 23:55:00'),
        "check_in_status" => AttendanceCheckInStatus::ON_TIME,
        "status" => AttendanceStatus::PRESENT,
    ]);

    // student1 tap card again (throw error)
    $status = false;
    try {
        $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card->card_number, $datetime);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('Attendance already taken / 出席已被记录')
            ->and($e->getCode())->toBe(42006);
    }

    // student2 tap card (LATE)
    $late_datetime = Carbon::parse('2025-03-17 00:01:00')->toDateTimeString();
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card2->card_number, $late_datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckInStatus::LATE->value)
        ->and(Cache::has('card-lock-' . $card2->card_number))->toBeTrue();

    // set cache
    // student1 tap card after interval
    Cache::clear();
    expect(Cache::has('card-lock-' . $card->card_number))->toBeFalse(); // cache expires
    $datetime_after_3_seconds = Carbon::parse($late_datetime)->addSeconds(3)->toDateTimeString();
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card->card_number, $datetime_after_3_seconds);
    expect(Cache::has('card-lock-' . $card->card_number))->toBeTrue()
        ->and($userable->attendance_status)->toBe(AttendanceCheckOutStatus::LEFT_EARLY->value)
        ->and(AttendanceInputErrorLog::count())->toBe(0)
        ->and(AttendanceInput::count())->toBe(3)
        ->and(AttendanceInput::all()->makeHidden(['id', 'created_at', 'updated_at'])->toArray())->toMatchArray([
            [
                'attendance_recordable_type' => Student::class,
                'attendance_recordable_id' => $student->id,
                'card_id' => $card->id,
                'remarks' => null,
                'date' => now()->timezone(config('school.timezone'))->toDateString(),
                'record_datetime' => Carbon::parse($datetime)->toISOString(),
                'is_manual' => false,
                'updated_by_employee_id' => null,
                'terminal_id' => $pos_terminal_key->terminal_id,
            ],
            [
                'attendance_recordable_type' => Student::class,
                'attendance_recordable_id' => $student2->id,
                'card_id' => $card2->id,
                'remarks' => null,
                'date' => now()->timezone(config('school.timezone'))->toDateString(),
                'record_datetime' => Carbon::parse($late_datetime)->toISOString(),
                'is_manual' => false,
                'updated_by_employee_id' => null,
                'terminal_id' => $pos_terminal_key->terminal_id,
            ],
            [
                'attendance_recordable_type' => Student::class,
                'attendance_recordable_id' => $student->id,
                'card_id' => $card->id,
                'remarks' => null,
                'date' => now()->timezone(config('school.timezone'))->toDateString(),
                'record_datetime' => Carbon::parse($datetime_after_3_seconds)->toISOString(),
                'is_manual' => false,
                'updated_by_employee_id' => null,
                'terminal_id' => $pos_terminal_key->terminal_id,
            ],
        ]); // set cache
    // no error log being created

    // invalid card - throw error, wont create attendance input, store in attendance input error log
    AttendanceInput::truncate();
    $status = false;
    try {
        $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard('123456', $datetime);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('Invalid card / 无法识别此卡')
            ->and($e->getCode())->toBe(42007);
    }
    expect(AttendanceInputErrorLog::count())->toBe(1);
    $this->assertDatabaseHas('attendance_input_error_logs', [
        "card_number" => "123456",
        'time' => $datetime,
        'error_message' => 'Invalid card number',
        'terminal_id' => $pos_terminal_key->terminal_id,
    ]);
    expect(AttendanceInput::count())->toBe(0)
        ->and(Cache::has('card-lock-' . '123456'))->toBeFalse(); // cache not set


    // student3 tap card (without timetable) - throw error but will still create attendance input
    AttendanceInputErrorLog::truncate();
    $student3 = Student::factory()->create(['is_active' => true]);
    $card3 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student3->id
    ]);
    $status = false;
    try {
        $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card3->card_number, $datetime);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('Timetable not set up. Please contact admin / 课程表未设置，请联系管理员')
            ->and($e->getCode())->toBe(42008);
    }
    expect(Cache::has('card-lock-' . $card3->card_number))->toBeTrue()
        ->and(AttendanceInputErrorLog::count())->toBe(0)
        ->and(AttendanceInput::count())->toBe(1)
        ->and(AttendanceInput::all()->makeHidden(['id', 'created_at', 'updated_at'])->toArray())->toMatchArray([
            [
                'attendance_recordable_type' => Student::class,
                'attendance_recordable_id' => $student3->id,
                'card_id' => $card3->id,
                'remarks' => null,
                'date' => now()->timezone(config('school.timezone'))->toDateString(),
                'record_datetime' => Carbon::parse($datetime)->toISOString(),
                'is_manual' => false,
                'updated_by_employee_id' => null,
                'terminal_id' => $pos_terminal_key->terminal_id,
            ],
        ]); // set cache

    Attendance::truncate();
    AttendanceInput::truncate();
    // contractor tap card
    $contractor = Contractor::factory()->create();
    $contractor_card = Card::factory()->create([
        'userable_type' => Contractor::class,
        'userable_id' => $contractor->id
    ]);
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($contractor_card->card_number, $datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckInStatus::ON_TIME->value)
        ->and(Cache::has('card-lock-' . $contractor_card->card_number))->toBeTrue(); // set cache

    expect(AttendanceInput::count())->toBe(1);
    expect(Attendance::count())->toBe(1);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Contractor::class,
        "attendance_recordable_id" => $contractor->id,
        "date" => "2025-03-17",
        "check_in_datetime" => Carbon::parse('2025-03-16 23:55:00'),
        "check_in_status" => AttendanceCheckInStatus::ON_TIME,
        "status" => AttendanceStatus::PRESENT,
    ]);

    Attendance::truncate();
    AttendanceInput::truncate();
    // employee tap card
    $employee = Employee::factory()->create();
    $employee_card = Card::factory()->create([
        'userable_type' => Employee::class,
        'userable_id' => $employee->id
    ]);
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($employee_card->card_number, $datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckInStatus::ON_TIME->value)
        ->and(Cache::has('card-lock-' . $employee_card->card_number))->toBeTrue(); // set cache

    expect(AttendanceInput::count())->toBe(1);
    expect(Attendance::count())->toBe(1);
    $this->assertDatabaseHas('attendances', [
        "attendance_recordable_type" => Employee::class,
        "attendance_recordable_id" => $employee->id,
        "date" => "2025-03-17",
        "check_in_datetime" => Carbon::parse('2025-03-16 23:55:00'),
        "check_in_status" => AttendanceCheckInStatus::ON_TIME,
        "status" => AttendanceStatus::PRESENT,
    ]);

    Attendance::truncate();
    AttendanceInput::truncate();
    AttendanceInputErrorLog::truncate();
    // student tap card (inactive card)
    $student_with_inactive_card = Student::factory()->create();
    $inactive_card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student_with_inactive_card->id,
        'status' => CardStatus::INACTIVE,
    ]);
    $status = false;
    try {
        $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($inactive_card->card_number, $datetime);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('Card has been inactivated / 此卡已被停用')
            ->and($e->getCode())->toBe(42011);
    }
    expect(AttendanceInputErrorLog::count())->toBe(1);
    $this->assertDatabaseHas('attendance_input_error_logs', [
        "card_number" => $inactive_card->card_number,
        'time' => $datetime,
        'error_message' => 'Inactive card',
        'terminal_id' => $pos_terminal_key->terminal_id,
    ]);
    expect(AttendanceInput::count())->toBe(0)
        ->and(Cache::has('card-lock-' . $inactive_card->card_number))->toBeFalse(); // cache not set
});

test('createAttendanceInputWithCard - with timeslot override', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);

    $student = Student::factory()->create(['is_active' => true]);
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
        'day' => Day::MONDAY,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
        'day' => Day::MONDAY,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    StudentTimetable::refreshViewTable(false);
    $pos_terminal_key = PosTerminalKey::factory()->create();

    // Set to UTC 2025-03-16 23:55:00
    // Which equals to UTC+8 2025-03-17 07:55:00
    Carbon::setTestNow(Carbon::parse('2025-03-16 23:55:00'));

    Cache::clear();
    ConfigHelper::put(Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND, 3, Config::CATEGORY_GENERAL);

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $card->id,
        'date' => '2025-03-17',
        'record_datetime' => '2025-03-16 23:55:00',
        'is_manual' => false,
    ]);

    // student check out at 08:40
    $datetime = Carbon::parse('2025-03-17 00:40:00')->toDateTimeString();
    // student1 tap card
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card->card_number, $datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckOutStatus::LEFT_EARLY->value)
        ->and(Cache::has('card-lock-' . $card->card_number))->toBeTrue();

    TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-03-17',
        'period' => 2,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => false,
        'is_empty' => true, // set to empty
    ]);
    Cache::clear();
    $userable = $this->attendanceInputService->setTerminalId($pos_terminal_key->terminal_id)->setRepostSchoolAttendance(true)->createAttendanceInputWithCard($card->card_number, $datetime);
    expect($userable->attendance_status)->toBe(AttendanceCheckOutStatus::ON_TIME->value)
        ->and(Cache::has('card-lock-' . $card->card_number))->toBeTrue();
});
