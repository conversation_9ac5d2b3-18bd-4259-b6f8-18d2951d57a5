<?php

use App\Enums\Gender;
use App\Models\Country;
use App\Models\Enrollment;
use App\Models\Grade;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\User;
use App\Repositories\EnrollmentRepository;

beforeEach(function () {
    $this->enrollmentRepository = resolve(EnrollmentRepository::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->enrollmentRepository->getModelClass();

    expect($response)->toEqual(Enrollment::class);
});


test('getAll()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'John Jones'
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'BBO Name'
    ]);

    $grade = Grade::factory()->create();
    $fourth_enrollment = Enrollment::factory()->create([
        'admission_grade_id' => $grade->id,
    ]);

    $birthplace_country = Country::factory()->create();
    $fifth_enrollment = Enrollment::factory()->create([
        'birthplace_id' => $birthplace_country->id,
    ]);

    $nationality = Country::factory()->create();
    $sixth_enrollment = Enrollment::factory()->create([
        'nationality_id' => $nationality->id,
    ]);

    $race = Race::factory()->create();
    $seventh_enrollment = Enrollment::factory()->create([
        'race_id' => $race->id,
    ]);

    $religion = Religion::factory()->create();
    $eighth_enrollment = Enrollment::factory()->create([
        'religion_id' => $religion->id,
    ]);

    $birth_cert_no = fake()->uuid();
    $nineth_enrollment = Enrollment::factory()->create([
        'birth_cert_no' => $birth_cert_no,
    ]);

    $nric_no = fake()->uuid();
    $tenth_enrollment = Enrollment::factory()->create([
        'nric_no' => $nric_no,
    ]);

    $gender = Gender::FEMALE->value;
    $eleventh_enrollment = Enrollment::factory()->create([
        'gender' => $gender,
    ]);

    $created_by = User::factory()->create();
    $twelveth_enrollment = Enrollment::factory()->create([
        'created_by' => $created_by->id,
    ]);

    $thirteen_enrollment = Enrollment::factory()->create([
        'admission_year' => 2035,
    ]);

    $passport_no = fake()->uuid();
    $fourteen_enrollment = Enrollment::factory()->create([
        'passport_no' => $passport_no,
    ]);

    $state = State::factory()->create();
    $fifteen_enrollment = Enrollment::factory()->create([
        'state_id' => $state->id,
    ]);

    $country = Country::factory()->create();
    $sixteen_enrollment = Enrollment::factory()->create([
        'country_id' => $country->id,
    ]);

    // =================================================================================

    // Filter by admission_year = 2035
    $response = $this->enrollmentRepository->getAll(['admission_year' => 2035])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$thirteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by admission_grade_id = $grade->id
    $response = $this->enrollmentRepository->getAll(['admission_grade_id' => $grade->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fourth_enrollment->toArray()]);

    // =================================================================================

    // Filter by student_name = John Jones
    $response = $this->enrollmentRepository->getAll(['student_name' => 'John Jones'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by student_name = BBO Name
    $response = $this->enrollmentRepository->getAll(['student_name' => 'BBO Name'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$third_enrollment->toArray()]);

    // Filter by non-existing student_name = Meh
    $response = $this->enrollmentRepository->getAll(['student_name' => 'Meh'])->toArray();

    expect($response)->toHaveCount(0)->toBeEmpty();

    // =================================================================================

    // Filter by nric_no = $nric_no
    $response = $this->enrollmentRepository->getAll(['nric_no' => $nric_no])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$tenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by passport_no = $passport_no
    $response = $this->enrollmentRepository->getAll(['passport_no' => $passport_no])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fourteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by birthplace_id = $birthplace_country->id
    $response = $this->enrollmentRepository->getAll(['birthplace_id' => $birthplace_country->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fifth_enrollment->toArray()]);

    // =================================================================================

    // Filter by nationality_id = $nationality->id
    $response = $this->enrollmentRepository->getAll(['nationality_id' => $nationality->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$sixth_enrollment->toArray()]);

    // =================================================================================

    // Filter by gender = $gender
    $response = $this->enrollmentRepository->getAll(['gender' => $gender])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$eleventh_enrollment->toArray()]);

    // =================================================================================


    // Filter by birth_cert_no = $birth_cert_no
    $response = $this->enrollmentRepository->getAll(['birth_cert_no' => $birth_cert_no])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$nineth_enrollment->toArray()]);

    // =================================================================================

    // Filter by race_id = $race->id
    $response = $this->enrollmentRepository->getAll(['race_id' => $race->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$seventh_enrollment->toArray()]);

    // =================================================================================

    // Filter by religion_id = $religion->id
    $response = $this->enrollmentRepository->getAll(['religion_id' => $religion->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$eighth_enrollment->toArray()]);

    // =================================================================================

    // Filter by state_id = $state->id
    $response = $this->enrollmentRepository->getAll(['state_id' => $state->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fifteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by country_id = $country->id
    $response = $this->enrollmentRepository->getAll(['country_id' => $country->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$sixteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by created_by = $created_by->id
    $response = $this->enrollmentRepository->getAll(['created_by' => $created_by->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$twelveth_enrollment->toArray()]);

    // =================================================================================
    // =================================================================================
    // =================================================================================

    // Sort by id asc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($twelveth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
    );

    // Sort by id desc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($twelveth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );

    // Sort by nationality_id asc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['nationality_id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($twelveth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
    );

    // Sort by nationality_id desc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['nationality_id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($twelveth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
    );
});

test('getAllPaginated()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'John Jones'
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'student_name->en' => 'BBO Name'
    ]);

    $grade = Grade::factory()->create();
    $fourth_enrollment = Enrollment::factory()->create([
        'admission_grade_id' => $grade->id,
    ]);

    $birthplace_country = Country::factory()->create();
    $fifth_enrollment = Enrollment::factory()->create([
        'birthplace_id' => $birthplace_country->id,
    ]);

    $nationality = Country::factory()->create();
    $sixth_enrollment = Enrollment::factory()->create([
        'nationality_id' => $nationality->id,
    ]);

    $race = Race::factory()->create();
    $seventh_enrollment = Enrollment::factory()->create([
        'race_id' => $race->id,
    ]);

    $religion = Religion::factory()->create();
    $eighth_enrollment = Enrollment::factory()->create([
        'religion_id' => $religion->id,
    ]);

    $birth_cert_no = fake()->uuid();
    $nineth_enrollment = Enrollment::factory()->create([
        'birth_cert_no' => $birth_cert_no,
    ]);

    $nric_no = fake()->uuid();
    $tenth_enrollment = Enrollment::factory()->create([
        'nric_no' => $nric_no,
    ]);

    $gender = Gender::FEMALE->value;
    $eleventh_enrollment = Enrollment::factory()->create([
        'gender' => $gender,
    ]);

    $created_by = User::factory()->create();
    $twelveth_enrollment = Enrollment::factory()->create([
        'created_by' => $created_by->id,
    ]);

    $thirteen_enrollment = Enrollment::factory()->create([
        'admission_year' => 2035,
    ]);

    $passport_no = fake()->uuid();
    $fourteen_enrollment = Enrollment::factory()->create([
        'passport_no' => $passport_no,
    ]);

    $state = State::factory()->create();
    $fifteen_enrollment = Enrollment::factory()->create([
        'state_id' => $state->id,
    ]);

    $country = Country::factory()->create();
    $sixteen_enrollment = Enrollment::factory()->create([
        'country_id' => $country->id,
    ]);

    // =================================================================================

    // Filter by admission_year = 2035
    $response = $this->enrollmentRepository->getAllPaginated(['admission_year' => 2035])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$thirteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by admission_grade_id = $grade->id
    $response = $this->enrollmentRepository->getAllPaginated(['admission_grade_id' => $grade->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fourth_enrollment->toArray()]);

    // =================================================================================

    // Filter by student_name = John Jones
    $response = $this->enrollmentRepository->getAllPaginated(['student_name' => 'John Jones'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by student_name = BBO Name
    $response = $this->enrollmentRepository->getAllPaginated(['student_name' => 'BBO Name'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$third_enrollment->toArray()]);

    // Filter by non-existing student_name = Meh
    $response = $this->enrollmentRepository->getAllPaginated(['student_name' => 'Meh'])->toArray();

    expect($response['data'])->toHaveCount(0)->toBeEmpty();

    // =================================================================================

    // Filter by nric_no = $nric_no
    $response = $this->enrollmentRepository->getAllPaginated(['nric_no' => $nric_no])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$tenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by passport_no = $passport_no
    $response = $this->enrollmentRepository->getAllPaginated(['passport_no' => $passport_no])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fourteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by birthplace_id = $birthplace_country->id
    $response = $this->enrollmentRepository->getAllPaginated(['birthplace_id' => $birthplace_country->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fifth_enrollment->toArray()]);

    // =================================================================================

    // Filter by nationality_id = $nationality->id
    $response = $this->enrollmentRepository->getAllPaginated(['nationality_id' => $nationality->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$sixth_enrollment->toArray()]);

    // =================================================================================

    // Filter by gender = $gender
    $response = $this->enrollmentRepository->getAllPaginated(['gender' => $gender])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eleventh_enrollment->toArray()]);

    // =================================================================================


    // Filter by birth_cert_no = $birth_cert_no
    $response = $this->enrollmentRepository->getAllPaginated(['birth_cert_no' => $birth_cert_no])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$nineth_enrollment->toArray()]);

    // =================================================================================

    // Filter by race_id = $race->id
    $response = $this->enrollmentRepository->getAllPaginated(['race_id' => $race->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$seventh_enrollment->toArray()]);

    // =================================================================================

    // Filter by religion_id = $religion->id
    $response = $this->enrollmentRepository->getAllPaginated(['religion_id' => $religion->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eighth_enrollment->toArray()]);

    // =================================================================================

    // Filter by state_id = $state->id
    $response = $this->enrollmentRepository->getAllPaginated(['state_id' => $state->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fifteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by country_id = $country->id
    $response = $this->enrollmentRepository->getAllPaginated(['country_id' => $country->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$sixteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by created_by = $created_by->id
    $response = $this->enrollmentRepository->getAllPaginated(['created_by' => $created_by->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$twelveth_enrollment->toArray()]);

    // =================================================================================
    // =================================================================================
    // =================================================================================

    // Sort by id asc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($twelveth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
    );

    // Sort by id desc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($twelveth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );

    // Sort by nationality_id asc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['nationality_id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($twelveth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
    );

    // Sort by nationality_id desc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['nationality_id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($twelveth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
    );
});
