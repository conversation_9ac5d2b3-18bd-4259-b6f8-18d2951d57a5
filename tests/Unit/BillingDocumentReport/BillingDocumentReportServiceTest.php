<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\ClassModel;
use App\Models\CurrentStudentClassAndGrade;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\Product;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\Reports\BillingDocumentReportService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
});

test('getDailyCollectionReportData, test excel content, with or without product_ids', function () {

    $grade = Grade::factory()->create([
        'name->en' => 'J1',
    ]);
    $class = ClassModel::factory()->create(['name->en' => 'J111', 'grade_id' => $grade->id]);

    // ensure that only main class is used
    $elective_class = ClassModel::factory()->create(['name->en' => 'other', 'grade_id' => null, 'type' => ClassType::ELECTIVE]);
    $cocu_class = ClassModel::factory()->create(['name->en' => 'cocu', 'grade_id' => null, 'type' => ClassType::SOCIETY]);

    $semester_setting1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $elective_class->id,
    ]);
    $semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $cocu_class->id,
    ]);

    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Albert',
            'name->zh' => 'Albert2',
        ],
        [
            'name->en' => 'Bobby',
            'name->zh' => 'Bobby2',
        ],
        [
            'name->en' => 'Charlie',
            'name->zh' => 'Charlie2',
        ],
        [
            'name->en' => 'David',
            'name->zh' => 'David2',
        ],
        [
            'name->en' => 'Eddy',
            'name->zh' => 'Eddy2',
        ],
    ))->create();

    foreach ($students as $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $student->id,
            'class_type' => ClassType::PRIMARY
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class2->id,
            'student_id' => $student->id,
            'class_type' => ClassType::ELECTIVE
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class3->id,
            'student_id' => $student->id,
            'class_type' => ClassType::SOCIETY
        ]);
    }

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $billing_documents = BillingDocument::factory(5)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 234.50,
            'bill_to_type' => $students[0]->getBillToType(),
            'bill_to_id' => $students[0]->getBillToId(),
            'bill_to_name' => $students[0]->getBillToName(),
            'bill_to_reference_number' => $students[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 123.50,
            'bill_to_type' => $students[1]->getBillToType(),
            'bill_to_id' => $students[1]->getBillToId(),
            'bill_to_name' => $students[1]->getBillToName(),
            'bill_to_reference_number' => $students[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-09-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-08-02',
            'amount_after_tax' => 345.50,
            'bill_to_type' => $students[2]->getBillToType(),
            'bill_to_id' => $students[2]->getBillToId(),
            'bill_to_name' => $students[2]->getBillToName(),
            'bill_to_reference_number' => $students[2]->getBillToReferenceNumber(),
            'paid_at' => '2024-08-02 12:40:00',
        ],

        [
            'status' => BillingDocument::STATUS_POSTED, // to be excluded because status is not STATUS_CONFIRMED
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-03-01',
            'amount_after_tax' => 456.50,
            'bill_to_type' => $students[3]->getBillToType(),
            'bill_to_id' => $students[3]->getBillToId(),
            'bill_to_name' => $students[3]->getBillToName(),
            'bill_to_reference_number' => $students[3]->getBillToReferenceNumber(),
            'paid_at' => null,
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ECOMMERCE, // to be excluded because sub_type is not SUB_TYPE_FEES
            'document_date' => '2024-01-01',
            'amount_after_tax' => 156.50,
            'bill_to_type' => $students[4]->getBillToType(),
            'bill_to_id' => $students[4]->getBillToId(),
            'bill_to_name' => $students[4]->getBillToName(),
            'bill_to_reference_number' => $students[4]->getBillToReferenceNumber(),
            'paid_at' => '2024-01-01 10:00:00',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(5)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'School Fees Aug 2024',
            'period' => '2024-08-02',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'School Fees Aug Special 2024',
            'period' => '2024-08-02',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[3]->id,
            'description' => 'School Fees Dec 2024',
            'period' => '2024-12-01',
        ],
    ))->create();

    $line_items = BillingDocumentLineItem::factory(8)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Jan 2024',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 134.50, // 234.50 - 100 = 134.50
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'School Fees Mar 2024',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 99, // 234.50 - 134.50 - 1 = 99
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => "{$billing_documents[0]->reference_no} Unrelated Charge 2024",
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'School Fees Aug 2024',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 122.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => "{$billing_documents[1]->reference_no} Unrelated Charge 2024",
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'School Fees Aug Special 2024',
            'billable_item_id' => $unpaid_items[3]->id,
            'billable_item_type' => get_class($unpaid_items[3]),
            'amount_before_tax' => 344.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'description' => "{$billing_documents[2]->reference_no} Unrelated Charge 2024",
            'amount_before_tax' => 1,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[3]->id,
            'description' => 'School Fees Dec 2024',
            'billable_item_id' => $unpaid_items[4]->id,
            'billable_item_type' => get_class($unpaid_items[4]),
        ],
    ))->create();

    $payment_methods = PaymentMethod::factory(3)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ],
    ))->create();

    $maybank = Bank::factory()->create([
        'name->en' => 'Maybank',
    ]);

    $payment_requests = PaymentRequest::factory(2)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // BANK_TRANSFER
            'bank_id' => $maybank->id,
        ],
        // below payment requests are excluded because not in filtered
        [
            'billing_document_id' => $billing_documents[3]->id,
            'payment_method_id' => $payment_methods[2]->id, // BANK_TRANSFER
            'bank_id' => $maybank->id,
        ],
    ))->create();

    $payments = Payment::factory(6)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[0]->reference_no,
            'amount_received' => 50,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 184.50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Bank Transfer
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 123.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[2]->reference_no,
            'amount_received' => 345.50,
        ],
        [
            'billing_document_id' => $billing_documents[3]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 456.50,
        ],
        [
            'billing_document_id' => $billing_documents[4]->id,
            'payment_method_id' => $payment_methods[2]->id, // Bank Transfer
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 156.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[1]->id,
        ],
    ))->create();

    CurrentStudentClassAndGrade::refreshViewTable(false);

    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('url');
    });

    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    $file_name = 'billing-document-report-by-daily-collection';

    $expected_headers = [
        __('general.no'),
        __('general.payment_date'),
        __('general.invoice_date'),
        __('general.invoice_no'),
        __('general.bill_to_name'),
        __('general.bill_to_reference_no'),
        __('general.class'),
        __('general.description'),
        __('general.amount') . ' (MYR)',
        __('general.payment'),
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.billing-documents.by-daily-collection';

    // not filtering by product
    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'from_date' => '2024-01-01',
            'to_date' => '2024-12-31',
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $billing_documents,
            $payments,
            $maybank,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            // Invoice 1
            $view->assertSeeInOrder([
                '1',
                '2024-12-01',
                '2024-12-01',
                $billing_documents[0]->reference_no,
                'Albert2',
                $billing_documents[0]->bill_to_reference_number,
                'J111',
                '234.50',
            ], false);

            // Invoice 2
            $view->assertSeeInOrder([
                '2',
                '2024-09-01',
                '2024-09-01',
                $billing_documents[1]->reference_no,
                'Bobby2',
                $billing_documents[1]->bill_to_reference_number,
                'J111',
                '123.50',
            ], false);

            // Invoice 3
            $view->assertSeeInOrder([
                '3',
                '2024-08-02',
                '2024-08-02',
                $billing_documents[2]->reference_no,
                'Charlie2',
                $billing_documents[2]->bill_to_reference_number,
                'J111',
                '345.50',
            ], false);

            // line items description
            $view->assertSeeInOrder([
                'School Fees Jan 2024',
                'School Fees Mar 2024',
                "{$billing_documents[0]->reference_no} Unrelated Charge 2024", // unrelated product included
                'School Fees Aug 2024',
                "{$billing_documents[1]->reference_no} Unrelated Charge 2024",
                'School Fees Aug Special 2024',
                "{$billing_documents[2]->reference_no} Unrelated Charge 2024",
            ], false);

            $view->assertSee($maybank->name);
            $view->assertSee($maybank->swift_code);

            // payments
            $view->assertSeeInOrder([
                $payments[0]->paymentMethod->code,
                $payments[0]->payment_reference_no,
                $payments[1]->paymentMethod->code,
                $payments[1]->payment_reference_no,
                $payments[2]->paymentMethod->code,
                $payments[2]->payment_reference_no,
                $payments[3]->paymentMethod->code,
                $payments[3]->payment_reference_no,
            ], false);


            /**
             * 
             * total amount calculation + net amount calculation
             * 
             */

            $total_amount =
                $billing_documents[0]->amount_after_tax +
                $billing_documents[1]->amount_after_tax +
                $billing_documents[2]->amount_after_tax;

            $net_amount = $total_amount;

            collect(
                [
                    $billing_documents[0],
                    $billing_documents[1],
                    $billing_documents[2]
                ]
            )->each(function ($item) use (&$net_amount) {
                $payex_charge = $item->amount_after_tax <= 250 ? bcadd('0.6', bcmul('0.001', $item->amount_after_tax, 2), 2) : 0.85 ;
                $net_amount -= $payex_charge;
            });
 
            // total amount
            $view->assertSeeInOrder([
                __('general.total_amount'),
                $total_amount,
            ], false);

            // net amount
            $view->assertSeeInOrder([
                __('general.net_amount'),
                $net_amount,
            ], false);

            return true;
        }
    );


    /**
     * only filter billing_document with the $product->id
     */

    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'from_date' => '2024-01-01',
            'to_date' => '2024-12-31',
            'product_ids' => [$product->id], // include only this product
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $billing_documents,
            $payments,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            // Invoice 1
            $view->assertSeeInOrder([
                '1',
                '2024-12-01',
                '2024-12-01',
                $billing_documents[0]->reference_no,
                'Albert2',
                $billing_documents[0]->bill_to_reference_number,
                'J111',
                '233.50', // sum of lineItems where product_id = $product->id
            ], false);

            // Invoice 2
            $view->assertSeeInOrder([
                '2',
                '2024-09-01',
                '2024-09-01',
                $billing_documents[1]->reference_no,
                'Bobby2',
                $billing_documents[1]->bill_to_reference_number,
                'J111',
                '122.50',
            ], false);

            // Invoice 3
            $view->assertSeeInOrder([
                '3',
                '2024-08-02',
                '2024-08-02',
                $billing_documents[2]->reference_no,
                'Charlie2',
                $billing_documents[2]->bill_to_reference_number,
                'J111',
                '344.50',
            ], false);

            // line items description
            $view->assertSeeInOrder([
                'School Fees Jan 2024',
                'School Fees Mar 2024',
                'School Fees Aug 2024',
                'School Fees Aug Special 2024',
                // unrelated product is excluded
            ], false);

            // payments
            $view->assertSeeInOrder([
                $payments[0]->paymentMethod->code,
                $payments[0]->payment_reference_no,
                $payments[1]->paymentMethod->code,
                $payments[1]->payment_reference_no,
                $payments[2]->paymentMethod->code,
                $payments[2]->payment_reference_no,
                $payments[3]->paymentMethod->code,
                $payments[3]->payment_reference_no,
            ], false);


            /**
             * 
             * total amount calculation + net amount calculation
             * 
             */

            $total_amount =
                233.50 +
                122.50 +
                344.50;

            // total amount
            $view->assertSeeInOrder([
                __('general.total_amount'),
                $total_amount,
            ], false);

            // net amount
            $view->assertDontSee(__('general.net_amount'));

            return true;
        }
    );
});

test('getDailyCollectionReportData, test excel content, with discount and advance', function () {
    $grade = Grade::factory()->create([
        'name->en' => 'Junior 1',
    ]);

    $class = ClassModel::factory()->create(['name->en' => 'J111', 'grade_id' => $grade->id]);

    $semester_setting1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class->id,
    ]);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Albert',
            'name->zh' => 'Albert2',
        ],
        [
            'name->en' => 'Bobby',
            'name->zh' => 'Bobby2',
        ],
    ))->create();

    foreach ($students as $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $student->id,
            'class_type' => ClassType::PRIMARY
        ]);
    }

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $albert_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 40,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[0]->id,
        'is_active' => true,
    ]);

    $bobby_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 50,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[1]->id,
        'is_active' => true,
    ]);

    $billing_documents = BillingDocument::factory(2)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300, // 400 (line_item_1) - 40 (discount_line_item_1) - 60 (advance) = 300
            'bill_to_type' => $students[0]->getBillToType(),
            'bill_to_id' => $students[0]->getBillToId(),
            'bill_to_name' => $students[0]->getBillToName(),
            'bill_to_reference_number' => $students[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 300, // 100 (line_item_1) + 350 (line_item_2) - 50 (discount_line_item_1) - 50 (discount_line_item_2) - 50 (advance)= 300
            'bill_to_type' => $students[1]->getBillToType(),
            'bill_to_id' => $students[1]->getBillToId(),
            'bill_to_name' => $students[1]->getBillToName(),
            'bill_to_reference_number' => $students[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-09-01 12:40:00',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(3)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024',
            'period' => '2024-04-01',
        ],
    ))->create();

    $line_items = BillingDocumentLineItem::factory(8)->state(new Sequence(
        // $billing_documents[0]
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024 $product',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 400,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees Jan 2024 $product',
            'amount_before_tax' => -60,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'amount_before_tax' => -40,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[0]->id,
            'description' => 'Less advance payment for Albert SCH0001.',
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
        // $billing_documents[1]
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024 $unrelated_product',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 100,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024 $product',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 350,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees Mar 2024 $unrelated_product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees April 2024 $product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'amount_before_tax' => -50,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[1]->id,
            'description' => "Less advance payment for Bobby SCH0002.",
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
    ))->create();

    // update discount line item with the original line item id
    $line_items[1]->update([
        'discount_original_line_item_id' => $line_items[0]->id,
    ]);
    $line_items[5]->update([
        'discount_original_line_item_id' => $line_items[3]->id,
    ]);
    $line_items[6]->update([
        'discount_original_line_item_id' => $line_items[4]->id,
    ]);

    $payment_methods = PaymentMethod::factory(2)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
    ))->create();

    $payments = Payment::factory(2)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[0]->reference_no,
            'amount_received' => 300,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 300,
        ],
    ))->create();

    CurrentStudentClassAndGrade::refreshViewTable(false);

    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->times(3)->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->times(3)->andReturn('url');
    });

    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    $file_name = 'billing-document-report-by-daily-collection';

    // Test Excel
    Excel::fake();

    $view_name = 'reports.billing-documents.by-daily-collection';

    // not filtering by product
    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'from_date' => '2024-01-01',
            'to_date' => '2024-12-31',
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $view_name,
            $line_items,
            $billing_documents,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            // line items description
            $view->assertSeeInOrder([
                'Albert School Fees Jan 2024 $product',
                'Less advance payment for Albert SCH0001.',
                'Discount for Albert School Fees Jan 2024 $product',
            ], false);

            $albert_amount_before_tax =
                $line_items[0]->amount_before_tax +
                $line_items[1]->amount_before_tax +
                $line_items[2]->amount_before_tax;

            $view->assertSee($albert_amount_before_tax);

            $view->assertSeeInOrder([
                'Bobby School Fees Mar 2024 $unrelated_product',
                'Bobby School Fees April 2024 $product',
                'Less advance payment for Bobby SCH0002.',
                'Discount for Bobby School Fees Mar 2024 $unrelated_product',
                'Discount for Bobby School Fees April 2024 $product',
            ], false);

            $bobby_amount_before_tax =
                $line_items[3]->amount_before_tax +
                $line_items[4]->amount_before_tax +
                $line_items[5]->amount_before_tax +
                $line_items[6]->amount_before_tax +
                $line_items[7]->amount_before_tax;

            $view->assertSee($bobby_amount_before_tax);


            /**
             * 
             * total amount calculation + net amount calculation
             * 
             */

            $total_amount =
                $billing_documents[0]->amount_after_tax +
                $billing_documents[1]->amount_after_tax;

            $net_amount = $total_amount;

            collect(
                [
                    $billing_documents[0],
                    $billing_documents[1],
                ]
            )->each(function ($item) use (&$net_amount) {
                $payex_charge = $item->amount_after_tax <= 250 ? bcadd('0.6', bcmul('0.001', $item->amount_after_tax, 2), 2) : 0.85 ;
                $net_amount -= $payex_charge;
            });

            // total amount
            $view->assertSeeInOrder([
                __('general.total_amount'),
                $total_amount,
            ], false);

            // net amount
            $view->assertSeeInOrder([
                __('general.net_amount'),
                $net_amount,
            ], false);

            return true;
        }
    );

    /**
     * only filter billing_document with the $product->id
     * 
     * expect billing line item + line item discount for $unrelated_product->id to be excluded
     */

    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'from_date' => '2024-01-01',
            'to_date' => '2024-12-31',
            'product_ids' => [$product->id], // include only this product
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $view_name,
            $line_items,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            // line items description
            $view->assertSeeInOrder([
                'Albert School Fees Jan 2024 $product',
                'Less advance payment for Albert SCH0001.',
                'Discount for Albert School Fees Jan 2024 $product',
            ], false);

            $albert_amount_before_tax =
                $line_items[0]->amount_before_tax +
                $line_items[1]->amount_before_tax +
                $line_items[2]->amount_before_tax;

            $view->assertSee($albert_amount_before_tax);

            $view->assertSeeInOrder([
                // 'Bobby School Fees Mar 2024 $unrelated_product', // excluded
                'Bobby School Fees April 2024 $product',
                'Less advance payment for Bobby SCH0002.',
                // 'Discount for Bobby School Fees Mar 2024 $unrelated_product', // excluded
                'Discount for Bobby School Fees April 2024 $product',
            ], false);

            $bobby_amount_before_tax =
                // $line_items[3]->amount_before_tax +  // unrelated product excluded
                $line_items[4]->amount_before_tax +
                // $line_items[5]->amount_before_tax +  // unrelated product excluded
                $line_items[6]->amount_before_tax +
                $line_items[7]->amount_before_tax;

            $view->assertSee($bobby_amount_before_tax);


            /**
             * 
             * total amount calculation + net amount calculation
             * 
             */

            $total_amount =
                $line_items[0]->amount_before_tax +
                $line_items[1]->amount_before_tax +
                $line_items[2]->amount_before_tax +
                // $line_items[3]->amount_before_tax +  // unrelated product excluded
                $line_items[4]->amount_before_tax +
                // $line_items[5]->amount_before_tax +  // unrelated product excluded
                $line_items[6]->amount_before_tax +
                $line_items[7]->amount_before_tax;;

            // total amount
            $view->assertSeeInOrder([
                __('general.total_amount'),
                $total_amount,
            ], false);

            // net amount
            $view->assertDontSee(__('general.net_amount'));

            return true;
        }
    );



    /**
     * only filter billing_document with the $unrelated_product->id
     * 
     * expect billing line item + line item discount for $product->id to be excluded
     */

    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'from_date' => '2024-01-01',
            'to_date' => '2024-12-31',
            'product_ids' => [$unrelated_product->id], // include only this product
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $view_name,
            $line_items,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            // line items description
            $view->assertDontSee('Albert School Fees Jan 2024 $product');
            $view->assertDontSee('Less advance payment for Albert SCH0001.');
            $view->assertDontSee('Discount for Albert School Fees Jan 2024 $product');

            $view->assertSeeInOrder([
                'Bobby School Fees Mar 2024 $unrelated_product',
                // 'Bobby School Fees April 2024 $product', // excluded
                'Less advance payment for Bobby SCH0002.',
                'Discount for Bobby School Fees Mar 2024 $unrelated_product',
                // 'Discount for Bobby School Fees April 2024 $product', // excluded
            ], false);

            $bobby_amount_before_tax =
                $line_items[3]->amount_before_tax +
                // $line_items[4]->amount_before_tax +  // $product excluded
                $line_items[5]->amount_before_tax +
                // $line_items[6]->amount_before_tax +  // $product excluded
                $line_items[7]->amount_before_tax;

            $view->assertSee($bobby_amount_before_tax);


            /**
             * 
             * total amount calculation + net amount calculation
             * 
             */

            $total_amount =
                $line_items[3]->amount_before_tax +
                // $line_items[4]->amount_before_tax +  // $product excluded
                $line_items[5]->amount_before_tax +
                // $line_items[6]->amount_before_tax +  // $product excluded
                $line_items[7]->amount_before_tax;


            // total amount
            $view->assertSeeInOrder([
                __('general.total_amount'),
                $total_amount,
            ], false);

            // net amount
            $view->assertDontSee(__('general.net_amount'));

            return true;
        }
    );
});
