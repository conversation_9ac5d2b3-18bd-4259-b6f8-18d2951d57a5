<?php

use App\Models\Country;
use App\Models\Course;
use App\Models\EnrollmentSession;
use App\Models\Subject;
use App\Services\EnrollmentSessionService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->enrollmentSessionService = app()->make(EnrollmentSessionService::class);
});

test('getAllPaginatedEnrollmentSessions', function () {
    $enrollment_sessions = EnrollmentSession::factory(2)->create();

    $this->mock(EnrollmentSessionService::class, function (\Mockery\MockInterface $mock) use ($enrollment_sessions) {
        $mock->shouldReceive('getAllPaginatedEnrollmentSessions')
            ->once()
            ->andReturn(new LengthAwarePaginator($enrollment_sessions, 2, 1));
    });

    $response = app()->make(EnrollmentSessionService::class)
        ->getAllPaginatedEnrollmentSessions()
        ->toArray();

    expect($response['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $enrollment_sessions[0]->id)
        ->toHaveKey('1.id', $enrollment_sessions[1]->id);
});

test('getAllEnrollmentSessions', function () {
    $enrollment_sessions = EnrollmentSession::factory(2)->create();

    $this->mock(EnrollmentSessionService::class, function (\Mockery\MockInterface $mock) use ($enrollment_sessions) {
        $mock->shouldReceive('getAllEnrollmentSessions')
            ->once()
            ->andReturn($enrollment_sessions);
    });

    $response = app()->make(EnrollmentSessionService::class)
        ->getAllEnrollmentSessions()
        ->toArray();

    expect($response)->toHaveCount(2)
        ->toHaveKey('0.id', $enrollment_sessions[0]->id)
        ->toHaveKey('1.id', $enrollment_sessions[1]->id);
});

test('createEnrollmentSession without fee_assignment_settings', function () {
    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();

    $data = [
        'name' => 'Test Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject_1->id, $subject_2->id],
    ];

    $response = $this->enrollmentSessionService->createEnrollmentSession($data);

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id']);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'fee_assignment_settings' => null, // No fee assignment settings
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_2->id,
    ]);
});

test('createEnrollmentSession with fee_assignment_settings', function () {
    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();

    // temp fee assignment settings
    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => 1,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '1',
                ],
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '2',
                ],
            ],
            'outcome' => [
                'product_id' => 2,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $data = [
        'name' => 'Test Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject_1->id, $subject_2->id],
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ];

    $response = $this->enrollmentSessionService->createEnrollmentSession($data);

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id'])
        ->fee_assignment_settings->toEqual($temp_fee_assignment_settings);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'fee_assignment_settings' => json_encode($temp_fee_assignment_settings),
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_2->id,
    ]);
});

test('updateEnrollmentSession', function () {
    $old_subject_1 = Subject::factory()->create();
    $new_subject_1 = Subject::factory()->create();
    $new_subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();

    $enrollment_session = EnrollmentSession::factory()->create();
    $enrollment_session->examSubjects()->attach($old_subject_1);

    // temp fee assignment settings
    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => 1,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '1',
                ],
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => '2',
                ],
            ],
            'outcome' => [
                'product_id' => 2,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $data = [
        'name' => 'Updated Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$new_subject_1->id, $new_subject_2->id],
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ];

    $response = $this->enrollmentSessionService->updateEnrollmentSession($enrollment_session, $data);

    $response->refresh();

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id'])
        ->fee_assignment_settings->toEqual($temp_fee_assignment_settings);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'fee_assignment_settings' => json_encode($temp_fee_assignment_settings),
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $new_subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $new_subject_2->id,
    ]);

    // old subject should be removed
    $this->assertDatabaseMissing('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $old_subject_1->id,
    ]);
});

test('deleteEnrollmentSession', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $this->enrollmentSessionService->deleteEnrollmentSession($enrollment_session);

    $this->assertDatabaseMissing('enrollment_sessions', [
        'id' => $enrollment_session->id,
    ]);
});

test('deleteEnrollmentSession failed because already attached with subjects', function () {
    $enrollment_session = EnrollmentSession::factory()->create();
    $subject = Subject::factory()->create();

    $enrollment_session->examSubjects()->attach($subject);

    expect(function () use ($enrollment_session) {
        $this->enrollmentSessionService->deleteEnrollmentSession($enrollment_session);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(5002);
    }, __('system_error.5002'));
});

test('getFeeSettingConditions', function () {
    $countries = Country::factory(2)->state(new Sequence(
        ['name->en' => 'Malaysia'],
        ['name->en' => 'Indonesia']
    ))->create();

    $conditions = $this->enrollmentSessionService->getFeeSettingConditions();

    expect($conditions)->toBeArray()
        ->toHaveCount(2);

    expect($conditions[0]['value'])->toBe('nationality_id');
    expect($conditions[0]['label'])->toBe('Nationality');
    expect($conditions[0]['options'])->toBeArray();
    expect($conditions[0]['options'][0]['value'])->toBe($countries[0]->id);
    expect($conditions[0]['options'][0]['label'])->toBe($countries[0]->name);
    expect($conditions[0]['options'][1]['value'])->toBe($countries[1]->id);
    expect($conditions[0]['options'][1]['label'])->toBe($countries[1]->name);

    expect($conditions[1]['value'])->toBe('is_hostel');
    expect($conditions[1]['label'])->toBe('Staying in hostel');
    expect($conditions[1]['options'])->toBeArray();
    expect($conditions[1]['options'][0]['value'])->toBe(true);
    expect($conditions[1]['options'][0]['label'])->toBe('Yes');
    expect($conditions[1]['options'][1]['value'])->toBe(false);
    expect($conditions[1]['options'][1]['label'])->toBe('No');
});
