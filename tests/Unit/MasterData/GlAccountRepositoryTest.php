<?php

use App\Models\GlAccount;
use App\Repositories\GlAccountRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->glAccountRepository = app(GlAccountRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(GlAccount::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->glAccountRepository->getModelClass();

    expect($response)->toEqual(GlAccount::class);
});

test('getAll()', function () {
    $gl_accounts = GlAccount::factory(3)->state(new Sequence(
        [
            'code' => GlAccount::CODE_OTHERS,
            'name' => 'Others',
            'label' => 'Others',
            'is_active' => true
        ],
        [
            'code' => GlAccount::CODE_EWALLET,
            'name' => 'E-Wallet',
            'label' => 'E-Wallet',
            'is_active' => true
        ],
        [
            'code' => GlAccount::CODE_ENROLLMENT_REGISTRATION_NEW,
            'name' => 'Registration Fees (New Student)',
            'label' => 'Registration Fees (New Student)',
            'is_active' => true
        ],
    ))->create();

    $response = $this->glAccountRepository->getAll()->toArray();

    expect($response)->toEqual($gl_accounts->toArray());
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $gl_accounts = [
        'first' => GlAccount::factory()->create([
            'code' => GlAccount::CODE_OTHERS,
            'name' => '1Others',
            'label' => 'Others',
            'is_active' => true
        ]),
        'second' => GlAccount::factory()->create([
            'code' => GlAccount::CODE_EWALLET,
            'name' => '2E-Wallet',
            'label' => 'E-Wallet',
            'is_active' => true
        ]),
        'third' => GlAccount::factory()->create([
            'code' => GlAccount::CODE_ENROLLMENT_REGISTRATION_NEW,
            'name' => '3Enrollment',
            'label' => 'Enrollment',
            'is_active' => false
        ]),
    ];

    $result = $this->glAccountRepository->getAllPaginated([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($gl_accounts[$value]->toArray());
    }
})->with([
    'filter by name = E-Wallet' => [1, 'name', 'E-Wallet', ['second']],
    'filter by name = Enrollment' => [1, 'name', 'Enrollment', ['third']],
    'filter by code = **********' => [1, 'code', '**********', ['first']],
    'filter by code = EWALLET001' => [1, 'code', 'EWALLET001', ['second']],
    'filter by is_active = true' => [2, 'is_active', true, ['first', 'second']],
    'filter by is_active = false' => [1, 'is_active', false, ['third']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['first', 'second', 'third']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['third', 'second', 'first']],
]);
