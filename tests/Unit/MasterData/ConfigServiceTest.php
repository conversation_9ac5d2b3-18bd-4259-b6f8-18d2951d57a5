<?php

use App\Enums\Day;
use App\Models\Config;
use App\Services\ConfigService;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    $this->configService = app(ConfigService::class);

    app()->setLocale('en');

    $this->table = resolve(Config::class)->getTable();
});

test('getAllConfigs() : value is of type string', function () {
    $first_config = Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ]);

    Config::factory()->create([
        'key' => Config::MAINTENANCE_MODE,
        'value' => false,
        'category' => Config::CATEGORY_SYSTEM,
    ]);

    // Get all config - ensure default data and new data included
    $response = $this->configService->getAllConfigs();

    expect($response)->toMatchArray([
        [
            'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
            'value' => Config::AVAILABLE_CONFIGS[Config::ENROLLMENT_DOCUMENT_TYPE],
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'array',
            'options' => [
                ['value' => 'file_ic_front', 'label' => 'IC Front'],
                ['value' => 'file_ic_back', 'label' => 'IC Back'],
                ['value' => 'file_upsr_result', 'label' => 'UPSR Result'],
            ],
        ],
        [
            'key' => Config::WALLET_TRANSFERABLE_MODEL,
            'value' => Config::AVAILABLE_CONFIGS[Config::WALLET_TRANSFERABLE_MODEL],
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'object',
            'options' => [
                [
                    'value' => 'guardian',
                    'label' => 'Guardian',
                    'sub_value' => [
                        [
                            'value' => 'student',
                            'label' => 'Student'
                        ]
                    ]
                ],
                [
                    'value' => 'employee',
                    'label' => 'Employee',
                    'sub_value' => [
                        [
                            'value' => 'student',
                            'label' => 'Student'
                        ]
                    ]
                ],
                [
                    'value' => 'student',
                    'label' => 'Student',
                    'sub_value' => [
                        [
                            'value' => 'student',
                            'label' => 'Student'
                        ]
                    ]
                ]
            ]
        ],
        [
            'key' => Config::PAYEX_MERCHANT_EMAIL,
            'value' => '<EMAIL>',
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'string',
            'options' => []
        ],
        [
            'key' => Config::PAYEX_MERCHANT_SECRET,
            'value' => Config::AVAILABLE_CONFIGS[Config::PAYEX_MERCHANT_SECRET],
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'string',
            'options' => []
        ],
        [
            "key" => Config::DATA_COMPULSORY_LOCALE,
            "value" => Config::AVAILABLE_CONFIGS[Config::DATA_COMPULSORY_LOCALE],
            "category" => Config::CATEGORY_GENERAL,
            "type" => "array",
            "options" => []
        ],
        [
            "key" => Config::ENROLLMENT_FEES,
            "value" => Config::AVAILABLE_CONFIGS[Config::ENROLLMENT_FEES],
            "category" => Config::CATEGORY_GENERAL,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_BORROW_LIMIT_STUDENT,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_STUDENT],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_BORROW_LIMIT_EMPLOYEE,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_EMPLOYEE],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_BORROW_LIMIT_LIBRARIAN,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_LIBRARIAN],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_BORROW_LIMIT_OTHER,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_OTHER],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LOAN_PERIOD_DAY_STUDENT,
            "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_STUDENT],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LOAN_PERIOD_DAY_EMPLOYEE,
            "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_EMPLOYEE],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LOAN_PERIOD_DAY_LIBRARIAN,
            "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_LIBRARIAN],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::LOAN_PERIOD_DAY_OTHER,
            "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_OTHER],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT,
            "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "boolean",
            "options" => []
        ],
        [
            "key" => Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE,
            "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "boolean",
            "options" => []
        ],
        [
            "key" => Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN,
            "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "boolean",
            "options" => []
        ],
        [
            "key" => Config::BORROW_WITH_UNRETURNED_BOOK_OTHER,
            "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_OTHER],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "boolean",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_FINE_PER_DAY_STUDENT,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_STUDENT],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_FINE_PER_DAY_EMPLOYEE,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_EMPLOYEE],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_FINE_PER_DAY_LIBRARIAN,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_LIBRARIAN],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::LIBRARY_FINE_PER_DAY_OTHER,
            "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_OTHER],
            "category" => Config::CATEGORY_LIBRARY,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS,
            "value" => Config::AVAILABLE_CONFIGS[Config::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS],
            "category" => Config::CATEGORY_HOSTEL,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::WALLET_DEPOSIT_MIN_AMOUNT,
            "value" => Config::AVAILABLE_CONFIGS[Config::WALLET_DEPOSIT_MIN_AMOUNT],
            "category" => Config::CATEGORY_GENERAL,
            "type" => "decimal",
            "options" => []
        ],
        [
            "key" => Config::WALLET_DEPOSIT_MAX_AMOUNT,
            "value" => Config::AVAILABLE_CONFIGS[Config::WALLET_DEPOSIT_MAX_AMOUNT],
            "category" => Config::CATEGORY_GENERAL,
            "type" => "decimal",
            "options" => []
        ],
        [
            'key' => Config::CANTEEN_OPEN_STATUS,
            'value' => true,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'boolean',
            'options' => []
        ],
        [
            'key' => Config::CANTEEN_OPENING_DAY,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'string',
            'options' => [
                ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
            ]
        ],
        [
            'key' => Config::CANTEEN_OPENING_TIME,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'time',
            'options' => []
        ],
        [
            'key' => Config::CANTEEN_CUTOFF_DAY,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'string',
            'options' => [
                ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
            ]
        ],
        [
            'key' => Config::CANTEEN_CUTOFF_TIME,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'time',
            'options' => []
        ],
        [
            'key' => Config::CANTEEN_NOTIFICATION_DAY,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'string',
            'options' => [
                ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
            ]
        ],
        [
            "key" => Config::CANTEEN_NOTIFICATION_TIME,
            "value" => null,
            "category" => Config::CATEGORY_CANTEEN,
            "type" => "time",
            "options" => []
        ],
        [
            'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
            'value' => null,
            'category' => Config::CATEGORY_CANTEEN,
            'type' => 'string',
            'options' => []
        ],
        [
            "key" => Config::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT,
            "value" => null,
            "category" => Config::CATEGORY_BANK_ACCOUNT,
            "type" => "integer",
            "options" => []
        ],
        [
            "key" => Config::BANK_ACCOUNT_DEFAULT_SCHOOL_FEES,
            "value" => null,
            "category" => Config::CATEGORY_BANK_ACCOUNT,
            "type" => "integer",
            "options" => []
        ],
        [
            'key' => Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND,
            'value' => 300,
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'integer',
            'options' => []
        ],
    ]);

    // Check if only keys in AVAILABLE_CONFIGS are in response
    $maintenance_mode = [
        "key" => Config::MAINTENANCE_MODE,
        "value" => false,
        "category" => Config::CATEGORY_SYSTEM,
        "type" => Config::DATA_TYPE_BOOLEAN,
        "options" => []
    ];
    $check = in_array($maintenance_mode, $response);
    expect($check)->toBeFalse();
});


test('getAllConfigs() : value is of correct data type in Config::CONFIG_DATA_TYPE', function () {
    $config_array = Config::factory()->create([
        'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
        'value' => [
            'hello',
            'world'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $config_assoc_array = Config::factory()->create([
        'key' => Config::WALLET_TRANSFERABLE_MODEL,
        'value' => [
            'guardian' => 'student',
            'employee' => 'student'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $config_string = Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // Get all config - ensure default data and new data included
    $response = $this->configService->getAllConfigs();

    expect($response)
        ->toMatchArray([
            [
                'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
                'value' => $config_array->value,
                'type' => 'array',
                'options' => Config::getAvailableConfigOptions(Config::ENROLLMENT_DOCUMENT_TYPE),
                'category' => Config::CATEGORY_GENERAL,
            ],
            [
                'key' => Config::WALLET_TRANSFERABLE_MODEL,
                'value' => $config_assoc_array->value,
                'type' => 'object',
                'options' => Config::getAvailableConfigOptions(Config::WALLET_TRANSFERABLE_MODEL),
                'category' => Config::CATEGORY_GENERAL,
            ],
            [
                'key' => Config::PAYEX_MERCHANT_EMAIL,
                'value' => $config_string->value,
                'type' => 'string',
                'options' => [],
                'category' => Config::CATEGORY_GENERAL,
            ],
        ])->and($response[0]['value'])->toBeArray()
        ->and($response[1]['value'])->toBeArray()
        ->and($response[2]['value'])->toBeString();
});

test('updateOrCreateConfig() : update config of type string', function () {
    $first_config = Config::factory()->create([
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ]);

    //update with key exist
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'value' => 'updated value',
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->configService->updateOrCreateConfig($first_config->key, $payload['value'], $payload['category']);

    expect($response)->toMatchArray([
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => $payload['value'],
        'category' => Config::CATEGORY_LIBRARY,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'key' => $first_config->key,
        'value' => $payload['value'],
        'category' => Config::CATEGORY_LIBRARY,
    ]);

    //update with key not exist in db -> will be created
    $new_payload = [
        'key' => 'new key',
        'value' => 'new value',
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $this->configService->updateOrCreateConfig($new_payload['key'], $new_payload['value'], $new_payload['category']);

    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'key' => $new_payload['key'],
        'value' => $new_payload['value'],
        'category' => Config::CATEGORY_LIBRARY,
    ]);
});

test('updateOrCreateConfig() : update config of type string fail because try to update with json', function () {
    $initial_key = Config::CANTEEN_NOTIFICATION_EMAIL; // type string
    $initial_value = '<EMAIL>';

    $first_config = Config::factory()->create([
        'key' => $initial_key,
        'value' => $initial_value,
    ]);

    //update with key exist is of type json array
    $payload = [
        'value' => [
            'new',
            'hello'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ];

    $has_error = false;

    try {
        $response = $this->configService->updateOrCreateConfig($first_config->key, $payload['value'], $payload['category']);
    } catch (\Throwable $th) {
        // Data will not be encoded if Config::CONFIG_DATA_TYPE is not json
        expect($th)->toBeInstanceOf(QueryException::class);
        $has_error = true;
    }

    expect($has_error)->toBeTruthy();

    $this->assertDatabaseHas($this->table, [
        'key' => $initial_key,
        'value' => $initial_value,
    ]);

    //update with key exist is of type json assoc_array
    $payload = [
        'value' => [
            'full' => 'name',
            'current' => 'age'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ];

    $has_error = false;

    try {
        $this->configService->updateOrCreateConfig($first_config->key, $payload['value'], $payload['category']);
    } catch (\Throwable $th) {
        // Data will not be encoded if Config::CONFIG_DATA_TYPE is not json
        expect($th)->toBeInstanceOf(QueryException::class);
        $has_error = true;
    }

    expect($has_error)->toBeTruthy();

    $this->assertDatabaseHas($this->table, [
        'key' => $initial_key,
        'value' => $initial_value,
    ]);
});

test('getConfig() : value is string', function () {
    // get config by key
    $first_config = Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $response = $this->configService->getConfig($first_config->key);

    expect($response)->toMatchArray([
        'key' => $first_config->key,
        'value' => $first_config->value,
        'category' => Config::CATEGORY_GENERAL,
    ]);
});

test('getConfig() : fail because key non_existing', function () {
    $has_error = false;

    try {
        $this->configService->getConfig('non_existing_key');
    } catch (\Throwable $th) {
        expect($th)->toBeInstanceOf(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $has_error = true;
    }

    expect($has_error)->toBeTruthy();
});

test('getConfig() : config return correct type from Config::CONFIG_DATA_TYPE', function () {
    // type array
    $enrol_key = Config::ENROLLMENT_DOCUMENT_TYPE;

    Config::factory()->create([
        'key' => $enrol_key,
        'value' => [
            'hello',
            'world'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $response = $this->configService->getConfig($enrol_key);

    expect($response)->toEqual([
        'key' => $enrol_key,
        'value' => [
            'hello',
            'world'
        ],
        'type' => 'array',
        'options' => Config::getAvailableConfigOptions($enrol_key),
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // type assoc array
    $wallet_key = Config::WALLET_TRANSFERABLE_MODEL;

    Config::factory()->create([
        'key' => $wallet_key,
        'value' => [
            'guardian' => ['student'],
            'employee' => ['student']
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $response = $this->configService->getConfig($wallet_key);

    expect($response)->toEqual([
        'key' => $wallet_key,
        'value' => [
            'guardian' => ['student'],
            'employee' => ['student']
        ],
        'type' => 'object',
        'options' => Config::getAvailableConfigOptions($wallet_key),
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // type string
    $attendance_key = Config::PAYEX_MERCHANT_EMAIL;

    Config::factory()->create([
        'key' => $attendance_key,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ]);

    $response = $this->configService->getConfig($attendance_key);

    expect($response)->toEqual([
        'key' => $attendance_key,
        'value' => '<EMAIL>',
        'type' => 'string',
        'options' => [],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // type time
    $attendance_key = Config::CANTEEN_CUTOFF_TIME;

    Config::factory()->create([
        'key' => $attendance_key,
        'value' => '11:30',
        'category' => Config::CATEGORY_CANTEEN,
    ]);

    $response = $this->configService->getConfig($attendance_key);

    expect($response)->toEqual([
        'key' => $attendance_key,
        'value' => '11:30',
        'type' => 'time',
        'options' => [],
        'category' => Config::CATEGORY_CANTEEN,
    ]);
});

test('getUserConfigs() from db', function () {
    Config::factory()->create([
        'key' => Config::CANTEEN_CUTOFF_DAY,
        'value' => 'FRIDAY'
    ]);

    Config::factory()->create([
        'key' => Config::CANTEEN_CUTOFF_TIME,
        'value' => '23:59'
    ]);

    $response = $this->configService->getUserConfigs();
    expect($response)->toMatchArray([
        [
            'key' => Config::CANTEEN_CUTOFF_DAY,
            'value' => 'FRIDAY',
        ],
        [
            'key' => Config::CANTEEN_CUTOFF_TIME,
            'value' => '23:59',
        ]
    ]);
});

test('getUserConfigs() from constant', function () {
    Cache::flush();
    $response = $this->configService->getUserConfigs();

    expect($response)->toMatchArray([
        [
            'key' => Config::CANTEEN_CUTOFF_DAY,
            'value' => Config::AVAILABLE_CONFIGS[Config::CANTEEN_CUTOFF_DAY],
        ],
        [
            'key' => Config::CANTEEN_CUTOFF_TIME,
            'value' => Config::AVAILABLE_CONFIGS[Config::CANTEEN_CUTOFF_TIME],
        ]
    ]);
});

test('getConstantConfigValue() key not found and throw_exception true', function () {
    expect(function () {
        $this->configService->getConstantConfigValue('NONEXIST');
    })->toThrow(function (Exception $e) {
    }, 'Config settings "NONEXIST" not found');
});

test('getConstantConfigValue() key not found and throw_exception false', function () {
    $response = $this->configService->getConstantConfigValue('NONEXIST', false);

    expect($response)->toBeNull();
});

test('getConstantConfigValue() key found', function () {
    $response = $this->configService->getConstantConfigValue(Config::CANTEEN_OPEN_STATUS);

    expect($response)->toBe(Config::AVAILABLE_CONFIGS[Config::CANTEEN_OPEN_STATUS]);
});
