<?php

use App\Models\BookSource;
use App\Services\BookSourceService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    
    $this->bookSourceService = app(BookSourceService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(BookSource::class)->getTable();
});

test('getAllPaginatedBookSources()', function () {
    $book_sources = BookSource::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
            'name->zh' => '印度人'
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.$this->testLocale", 'English 2');

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_sources[0]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[1]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->bookSourceService->getAllPaginatedBookSources($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_sources[2]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[1]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[0]->id),
        );
});

test('getAllBookSources()', function () {
    $book_sources = BookSource::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
            'name->zh' => '印度人'
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toHaveKey("0.name.$this->testLocale", 'English 2');

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_sources[0]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[1]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->bookSourceService->getAllBookSources($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_sources[2]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[1]->id),
            fn($data) => $data->toHaveKey('id', $book_sources[0]->id),
        );
});

test('createBookSource()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
    ];

    $response = $this->bookSourceService->createBookSource($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('updateBookSource()', function () {
    $book_source = BookSource::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
    ];

    $response = $this->bookSourceService->updateBookSource($book_source->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $book_source->id,
        'name' => $payload['name']
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->bookSourceService->updateBookSource(9999, $payload)->toArray();
});

test('deleteBookSource()', function () {
    $book_source = BookSource::factory()->create();
    $other_book_sources = BookSource::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->bookSourceService->deleteBookSource($book_source->id);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $book_source->id]);

    foreach ($other_book_sources as $other_book_source) {
        $this->assertDatabaseHas($this->table, ['id' => $other_book_source->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->bookSourceService->deleteBookSource(9999);
});
