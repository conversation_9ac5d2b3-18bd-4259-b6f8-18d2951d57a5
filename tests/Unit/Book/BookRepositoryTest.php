<?php

use App\Enums\BookLoanSettingType;
use App\Enums\LibraryMemberType;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookLanguage;
use App\Models\BookLoanSetting;
use App\Models\BookSubClassification;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\Student;
use App\Repositories\BookRepository;
use Carbon\Carbon;
use Database\Seeders\BookLanguageSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed([
        BookLanguageSeeder::class,
    ]);
    $this->bookRepository = app(BookRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->bookLanguages = BookLanguage::all();

    $this->table = resolve(Book::class)->getTable();
    $this->author_book_table = 'author_book';
    $this->book_loan_setting_table = resolve(BookLoanSetting::class)->getTable();

    $this->bookSubClassifications = BookSubClassification::factory(2)->create();
    $this->authors = Author::factory(2)
        ->state(new Sequence(
            [
                'name' => 'David',
            ],
            [
                'name' => 'Simon'
            ]
        ))
        ->create();
    $this->books = Book::factory(3)->state(new Sequence(
        [
            'book_no' => '12345',
            'call_no' => '12345',
            'book_language_id' => $this->bookLanguages[0]->id,
            'book_sub_classification_id' => $this->bookSubClassifications[0]->id,
            'title' => 'English',
            'isbn' => '123456789',
        ],
        [
            'book_no' => '12346',
            'call_no' => '12346',
            'book_language_id' => $this->bookLanguages[0]->id,
            'book_sub_classification_id' => $this->bookSubClassifications[0]->id,
            'title' => 'English 2',
            'isbn' => '123456788',
        ],
        [
            'book_no' => '12347',
            'call_no' => '12347',
            'book_language_id' => $this->bookLanguages[1]->id,
            'book_sub_classification_id' => $this->bookSubClassifications[1]->id,
            'title' => 'Tamil',
            'isbn' => '123456787',
        ]
    ))->create();

    $this->books[0]->authors()->sync([$this->authors[0]->id]);
    $this->books[1]->authors()->sync([$this->authors[0]->id]);
    $this->books[2]->authors()->sync([$this->authors[1]->id]);

    $this->students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Student 1'
        ],
        [
            'name->en' => 'Student 2'
        ],
    ))->create();

    $this->members = LibraryMember::factory(3)->state(new Sequence(
        [
            'name' => 'member 1',
            'type' => LibraryMemberType::STUDENT,
            'userable_id' => $this->students[0]->id,
            'userable_type' => Student::class,
        ],
        [
            'name' => 'member 2',
            'type' => LibraryMemberType::STUDENT,
            'userable_id' => $this->students[1]->id,
            'userable_type' => Student::class,
        ],
        [
            'name' => 'member 3',
            'type' => LibraryMemberType::GUARDIAN
        ],
    ))->create();

    $this->bookLoans = LibraryBookLoan::factory(4)->state(new Sequence(
        [
            'book_id' => $this->books[0]->id,
            'member_id' => $this->members[0]->id,
            'loan_date' => Carbon::parse('2024-01-01')
        ],
        [
            'book_id' => $this->books[0]->id,
            'member_id' => $this->members[0]->id,
            'loan_date' => Carbon::parse('2024-01-02')
        ],
        [
            'book_id' => $this->books[0]->id,
            'member_id' => $this->members[1]->id,
            'loan_date' => Carbon::parse('2024-01-03')
        ],
        [
            'book_id' => $this->books[1]->id,
            'member_id' => $this->members[2]->id,
            'loan_date' => Carbon::parse('2024-01-03')
        ],
        [
            'book_id' => $this->books[2]->id,
            'member_id' => $this->members[2]->id,
            'loan_date' => Carbon::parse('2024-01-05')
        ]
    ))->create();


});

test('getModelClass()', function () {
    $response = $this->bookRepository->getModelClass();

    expect($response)->toEqual(Book::class);
});

test('getAll()', function () {
    $this->books->load('authors');
    $response = $this->bookRepository->getAll()->toArray();

    expect($response)->toMatchArray($this->books->toArray());
});

test('getAllPaginated() filter by period loan date', function () {
    $start_loan_date = Carbon::parse('2024-01-01', 'UTC')->startOfDay();
    $end_loan_date = Carbon::parse('2024-01-02', 'UTC')->endOfDay();
    $payload = [
        'period_loan_date_from' => $start_loan_date->format('Y-m-d'),
        'period_loan_date_to' => $end_loan_date->format('Y-m-d'),
        'includes' => [
            'bookLoans' => function ($query) use ($start_loan_date, $end_loan_date) {
                $query->where('loan_date', '>=', $start_loan_date)
                    ->where('loan_date', '<=', $end_loan_date);
            }
        ]
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1);

    foreach ($response['data'] as $book) {
        foreach ($book['book_loans'] as $book_loan) {
            $loan_date = Carbon::parse($book_loan['loan_date'], 'UTC');
            expect($loan_date)->toBeGreaterThanOrEqual($start_loan_date)
                ->and($loan_date)->toBeLessThanOrEqual($end_loan_date);
        }
    }
});

test('getAllPaginated() filter by book language', function () {
    $payload = [
        'book_language_id' => $this->bookLanguages[0]->id,
    ];

    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2);

    foreach ($response['data'] as $book) {
        expect($book['book_language_id'])->toBe($this->bookLanguages[0]->id);
    }
});

test('getAllPaginated() filter by book_loan_exist', function () {
    $payload = [
        'book_loan_exist' => true,
        'includes' => ['bookLoans']
    ];

    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2);

    foreach ($response['data'] as $book) {
        expect($book['book_loans'])->not->toBeEmpty();
    }
});

test('getAllPaginated()', function () {
    //Filter by title = English 2
    $payload = [
        'title' => 'English 2'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter by partial title = English
    $payload = [
        'title' => 'English'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter non-existing title = No Exist
    $payload = [
        'title' => 'No Exist'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by author ids
    $payload = [
        'author_ids' => [$this->authors[0]->id]
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('authors.0.id', $this->authors[0]->id),
            fn($data) => $data->toHaveKey('authors.0.id', $this->authors[0]->id)
        );

    //Filter non-existing author_ids
    $payload = [
        'author_ids' => [9999]
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by isbn
    $payload = [
        'isbn' => '123456789'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('isbn', '123456789')
        );

    //Filter by no exist isbn
    $payload = [
        'isbn' => '999999'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by book sub classification id = 1
    $payload = [
        'book_sub_classification_id' => $this->bookSubClassifications[1]->id
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_sub_classification_id', $this->bookSubClassifications[1]->id)
        );

    //Filter non-existing book sub classification id = 99999
    $payload = [
        'book_sub_classification_id' => 99999
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by book_no = 12345
    $payload = [
        'book_no' => '12345'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_no', '12345')
        );

    //Filter by book_no_wildcard = 345
    $payload = [
        'book_no_wildcard' => '345'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_no', '12345')
        );

    //Filter non-existing book_no = No Exist
    $payload = [
        'book_no' => 'No Exist'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by call_no = 12345
    $payload = [
        'call_no' => '12345'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('call_no', '12345')
        );

    //Filter by call_no_wildcard = 345
    $payload = [
        'call_no_wildcard' => '345'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('call_no', '12345')
        );

    //Filter non-existing call_no = No Exist
    $payload = [
        'call_no' => 'No Exist'
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by title asc
    $payload = [
        'order_by' => ['title' => 'asc'],
    ];

    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'Tamil'),
        );

    //sort by title desc
    $payload = [
        'order_by' => ['title' => 'desc'],
    ];

    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'Tamil'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $this->books[0]->id),
            fn($data) => $data->toHaveKey('id', $this->books[1]->id),
            fn($data) => $data->toHaveKey('id', $this->books[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->bookRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $this->books[2]->id),
            fn($data) => $data->toHaveKey('id', $this->books[1]->id),
            fn($data) => $data->toHaveKey('id', $this->books[0]->id),
        );
});

test('syncAuthors()', function () {

    $this->bookRepository->syncAuthors($this->books[0], [$this->authors[0]->id]);

    $this->assertDatabaseCount($this->author_book_table, 3);
    $this->assertDatabaseHas($this->author_book_table, [
        'book_id' => $this->books[0]->id,
        'author_id' => $this->authors[0]->id
    ]);

    $this->bookRepository->syncAuthors($this->books[0], [$this->authors[0]->id, $this->authors[1]->id]);

    $this->assertDatabaseCount($this->author_book_table, 4);

    $this->assertDatabaseHas($this->author_book_table, [
        'book_id' => $this->books[0]->id,
        'author_id' => $this->authors[0]->id
    ]);

    $this->assertDatabaseHas($this->author_book_table, [
        'book_id' => $this->books[0]->id,
        'author_id' => $this->authors[1]->id
    ]);
});

test('updateLoanSettings', function () {
    $book = Book::factory()->create();
    $payload = [
        BookLoanSettingType::EMPLOYEE->value => [
            'loan_period_day' => 5,
            'can_borrow' => true
        ],
        BookLoanSettingType::STUDENT->value => [
            'loan_period_day' => 10,
            'can_borrow' => true
        ],
        BookLoanSettingType::LIBRARIAN->value => [
            'loan_period_day' => 15,
            'can_borrow' => true
        ],
//        'other' => [
//            'loan_period_day' => 20,
//            'can_borrow' => true
//        ],
    ];

    $this->bookRepository->updateLoanSettings($book, $payload);
    $book->refresh();

    //book_loan_settings table inserted 4 records
    $this->assertDatabaseCount($this->book_loan_setting_table, 3);

    foreach ($payload as $type => $value) {
        $this->assertDatabaseHas($this->book_loan_setting_table, [
            'book_id' => $book->id,
            'type' => $type,
            'loan_period_day' => $value['loan_period_day'],
            'can_borrow' => $value['can_borrow'],
        ]);
    }

});

test('updateLoanSettings update', function () {

    $book = Book::factory()->create();

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
        ))
        ->create();


    $update_payload = [
        BookLoanSettingType::EMPLOYEE->value => [
            'loan_period_day' => 20,
            'can_borrow' => true
        ],
        BookLoanSettingType::STUDENT->value => [
            'loan_period_day' => 15,
            'can_borrow' => true
        ],
        BookLoanSettingType::LIBRARIAN->value => [
            'loan_period_day' => 10,
            'can_borrow' => true
        ],
        BookLoanSettingType::OTHERS->value => [
            'loan_period_day' => 5,
            'can_borrow' => true
        ],
    ];

    $this->bookRepository->updateLoanSettings($book, $update_payload);

    //book_loan_settings table inserted 4 records
    $this->assertDatabaseCount($this->book_loan_setting_table, 4);

    foreach ($update_payload as $type => $value) {
        $this->assertDatabaseHas($this->book_loan_setting_table, [
            'book_id' => $book->id,
            'type' => $type,
            'loan_period_day' => $value['loan_period_day'],
            'can_borrow' => $value['can_borrow'],
        ]);
    }
});

test('orderByTotalBookLoan()', function () {
    $start_loan_date = Carbon::parse('2024-01-01', 'UTC')->startOfDay();
    $end_loan_date = Carbon::parse('2024-01-04', 'UTC')->endOfDay();
    $payload = [
        'period_loan_date_from' => $start_loan_date->format('Y-m-d'),
        'period_loan_date_to' => $end_loan_date->format('Y-m-d'),
    ];
    $response = $this->bookRepository->orderByTotalBookLoan($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('book_loans_count', 3),
            fn($data) => $data->toHaveKey('book_loans_count', 1)
        );
});
