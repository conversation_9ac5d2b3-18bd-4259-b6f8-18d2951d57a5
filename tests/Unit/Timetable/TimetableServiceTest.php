<?php

use App\Enums\Day;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\TimeslotTeacherType;
use App\Models\ClassSubject;
use App\Models\Employee;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\Timetable\TimetableService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->timetableService = app(TimetableService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Timetable::class)->getTable();
    $this->timeslotTable = resolve(Timeslot::class)->getTable();
    $this->timeslotTeacherTable = resolve(TimeslotTeacher::class)->getTable();
});

test('getAllPaginatedTimetables()', function () {
    $timetables = Timetable::factory(3)->state(new Sequence(
        [
            'name' => 'English'
        ],
        [
            'name' => 'English 2'
        ],
        [
            'name' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->timetableService->getAllPaginatedTimetables($payload)->toArray();


    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English 2'),
        );
});

test('createTimetable()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $semester_class = SemesterClass::factory()->create();
    $payload = [
        'name' => 'English 2',
        'semester_class_id' => $semester_class->id,
        'is_active' => true,
    ];

    $response = $this->timetableService->createTimetable($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'semester_class_id' => $payload['semester_class_id'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'semester_class_id' => $payload['semester_class_id'],
        'is_active' => $payload['is_active'],
    ]);
});


test('updateTimetable()', function () {
    $timetable = Timetable::factory()->create([
        'name' => 'Test EN',
        'is_active' => false,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => 'Test EN2',
        'is_active' => true,
    ];

    $response = $this->timetableService->updateTimetable($timetable->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $timetable->id,
        'name' => $payload['name'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'is_active' => $payload['is_active'],
    ]);

    //update with id not exist
    $payload = [
        'name' => 'Test 3'
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->timetableService->updateTimetable(9999, $payload)->toArray();
});

test('createTimeSlotsViaPeriods()', function () {
    $period_group = PeriodGroup::factory()->create();
    $periods = Period::factory(2)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
    ))->create();

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
    ]);

    $this->assertDatabaseCount($this->timeslotTable, 0);

    $this->timetableService
        ->setTimetable($timetable)
        ->createTimeSlotsViaPeriods();

    $this->assertDatabaseCount($this->timeslotTable, 2);

    foreach ($periods as $period) {
        $this->assertDatabaseHas($this->timeslotTable, [
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
            'class_subject_id' => null,
            'placeholder' => null,
            'period_id' => $period->id,
        ]);
    }
});

test('checkAllTimeslotsIsExist()', function () {
    $timetable = Timetable::factory()->create();
    $timeslots = Timeslot::factory(2)->create([
        'timetable_id' => $timetable->id,
    ]);

    //Missing one timeslot
    $payload = [
        [
            'id' => $timeslots[0]->id,
        ]
    ];

    try {
        $this->timetableService
            ->setTimetable($timetable)
            ->setTimeslotsData($payload)
            ->checkAllTimeslotsIsExist();
    } catch (Exception $e) {
        expect($e->getMessage())->toBe('Incorrect timeslots data.');
    }

    //all timeslot exist
    $payload = [
        [
            'id' => $timeslots[0]->id,
        ],
        [
            'id' => $timeslots[1]->id,
        ],
    ];

    $this->timetableService
        ->setTimetable($timetable)
        ->setTimeslotsData($payload)
        ->checkAllTimeslotsIsExist();

});

test('updateTimeSlots()', function () {
    $employee = Employee::factory()->create();
    $class_subject = ClassSubject::factory()->create();
    $timetable = Timetable::factory()->create();
    $timeslots = Timeslot::factory(2)->create([
        'timetable_id' => $timetable->id,
        'class_subject_id' => null,
        'placeholder' => null,
        'attendance_from' => '22:00',
        'attendance_to' => '23:00',
    ]);

    $this->assertDatabaseCount($this->timeslotTable, 2);

    //Missing one timeslot
    $payload = [
        [
            'id' => $timeslots[0]->id,
            'class_subject_id' => $class_subject->id,
            'placeholder' => null,
            'attendance_from' => '10:00',
            'attendance_to' => '11:00',
            'default_init_status' => PeriodAttendanceStatus::ABSENT->value,
            'has_mark_deduction' => true,
            'teachers' => [
                [
                    'employee_id' => $employee->id,
                    'type' => TimeslotTeacherType::PRIMARY->value
                ]
            ]
        ],
        [
            'id' => $timeslots[1]->id,
            'class_subject_id' => null,
            'placeholder' => 'abc',
            'attendance_from' => '11:00',
            'attendance_to' => '12:00',
            'default_init_status' => PeriodAttendanceStatus::PRESENT->value,
            'has_mark_deduction' => false,
            'teachers' => [
                [
                    'employee_id' => $employee->id,
                    'type' => TimeslotTeacherType::PRIMARY->value
                ]
            ]
        ],
    ];

    $this->timetableService
        ->setTimetable($timetable)
        ->setTimeslotsData($payload)
        ->updateTimeSlots();

    $this->assertDatabaseCount($this->timeslotTable, 2);

    foreach ($payload as $timeslot) {
        $this->assertDatabaseHas($this->timeslotTable, [
            'id' => $timeslot['id'],
            'class_subject_id' => $timeslot['class_subject_id'],
            'placeholder' => $timeslot['placeholder'],
            'attendance_from' => $timeslot['attendance_from'],
            'attendance_to' => $timeslot['attendance_to'],
        ]);
    }
});

test('getFormatTimetable()', function () {
    $period_group = PeriodGroup::factory()->create();

    $periods = Period::factory(5)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'from_time' => '11:00',
            'to_time' => '12:00',
            'day' => Day::MONDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '10:00',
            'to_time' => '11:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'from_time' => '11:00',
            'to_time' => '12:00',
            'day' => Day::TUESDAY,
            'display_group' => 1
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'from_time' => '13:00',
            'to_time' => '14:00',
            'day' => Day::MONDAY,
            'display_group' => 2
        ]
    ))->create();

    PeriodLabel::factory(2)->state(new Sequence(
        [
            'period_group_id' => $period_group->id,
            'period' => 1,
            'name' => [
                'en' => 'First period',
                'zh' => '第一节',
            ]
        ],
        [
            'period_group_id' => $period_group->id,
            'period' => 2,
            'name' => [
                'en' => 'Second period',
                'zh' => '第二节',
            ]
        ],
    ))->create();

    $timetable = Timetable::factory()->create([
        'period_group_id' => $period_group->id,
    ]);

    $timeslots = Timeslot::factory(5)->state(new Sequence(
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[0]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[1]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::TUESDAY,
            'period_id' => $periods[2]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::TUESDAY,
            'period_id' => $periods[3]->id,
            'timetable_id' => $timetable->id,
        ],
        [
            'day' => Day::MONDAY,
            'period_id' => $periods[4]->id,
            'timetable_id' => $timetable->id,
        ],
    ))->create();

    $employee = Employee::factory()->create();

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslots[0]->id,
        'employee_id' => $employee->id,
        'type' => TimeslotTeacherType::PRIMARY
    ]);

    $response = $this->timetableService
        ->setTimetable($timetable)
        ->getFormatTimetable();

    expect($response)->toBe([
        'id' => $timetable->id,
        'name' => $timetable->name,
        'is_active' => $timetable->is_active,
        'semester_class_id' => $timetable->semester_class_id,
        'display_groups' => [
            [
                'id' => 1,
                'periods' => [
                    [
                        'period_label_name' => 'First period',
                        'period_label_name_translations' => [
                            'en' => 'First period',
                            'zh' => '第一节',
                        ],
                        'from_time' => '10:00:00',
                        'to_time' => '11:00:00',
                    ],
                    [
                        'period_label_name' => 'Second period',
                        'period_label_name_translations' => [
                            'en' => 'Second period',
                            'zh' => '第二节',
                        ],
                        'from_time' => '11:00:00',
                        'to_time' => '12:00:00',
                    ]
                ],
                'days' => [
                    'MONDAY' => [
                        '1' => [
                            "timeslot_id" => $timeslots[0]->id,
                            "timeslot_class_subject_id" => $timeslots[0]->class_subject_id,
                            "timeslot_placeholder" => $timeslots[0]->placeholder,
                            "timeslot_attendance_from" => $timeslots[0]->attendance_from,
                            "timeslot_attendance_to" => $timeslots[0]->attendance_to,
                            "timeslot_default_init_status" => $timeslots[0]->default_init_status,
                            "timeslot_has_mark_deduction" => $timeslots[0]->has_mark_deduction,
                            "timeslot_teachers" => [
                                [
                                    'employee_id' => $employee->id,
                                    'type' => TimeslotTeacherType::PRIMARY,
                                    'name' => $employee->name
                                ]
                            ],
                        ],
                        '2' => [
                            "timeslot_id" => $timeslots[1]->id,
                            "timeslot_class_subject_id" => $timeslots[1]->class_subject_id,
                            "timeslot_placeholder" => $timeslots[1]->placeholder,
                            "timeslot_attendance_from" => $timeslots[1]->attendance_from,
                            "timeslot_attendance_to" => $timeslots[1]->attendance_to,
                            "timeslot_default_init_status" => $timeslots[1]->default_init_status,
                            "timeslot_has_mark_deduction" => $timeslots[1]->has_mark_deduction,
                            "timeslot_teachers" => [],
                        ]
                    ],
                    'TUESDAY' => [
                        '1' => [
                            "timeslot_id" => $timeslots[2]->id,
                            "timeslot_class_subject_id" => $timeslots[2]->class_subject_id,
                            "timeslot_placeholder" => $timeslots[2]->placeholder,
                            "timeslot_attendance_from" => $timeslots[2]->attendance_from,
                            "timeslot_attendance_to" => $timeslots[2]->attendance_to,
                            "timeslot_default_init_status" => $timeslots[2]->default_init_status,
                            "timeslot_has_mark_deduction" => $timeslots[2]->has_mark_deduction,
                            "timeslot_teachers" => [],
                        ],
                        '2' => [
                            "timeslot_id" => $timeslots[3]->id,
                            "timeslot_class_subject_id" => $timeslots[3]->class_subject_id,
                            "timeslot_placeholder" => $timeslots[3]->placeholder,
                            "timeslot_attendance_from" => $timeslots[3]->attendance_from,
                            "timeslot_attendance_to" => $timeslots[3]->attendance_to,
                            "timeslot_default_init_status" => $timeslots[3]->default_init_status,
                            "timeslot_has_mark_deduction" => $timeslots[3]->has_mark_deduction,
                            "timeslot_teachers" => [],
                        ],
                    ],
                ]
            ],
            [
                'id' => 2,
                'periods' => [
                    [
                        'period_label_name' => 'First period',
                        'period_label_name_translations' => [
                            'en' => 'First period',
                            'zh' => '第一节',
                        ],
                        'from_time' => '13:00:00',
                        'to_time' => '14:00:00',
                    ]
                ],
                'days' => [
                    'MONDAY' => [
                        '1' => [
                            "timeslot_id" => $timeslots[4]->id,
                            "timeslot_class_subject_id" => $timeslots[4]->class_subject_id,
                            "timeslot_placeholder" => $timeslots[4]->placeholder,
                            "timeslot_attendance_from" => $timeslots[4]->attendance_from,
                            "timeslot_attendance_to" => $timeslots[4]->attendance_to,
                            "timeslot_default_init_status" => $timeslots[4]->default_init_status,
                            "timeslot_has_mark_deduction" => $timeslots[4]->has_mark_deduction,
                            "timeslot_teachers" => [],
                        ]
                    ]
                ]
            ]
        ]
    ]);
});

//
//test('deleteTimetable()', function () {
//    $timetable = Timetable::factory()->create();
//    $other_timetables = Timetable::factory(3)->create();
//
//    $this->assertDatabaseCount($this->table, 4);
//
//    //delete success
//    $this->timetableService->deleteTimetable($timetable->id);
//
//    $this->assertDatabaseCount($this->table, 3);
//    $this->assertDatabaseMissing($this->table, ['id' => $timetable->id]);
//
//    foreach ($other_timetables as $other_timetable) {
//        $this->assertDatabaseHas($this->table, ['id' => $other_timetable->id]);
//    }
//
//    //id not exist
//    $this->expectException(ModelNotFoundException::class);
//    $this->timetableService->deleteTimetable(9999);
//});
