<?php

use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Models\AttendancePeriodOverride;
use App\Models\AttendancePeriodOverrideLeaveApplication;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\Timetable\AttendancePeriodOverrideService;
use App\Services\Timetable\StudentTimetableService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
    $this->attendancePeriodOverrideService = app()->make(AttendancePeriodOverrideService::class);
    Employee::factory()->create(['employee_number' => Employee::SYSTEM_ID]);
});

test('create success', function () {

    $employee = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $student = Student::factory()->create();

    $data = $this->attendancePeriodOverrideService
        ->setUserable($student)
        ->setUpdatedBy($employee)
        ->setDate('2025-02-04')
        ->setAttendanceFromTime('08:45:00')
        ->setAttendanceToTime('10:45:59')
        ->create();

    expect($data)->toBeInstanceOf(AttendancePeriodOverride::class);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 1);
    $this->assertDatabaseHas(AttendancePeriodOverride::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-04',
        'attendance_from' => '08:45:00',
        'attendance_to' => '10:45:59',
        'updated_by_employee_id' => $employee->id,
    ]);

});

test('create error - existed', function () {

    $employee = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $student = Student::factory()->create();

    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-04',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
    ]);

    $this->expectExceptionCode('42002');
    $this->expectException(\Exception::class);
    $this->expectExceptionMessage('Attendance period for the student/employee & date already exists');

    $this->attendancePeriodOverrideService
        ->setUserable($student)
        ->setUpdatedBy($employee)
        ->setDate('2025-02-04')
        ->setAttendanceFromTime('08:45:00')
        ->setAttendanceToTime('10:45:59')
        ->create();

});


test('update', function () {

    $employee = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);
    $employee2 = Employee::factory()->create([
        'name->en' => 'Teacher B',
    ]);

    $student = Student::factory()->create();

    $override1 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'updated_by_employee_id' => $employee2->id,
    ]);
    $override2 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee2->id,
    ]);

    $data = $this->attendancePeriodOverrideService
        ->setUpdatedBy($employee)
        ->update($override1, [
            'period' => '2025-01-27',
            'attendance_from' => '10:00:00',
            'attendance_to' => '11:00:00',
        ]);

    expect($data)->toBeInstanceOf(AttendancePeriodOverride::class);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 2);

    // only this entry gets updated
    $this->assertDatabaseHas(AttendancePeriodOverride::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-27',
        'attendance_from' => '10:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);

    // another entry not updated (control)
    $this->assertDatabaseHas(AttendancePeriodOverride::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee2->id,
    ]);

});


test('delete', function () {

    $employee = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $student = Student::factory()->create();

    $override1 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);
    $override2 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 2);

    $this->attendancePeriodOverrideService
        ->delete($override1);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 1);

    $this->assertDatabaseMissing(AttendancePeriodOverride::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);

    // another entry not deleted (control)
    $this->assertDatabaseHas(AttendancePeriodOverride::class, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);

});

test('getAllPaginated', function () {

    $employee = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $student = Student::factory()->create();
    $student2 = Student::factory()->create();

    $override1 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);
    $override2 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'updated_by_employee_id' => $employee->id,
    ]);
    $override3 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:30:00',
        'updated_by_employee_id' => $employee->id,
    ]);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 3);

    $data = $this->attendancePeriodOverrideService
        ->getAllPaginated([
            'per_page' => 100,
        ]);

    expect($data)->toBeInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class)
        ->and($data->perPage())->toBe(100)
        ->and($data->currentPage())->toBe(1)
        ->and($data->total())->toBe(3)
        ->and($data->items())->toHaveCount(3)
        ->and($data->pluck('id'))->toContain($override1->id, $override2->id, $override3->id);

    foreach ($data->items() as $item) {
        expect($item)->toBeInstanceOf(AttendancePeriodOverride::class);
    }

});

test('batch delete', function () {

    $override1 = AttendancePeriodOverride::factory()->create();
    $override2 = AttendancePeriodOverride::factory()->create();
    $override3 = AttendancePeriodOverride::factory()->create();

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 3);

    $this->attendancePeriodOverrideService
        ->batchDelete([]);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 3);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 3);

    $this->attendancePeriodOverrideService
        ->batchDelete([$override1->id, $override2->id]);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 1);
});

test('updateIndividualOverrideByLeaveApplications (pending -> approved) - no existing override', function () {

    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);

    StudentTimetable::refreshViewTable(false);

    Carbon::setTestNow('2025-02-14 00:00:00');

    $leave_application = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // 2025-02-24 Monday
    // student 1, leave periods - 1,2
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2025-02-24', 'period' => $period_label1->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2025-02-24', 'period' => $period_label2->period]);

    $leave_application2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    $leave_application3 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    // 2025-02-24 Monday
    // student 2, leave periods - 2,3
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2->id, 'date' => '2025-02-24', 'period' => $period_label2->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-24', 'period' => $period_label3->period]);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 0);

    $employee = Employee::factory()->create();
    Sanctum::actingAs($employee->user);

    $periods = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student->id, $student2->id])
        ->getAttendancePeriods('2025-02-24', '2025-02-24');

    expect($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_to'])->toBe('10:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_to'])->toBe('10:00:00');

    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application, $leave_application2, $leave_application3]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 2);
    $student_attendance_period_override = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student->id)
        ->where('period', '2025-02-24')
        ->where('attendance_from', '09:00:00')
        ->where('attendance_to', '10:00:00')
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertNotNull($student_attendance_period_override);

    $student2_attendance_period_override = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student2->id)
        ->where('period', '2025-02-24')
        ->where('attendance_from', '08:00:00')
        ->where('attendance_to', '08:30:00')
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertNotNull($student2_attendance_period_override);

    $this->assertDatabaseCount(AttendancePeriodOverrideLeaveApplication::class, 3);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application->id,
        'attendance_period_override_id' => $student_attendance_period_override->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application2->id,
        'attendance_period_override_id' => $student2_attendance_period_override->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application3->id,
        'attendance_period_override_id' => $student2_attendance_period_override->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    $periods = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student->id, $student2->id])
        ->getAttendancePeriods('2025-02-24', '2025-02-24');

    expect($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_from'])->toBe('09:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_to'])->toBe('10:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['_default_attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['_default_attendance_to'])->toBe('10:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_to'])->toBe('08:30:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['_default_attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['_default_attendance_to'])->toBe('10:00:00');
});


test('updateIndividualOverrideByLeaveApplications (pending -> approved) - with existing override error', function () {

    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);

    Carbon::setTestNow('2025-02-14 00:00:00');

    $leave_application = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // 2025-02-17 Monday
    // student 1, leave periods - 1,2
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2025-02-17', 'period' => $period_label1->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2025-02-17', 'period' => $period_label2->period]);

    $leave_application2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    // 2025-02-17 Monday
    // student 2, leave periods - 2,3
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2->id, 'date' => '2025-02-17', 'period' => $period_label2->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2->id, 'date' => '2025-02-17', 'period' => $period_label3->period]);


    // existing override for student 1
    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-02-17',
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
    ]);

    StudentTimetable::refreshViewTable(false);

    $periods = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student->id, $student2->id])
        ->getAttendancePeriods('2025-02-17', '2025-02-17');

    expect($periods->count())->toBe(2);

    expect($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['attendance_to'])->toBe('10:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['_default_attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student->id)->first()['_default_attendance_to'])->toBe('10:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_from'])->toBe('09:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['attendance_to'])->toBe('09:30:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['_default_attendance_from'])->toBe('08:00:00')
        ->and($periods->where('userable_type', Student::class)->where('userable_id', $student2->id)->first()['_default_attendance_to'])->toBe('10:00:00');

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 1);

    $employee = Employee::factory()->create();
    Sanctum::actingAs($employee->user);

    $this->expectExceptionCode(50004);
    $this->expectExceptionMessage('Cannot approve leave application because has existing individual period override that was manually created on 2025-02-17 for student ' . $student2->student_number);

    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application, $leave_application2]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();
});

test('updateIndividualOverrideByLeaveApplications (approved -> pending)', function () {
    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);

    Carbon::setTestNow('2025-02-14 00:00:00');

    $leave_application_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    $leave_application2_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // 2025-02-10 Monday
    // student 1, leave periods - 1,2 (2 leaves)
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student->id, 'date' => '2025-02-10', 'period' => $period_label1->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2_student->id, 'date' => '2025-02-10', 'period' => $period_label2->period]);
    $attendance_period_override_student = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-10',
        'attendance_from' => $period3->from_time,
        'attendance_to' => $period3->to_time,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_student->id,
        'leave_application_id' => $leave_application_student->id,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_student->id,
        'leave_application_id' => $leave_application2_student->id,
    ]);

    $leave_application_student2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    // 2025-02-10 Monday
    // student 2, leave periods - 2,3
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period' => $period_label2->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period' => $period_label3->period]);
    $attendance_period_override_student2 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-02-10',
        'attendance_from' => $period1->from_time,
        'attendance_to' => $period1->to_time,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_student2->id,
        'leave_application_id' => $leave_application_student2->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 2);
    $this->assertDatabaseCount(AttendancePeriodOverrideLeaveApplication::class, 3);

    $employee = Employee::factory()->create();
    Sanctum::actingAs($employee->user);

    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application2_student, $leave_application_student2]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::PENDING->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    $this->assertDatabaseCount(AttendancePeriodOverride::class, 1);
    $this->assertDatabaseCount(AttendancePeriodOverrideLeaveApplication::class, 1);
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-10',
        'attendance_from' => $period2->from_time, // updated
        'attendance_to' => $period3->to_time,
        'updated_by_employee_id' => $employee->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);
});

test('updateIndividualOverrideByLeaveApplications left empty timeslot', function () {
    $student = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '09:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
        'has_mark_deduction' => true,
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
        'has_mark_deduction' => true,
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '09:30',
        'has_mark_deduction' => false,
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);

    $timeslot_override = TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-02-17',
        'period' => 2,
        'placeholder' => 'empty',
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => false,
        'is_empty' => true,
    ]);

    $leave_application_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student->id, 'date' => '2025-02-17', 'period' => $period1->period]);

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    StudentTimetable::refreshViewTable(false);

    Carbon::setTestNow('2025-02-14 00:00:00');
    $employee = Employee::factory()->create();
    Sanctum::actingAs($employee->user);

    expect(AttendancePeriodOverride::count())->toBe(0);

    $status = true;
    // shouldn't throw error
    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application_student]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    expect(AttendancePeriodOverride::count())->toBe(0); // empty timeslot, no individual override was created

    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application_student]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::PENDING->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    expect(AttendancePeriodOverride::count())->toBe(0);
    expect($status)->toBeTrue();
});

test('updateIndividualOverrideByLeaveApplications with timeslot has_mark_deduction = false', function () {
    $student = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '09:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
        'has_mark_deduction' => true,
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
        'has_mark_deduction' => true,
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '09:30',
        'has_mark_deduction' => false,
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);

    $leave_application_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student->id, 'date' => '2025-02-17', 'period' => $period1->period]);

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    StudentTimetable::refreshViewTable(false);

    Carbon::setTestNow('2025-02-14 00:00:00');
    $employee = Employee::factory()->create();
    Sanctum::actingAs($employee->user);

    expect(AttendancePeriodOverride::count())->toBe(0);
    
    // period 1 (has_mark_deduction = true) - apply leave for period 1
    // period 2 (has_mark_deduction = true)
    // period 3 (has_mark_deduction = false)
    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application_student]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    expect(AttendancePeriodOverride::count())->toBe(1);
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-17',
        'attendance_from' => $period2->from_time, // period 2 instead of period 3
        'attendance_to' => $period2->to_time, // period 2 instead of period 3
        'updated_by_employee_id' => $employee->id,
    ]);

    $this->attendancePeriodOverrideService
        ->setLeaveApplications(collect([$leave_application_student]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::PENDING->value)
        ->setEmployee($employee)
        ->updateIndividualOverrideByLeaveApplications();

    expect(AttendancePeriodOverride::count())->toBe(0);
});
