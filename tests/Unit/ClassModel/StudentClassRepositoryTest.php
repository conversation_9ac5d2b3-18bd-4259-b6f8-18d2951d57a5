<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Repositories\StudentClassRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->studentClassRepository = resolve(StudentClassRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();


    // Init Data
    $this->course_uec = Course::factory()->uec()->create();
    $this->course_igcse = Course::factory()->igcse()->create();

    $this->homeroom_teacher = Employee::factory()->create();

    $this->semester_year_setting_2024 = SemesterYearSetting::create(['year' => 2024]);

    $this->semester_setting_uec_2024_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'is_current_semester' => false
    ]);

    $this->semester_setting_uec_2024_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 2',
        'is_current_semester' => true
    ]);

    $this->semester_setting_igcse_2024_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'is_current_semester' => false
    ]);

    $this->semester_setting_igcse_2024_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 2',
        'is_current_semester' => true
    ]);

    $this->first_grade_uec = Grade::factory()->create(['name' => 'Junior 1']);
    $this->second_grade_uec = Grade::factory()->create(['name' => 'Junior 2']);

    $this->first_grade_igcse = Grade::factory()->create(['name' => 'Year 1']);
    $this->second_grade_igcse = Grade::factory()->create(['name' => 'Year 2']);

    $this->first_class_uec = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11班',
        'grade_id' => $this->first_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $this->second_class_uec = ClassModel::factory()->create([
        'name->en' => 'J211',
        'name->zh' => '初二11班',
        'grade_id' => $this->second_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $this->first_class_igcse = ClassModel::factory()->create([
        'name->en' => 'Y111',
        'grade_id' => $this->first_grade_igcse->id,
        'type' => ClassType::PRIMARY
    ]);

    $this->second_class_igcse = ClassModel::factory()->create([
        'name->en' => 'Y211',
        'grade_id' => $this->second_grade_igcse->id,
        'type' => ClassType::PRIMARY
    ]);

    $this->uec_year_1_student_1 = Student::factory()->create();
    $this->uec_year_1_student_2 = Student::factory()->create();
    $this->uec_year_2_student_1 = Student::factory()->create();
    $this->uec_year_2_student_2 = Student::factory()->create();

    $this->igcse_year_1_student_1 = Student::factory()->create();
    $this->igcse_year_1_student_2 = Student::factory()->create();
    $this->igcse_year_2_student_1 = Student::factory()->create();
    $this->igcse_year_2_student_2 = Student::factory()->create();


    $this->semester_class_first_uec_class_first_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'class_id' => $this->first_class_uec->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->semester_class_second_uec_class_first_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'class_id' => $this->second_class_uec->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->semester_class_first_uec_class_second_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_2->id,
        'class_id' => $this->first_class_uec->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->semester_class_first_igcse_class_first_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'class_id' => $this->first_class_igcse->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->semester_class_second_igcse_class_first_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'class_id' => $this->second_class_igcse->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->semester_class_second_igcse_class_second_sem = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_2->id,
        'class_id' => $this->first_class_igcse->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    // Assign UEC Student
    $this->uec_sem1_class1_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_first_uec_class_first_sem->id,
        'class_type' => $this->first_class_uec->type,
        'student_id' => $this->uec_year_1_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->uec_sem1_class1_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_first_uec_class_first_sem->id,
        'class_type' => $this->first_class_uec->type,
        'student_id' => $this->uec_year_1_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->uec_sem1_class2_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_second_uec_class_first_sem->id,
        'class_type' => $this->second_class_uec->type,
        'student_id' => $this->uec_year_2_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->uec_sem1_class2_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_second_uec_class_first_sem->id,
        'class_type' => $this->second_class_uec->type,
        'student_id' => $this->uec_year_2_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->uec_sem2_class1_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_2->id,
        'semester_class_id' => $this->semester_class_first_uec_class_second_sem->id,
        'class_type' => $this->first_class_uec->type,
        'student_id' => $this->uec_year_1_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->uec_sem2_class1_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_2->id,
        'semester_class_id' => $this->semester_class_first_uec_class_second_sem->id,
        'class_type' => $this->first_class_uec->type,
        'student_id' => $this->uec_year_1_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    // Assign IGCSE Student
    $this->igcse_sem1_class1_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_first_igcse_class_first_sem->id,
        'class_type' => $this->first_class_igcse->type,
        'student_id' => $this->igcse_year_1_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->igcse_sem1_class1_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_first_igcse_class_first_sem->id,
        'class_type' => $this->first_class_igcse->type,
        'student_id' => $this->igcse_year_1_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->igcse_sem1_class2_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_second_igcse_class_first_sem->id,
        'class_type' => $this->second_class_igcse->type,
        'student_id' => $this->igcse_year_2_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->igcse_sem1_class2_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id,
        'semester_class_id' => $this->semester_class_second_igcse_class_first_sem->id,
        'class_type' => $this->second_class_igcse->type,
        'student_id' => $this->igcse_year_2_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->igcse_sem2_class1_student1 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_2->id,
        'semester_class_id' => $this->semester_class_first_igcse_class_first_sem->id,
        'class_type' => $this->first_class_igcse->type,
        'student_id' => $this->igcse_year_1_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    $this->igcse_sem2_class1_student2 = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_2->id,
        'semester_class_id' => $this->semester_class_first_igcse_class_first_sem->id,
        'class_type' => $this->first_class_igcse->type,
        'student_id' => $this->igcse_year_1_student_2->id,
        'seat_no' => 2,
        'class_enter_date' => Carbon::now()->addWeek()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);
});

test('bulkUpdate()', function () {
    $this->studentClassRepository->bulkUpdate([$this->uec_sem1_class1_student1->id, $this->uec_sem2_class1_student2->id], ['is_active' => false]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $this->uec_sem1_class1_student1->id,
        'is_active' => false
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $this->uec_sem2_class1_student2->id,
        'is_active' => false
    ]);
});

test('getModelClass()', function () {
    $response = $this->studentClassRepository->getModelClass();

    expect($response)->toEqual(StudentClass::class);
});

test('getAll()', function () {
    $response = $this->studentClassRepository->getAll()->toArray();

    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class1_student2->toArray(),
        $this->uec_sem1_class2_student1->toArray(),
        $this->uec_sem1_class2_student2->toArray(),
        $this->uec_sem2_class1_student1->toArray(),
        $this->uec_sem2_class1_student2->toArray(),
        $this->igcse_sem1_class1_student1->toArray(),
        $this->igcse_sem1_class1_student2->toArray(),
        $this->igcse_sem1_class2_student1->toArray(),
        $this->igcse_sem1_class2_student2->toArray(),
        $this->igcse_sem2_class1_student1->toArray(),
        $this->igcse_sem2_class1_student2->toArray(),
    ]);

    // Filter by semester_setting_id
    $response = $this->studentClassRepository->getAll([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class1_student2->toArray(),
        $this->uec_sem1_class2_student1->toArray(),
        $this->uec_sem1_class2_student2->toArray(),
    ]);

    // Filter by semester_class_id
    $response = $this->studentClassRepository->getAll([
        'semester_class_id' => $this->semester_class_first_uec_class_first_sem->id
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class1_student2->toArray(),
    ]);

    $english_class = ClassModel::factory()->create([
        'name->en' => 'EA',
        'type' => ClassType::ENGLISH
    ]);

    $english_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'class_id' => $english_class->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true
    ]);

    $english_student_class = StudentClass::create([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id,
        'semester_class_id' => $english_semester_class->id,
        'class_type' => $english_class->type,
        'student_id' => $this->uec_year_1_student_1->id,
        'seat_no' => 1,
        'class_enter_date' => Carbon::now()->toDateString(),
        'class_leave_date' => null,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
    ]);

    // Filter by class_type
    $response = $this->studentClassRepository->getAll([
        'class_type' => $this->first_class_uec->type
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class1_student2->toArray(),
        $this->uec_sem1_class2_student1->toArray(),
        $this->uec_sem1_class2_student2->toArray(),
        $this->uec_sem2_class1_student1->toArray(),
        $this->uec_sem2_class1_student2->toArray(),
        $this->igcse_sem1_class1_student1->toArray(),
        $this->igcse_sem1_class1_student2->toArray(),
        $this->igcse_sem1_class2_student1->toArray(),
        $this->igcse_sem1_class2_student2->toArray(),
        $this->igcse_sem2_class1_student1->toArray(),
        $this->igcse_sem2_class1_student2->toArray(),
    ]);

    $response = $this->studentClassRepository->getAll([
        'class_type' => $english_class->type
    ])->toArray();
    expect($response)->toEqual([
        $english_student_class->toArray()
    ]);

    // Filter by student_id
    $response = $this->studentClassRepository->getAll([
        'student_id' => $this->uec_year_1_student_1->id,
        'order_by' => 'id'
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem2_class1_student1->toArray(),
        $english_student_class->toArray(),
    ]);

    // Filter by multiple student_id
    $response = $this->studentClassRepository->getAll([
        'student_id' => [
            $this->uec_year_1_student_1->id,
            $this->uec_year_2_student_1->id
        ]
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class2_student1->toArray(),
        $this->uec_sem2_class1_student1->toArray(),
        $english_student_class->toArray(),
    ]);

    // Filter by class_enter_date
    $response = $this->studentClassRepository->getAll([
        'class_enter_date' => Carbon::now()->toDateString()
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
        $this->uec_sem1_class1_student2->toArray(),
        $this->uec_sem1_class2_student1->toArray(),
        $this->uec_sem1_class2_student2->toArray(),
        $this->uec_sem2_class1_student1->toArray(),
        $this->uec_sem2_class1_student2->toArray(),
        $english_student_class->toArray(),
    ]);

    // Filter by is_active
    $this->uec_sem1_class1_student1->update(['is_active' => false]);
    $response = $this->studentClassRepository->getAll([
        'is_active' => false
    ])->toArray();
    expect($response)->toEqual([
        $this->uec_sem1_class1_student1->toArray(),
    ]);
});

test('getAll(), get students for seats assignment purpose', function () {
    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Koko',
        ],
        [
            'name->en' => 'Charlie',
        ],
        [
            'name->en' => 'David', // this student is deleted so will be excluded for seat settings
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'K100',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'K200',
            'type' => ClassType::PRIMARY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
            'homeroom_teacher_id' => $this->homeroom_teacher->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
            'homeroom_teacher_id' => $this->homeroom_teacher->id,
            'is_active' => true,
        ],
    ))->create();

    /**
     *
     * Jon active in $semester_classes[0] , seat_no = 1
     * Jack active in $semester_classes[0], seat_no = null
     * Koko inactive in $semester_classes[0], seat_no = 2   -- still assigned seat
     * Charlie inactive in $semester_classes[0], seat_no = null     -- no seat
     *
     *
     * Koko active in $semester_classes[1], seat_no = 1
     * Charlie active in $semester_classes[1], seat_no = 2
     * Jon inactive in $semester_classes[1], seat_no = null         -- no seat
     *
     * David (STUDENT) in $semester_classes[1] is deleted, exluded
     *
     */
    $student_classes = StudentClass::factory(8)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jack seat_no = null
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 2,
            'is_active' => false, // Koko inactive but still assigned seat, expect to be included
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => false, // Charlie inactive but seat_no = null, expect to be excluded
        ],


        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 1,
            'is_active' => true, // Koko
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Charlie
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => false, // Jon old record but no seat - expected to be excluded
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[4]->id,
            'seat_no' => 30,
            'is_active' => false, // David record, STUDENT is deleted - expected to be excluded
        ],
    ))->create();

    // delete David
    $students[4]->delete();

    /**
     * Filter by semester_classes[0]->id,
     * expect to get all active student_class + inactive students with seat
     */
    $response = $this->studentClassRepository->getAll([
        'semester_class_id' => $semester_classes[0]->id,
        'for_seats' => true,
    ])->toArray();

    expect($response)->toEqual([
        $student_classes[0]->toArray(), // Jon active and have seat
        $student_classes[2]->toArray(), // Koko inactive and have seat
        $student_classes[1]->toArray(), // Jack active but no seat
    ]);


    /**
     * Filter by semester_classes[1]->id,
     * expect to get all active student_class with or without seat
     */
    $response = $this->studentClassRepository->getAll([
        'semester_class_id' => $semester_classes[1]->id,
        'for_seats' => true,
    ])->toArray();

    expect($response)->toEqual([
        $student_classes[4]->toArray(), // Koko active and have seat
        $student_classes[5]->toArray(), // Charlie active but no seat
    ]);
});

test('getAllPaginated()', function () {
    // Filter by class_id = $first_student_class->class_id
    $response = $this->studentClassRepository->getAllPaginated([
        'semester_setting_id' => $this->semester_setting_uec_2024_sem_1->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class1_student2->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class2_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class2_student2->toArray()),
    );

    $response = $this->studentClassRepository->getAllPaginated([
        'semester_setting_id' => $this->semester_setting_igcse_2024_sem_1->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->igcse_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class1_student2->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class2_student1->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class2_student2->toArray()),
    );

    // Filter by class_id = $first_student_class->class_id
    $response = $this->studentClassRepository->getAllPaginated([
        'semester_class_id' => $this->semester_class_first_uec_class_first_sem->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class1_student2->toArray()),
    );

    $response = $this->studentClassRepository->getAllPaginated([
        'semester_class_id' => $this->semester_class_second_uec_class_first_sem->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class2_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class2_student2->toArray()),
    );

    // Filter by student_id
    $response = $this->studentClassRepository->getAllPaginated([
        'student_id' => $this->uec_year_1_student_1->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem2_class1_student1->toArray()),
    );

    $response = $this->studentClassRepository->getAllPaginated([
        'student_id' => $this->uec_year_2_student_1->id
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class2_student1->toArray()),
    );

    // Filter by class_type
    $response = $this->studentClassRepository->getAllPaginated([
        'class_type' => $this->first_class_uec->type
    ])->toArray();
    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($this->uec_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class1_student2->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class2_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem1_class2_student2->toArray()),
        fn($item) => $item->toEqual($this->uec_sem2_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->uec_sem2_class1_student2->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class1_student2->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class2_student1->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem1_class2_student2->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem2_class1_student1->toArray()),
        fn($item) => $item->toEqual($this->igcse_sem2_class1_student2->toArray()),
    );

    $response = $this->studentClassRepository->getAllPaginated([
        'class_type' => 'ENGLISH'
    ])->toArray();
    expect($response['data'])->toBeEmpty();
});

test('bulkUpdateStudentClassSeats()', function () {
    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Koko',
        ],
        [
            'name->en' => 'Charlie',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $class = ClassModel::factory()->create([
        'name->en' => 'K100',
        'type' => ClassType::PRIMARY,
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'homeroom_teacher_id' => $this->homeroom_teacher->id,
        'is_active' => true,
    ]);

    //  active student, rearrange all seats
    $student_classes = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jack
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 2,
            'is_active' => true, // Koko
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Charlie
        ],
    ))->create();

    /**
     *
     *   seat_no desc based on student_class->id
     *
     */
    $payload = [
        [
            'id' => $student_classes[0]->id,
            'seat_no' => 4,
        ],
        [
            'id' => $student_classes[1]->id,
            'seat_no' => 3,
        ],
        [
            'id' => $student_classes[2]->id,
            'seat_no' => 2,
        ],
        [
            'id' => $student_classes[3]->id,
            'seat_no' => 1,
        ],
    ];

    $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['id'],
        'seat_no' => 4,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['id'],
        'seat_no' => 3,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[2]['id'],
        'seat_no' => 2,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[3]['id'],
        'seat_no' => 1,
    ]);


    /**
     *
     *   seat_no randomly
     *
     */
    $payload = [
        [
            'id' => $student_classes[0]->id,
            'seat_no' => 2,
        ],
        [
            'id' => $student_classes[1]->id,
            'seat_no' => 1,
        ],
        [
            'id' => $student_classes[2]->id,
            'seat_no' => 3,
        ],
        [
            'id' => $student_classes[3]->id,
            'seat_no' => 4,
        ],
    ];

    $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['id'],
        'seat_no' => 2,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['id'],
        'seat_no' => 1,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[2]['id'],
        'seat_no' => 3,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[3]['id'],
        'seat_no' => 4,
    ]);


    /**
     *
     *   assign 2 seat,  unassign 2 seat
     *
     */
    $payload = [
        [
            'id' => $student_classes[0]->id,
            'seat_no' => 1,
        ],
        [
            'id' => $student_classes[1]->id,
            'seat_no' => 2,
        ],
        [
            'id' => $student_classes[2]->id,
            'seat_no' => null,
        ],
        [
            'id' => $student_classes[3]->id,
            'seat_no' => null,
        ],
    ];

    $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['id'],
        'seat_no' => 1,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['id'],
        'seat_no' => 2,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[2]['id'],
        'seat_no' => null,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[3]['id'],
        'seat_no' => null,
    ]);


    /**
     *
     *   unassign all seat
     *
     */
    $payload = [
        [
            'id' => $student_classes[0]->id,
            'seat_no' => null,
        ],
        [
            'id' => $student_classes[1]->id,
            'seat_no' => null,
        ],
        [
            'id' => $student_classes[2]->id,
            'seat_no' => null,
        ],
        [
            'id' => $student_classes[3]->id,
            'seat_no' => null,
        ],
    ];

    $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['id'],
        'seat_no' => null,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['id'],
        'seat_no' => null,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[2]['id'],
        'seat_no' => null,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[3]['id'],
        'seat_no' => null,
    ]);


    /**
     *
     *   reassign all seat
     *
     */
    $payload = [
        [
            'id' => $student_classes[0]->id,
            'seat_no' => 1,
        ],
        [
            'id' => $student_classes[1]->id,
            'seat_no' => 2,
        ],
        [
            'id' => $student_classes[2]->id,
            'seat_no' => 3,
        ],
        [
            'id' => $student_classes[3]->id,
            'seat_no' => 4,
        ],
    ];

    $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['id'],
        'seat_no' => 1,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['id'],
        'seat_no' => 2,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[2]['id'],
        'seat_no' => 3,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[3]['id'],
        'seat_no' => 4,
    ]);
});

test('getActiveStudentsBySemesterClassIds()', function () {
    $students = Student::factory(6)->state(new Sequence(
        [
            'name->en' => 'Serena', // Serena is in 2 class
        ],
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Paul',
        ],
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Cavill',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'K100',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'K200',
            'type' => ClassType::PRIMARY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
            'is_active' => true,
        ],
    ))->create();

    $student_classes = StudentClass::factory(7)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => null,
            'is_active' => true, // Jack
        ],

        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Paul
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[4]->id,
            'seat_no' => null,
            'is_active' => true, // David
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[5]->id,
            'seat_no' => null,
            'is_active' => true, // Cavill
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => false, // Jon old class, expect to be excluded because inactive
        ],
    ))->create();


    $payload = [
        $semester_classes[0]->id,
        $semester_classes[1]->id,
    ];

    $response = $this->studentClassRepository->getActiveStudentsBySemesterClassIds($payload);

    expect($response)->toHaveCount(7)
        ->and($response[0]->id)->toEqual($student_classes[0]->id)
        ->and(json_decode($response[0]->student_name)->en)->toEqual('Serena')
        ->and($response[1]->id)->toEqual($student_classes[1]->id)
        ->and(json_decode($response[1]->student_name)->en)->toEqual('Jon')
        ->and($response[2]->id)->toEqual($student_classes[2]->id)
        ->and(json_decode($response[2]->student_name)->en)->toEqual('Jack')
        ->and($response[3]->id)->toEqual($student_classes[3]->id)
        ->and(json_decode($response[3]->student_name)->en)->toEqual('Paul')
        ->and($response[4]->id)->toEqual($student_classes[4]->id)
        ->and(json_decode($response[4]->student_name)->en)->toEqual('David')
        ->and($response[5]->id)->toEqual($student_classes[5]->id)
        ->and(json_decode($response[5]->student_name)->en)->toEqual('Cavill')
        ->and($response[6]->id)->toEqual($student_classes[6]->id)
        ->and(json_decode($response[6]->student_name)->en)->toEqual('Serena');
});
