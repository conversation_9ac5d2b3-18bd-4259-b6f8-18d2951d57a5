<?php

use App\Enums\ExportType;
use App\Enums\GuardianType;
use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Enums\PushNotificationPlatform;
use App\Enums\TaxType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Card;
use App\Models\Config;
use App\Models\Currency;
use App\Models\GlAccount;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\Terminal;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\AdHocNotificationService;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\WalletTransactionService;
use Carbon\Carbon;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\ProductSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->walletTransactionService = app(WalletTransactionService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(WalletTransaction::class)->getTable();
    $this->walletTable = resolve(Wallet::class)->getTable();

    $this->seed([
        ProductSeeder::class,
        TaxSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
        PermissionSeeder::class,
    ]);

    Excel::fake();
});

test('getAllPaginatedWalletTransactions()', function (int $expected_count, mixed $filter_by, mixed $filter_value, array $expected_model) {

    $card_1 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $card_2 = Card::factory()->create([
        'card_number' => '***************'
    ]);

    $card_3 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $wallet_transactions = [
        'first' => WalletTransaction::factory()->create([
            'card_id' => $card_1->id,
            'reference_no' => '222',
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
        ]),
        'second' => WalletTransaction::factory()->create([
            'card_id' => $card_3->id,
            'reference_no' => '111',
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::FAILED->value,

        ]),
        'third' => WalletTransaction::factory()->create([
            'card_id' => $card_2->id,
            'reference_no' => '333',
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
        ]),
    ];

    $payload = [];

    if (isset($filter_by) && isset($filter_value)) {
        $payload = [
            $filter_by => $filter_value
        ];
    }

    $result = $this->walletTransactionService->getAllPaginatedWalletTransactions($payload)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toMatchArray($wallet_transactions[$value]->toArray());
    }
})->with([
    'no filter' => [3, null, null, ['first', 'second', 'third']],
    'filter by reference_no = 111' => [1, 'reference_no', '111', ['second']],
    'filter by card_number = **********' => [1, 'card_number', '**********', ['first']],
    'filter by type = DEPOSIT' => [1, 'type', WalletTransactionType::DEPOSIT->value, ['second']],
    'filter by status = PENDING' => [1, 'status', WalletTransactionStatus::PENDING->value, ['third']],
]);

test('getAllWalletTransactions()', function (int $expected_count, mixed $filter_by, mixed $filter_value, array $expected_model) {
    $card_1 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $card_2 = Card::factory()->create([
        'card_number' => '***************'
    ]);

    $card_3 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $wallet_transactions = [
        'first' => WalletTransaction::factory()->create([
            'card_id' => $card_1->id,
            'reference_no' => '222',
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
        ]),
        'second' => WalletTransaction::factory()->create([
            'card_id' => $card_3->id,
            'reference_no' => '111',
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::FAILED->value,

        ]),
        'third' => WalletTransaction::factory()->create([
            'card_id' => $card_2->id,
            'reference_no' => '333',
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
        ]),
    ];

    $payload = [];

    if (isset($filter_by) && isset($filter_value)) {
        $payload = [
            $filter_by => $filter_value
        ];
    }

    $result = $this->walletTransactionService->getAllWalletTransactions($payload)->toArray();

    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toMatchArray($wallet_transactions[$value]->toArray());
    }
})->with([
    'no filter' => [3, null, null, ['first', 'second', 'third']],
    'filter by reference_no = 111' => [1, 'reference_no', '111', ['second']],
    'filter by card_number = **********' => [1, 'card_number', '**********', ['first']],
    'filter by type = DEPOSIT' => [1, 'type', WalletTransactionType::DEPOSIT->value, ['second']],
    'filter by status = PENDING' => [1, 'status', WalletTransactionStatus::PENDING->value, ['third']],
]);

test('reportByAllWalletTransactions() export excel', function () {
    $card_1 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $card_2 = Card::factory()->create([
        'card_number' => '***************'
    ]);

    $card_3 = Card::factory()->create([
        'card_number' => '**********'
    ]);

    $student = Student::factory()->create();

    $wallet_transactions = [
        'first' => WalletTransaction::factory()->create([
            'card_id' => $card_1->id,
            'reference_no' => '222',
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'userable_type' => get_class($student),
            'userable_id' => $student->id,
        ]),
        'second' => WalletTransaction::factory()->create([
            'card_id' => $card_3->id,
            'reference_no' => '111',
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::FAILED->value,

        ]),
        'third' => WalletTransaction::factory()->create([
            'card_id' => $card_2->id,
            'reference_no' => '333',
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
        ]),
    ];

    $filters = [
        'includes' => ['userable']
    ];

    $response = $this->walletTransactionService->getAllWalletTransactions($filters);

    $report_data = ['data' => $response];
    $report_view_name = 'reports.wallet-transactions.by-all-wallet-transactions';
    $file_name = 'all-wallet-transactions';

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Reference No.', 'User Type', 'User Name', 'User No.', 'Card Number', 'Type', 'Status', 'Transaction Amount', 'Balance Before', 'Balance After', 'Description', 'Remarks', 'Created At'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data, $expected_headers) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            foreach ($report_data['data'] as $wallet_transaction) {
                $view->assertSee($wallet_transaction->reference_no);
                $view->assertSee($wallet_transaction->userable?->getUserTypeDescription());
                $view->assertSee($wallet_transaction->userable?->getUserName());
                $view->assertSee($wallet_transaction->userable?->getUserNumber());
                $view->assertSee($wallet_transaction->type->value);
                $view->assertSee($wallet_transaction->status->value);
                $view->assertSee($wallet_transaction->total_amount);
                $view->assertSee($wallet_transaction->balance_before);
                $view->assertSee($wallet_transaction->balance_after);
                $view->assertSee($wallet_transaction->description);
                $view->assertSee($wallet_transaction->remarks);
                $view->assertSee(Carbon::parse($wallet_transaction->created_at)->setTimezone(config('school.timezone')));
            }

            return true;
        }
    );
});

test('createWalletTransaction() without transfer', function () {
    $user = User::factory()->create();
    $currency = Currency::factory()->create();
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 10,
        'currency_id' => $currency->id,
    ]);

    $payload = [
        'type' => WalletTransactionType::DEPOSIT,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 10,
        'description' => "Deposit from payment gateway",
        'amount_before_tax' => 10,
        'amount_after_tax' => 10,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
    ];

    $response = $this->walletTransactionService
        ->setUser($user)
        ->setWalletById($wallet->id)
        ->createWalletTransaction($payload, $wallet, $wallet)
        ->toArray();

    $wallet_balance_after = $wallet->balance + $payload['total_amount'];

    expect($response)
        ->toMatchArray([
            'wallet_id' => $wallet->id,
            'type' => $payload['type']->value,
            'status' => $payload['status']->value,
            'total_amount' => $payload['total_amount'],
            'balance_before' => $wallet->balance,
            'balance_after' => $wallet_balance_after,
            'wallet_transactable_type' => get_class($wallet),
            'wallet_transactable_id' => $wallet->id,
            'description' => $payload['description'],
            'amount_before_tax' => $payload['amount_before_tax'],
            'amount_after_tax' => $payload['amount_after_tax'],
        ]);

    //check wallet transaction is created
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'card_id' => null,
        'reference_no' => null,
        'remark' => null,
        'wallet_id' => $wallet->id,
        'type' => $payload['type']->value,
        'status' => $payload['status']->value,
        'total_amount' => $payload['total_amount'],
        'balance_before' => $wallet->balance,
        'balance_after' => $wallet_balance_after,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'description' => $payload['description'],
        'amount_before_tax' => $payload['amount_before_tax'],
        'amount_after_tax' => $payload['amount_after_tax'],
    ]);
});

test('createWalletTransaction() with transfer', function () {
    $currency = Currency::factory()->create();

    $sender = User::factory()->create();
    $sender_wallet = Wallet::factory()->create([
        'user_id' => $sender->id,
        'balance' => 10,
        'currency_id' => $currency->id,
    ]);

    $receiver = User::factory()->create();
    $receiver_wallet = Wallet::factory()->create([
        'user_id' => $receiver->id,
        'balance' => 10,
        'currency_id' => $currency->id,
    ]);

    $payload = [
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => -10,
        'description' => 'ABC',
        'amount_before_tax' => -10,
        'amount_after_tax' => -10,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
    ];

    $response = $this->walletTransactionService
        ->setUser($sender)
        ->setWalletById($sender_wallet->id)
        ->createWalletTransaction($payload, $sender_wallet, $receiver_wallet)
        ->toArray();

    $sender_wallet_balance_after = $sender_wallet->balance + $payload['total_amount'];

    expect($response)
        ->toMatchArray([
            'wallet_id' => $sender_wallet->id,
            'type' => $payload['type']->value,
            'status' => $payload['status']->value,
            'total_amount' => $payload['total_amount'],
            'balance_before' => $sender_wallet->balance,
            'balance_after' => $sender_wallet_balance_after,
            'wallet_transactable_type' => get_class($receiver_wallet),
            'wallet_transactable_id' => $receiver_wallet->id,
            'description' => $payload['description'],
            'amount_before_tax' => $payload['amount_before_tax'],
            'amount_after_tax' => $payload['amount_after_tax'],
        ]);

    //check wallet transaction is created
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'card_id' => null,
        'reference_no' => null,
        'remark' => null,
        'wallet_id' => $sender_wallet->id,
        'type' => $payload['type']->value,
        'status' => $payload['status']->value,
        'total_amount' => $payload['total_amount'],
        'balance_before' => $sender_wallet->balance,
        'balance_after' => $sender_wallet_balance_after,
        'wallet_transactable_type' => get_class($receiver_wallet),
        'wallet_transactable_id' => $receiver_wallet->id,
        'description' => $payload['description'],
        'amount_before_tax' => $payload['amount_before_tax'],
        'amount_after_tax' => $payload['amount_after_tax'],
    ]);
});

test('chargeWalletTransaction', function () {
    //success
    $this->assertDatabaseCount($this->table, 0);

    $student = Student::factory()
        ->forUser()
        ->create();

    $user = $student->user;
    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    //with card number
    $payload = [
        'card_number' => $card->card_number,
        'amount_before_tax' => 10,
        'amount_after_tax' => 11,
        'tax_percent' => 0,
        'remark' => 'ABC',
        'description' => "Test Transaction",
        'currency' => $currency->code,
        'transaction_date' => '2024-07-01 00:00:00',
        'order_reference_no' => uniqid(),
    ];

    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($payload, $student) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($student) {
            return $value->id === $student->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with('Wallet Charge Transaction')->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with('Payment of MYR 11.00 for Wallet is successful')->once()->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $response = app()->make(WalletTransactionService::class)
        ->setWalletTransactable($wallet)
        ->chargeWalletTransaction($payload)
        ->toArray();


    $amount = $payload['amount_after_tax'] * -1;
    $wallet_balance_after = $wallet->balance + $amount;

    expect($response)
        ->toMatchArray([
            'card_id' => $card->id,
            'wallet_id' => $wallet->id,
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'total_amount' => $amount,
            'balance_before' => 20,
            'balance_after' => 9,
            'wallet_transactable_type' => get_class($wallet),
            'wallet_transactable_id' => $wallet->id,
            'description' => $payload['description'],
            'reference_no' => $payload['order_reference_no'],
            'remark' => $payload['remark'],
            'amount_before_tax' => $payload['amount_before_tax'] * -1,
            'amount_after_tax' => $payload['amount_after_tax'] * -1,
        ]);

    //Todo::add in tax once it working
    //check wallet transaction is created
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'card_id' => $card->id,
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => $amount,
        'balance_before' => 20,
        'balance_after' => 9,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'userable_type' => $card->userable_type,
        'userable_id' => $card->userable_id,
        'description' => $payload['description'],
        'reference_no' => $payload['order_reference_no'],
        'remark' => $payload['remark'],
        'amount_before_tax' => $payload['amount_before_tax'] * -1,
        'amount_after_tax' => $payload['amount_after_tax'] * -1,
    ]);

    //check balance should be deducted
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => $wallet_balance_after
    ]);

    //with user
    $payload = [
        'user' => $user,
        'amount_before_tax' => 5,
        'amount_after_tax' => 5,
        'tax_percent' => 0,
        'remark' => 'ABC',
        'description' => "Test Transaction",
        'currency' => $currency->code,
        'transaction_date' => '2024-07-01 00:00:00',
        'order_reference_no' => uniqid(),
    ];

    $response = app()->make(WalletTransactionService::class)
        ->setWalletTransactable($wallet)
        ->chargeWalletTransaction($payload)
        ->toArray();

    $amount = $payload['amount_after_tax'] * -1;
    $wallet_balance_after += $amount;

    expect($response)
        ->toMatchArray([
            'card_id' => null,
            'wallet_id' => $wallet->id,
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'total_amount' => $amount,
            'balance_before' => 9,
            'balance_after' => 4,
            'wallet_transactable_type' => get_class($wallet),
            'wallet_transactable_id' => $wallet->id,
            'description' => $payload['description'],
            'reference_no' => $payload['order_reference_no'],
            'remark' => $payload['remark'],
            'amount_before_tax' => $payload['amount_before_tax'] * -1,
            'amount_after_tax' => $payload['amount_after_tax'] * -1,
        ]);

    //check wallet transaction is created
    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'card_id' => null,
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => $amount,
        'balance_before' => 9,
        'balance_after' => 4,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'description' => $payload['description'],
        'reference_no' => $payload['order_reference_no'],
        'remark' => $payload['remark'],
        'amount_before_tax' => $payload['amount_before_tax'] * -1,
        'amount_after_tax' => $payload['amount_after_tax'] * -1,
    ]);

    //check balance should be deducted
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => $wallet_balance_after
    ]);

    //balance not enough
    $student2 = Student::factory()
        ->forUser()
        ->create();
    $user2 = $student2->user;
    Wallet::factory()->create([
        'user_id' => $user2->id,
        'balance' => 10,
        'currency_id' => $currency->id,
    ]);
    $card2 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id,
    ]);
    $payload = [
        'card_number' => $card2->card_number,
        'amount_before_tax' => 10,
        'amount_after_tax' => 11,
        'tax_percent' => 0,
        'remark' => 'ABC',
        'description' => "Test Transaction",
        'currency' => $currency->code,
        'transaction_date' => '2024-07-01 00:00:00',
        'order_reference_no' => uniqid(),
    ];

    $this->expectExceptionMessage('User has insufficient balance.');
    app()->make(WalletTransactionService::class)
        ->setWalletTransactable($wallet)
        ->chargeWalletTransaction($payload)
        ->toArray();
});

test('chargeWalletTransaction, from Terminal', function () {
    //success
    $this->assertDatabaseCount($this->table, 0);

    $student = Student::factory()
        ->forUser()
        ->create();

    $user = $student->user;
    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    $terminal = Terminal::factory()->create([
        'name' => 'Canteen Level 01 Cashier 1',
        'type' => \App\Enums\TerminalType::CANTEEN,
        'code' => 'CL0101',
    ]);

    $payload = [
        'card_number' => $card->card_number,
        'amount_before_tax' => 10,
        'amount_after_tax' => 11,
        'tax_percent' => 0,
        'remark' => 'ABC',
        'description' => "Test Transaction",
        'currency' => $currency->code,
        'transaction_date' => '2024-07-01 00:00:00',
        'order_reference_no' => "01-{$terminal->code}-20240015", // Terminal ref_no
    ];

    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($payload, $student) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($student) {
            return $value->id === $student->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with('Wallet Charge Transaction')->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with('Payment of MYR 11.00 for Terminal Canteen Level 01 Cashier 1 is successful')->once()->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $response = $this->walletTransactionService
        ->setWalletTransactableByQubeRefNo($payload['order_reference_no'])
        ->chargeWalletTransaction($payload)
        ->toArray();

    $amount = $payload['amount_after_tax'] * -1;

    expect($response)
        ->toMatchArray([
            'card_id' => $card->id,
            'wallet_id' => $wallet->id,
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'total_amount' => $amount,
            'balance_before' => 20,
            'balance_after' => 9,
            'wallet_transactable_type' => get_class($terminal), // Terminal
            'wallet_transactable_id' => $terminal->id, // Terminal
            'userable_type' => $card->userable_type,
            'userable_id' => $card->userable_id,
            'description' => $payload['description'],
            'reference_no' => $payload['order_reference_no'],
            'remark' => $payload['remark'],
            'amount_before_tax' => $payload['amount_before_tax'] * -1,
            'amount_after_tax' => $payload['amount_after_tax'] * -1,
        ]);

    //Todo::add in tax once it working
    //check wallet transaction is created
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'card_id' => $card->id,
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => $amount,
        'balance_before' => 20,
        'balance_after' => 9,
        'wallet_transactable_type' => get_class($terminal), // Terminal
        'wallet_transactable_id' => $terminal->id, // Terminal
        'userable_type' => $card->userable_type,
        'userable_id' => $card->userable_id,
        'description' => $payload['description'],
        'reference_no' => $payload['order_reference_no'],
        'remark' => $payload['remark'],
        'amount_before_tax' => $payload['amount_before_tax'] * -1,
        'amount_after_tax' => $payload['amount_after_tax'] * -1,
    ]);
});

test('refundWalletTransaction() is refund before', function () {
    $user = User::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 0,
    ]);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => -10,
        'amount_before_tax' => -10,
        'total_amount' => -10,
        'reference_no' => 'INV1000',
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => 10,
        'amount_before_tax' => 10,
        'total_amount' => 10,
        'reference_no' => 'INV1000-REFUND',
        'type' => WalletTransactionType::REFUND,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'wallet_transactable_type' => WalletTransaction::class,
        'wallet_transactable_id' => $wallet_transaction->id,
    ]);

    expect(function () use ($wallet_transaction) {
        $this->walletTransactionService
            ->setWalletTransactable($wallet_transaction)
            ->setWalletTransaction($wallet_transaction)
            ->validateCanManualRefund()
            ->refundWalletTransaction([
                'remark' => 'E-commerce Cancellation: ORD1000'
            ]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(1012);
    }, 'Transaction is not allowed to refund.');
});

test('refundWalletTransaction()', function () {
    $user = User::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 0,
    ]);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => -10,
        'amount_before_tax' => -10,
        'total_amount' => -10,
        'reference_no' => 'INV1000',
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->walletTransactionService
        ->setWalletTransactable($wallet_transaction)
        ->setWalletTransaction($wallet_transaction)
        ->validateCanManualRefund()
        ->refundWalletTransaction([
            'remark' => 'E-commerce Cancellation: ORD1000'
        ]);

    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'reference_no' => $wallet_transaction->reference_no . '-REFUND',
        'type' => WalletTransactionType::REFUND->value,
        'amount_after_tax' => 10,
        'amount_before_tax' => 10,
        'total_amount' => 10,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'remark' => 'E-commerce Cancellation: ORD1000',
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'wallet_transactable_type' => get_class($wallet_transaction),
        'wallet_transactable_id' => $wallet_transaction->id,
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => 10
    ]);
});

test('isAllowToRefund() type not TRANSACTION', function () {
    $user = User::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 0,
    ]);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => 10,
        'amount_before_tax' => 10,
        'total_amount' => 10,
        'reference_no' => 'INV1000',
        'type' => WalletTransactionType::DEPOSIT,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    expect(function () use ($wallet_transaction) {
        $this->walletTransactionService
            ->setWalletTransactable($wallet_transaction)
            ->setWalletTransaction($wallet_transaction)
            ->validateCanManualRefund();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(1012);
    }, __('system_error.1012'));
});

test('isAllowToRefund() is refund before', function () {
    $user = User::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 0,
    ]);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => -10,
        'amount_before_tax' => -10,
        'total_amount' => -10,
        'reference_no' => 'INV1000',
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => 10,
        'amount_before_tax' => 10,
        'total_amount' => 10,
        'reference_no' => 'INV1000-REFUND',
        'type' => WalletTransactionType::REFUND,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'wallet_transactable_type' => WalletTransaction::class,
        'wallet_transactable_id' => $wallet_transaction->id,
    ]);

    expect(function () use ($wallet_transaction) {
        $this->walletTransactionService
            ->setWalletTransactable($wallet_transaction)
            ->setWalletTransaction($wallet_transaction)
            ->validateCanManualRefund();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(1012);
    }, __('system_error.1012'));
});

test('isAllowToRefund() success', function () {
    $user = User::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 0,
    ]);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'amount_after_tax' => -10,
        'amount_before_tax' => -10,
        'total_amount' => -10,
        'reference_no' => 'INV1000',
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    $response = $this->walletTransactionService
        ->setWalletTransactable($wallet_transaction)
        ->setWalletTransaction($wallet_transaction)
        ->validateCanManualRefund();

    expect($response)->toBeInstanceOf(WalletTransactionService::class);
});

test('getWalletTransactionByReferenceNo()', function () {
    $reference_no = '123456';

    WalletTransaction::factory()->create([
        'reference_no' => $reference_no,
    ]);

    //Get by valid reference no
    $response = $this->walletTransactionService
        ->getWalletTransactionByReferenceNo($reference_no)
        ->toArray();

    expect($response['reference_no'])->toBe($reference_no);

    //Get by invalid reference no
    $this->expectExceptionMessage('Invalid Order Reference No.');
    $this->walletTransactionService->getWalletTransactionByReferenceNo(99999);
});

test('createWalletDepositTransaction()', function () {
    $this->assertDatabaseCount($this->table, 0);

    $user = User::factory()->create();
    $currency = Currency::factory()->create();
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $response = $this->walletTransactionService
        ->setUser($user)
        ->setWalletById($wallet->id)
        ->createWalletDepositTransaction(10, 'REF12345')
        ->toArray();

    $amount = 10;
    expect($response)
        ->toMatchArray([
            'wallet_id' => $wallet->id,
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::PENDING->value,
            'total_amount' => $amount,
            'balance_before' => 20,
            'balance_after' => 30,
            'wallet_transactable_type' => get_class($wallet),
            'wallet_transactable_id' => $wallet->id,
            'description' => "Deposit from payment gateway",
            'amount_before_tax' => $amount,
            'amount_after_tax' => $amount,
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'card_id' => null,
        'reference_no' => 'REF12345',
        'remark' => null,
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::DEPOSIT->value,
        'status' => WalletTransactionStatus::PENDING->value,
        'total_amount' => $amount,
        'balance_before' => $wallet->balance,
        'balance_after' => $amount + $wallet->balance,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'description' => "Deposit from payment gateway",
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
    ]);
});

test('updateWalletTransactionStatusByPaymentGateway()', closure: function () {
    $user = User::factory()->create();
    $currency = Currency::factory()->create();
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);
    $amount = 10;
    $wallet_balance_after = $wallet->balance + $amount;
    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $wallet->id,
        'status' => WalletTransactionStatus::PENDING->value,
        'total_amount' => $amount,
        'balance_before' => $wallet->balance,
        'balance_after' => $wallet->balance,
    ]);

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'transaction_loggable_id' => $wallet_transaction->id,
        'transaction_loggable_type' => get_class($wallet_transaction),
        'amount' => $amount
    ]);

    //Pending
    $payment_gateway_log->status = PaymentStatus::PENDING;
    $payment_gateway_log->save();

    $response = $this->walletTransactionService
        ->updateWalletTransactionStatusByPaymentGateway($payment_gateway_log, WalletTransactionStatus::PENDING)
        ->toArray();

    expect($response)
        ->toMatchArray([
            'id' => $wallet_transaction->id,
            'status' => WalletTransactionStatus::PENDING->value,
            'balance_before' => 20,
            'balance_after' => 30,
        ]);

    $this->assertDatabaseCount($this->table, 1);

    //status = pending, balance_after = wallet balance
    $this->assertDatabaseHas($this->table, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::PENDING->value,
        'balance_before' => 20,
        'balance_after' => 30,
    ]);

    //wallet balance not change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => $wallet->balance
    ]);

    //Failed
    $payment_gateway_log->status = PaymentStatus::FAILED;
    $payment_gateway_log->save();

    $response = $this->walletTransactionService
        ->updateWalletTransactionStatusByPaymentGateway($payment_gateway_log, WalletTransactionStatus::FAILED)
        ->toArray();

    expect($response)
        ->toMatchArray([
            'id' => $wallet_transaction->id,
            'status' => WalletTransactionStatus::FAILED->value,
            'balance_before' => 20,
            'balance_after' => 20,
        ]);

    $this->assertDatabaseCount($this->table, 1);

    //status = pending, balance_after = wallet balance
    $this->assertDatabaseHas($this->table, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
        'balance_before' => 20,
        'balance_after' => 20,
    ]);

    //wallet balance not change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => $wallet->balance
    ]);

    //Success
    $payment_gateway_log->status = PaymentStatus::SUCCESS;
    $payment_gateway_log->save();

    $payment_gateway_log->transactionLoggable()->update([
        'status' => WalletTransactionStatus::PENDING,
    ]);
    $payment_gateway_log->transactionLoggable->refresh();

    $response = $this->walletTransactionService
        ->updateWalletTransactionStatusByPaymentGateway($payment_gateway_log, WalletTransactionStatus::SUCCESS)
        ->toArray();

    expect($response)
        ->toMatchArray([
            'id' => $wallet_transaction->id,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'balance_before' => 20,
            'balance_after' => $wallet_balance_after,
        ]);

    //status = success, balance_after = wallet balance + amount
    $this->assertDatabaseHas($this->table, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'balance_before' => 20,
        'balance_after' => $wallet_balance_after
    ]);

    //wallet balance = wallet balance + amount
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $wallet->id,
        'balance' => $wallet_balance_after
    ]);
});

test('withdrawBalance() not enough balance', function () {
    $user = User::factory()->create();

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);


    $amount = -15.05;

    $payload = [
        'remark' => 'Test'
    ];

    expect(function () use ($wallet, $payload, $amount) {
        $this->walletTransactionService
            ->setWalletById($wallet->id)
            ->setUserableByWallet()
            ->setType(WalletTransactionType::WITHDRAW)
            ->setAmount($amount)
            ->setDescription('Balance Withdraw')
            ->setRemark($payload['remark'])
            ->walletAdjustment();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(1002);
    }, 'User has insufficient balance.');
});

test('walletAdjustment() negative amount', function () {
    $this->assertDatabaseCount($this->table, 0);

    $user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $student_user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_IOS
    ]);

    $student = Student::factory()->create([
        'user_id' => $student_user->id,
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'type' => GuardianType::FATHER,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id
    ]);

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);

    $amount = -10.05;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->walletTransactionService
        ->setWalletById($wallet->id)
        ->setUserableByWallet()
        ->setType(WalletTransactionType::ADJUSTMENT)
        ->setAmount($payload['amount'])
        ->setDescription('Balance Adjustment')
        ->setRemark($payload['remark'])
        ->walletAdjustment()
        ->getTransaction()
        ->toArray();

    expect($response)->toMatchArray([
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT->value,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => 4.95,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ])->and($response['reference_no'])->toContain(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::ADJUSTMENT));

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => 4.95,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'user_id' => $user->id,
        'balance' => 4.95
    ]);
});

test('walletAdjustment() positive amount', function () {
    $this->assertDatabaseCount($this->table, 0);

    $user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $student_user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_IOS
    ]);

    $student = Student::factory()->create([
        'user_id' => $student_user->id,
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'type' => GuardianType::FATHER,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id
    ]);

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);

    $amount = 10.05;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->walletTransactionService
        ->setWalletById($wallet->id)
        ->setUserableByWallet()
        ->setType(WalletTransactionType::ADJUSTMENT)
        ->setAmount($payload['amount'])
        ->setDescription('Balance Adjustment')
        ->setRemark($payload['remark'])
        ->walletAdjustment()
        ->getTransaction()
        ->toArray();

    expect($response)->toMatchArray([
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT->value,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => 25.05,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ])->and($response['reference_no'])->toContain(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::ADJUSTMENT));

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => 25.05,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'user_id' => $user->id,
        'balance' => 25.05
    ]);
});

test('sendWithdrawBalanceNotification()', function () {
    $user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $amount = 10;

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);

    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($guardian) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($guardian) {
            return $value->id === $guardian->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with('Wallet Balance Withdraw')->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with("RM10.00 from your wallet has been withdrawn successfully.")->once()->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $this->walletTransactionService
        ->setWalletById($wallet->id)
        ->setUserableByWallet()
        ->setAmount($amount)
        ->sendWithdrawBalanceNotification($amount);
});

test('sendAdjustmentBalanceNotification()', function () {
    $user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $amount = 10;

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);

    $this->mock(AdHocNotificationService::class, function (MockInterface $mock) use ($guardian) {
        $mock->shouldReceive('setUserable')->withArgs(function ($value) use ($guardian) {
            return $value->id === $guardian->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('setTitle')->with('Wallet Balance Adjustment')->once()->andReturnSelf();
        $mock->shouldReceive('setMessage')->with("RM10.05 from your wallet has been adjusted successfully.")->once()->andReturnSelf();
        $mock->shouldReceive('determineRecipients')->once()->andReturnSelf();
        $mock->shouldReceive('send')->once()->andReturnSelf();
    });

    $this->walletTransactionService
        ->setWalletById($wallet->id)
        ->setUserableByWallet()
        ->setAmount(10.05)
        ->sendAdjustmentBalanceNotification($amount);
});

test('setUserableByWallet()', function () {
    $user = User::factory()->create([
        'push_notification_token' => '123456',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $currency = Currency::factory()->create();

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 15
    ]);

    $userable = $this->walletTransactionService
        ->setWalletById($wallet->id)
        ->setUserableByWallet()
        ->getUserable();

    expect(get_class($userable))->toBe(get_class($guardian))
        ->and($userable->id)->toBe($guardian->id);
});

test('transferBalance()', function () {
    $this->assertDatabaseCount($this->table, 0);

    $currency = Currency::factory()->create();
    $sender_with_insufficient_balance_guardian = Guardian::factory()->create();
    $sender_with_insufficient_balance = $sender_with_insufficient_balance_guardian->user;
    $sender_with_insufficient_balance_wallet = Wallet::factory()->create([
        'user_id' => $sender_with_insufficient_balance->id,
        'balance' => 5,
        'currency_id' => $currency->id,
    ]);

    $sender_guardian = Guardian::factory()->create();
    $sender = $sender_guardian->user;
    $sender_wallet = Wallet::factory()->create([
        'user_id' => $sender->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $first_receiver_guardian = Guardian::factory()->create();
    $first_receiver = $first_receiver_guardian->user;
    $first_receiver_wallet = Wallet::factory()->create([
        'user_id' => $first_receiver->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $second_receiver_guardian = Guardian::factory()->create();
    $second_receiver = $second_receiver_guardian->user;
    $second_receiver_wallet = Wallet::factory()->create([
        'user_id' => $second_receiver->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $payload = [
        'userable_type' => Guardian::class,
        'userable_id' => $sender_with_insufficient_balance_guardian->id,
        'receivers' => [
            [
                'userable_type' => Guardian::class,
                'userable_id' => $first_receiver_guardian->id,
                'wallet_id' => $first_receiver_wallet->id,
                'amount' => 5,
                'description' => 'ABC'
            ],
            [
                'userable_type' => Guardian::class,
                'userable_id' => $second_receiver_guardian->id,
                'wallet_id' => $second_receiver_wallet->id,
                'amount' => 7,
                'description' => 'DEF'
            ]
        ]
    ];

    //Sender not enough balance
    $has_error = false;
    try {
        $this->walletTransactionService
            ->setUser($sender_with_insufficient_balance)
            ->setUserableByTypeAndId($payload['userable_type'], $payload['userable_id'])
            ->setWalletById($sender_with_insufficient_balance_wallet->id)
            ->transferBalance($payload);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('User has insufficient balance.');
        $has_error = true;
    }
    expect($has_error)->toBeTrue();

    $payload['userable_id'] = $sender_guardian->id;
    //Sender enough balance
    $response = $this->walletTransactionService
        ->setUser($sender)
        ->setUserableByTypeAndId($payload['userable_type'], $payload['userable_id'])
        ->setWalletById($sender_wallet->id)
        ->transferBalance($payload);

    // Receiver 5 + 7
    $sender_amount = 12 * -1;

    expect($response['sender_wallet_transaction'])
        ->toMatchArray([
            'name' => $sender_guardian->name,
            'wallet_id' => $sender_wallet->id,
            'balance_before' => $sender_wallet->balance,
            'balance_after' => $sender_amount + $sender_wallet->balance,
            'total_amount' => $sender_amount
        ])
        ->and($response['sender_wallet_transaction']['currency']->toArray())->toEqual($currency->toArray())
        ->and($response['receiver_wallet_transactions'])
        ->sequence(
            fn($item) => $item->toMatchArray([
                'name' => $first_receiver_guardian->name,
                'wallet_id' => $first_receiver_wallet->id,
                'balance_before' => $first_receiver_wallet->balance,
                'balance_after' => 5 + $first_receiver_wallet->balance,
                'total_amount' => 5,
            ])->toHaveKey('currency'),
            fn($item) => $item->toMatchArray([
                'name' => $second_receiver_guardian->name,
                'wallet_id' => $second_receiver_wallet->id,
                'balance_before' => $second_receiver_wallet->balance,
                'balance_after' => 7 + $second_receiver_wallet->balance,
                'total_amount' => 7,
            ])->toHaveKey('currency')
        );

    // Transferring to 2 person, 2 records from sender and 2 records from receiver
    $this->assertDatabaseCount($this->table, 4);

    //Sender wallet transaction
    $this->assertDatabaseHas($this->table, [
        'reference_no' => 'TRF' . now()->year . '0000001',
        'wallet_id' => $sender_wallet->id,
        'type' => WalletTransactionType::TRANSFER->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => -5,
        'balance_before' => 20,
        'balance_after' => 15,
        'wallet_transactable_type' => get_class($first_receiver_wallet),
        'wallet_transactable_id' => $first_receiver_wallet->id,
        'description' => 'ABC',
        'amount_before_tax' => -5,
        'amount_after_tax' => -5,
    ]);

    //Sender wallet transaction
    $this->assertDatabaseHas($this->table, [
        'reference_no' => 'TRF' . now()->year . '0000003',
        'wallet_id' => $sender_wallet->id,
        'type' => WalletTransactionType::TRANSFER->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => -7,
        'balance_before' => 15,
        'balance_after' => 8,
        'wallet_transactable_type' => get_class($second_receiver_wallet),
        'wallet_transactable_id' => $second_receiver_wallet->id,
        'description' => 'DEF',
        'amount_before_tax' => -7,
        'amount_after_tax' => -7,
    ]);

    //Sender wallet balance update
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $sender_wallet->id,
        'balance' => $sender_amount + $sender_wallet->balance
    ]);

    // First receiver wallet transaction
    $this->assertDatabaseHas($this->table, [
        'reference_no' => 'TRF' . now()->year . '0000002',
        'wallet_id' => $first_receiver_wallet->id,
        'type' => WalletTransactionType::TRANSFER->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => 5,
        'balance_before' => $first_receiver_wallet->balance,
        'balance_after' => 25,
        'wallet_transactable_type' => get_class($sender_wallet),
        'wallet_transactable_id' => $sender_wallet->id,
        'description' => 'ABC',
        'amount_before_tax' => 5,
        'amount_after_tax' => 5,
    ]);

    // First receiver wallet balance update
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $first_receiver_wallet->id,
        'balance' => 25
    ]);

    // Second receiver wallet transaction
    $this->assertDatabaseHas($this->table, [
        'reference_no' => 'TRF' . now()->year . '0000004',
        'wallet_id' => $second_receiver_wallet->id,
        'type' => WalletTransactionType::TRANSFER->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'total_amount' => 7,
        'balance_before' => $second_receiver_wallet->balance,
        'balance_after' => 27,
        'wallet_transactable_type' => get_class($sender_wallet),
        'wallet_transactable_id' => $sender_wallet->id,
        'description' => 'DEF',
        'amount_before_tax' => 7,
        'amount_after_tax' => 7,
    ]);

    // Second receiver wallet balance update
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $second_receiver_wallet->id,
        'balance' => 27
    ]);
});

test('createWalletTransferTransaction()', function () {
    $this->assertDatabaseCount($this->table, 0);

    $currency = Currency::factory()->create();

    $sender = User::factory()->create();
    $sender_wallet = Wallet::factory()->create([
        'user_id' => $sender->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $receiver = User::factory()->create();
    $receiver_wallet = Wallet::factory()->create([
        'user_id' => $receiver->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $payload = [
        'reference_no' => 'TRF20250000003',
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => -10, // Sender will have negative amount
        'description' => 'ABC',
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'remark' => 'To Tan Ah Meng'
    ];

    Carbon::setTestNow('2025-05-01 00:00:00');

    $response = $this->walletTransactionService
        ->setUser($sender)
        ->setWalletById($sender_wallet->id)
        ->createWalletTransferTransaction($payload, $sender_wallet, $receiver_wallet)
        ->toArray();

    $sender_amount = $payload['total_amount'];
    expect($response)
        ->toMatchArray([
            'wallet_id' => $sender_wallet->id,
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
            'total_amount' => $sender_amount,
            'balance_before' => $sender_wallet->balance,
            'balance_after' => $sender_amount + $sender_wallet->balance,
            'wallet_transactable_type' => get_class($receiver_wallet),
            'wallet_transactable_id' => $receiver_wallet->id,
            'description' => $payload['description'],
            'amount_before_tax' => $sender_amount,
            'amount_after_tax' => $sender_amount,
        ]);
    $this->assertDatabaseCount($this->table, 1);

    //Sender wallet transaction
    $this->assertDatabaseHas($this->table, [
        'wallet_id' => $sender_wallet->id,
        'reference_no' => 'TRF20250000003',
        'type' => WalletTransactionType::TRANSFER->value,
        'status' => WalletTransactionStatus::PENDING->value,
        'total_amount' => $sender_amount,
        'balance_before' => $sender_wallet->balance,
        'balance_after' => $sender_amount + $sender_wallet->balance,
        'wallet_transactable_type' => get_class($receiver_wallet),
        'wallet_transactable_id' => $receiver_wallet->id,
        'description' => $payload['description'],
        'amount_before_tax' => $sender_amount,
        'amount_after_tax' => $sender_amount,
    ]);
});

test('depositRequest() payment gateway success', function () {
    $currency = Currency::factory()->create();
    $student = Student::factory()
        ->forUser()
        ->create();
    $user = $student->user;
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    //amount = 15
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 15.00,
        'wallet_id' => $wallet->id,
        'payment_type' => PaymentMethod::CODE_CREDIT_CARD,
        'return_url' => 'valid return url'
    ];

    $response = $this->walletTransactionService
        ->setUser($user)
        ->setUserableByTypeAndId($payload['userable_type'], $payload['userable_id'])
        ->setWalletById($payload['wallet_id'])
        ->depositRequest($payload);

    expect($response)->toBeInstanceOf(PaymentGatewayLog::class)
        ->toMatchArray([
            'requested_by_id' => $user->id,
            'type' => 'WALLET_DEPOSIT',
            'provider' => 'PAYEX',
            'transaction_loggable_type' => WalletTransaction::class,
            'currency_id' => $currency->id,
            'currency_code' => $currency->code,
            'currency_name' => $currency->name,
            'amount' => 15.00,
            'status' => WalletTransactionStatus::PENDING->value,
            'description' => 'Deposit Wallet',
            'payment_url' => 'VALID_PAYMENT_URL',
        ]);

    $this->assertDatabaseCount('wallet_transactions', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 1);

    $billing_document = BillingDocument::first();
    $wallet_transaction = WalletTransaction::first();
    $userable = $this->walletTransactionService->getUserable();

    $after_balance = 20 + $payload['amount'];
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::DEPOSIT,
        'status' => WalletTransactionStatus::PENDING,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'total_amount' => $payload['amount'],
        'amount_before_tax' => $payload['amount'],
        'amount_after_tax' => $payload['amount'],
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 20,
        'balance_after' => $after_balance,
        'description' => 'Deposit from payment gateway'
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $user->id,
        'billing_document_id' => $billing_document->id,
        'type' => PaymentType::WALLET_DEPOSIT,
        'provider' => PaymentProvider::PAYEX,
        'transaction_loggable_type' => WalletTransaction::class,
        'currency_id' => $currency->id,
        'currency_code' => $currency->code,
        'currency_name' => $currency->name,
        'amount' => $payload['amount'],
        'status' => PaymentStatus::PENDING,
        'description' => 'Deposit Wallet',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'type' => BillingDocument::TYPE_INVOICE,
        'paid_at' => null,
        'amount_before_tax' => $payload['amount'],
        'bill_to_id' => $userable->id,
        'bill_to_type' => get_class($userable),
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $billing_document->id,
        'currency_code' => config('school.currency_code'),
        'description' => 'E-Wallet Topup',
        'amount_before_tax' => $payload['amount'],
        'gl_account_code' => GlAccount::CODE_EWALLET,
        'offset_billing_document_id' => null,
        'billable_item_id' => $wallet_transaction->id,
        'billable_item_type' => get_class($wallet_transaction),
    ]);

});

test('depositRequest() payment gateway failed', function () {
    $currency = Currency::factory()->create();
    $student = Student::factory()
        ->forUser()
        ->create();
    $user = $student->user;
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'balance' => 20,
        'currency_id' => $currency->id,
    ]);

    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456',
        'category' => Config::CATEGORY_GENERAL
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '01',
                'result' => [
                    [
                        'status' => '01',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'INVALID_PAYMENT_URL',
                        'error' => 'INVALID DEPOSIT'
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    //amount = 15
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 15.00,
        'wallet_id' => $wallet->id,
        'payment_type' => PaymentMethod::CODE_CREDIT_CARD,
    ];

    $has_error = false;

    Event::fake();

    try {
        $this->walletTransactionService
            ->setUser($user)
            ->setUserableByTypeAndId($payload['userable_type'], $payload['userable_id'])
            ->setWalletById($payload['wallet_id'])
            ->depositRequest($payload);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('INVALID DEPOSIT');
        $has_error = true;
    }

    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $payment_gateway_log = PaymentGatewayLog::first();

    expect($has_error)->toBeTrue()
        ->and($payment_gateway_log)->toBeInstanceOf(PaymentGatewayLog::class)
        ->toMatchArray([
            'requested_by_id' => $user->id,
            'type' => 'WALLET_DEPOSIT',
            'provider' => 'PAYEX',
            'transaction_loggable_type' => WalletTransaction::class,
            'currency_id' => $currency->id,
            'currency_code' => $currency->code,
            'currency_name' => $currency->name,
            'amount' => 15.00,
            'status' => WalletTransactionStatus::FAILED->value,
            'description' => 'Deposit Wallet',
            'payment_url' => null,
        ]);

    $this->assertDatabaseCount('wallet_transactions', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount(BillingDocument::class, 1);
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 1);

    $after_balance = 20 + $payload['amount'];
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::DEPOSIT,
        'status' => WalletTransactionStatus::FAILED,
        'wallet_transactable_type' => get_class($wallet),
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $payload['amount'],
        'amount_before_tax' => $payload['amount'],
        'amount_after_tax' => $payload['amount'],
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 20,
        'balance_after' => $after_balance,
        'description' => 'Deposit from payment gateway'
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $user->id,
        'type' => PaymentType::WALLET_DEPOSIT,
        'provider' => PaymentProvider::PAYEX,
        'transaction_loggable_type' => WalletTransaction::class,
        'currency_id' => $currency->id,
        'currency_code' => $currency->code,
        'currency_name' => $currency->name,
        'amount' => $payload['amount'],
        'status' => PaymentStatus::FAILED,
        'description' => 'Deposit Wallet',
        'payment_url' => null
    ]);

    $billing_document = BillingDocument::first();
    $wallet_transaction = WalletTransaction::first();
    $userable = $this->walletTransactionService->getUserable();

    Event::assertDispatched(\App\Events\InvoiceVoidedEvent::class);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_VOIDED,      // should be voided if event listener runs
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'type' => BillingDocument::TYPE_INVOICE,
        'paid_at' => null,
        'amount_before_tax' => $payload['amount'],
        'bill_to_id' => $userable->id,
        'bill_to_type' => get_class($userable),
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $billing_document->id,
        'currency_code' => config('school.currency_code'),
        'description' => 'E-Wallet Topup',
        'amount_before_tax' => $payload['amount'],
        'gl_account_code' => GlAccount::CODE_EWALLET,
        'offset_billing_document_id' => null,
        'billable_item_id' => $wallet_transaction->id,
        'billable_item_type' => get_class($wallet_transaction),
    ]);
});

test('getAllPaginatedWalletTransactionsByWallet()', function () {
    $first_guardian = Guardian::factory()->create();
    $first_user = $first_guardian->user;
    $first_guardian->user->assignRole('Super Admin');
    $first_wallet = Wallet::factory()->create(['user_id' => $first_user->id]);
    $first_wallet_transactions = WalletTransaction::factory()->count(5)->create(['wallet_id' => $first_wallet->id])->load('wallet.currency');

    $second_guardian = Guardian::factory()->create();
    $second_user = $second_guardian->user;
    $first_guardian->user->assignRole('Super Admin');
    $second_wallet = Wallet::factory()->create(['user_id' => $second_user->id]);
    $second_wallet_transactions = WalletTransaction::factory()->count(5)->create(['wallet_id' => $second_wallet->id])->load('wallet.currency');

    $response = $this->walletTransactionService->getAllPaginatedWalletTransactionsByWallet($first_user,
        $first_wallet, ['order_by' => 'id'])->toArray();

    expect($response['data'])->toEqual($first_wallet_transactions->toArray());

    $response = $this->walletTransactionService->getAllPaginatedWalletTransactionsByWallet($second_user,
        $second_wallet, ['order_by' => 'id'])->toArray();
    expect($response['data'])->toEqual($second_wallet_transactions->toArray());
});

test('generateReferenceNumber()', function () {
    $num = $this->walletTransactionService->generateReferenceNumber(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::TRANSFER), WalletTransactionType::TRANSFER->value);
    expect($num)->toBe('TRF' . now()->year . '0000001');

    //number will increase
    $num = $this->walletTransactionService->generateReferenceNumber(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::TRANSFER), WalletTransactionType::TRANSFER->value);
    expect($num)->toBe('TRF' . now()->year . '0000002');

    //change identify number will reset
    $num = $this->walletTransactionService->generateReferenceNumber(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::DEPOSIT), WalletTransactionType::DEPOSIT->value);
    expect($num)->toBe('TOP' . now()->year . '0000001');
});

test('getTerminalFromRefNo()', function () {
    /**
     * TRANSFER
     */
    $ref_no = 'TRF20240000015';

    $result = $this->walletTransactionService->getTerminalFromQubeRefNo($ref_no);

    expect($result)->toBeNull();

    /**
     * ECOMMERCE
     */
    $ref_no = 'ORD202400009';

    $result = $this->walletTransactionService->getTerminalFromQubeRefNo($ref_no);

    expect($result)->toBeNull();

    /**
     * TERMINAL but non existing code
     */
    $ref_no = '01-CL110000-809809';

    $result = $this->walletTransactionService->getTerminalFromQubeRefNo($ref_no);

    expect($result)->toBeNull();

    /**
     * TERMINAL but existing code
     */
    $terminal = Terminal::factory()->create([
        'name' => 'Canteen Level 01 Cashier 1',
        'code' => 'CL0101',
    ]);

    $ref_no = "01-{$terminal->code}-809809";

    $result = $this->walletTransactionService->getTerminalFromQubeRefNo($ref_no);

    expect($result->toArray())->toMatchArray($terminal->toArray());
});

test('billingDocumentVoidedActionCallback', function () {

    // pending -> failed OK
    $wallet_transaction = WalletTransaction::factory()->create([
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 10,
        'balance_before' => 0,
        'balance_after' => 10
    ]);

    $wallet_transaction->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(WalletTransaction::class, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED,
    ]);

    // success -> failed not gonna run
    $wallet_transaction = WalletTransaction::factory()->create([
        'type' => WalletTransactionType::TRANSACTION,
        'status' => WalletTransactionStatus::SUCCESS,
        'total_amount' => 10,
        'balance_before' => 0,
        'balance_after' => 10
    ]);

    $wallet_transaction->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(WalletTransaction::class, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS,
    ]);


});
