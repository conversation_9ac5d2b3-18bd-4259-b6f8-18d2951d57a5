<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;

uses(
    Tests\TestCase::class,
    RefreshDatabase::class,
)->in('Feature', 'Unit');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

expect()->extend('toHaveSuccessGeneralResponse', function () {
    return $this->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'Success.',
    ]);
});

expect()->extend('toHaveModelResourceNotFoundResponse', function () {
    return $this->toMatchArray([
        'status' => 'ERROR',
        'code' => 0,
        'message' => null,
        'error' => "Resource not found",
        'data' => null
    ]);
});

expect()->extend('toHaveFailedGeneralResponse', function () {
    return $this->toMatchArray([
        'status' => 'ERROR',
    ]);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function something()
{
    // ..
}

function getMediaFullUrl($media, $custom_file_name = null): string
{
    $prefix = config('media-library.prefix');

    if ($prefix) {
        $prefix .= '/';
    }

    return url('/storage/' . $prefix . $media->id . '/' . ($custom_file_name ?: $media->file_name));
}

/**
 * Creates a fake Excel file with specific test data
 *
 * @param  array  $data  Array of rows to write to Excel
 * @param  string  $filename  Optional custom filename
 * @return UploadedFile
 */
function createFakeExcelFileWithData(array $data, string $filename = 'test.xlsx'): UploadedFile
{
    $tempPath = sys_get_temp_dir() . '/' . uniqid();

    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Write data to sheet using coordinate notation
    foreach ($data as $rowIndex => $row) {
        foreach ($row as $columnIndex => $value) {
            $column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex + 1);
            $coordinate = $column . ($rowIndex + 1);
            $sheet->setCellValue($coordinate, $value);
        }
    }

    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save($tempPath);

    return new UploadedFile(
        $tempPath,
        $filename,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        null,
        true
    );
}
