<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        body{
            font-size: 10px;
        }
        table, th, td {
            border: 1px solid;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        td.label{
            padding-right: 5px;
        }
        table.no-border,
        table.no-border th,
        table.no-border td {
            border: none;
        }

        td.align-center{
            text-align: center;
        }
    </style>
</head>

<body>

<div style="display:block; width: 100%; height: 100px">&nbsp;</div>

<div style="text-align: left; font-size:14px">
        <h1 style="margin: 0;"> 
            {{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->format('Y') }}
            Annual Progress Report</h1 >
        <h1 style="margin: 0;"> 全年 成绩报告表  
            {{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->format('Y') }}
        </h1>
        <br></br>
    </div>

<div class="report-card-container">
    <table class="no-border" style="margin-bottom: 10px;">
        <tbody>
        <tr>
            <td class="label">姓名:</td>
            <td style="width: 25%">  {{ $student->getTranslation('name', 'zh') }} </td>
            <td class="label">班级:</td>
            <td style="width: 10%"> {{ $class->getTranslation('name', 'zh') }} </td>
            <td class="label">学号:</td>
            <td style="width: 15%"> {{ $student->student_number }} </td>
            <td class="label">发出日期:</td>
            <td> {{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->toDateString() }} </td>
        </tr>
        <tr>
            <td class="label">Name:</td>
            <td style="width: 25%"> {{ $student->getTranslation('name', 'en') }} </td>
            <td class="label">Class:</td>
            <td style="width: 10%"> {{ $class->getTranslation('name', 'en') }} </td>
            <td class="label">Student Number:</td>
            <td style="width: 15%"> {{ $student->student_number }} </td>
            <td class="label">Date Issued</td>
            <td>{{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->toDateString() }} </td>
        </tr>
        </tbody>
    </table>

    <table>
        <thead>
            <tr>
                <th style="width: 20% ">科目</th>
                <th style="width: 30% ">Subject</th>
                <th style="width: 8.3% ">第一学期<br />1st Semester</th>
                <th style="width: 8.3% ">第二学期<br />2nd Semester</th>
                <th style="width: 8.3% ">平均<br />Average</th>
                <th style="width: 8.3% ">百分比例等级<br />Percentile Rank</th>
                <th style="width: 8.3% ">学点<br />Point</th>
                <th style="width: 8.3% ">积分<br />Aggregate</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($subjects as $subject)
                @php
                    $target_output = $subject['output_type'];
                @endphp
                <tr>
                    <td>{{ $subject['zh'] }}</td>
                    <td>{{ $subject['en'] }}</td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['SEM1RESULT'][$subject['code']]) ? number_format($data['SEM1RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                        @else
                            {{ isset($data['SEM1RESULT'][$subject['code']]) ? $data['SEM1RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['SEM2RESULT'][$subject['code']]) ? number_format($data['SEM2RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                        @else
                            {{ isset($data['SEM2RESULT'][$subject['code']]) ? $data['SEM2RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['FINALRESULT'][$subject['code']]) ? number_format($data['FINALRESULT'][$subject['code']]->getValue($target_output), 2) : '-'  }}
                        @else
                            {{ isset($data['FINALRESULT'][$subject['code']]) ? $data['FINALRESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->weightage_multiplier) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->weightage_multiplier) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->weightage_multiplier) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->weightage_total) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->weightage_total) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->weightage_total) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
            @endforeach
                <tr>
                    <td>总积分</td>
                    <td>Gross Total</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['GT']))
                            {{  number_format($data['SEM1RESULT']['GT']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['GT']))
                            {{  number_format($data['SEM2RESULT']['GT']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['GT']))
                            {{  number_format($data['FINALRESULT']['GT']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td colspan="3" rowspan="12"></td>
                </tr>
                <tr>
                    <td>总平均</td>
                    <td>Gross Average</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['GA']))
                            {{  number_format($data['SEM1RESULT']['GA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['GA']))
                            {{  number_format($data['SEM2RESULT']['GA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['GA']))
                            {{  number_format($data['FINALRESULT']['GA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>校外学艺得分</td>
                    <td>Marks Added</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['MA']))
                            {{  number_format($data['SEM1RESULT']['MA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['MA']))
                            {{  number_format($data['SEM2RESULT']['MA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['MA']))
                            {{  number_format($data['FINALRESULT']['MA']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>扣分</td>
                    <td>Marks Subtracted</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['MS']))
                            {{  number_format($data['SEM1RESULT']['MS']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['MS']))
                            {{  number_format($data['SEM2RESULT']['MS']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['MS']))
                            {{  number_format($data['FINALRESULT']['MS']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>实得平均 </td>
                    <td>Net Average</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                            {{ number_format($data['SEM1RESULT']['SYS_NET_AVG']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                            {{ number_format($data['SEM2RESULT']['SYS_NET_AVG']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                            {{ number_format($data['FINALRESULT']['SYS_NET_AVG']->total, 2) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>成绩等级</td>
                    <td>Grade</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                            {{  $data['SEM1RESULT']['SYS_NET_AVG']->label  }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                            {{ $data['SEM2RESULT']['SYS_NET_AVG']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                            {{  $data['FINALRESULT']['SYS_NET_AVG']->label }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>全班名次</td>
                    <td>Position In Class</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                            {{ $data['SEM1RESULT']['SYS_NET_AVG']->class_rank }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                            {{ $data['SEM2RESULT']['SYS_NET_AVG']->class_rank }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                            {{ $data['FINALRESULT']['SYS_NET_AVG']->class_rank }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>操行</td>
                    <td>Conduct</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['CONDUCT']))
                            {{ $data['SEM1RESULT']['CONDUCT']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['CONDUCT']))
                            {{ $data['SEM2RESULT']['CONDUCT']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['CONDUCT']))
                            {{ $data['FINALRESULT']['CONDUCT']->label}}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>升留级 </td>
                    <td>Promoted/Retained</td>
                    <td class="align-center"> - </td>
                    <td class="align-center"> - </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['PROMOTION']))
                            {{ $data['FINALRESULT']['PROMOTION']->label }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>上课天数 </td>
                    <td>No. of School Day</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['SCHOOLDAYS']))
                            {{ number_format($data['SEM1RESULT']['SCHOOLDAYS']->total) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['SCHOOLDAYS']))
                            {{ number_format($data['SEM2RESULT']['SCHOOLDAYS']->total) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT']['SCHOOLDAYS']))
                            {{ number_format($data['FINALRESULT']['SCHOOLDAYS']->total) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>班级职务</td>
                    <td>Post(s) Held in Class</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['LEADERSHIP_POSITION']))
                            {{ $data['SEM1RESULT']['LEADERSHIP_POSITION']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['LEADERSHIP_POSITION']))
                            {{ $data['SEM2RESULT']['LEADERSHIP_POSITION']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        -
                    </td>
                </tr>
                <tr>
                    <td>学会</td>
                    <td>Society</td>
                    <td class="align-center">
                        @if(isset($data['SEM1RESULT']['SOCIETY']))
                            {{ $data['SEM1RESULT']['SOCIETY']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['SEM2RESULT']['SOCIETY']))
                            {{ $data['SEM2RESULT']['SOCIETY']->label }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        -
                    </td>
                </tr>
                <tr>
                    <td>请假 / 缺席记录</td>
                    <td>Record of Leave/Absence</td>
                    @if(isset($data['SEM1RESULT']['LEAVE']->label))
                        <td colspan="6"> {{  $data['SEM1RESULT']['LEAVE']->label }}</td>
                    @elseif(isset($data['SEM2RESULT']['LEAVE']->label))
                        <td colspan="6"> {{  $data['SEM2RESULT']['LEAVE']->label }}</td>
                    @elseif(isset($data['FINALRESULT']['LEAVE']->label))
                        <td colspan="6"> {{  $data['FINALRESULT']['LEAVE']->label }}</td>
                    @else
                        <td colspan='6'> - </td>
                    @endif
                </tr>
                <tr>
                    @if(isset($data['FINALRESULT']['MERIT']->label))
                        <td rowspan="{{ count(explode(';', $data['FINALRESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
                        <td rowspan="{{ count(explode(';', $data['FINALRESULT']['MERIT']->label)) + 1 }}" >Merit</td>
                        @foreach (explode(';', $data['FINALRESULT']['MERIT']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @elseif(isset($data['SEM1RESULT']['MERIT']->label))
                        <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
                        <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['MERIT']->label)) + 1 }}" >Merit</td>
                        @foreach (explode(';', $data['SEM1RESULT']['MERIT']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @elseif(isset($data['SEM2RESULT']['MERIT']->label))
                        <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
                        <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['MERIT']->label)) + 1 }}" >Merit</td>
                        @foreach (explode(';', $data['SEM2RESULT']['MERIT']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @else 
                        <td>奖励记录</td>
                        <td>Merit</td>
                        <td colspan='6'> - </td>
                    @endif
                </tr>

                <tr>
                    @if(isset($data['FINALRESULT']['EXCEPTIONAL']->label))
                        <td rowspan="{{ count(explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</td>
                        <td rowspan="{{ count(explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label)) + 1 }}" >Exceptional Performance</td>
                        @foreach (explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @elseif(isset($data['SEM1RESULT']['EXCEPTIONAL']->label))
                        <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</td>
                        <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label)) + 1 }}" >Exceptional Performance</td>
                        @foreach (explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @elseif(isset($data['SEM2RESULT']['EXCEPTIONAL']->label))
                        <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</td>
                        <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label)) + 1 }}" >Exceptional Performance</td>
                        @foreach (explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label) as $exceptional)
                        <tr>
                            <td colspan='6'> {{$exceptional }}</td>
                        </tr>
                        @endforeach
                    @else 
                        <td>特殊表现</td>
                        <td>Exceptional Performance</td>
                        <td colspan='6'> - </td>
                    @endif
                </tr>

                </td>
        </tbody>
    </table>

</div>

<div style="display:block; width: 100%; height: 100px">&nbsp;</div>

<div>
    <table>
        <thead>
            <tr>
                <th style="width: 30%" >校长 Principal</th>
                <th style="width: 30%">班导师 Form Teacher</th>
                <th style="width: 30%">家长/监护人 Parent / Guardian</th>
            </tr>
        </thead>

        <tbody>
            <tr style="height:200px">
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>
</div>
</body>

</html>
