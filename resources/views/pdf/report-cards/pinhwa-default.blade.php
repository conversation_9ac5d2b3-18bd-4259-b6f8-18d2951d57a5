<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        @font-face {
            font-family: 'Noto Serif SC';
            font-style: normal;
            font-weight: 200 900;
            font-display: swap;
            src: url({{ asset('/fonts/truetype/NotoSerifSC.ttf') }});
        }

        html, body {
            font-family: 'Noto Serif SC', sans-serif;
        }

        body {
            font-size: 10px;
            margin: 0px 30px;
        }

        table, th, td {
            border: 1px solid;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            font-size: 13px;
        }

        td.label {
            padding-right: 5px;
        }

        table.no-border,
        table.no-border th,
        table.no-border td {
            border: none;
        }

        td.align-center {
            text-align: center;
        }

        .align-left {
            text-align: left;
        }

        table.content, td, th {
            padding-left: 4px;
        }

        h1 {
            font-size: 21px;
            font-weight: bold;
        }
    </style>
</head>

<body>

<div style="display:block; width: 100%; height: 80px">&nbsp;</div>

<div style="text-align: left; font-size:14px">
    <h1 style="margin: 0;">
        {{ $year }}
        @if ( $posting_header->report_card_output_code == 'SEM1RESULT' )
            Semester 1 Progress Report
        @elseif ( $posting_header->report_card_output_code == 'SEM2RESULT' )
            Semester 2 Progress Report
        @else
            Annual Progress Report
        @endif
    </h1>
    <h1 style="margin: 0;">
        {{ $year }}
        @if ( $posting_header->report_card_output_code == 'SEM1RESULT' )
            第一学期 成绩报告表
        @elseif ( $posting_header->report_card_output_code == 'SEM2RESULT' )
            第二学期 成绩报告表
        @else
            全年 成绩报告表
        @endif
    </h1>
    <br></br>
</div>

<div class="report-card-container">
    <table class="no-border" style="margin-bottom: 10px;">
        <tbody>
        <tr style="font-weight:700">
            <td class="label">姓名:</td>
            <td style="width: 20%">  {{ $student->getTranslation('name', 'zh') }} </td>
            <td class="label">班级:</td>
            <td style="width: 15%"> {{ $class->getTranslation('name', 'zh') }} </td>
            <td class="label">学号:</td>
            <td style="width: 15%"> {{ $student->student_number }} </td>
            <td class="label">发出日期:</td>
            <td> {{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->toDateString() }} </td>
        </tr>
        <tr>
            <td class="label">Name:</td>
            <td style="width: 25%"> {{ $student->getTranslation('name', 'en') }} </td>
            <td class="label">Class:</td>
            <td style="width: 10%"> {{ $class->getTranslation('name', 'en') }} </td>
            <td class="label">Student No:</td>
            <td style="width: 15%"></td>
            <td class="label">Date Issued</td>
        </tr>
        </tbody>
    </table>

    <table class="content">
        <thead>
        <tr>
            <th style="width: 16% ">科目</th>
            <td class="align-center" style="width: 33% ">Subject</td>
            <td class="align-center" style="width: 7%; font-size:9px">第一学期<br/>1st Semester</td>
            <td class="align-center" style="width: 7%; font-size:9px">第二学期<br/>2nd Semester</td>
            <td class="align-center" style="width: 7%; font-size:9px">平均<br/>Average</td>
            <td class="align-center" style="width: 7%; font-size:9px">百分比例等级<br/>Percentile Rank</td>
            <td class="align-center" style="width: 7% ">学点<br/>Point</td>
            <td class="align-center" style="width: 7% ">积分<br/>Aggregate</td>
        </tr>
        </thead>
        <tbody>
        @foreach ($subjects as $subject)
            @php
                $target_output = $subject['output_type'];
            @endphp
            <tr>
                <th class="align-left">{{ $subject['zh'] }}</th>
                <td>{{ $subject['en'] }}</td>
                <td class="align-center">
                    @if ( $target_output === 'total' )
                        {{ isset($data['SEM1RESULT'][$subject['code']]) ? number_format($data['SEM1RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                    @else
                        {{ isset($data['SEM1RESULT'][$subject['code']]) ? $data['SEM1RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                    @endif
                </td>
                <td class="align-center">
                    @if ( $target_output === 'total' )
                        {{ isset($data['SEM2RESULT'][$subject['code']]) ? number_format($data['SEM2RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                    @else
                        {{ isset($data['SEM2RESULT'][$subject['code']]) ? $data['SEM2RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                    @endif
                </td>
                <td class="align-center">
                    @if ( $target_output === 'total' )
                        {{ isset($data['FINALRESULT'][$subject['code']]) ? number_format($data['FINALRESULT'][$subject['code']]->getValue($target_output), 2) : '-'  }}
                    @else
                        {{ isset($data['FINALRESULT'][$subject['code']]) ? $data['FINALRESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                    @endif
                </td>
                <td class="align-center">
                    @if(isset($data['FINALRESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['FINALRESULT'][$subject['code']]->grade_percentile_rank, 2) : '' }}
                    @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM2RESULT'][$subject['code']]->grade_percentile_rank, 2) : '' }}
                    @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM1RESULT'][$subject['code']]->grade_percentile_rank, 2) : '' }}
                    @else
                        -
                    @endif
                </td>
                <td class="align-center">
                    @if(isset($data['FINALRESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['FINALRESULT'][$subject['code']]->weightage_multiplier) : '' }}
                    @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM2RESULT'][$subject['code']]->weightage_multiplier) : '' }}
                    @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM1RESULT'][$subject['code']]->weightage_multiplier) : '' }}
                    @else
                        -
                    @endif
                </td>
                <td class="align-center">
                    @if(isset($data['FINALRESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['FINALRESULT'][$subject['code']]->weightage_total, 2) : $data['FINALRESULT'][$subject['code']]->getValue($target_output) }}
                    @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM2RESULT'][$subject['code']]->weightage_total, 2) : '' }}
                    @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                        {{ $target_output === 'total' ? number_format($data['SEM1RESULT'][$subject['code']]->weightage_total, 2) : '' }}
                    @else
                        -
                    @endif
                </td>
            </tr>
        @endforeach
        <tr>
            <th class="align-left">总积分</th>
            <td>Gross Total</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['GT']))
                    {{  number_format($data['SEM1RESULT']['GT']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['GT']))
                    {{  number_format($data['SEM2RESULT']['GT']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['GT']))
                    {{  number_format($data['FINALRESULT']['GT']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td colspan="3" rowspan="12"></td>
        </tr>
        <tr>
            <th class="align-left">总平均</th>
            <td>Gross Average</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['GA']))
                    {{  number_format($data['SEM1RESULT']['GA']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['GA']))
                    {{  number_format($data['SEM2RESULT']['GA']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['GA']))
                    {{  number_format($data['FINALRESULT']['GA']->total, 2) }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">校外学艺得分</th>
            <td>Marks Added</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['MA']))
                    {{  number_format($data['SEM1RESULT']['MA']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['MA']))
                    {{  number_format($data['SEM2RESULT']['MA']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['MA']))
                    {{  number_format($data['FINALRESULT']['MA']->total, 2) }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">扣分</th>
            <td>Marks Subtracted</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['MS']))
                    {{  number_format($data['SEM1RESULT']['MS']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['MS']))
                    {{  number_format($data['SEM2RESULT']['MS']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['MS']))
                    {{  number_format($data['FINALRESULT']['MS']->total, 2) }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">实得平均</th>
            <td>Net Average</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                    {{ number_format($data['SEM1RESULT']['SYS_NET_AVG']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                    {{ number_format($data['SEM2RESULT']['SYS_NET_AVG']->total, 2) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                    {{ number_format($data['FINALRESULT']['SYS_NET_AVG']->total, 2) }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">成绩等级</th>
            <td>Grade</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                    {{  $data['SEM1RESULT']['SYS_NET_AVG']->label  }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                    {{ $data['SEM2RESULT']['SYS_NET_AVG']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                    {{  $data['FINALRESULT']['SYS_NET_AVG']->label }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">全班名次</th>
            <td>Position In Class</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['SYS_NET_AVG']))
                    {{ $data['SEM1RESULT']['SYS_NET_AVG']->class_rank }} / {{ $data['SEM1RESULT']['SYS_NET_AVG']->class_population  }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['SYS_NET_AVG']))
                    {{ $data['SEM2RESULT']['SYS_NET_AVG']->class_rank }} / {{ $data['SEM2RESULT']['SYS_NET_AVG']->class_population  }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['SYS_NET_AVG']))
                    {{ $data['FINALRESULT']['SYS_NET_AVG']->class_rank }} / {{ $data['FINALRESULT']['SYS_NET_AVG']->class_population  }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">操行</th>
            <td>Conduct</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['CONDUCT']))
                    {{ $data['SEM1RESULT']['CONDUCT']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['CONDUCT']))
                    {{ $data['SEM2RESULT']['CONDUCT']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['CONDUCT']))
                    {{ $data['FINALRESULT']['CONDUCT']->label}}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">升留级</th>
            <td>Promoted/Retained</td>
            <td class="align-center"> -</td>
            <td class="align-center"> -</td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['PROMOTION']))
                    {{ $data['FINALRESULT']['PROMOTION']->label }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">上课天数</th>
            <td>No. of School Day</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['SCHOOLDAYS']))
                    {{ number_format($data['SEM1RESULT']['SCHOOLDAYS']->total) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['SCHOOLDAYS']))
                    {{ number_format($data['SEM2RESULT']['SCHOOLDAYS']->total) }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['FINALRESULT']['SCHOOLDAYS']))
                    {{ number_format($data['FINALRESULT']['SCHOOLDAYS']->total) }}
                @else
                    -
                @endif
            </td>
        </tr>
        <tr>
            <th class="align-left">班级职务</th>
            <td>Post(s) Held in Class</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['LEADERSHIP_POSITION']))
                    {{ $data['SEM1RESULT']['LEADERSHIP_POSITION']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['LEADERSHIP_POSITION']))
                    {{ $data['SEM2RESULT']['LEADERSHIP_POSITION']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                -
            </td>
        </tr>
        <tr>
            <th class="align-left">学会</th>
            <td>Society</td>
            <td class="align-center">
                @if(isset($data['SEM1RESULT']['SOCIETY']))
                    {{ $data['SEM1RESULT']['SOCIETY']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                @if(isset($data['SEM2RESULT']['SOCIETY']))
                    {{ $data['SEM2RESULT']['SOCIETY']->label }}
                @else
                    -
                @endif
            </td>
            <td class="align-center">
                -
            </td>
        </tr>
        <tr>
            <th class="align-left">请假 / 缺席记录</th>
            <td>Record of Leave/Absence</td>
            @if(isset($data['SEM1RESULT']['LEAVE']->label))
                <td colspan="6"> {{  $data['SEM1RESULT']['LEAVE']->label }}</td>
            @elseif(isset($data['SEM2RESULT']['LEAVE']->label))
                <td colspan="6"> {{  $data['SEM2RESULT']['LEAVE']->label }}</td>
            @elseif(isset($data['FINALRESULT']['LEAVE']->label))
                <td colspan="6"> {{  $data['FINALRESULT']['LEAVE']->label }}</td>
            @else
                <td colspan='6'> -</td>
            @endif
        </tr>
        <tr>
            @if(isset($data['FINALRESULT']['MERIT']->label))
                <td rowspan="{{ count(explode(';', $data['FINALRESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
                <td rowspan="{{ count(explode(';', $data['FINALRESULT']['MERIT']->label)) + 1 }}">Merit</td>
        @foreach (explode(';', $data['FINALRESULT']['MERIT']->label) as $exceptional)
            <tr>
                <td colspan='6'> {{$exceptional }}</td>
            </tr>
        @endforeach
        @elseif(isset($data['SEM1RESULT']['MERIT']->label))
            <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
            <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['MERIT']->label)) + 1 }}">Merit</td>
            @foreach (explode(';', $data['SEM1RESULT']['MERIT']->label) as $exceptional)
                <tr>
                    <td colspan='6'> {{$exceptional }}</td>
                </tr>
            @endforeach
        @elseif(isset($data['SEM2RESULT']['MERIT']->label))
            <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['MERIT']->label)) + 1 }}">奖励记录</td>
            <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['MERIT']->label)) + 1 }}">Merit</td>
            @foreach (explode(';', $data['SEM2RESULT']['MERIT']->label) as $exceptional)
                <tr>
                    <td colspan='6'> {{$exceptional }}</td>
                </tr>
            @endforeach
        @else
            <th class="align-left">奖励记录</th>
            <td>Merit</td>
            <td colspan='6'> -</td>
            @endif
            </tr>

            <tr>
                @if(isset($data['FINALRESULT']['EXCEPTIONAL']->label))
                    <th class="align-left" rowspan="{{ count(explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</th>
                    <td rowspan="{{ count(explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label)) + 1 }}">Exceptional Performance</td>
            @foreach (explode(';', $data['FINALRESULT']['EXCEPTIONAL']->label) as $exceptional)
                <tr>
                    <td colspan='6'> {{$exceptional }}</td>
                </tr>
            @endforeach
            @elseif(isset($data['SEM1RESULT']['EXCEPTIONAL']->label))
                <th class="align-left" rowspan="{{ count(explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</th>
                <td rowspan="{{ count(explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label)) + 1 }}">Exceptional Performance</td>
                @foreach (explode(';', $data['SEM1RESULT']['EXCEPTIONAL']->label) as $exceptional)
                    <tr>
                        <td colspan='6'> {{$exceptional }}</td>
                    </tr>
                @endforeach
            @elseif(isset($data['SEM2RESULT']['EXCEPTIONAL']->label))
                <th class="align-left" rowspan="{{ count(explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label)) + 1 }}">特殊表现</th>
                <td rowspan="{{ count(explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label)) + 1 }}">Exceptional Performance</td>
                @foreach (explode(';', $data['SEM2RESULT']['EXCEPTIONAL']->label) as $exceptional)
                    <tr>
                        <td colspan='6'> {{$exceptional }}</td>
                    </tr>
                @endforeach
            @else
                <th class="align-left">特殊表现</th>
                <td>Exceptional Performance</td>
                <td colspan='6'> -</td>
                @endif
                </tr>

                </td>
        </tbody>
    </table>

</div>

    <div style="position: fixed; bottom: 0">
        <table style="width: 99%">
            <thead>
            <tr>
                <th style="width: 33%">校长 Principal</th>
                <th style="width: 33%">班导师 Form Teacher</th>
                <th style="width: 33%">家长/监护人 Parent / Guardian</th>
            </tr>
            </thead>

            <tbody>
            <tr style="height:110px">
                <td></td>
                <td></td>
                <td></td>
            </tr>
            </tbody>
        </table>
    </div>
</body>

</html>
