<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        body{
            font-size: 10px;
        }
        table, th, td {
            border: 1px solid;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        td.label{
            padding-right: 5px;
        }
        table.no-border,
        table.no-border th,
        table.no-border td {
            border: none;
        }

        td.align-center{
            text-align: center;
        }
    </style>
</head>

<body>
<div class="report-card-container">

    <table class="no-border" style="margin-bottom: 10px;">
        <tbody>
        <tr>
            <td class="label">姓名:</td>
            <td style="width: 30%">{{ $student->getTranslation('name', 'zh') }}</td>
            <td class="label">班级:</td>
            <td>{{ $class->getTranslation('name', 'zh') }}</td>
            <td class="label">学号:</td>
            <td>{{ $student->student_number }}</td>
            <td class="label">发出日期:</td>
            <td>{{ \Carbon\Carbon::parse($posting_header->posted_at)->tz(config('school.timezone'))->toDateString() }}</td>
        </tr>
        <tr>
            <td class="label">Name</td>
            <td style="width: 30%">{{ $student->getTranslation('name', 'en') }}</td>
            <td class="label">Class</td>
            <td>{{ $class->getTranslation('name', 'en') }}</td>
            <td class="label">Student No</td>
            <td></td>
            <td class="label">Date Issued</td>
            <td></td>
        </tr>
        </tbody>
    </table>

    <table>
        <thead>
            <tr>
                <th>科目</th>
                <th>Subject</th>
                <th>第一学期<br />1st Semester</th>
                <th>第二学期<br />2nd Semester</th>
                <th>平均<br />Average</th>
                <th>百分比例等级<br />Percentile Rank</th>
                <th>学点<br />Point</th>
                <th>积分<br />Aggregate</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($subjects as $subject)
                @php
                    $target_output = $output_type[$subject['code']] ?? 'total';
                @endphp
                <tr>
                    <td>{{ $subject['zh'] }}</td>
                    <td>{{ $subject['en'] }}</td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['SEM1RESULT'][$subject['code']]) ? number_format($data['SEM1RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                        @else
                            {{ isset($data['SEM1RESULT'][$subject['code']]) ? $data['SEM1RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['SEM2RESULT'][$subject['code']]) ? number_format($data['SEM2RESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                        @else
                            {{ isset($data['SEM2RESULT'][$subject['code']]) ? $data['SEM2RESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if ( $target_output === 'total' )
                            {{ isset($data['FINALRESULT'][$subject['code']]) ? number_format($data['FINALRESULT'][$subject['code']]->getValue($target_output)) : '-'  }}
                        @else
                            {{ isset($data['FINALRESULT'][$subject['code']]) ? $data['FINALRESULT'][$subject['code']]->getValue($target_output) : '-'  }}
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->grade_percentile_rank, 2) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->weightage_multiplier) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->weightage_multiplier) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->weightage_multiplier) }}
                        @else
                            -
                        @endif
                    </td>
                    <td class="align-center">
                        @if(isset($data['FINALRESULT'][$subject['code']]))
                            {{ number_format($data['FINALRESULT'][$subject['code']]->weightage_total) }}
                        @elseif(isset($data['SEM2RESULT'][$subject['code']]))
                            {{ number_format($data['SEM2RESULT'][$subject['code']]->weightage_total) }}
                        @elseif(isset($data['SEM1RESULT'][$subject['code']]))
                            {{ number_format($data['SEM1RESULT'][$subject['code']]->weightage_total) }}
                        @else
                            -
                        @endif
                    </td>
                </tr>
            @endforeach
                <tr>
                    <td>总积分</td>
                    <td>Gross Total</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>总平均</td>
                    <td>Gross Average</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>校外学艺得分</td>
                    <td>Marks Added</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>扣分</td>
                    <td>Marks Subtracted</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>实得平均 </td>
                    <td>Net Average</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>成绩等级</td>
                    <td>Grade</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>全班名次</td>
                    <td>Position In Class</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>操行</td>
                    <td>Conduct</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>升留级 </td>
                    <td>Promoted/Retained</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>上课天数 </td>
                    <td>No. of School Day</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>班级职务</td>
                    <td>Post(s) Held in Class</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>学会</td>
                    <td>Society</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>请假 / 缺席记录</td>
                    <td>Record of Leave/Absence</td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>奖励记录</td>
                    <td>Merit</td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <td>特殊表现</td>
                    <td>Exceptional Performance</td>
                    <td></td>
                    <td colspan="4"></td>
                </tr>
        </tbody>
    </table>

</div>
</body>

</html>
