@extends('reports.layout')
@section('content')
    <table style="margin-bottom: 20px;">
        <tr>
            <td>{{__('general.employee_number')}} : {{$data['employee']['number']}}</td>
            <td></td>
        </tr>
        <tr class="bg-white">
            <td>{{__('general.teacher')}} : {{$data['employee']['name']}}</td>
            <td>{{__('general.date')}} : {{$data['date_from']}} - {{$data['date_to']}}</td>
        </tr>
    </table>
    <table>
        <thead>
        @foreach($data['period_groups'] as $period_groups)
            <tr>
                <th style="text-align: center;">{{ $period_groups['period_group_name'] }}</th>

                @foreach($period_groups['periods'] as $period)
                    <th style="text-align: center;">
                        {{ $period['period_label_name'] }} <br/>
                        {{ $period['from_time'] }} <br> {{ $period['to_time'] }}
                    </th>
                @endforeach
            </tr>
        @endforeach
        </thead>
        <tbody>
        @if(isset($data))
            @foreach($data['dates'] as $date)
                <tr>
                    <td style="text-align: center;">
                        {{$date['date']}}
                        <br>
                        {{$date['day']}}
                    </td>
                    @foreach($date['attendances'] as $attendance)
                        @if($attendance)
                            <td style="text-align: center">
                                {{$attendance['subject_name']}}
                                <br>
                                {{$attendance['class_name']}}
                                <br/>
                                {{$attendance['time_taken_at']}}
                            </td>
                        @else
                            <td></td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="{{$colspan}}">{{ __('general.no_data_available') }}</td>
            </tr>
        @endif
        </tbody>
    </table>
@endsection
