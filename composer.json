{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "ext-bcmath": "*", "aws/aws-sdk-php": "^3.324", "barryvdh/laravel-snappy": "^1.0", "guzzlehttp/guzzle": "^7.2", "lanin/laravel-api-debugger": "^4.0", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-path-prefixing": "^3.0", "league/flysystem-sftp-v3": "^3.0", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^13.6", "propaganistas/laravel-phone": "^5.3", "sentry/sentry-laravel": "^4.11", "spatie/image": "^3.8", "spatie/laravel-medialibrary": "^11", "spatie/laravel-permission": "^6.7", "spatie/laravel-translatable": "^6.6"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "brianium/paratest": "^7.4", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.34", "pestphp/pest-plugin-drift": "^2.5", "spatie/laravel-ignition": "^2.0", "spatie/laravel-ray": "^1.40"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/GlobalHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}