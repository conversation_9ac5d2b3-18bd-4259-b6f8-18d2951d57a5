<?php

return [
    // generic errors
    '403' => 'Forbidden',
    '404' => 'Not Found',

    // Wallet Errors
    '1001' => 'Invalid Signature.',
    '1002' => 'User has insufficient balance.',
    '1003' => 'Invalid User.',
    '1004' => 'Invalid Order Reference No.',
    '1005' => 'Unable to obtain transaction lock for transfer.',
    '1006' => 'User has no permission to access the selected wallet.',
    '1007' => 'Payment type not supported.',
    '1008' => 'User does not have a wallet.',
    '1009' => 'Default currency not found.',
    '1010' => 'User has been inactivated.',
    '1012' => 'Transaction is not allowed to refund.',

    // Payment Gateway Errors
    '2001' => 'Transaction record not found.',
    '2002' => 'Invalid callback response.',
    '2003' => 'The payment has already been processed.',
    '2004' => 'Wallet transaction record not found.',
    '2005' => 'Unable to obtain transaction lock for unpaid items payment.',

    // Hostel Errors
    '3001' => 'Maximum capacity exceeded.',
    '3002' => 'The block cannot be deleted as it contains existing rooms.',
    '3003' => 'The room cannot be deleted as it contains existing beds.',
    '3004' => 'The bed cannot be deleted as it has been occupied before.',
    '3005' => 'One or more students do not exist.',
    '3006' => 'One or more beds do not exist or occupied.',
    '3007' => 'One or more employees do not exist.',
    '3008' => 'One or more students already have a bed assignment.',
    '3009' => 'One or more students do not have a bed assignment.',
    '3010' => 'One or more employees already have a bed assignment.',
    '3011' => 'One or more employees do not have a bed assignment.',
    '3012' => 'The category cannot be deleted as it contains existing items.',
    '3013' => ':subject already assigned with a bed.',
    '3014' => ':subject number does not exist in the database.',
    '3015' => ':subject number provided already existed in previous row.',
    '3016' => 'This bed has already been assigned in previous row.',
    '3017' => 'Block does not exist in the database.',
    '3018' => 'Room does not exist in the database.',
    '3019' => 'Bed is currently occupied.',
    '3020' => 'Bed does not exist in the database.',
    '3021' => ':subject is not a hostel boarder.',
    '3022' => 'Hostel block type is not suitable for this :subject',

    // User Errors
    '4001' => 'User does not have the right permissions.',
    '4002' => 'User not found.',
    '4003' => 'Unable to send OTP. Please try again later.',
    '4004' => 'Invalid OTP.',
    '4005' => 'Maximum number of OTP requests reached.',
    '4006' => 'The provided credentials does not match our records.',
    '4007' => 'Your account has been inactivated, please contact admin.',
    '4008' => 'You do not have permission to access this resource.',
    '4009' => 'The given phone number is invalid.',
    '4010' => 'Email or phone number is required to create user.',
    '4011' => 'This guardian does not belongs to the user.',

    // Enrollment Errors
    '5001' => 'Payment cannot be made at this step.',
    '5002' => 'Enrollment session cannot be deleted because it is being used.',
    '5003' => 'Either NRIC or Passport number must be filled.',
    '5004' => 'Status must be SHORTLISTED, REJECTED or APPROVED.',
    '5005' => 'NRIC is duplicated.',
    '5006' => 'NRIC must be 12 digits.',
    '5007' => 'Passport number is duplicated.',
    '5008' => 'Hostel must be boolean.',
    '5009' => 'Exam slip number is duplicated.',
    '5010' => 'Guardian type must be GUARDIAN, FATHER or MOTHER.',
    '5011' => 'Have siblings must be boolean.',
    '5012' => 'Dietary restriction must be :dietary_restrictions',
    '5013' => 'Foreigner must be boolean.',
    '5014' => ':key :value does not exist in the database. It will be created.',
    '5015' => ':key :value already exists in the database.',
    '5016' => 'Subject :code is missing in the import file.',
    '5017' => 'Column counts do not match.',
    '5018' => 'Invalid phone number.',
    '5019' => 'Phone number without country code must start with Malaysian number: 601, 01, 1',
    '5020' => 'Phone number and email does not match with registered user.',

    // Class Errors
    '6001' => 'Unable to assign class to semester.',
    '6002' => 'Unable to assign class to student.',
    '6003' => 'Unable to assign semester to class.',
    '6004' => 'The class cannot be deleted because it is currently being used.',
    '6005' => 'The semester class cannot be deleted because it is currently being used.',
    '6006' => 'Unable to do auto-assignment for seats because there is already a manual seat assignment in the semester.',
    '6007' => 'Unable to assign multiple classes to multiple semesters.',

    //Guardian Errors
    '7001' => 'Email is required to create guardian user.',
    '7002' => 'Email guardian already exists.',
    '7003' => 'Phone number is required to create guardian user.',
    '7004' => 'Guardian cannot be deleted once is linked.',
    '7005' => 'Guardian :guardian_name already existed.',

    //Card Errors
    '8001' => 'Please deactivate the existing active card first.',
    '8002' => ':subject number provided already existed in previous row.',
    '8003' => 'This card has already been assigned in previous row.',
    '8004' => 'Card number must be 10 digits.',
    '8005' => 'Card number2 must be 3 digits.',
    '8006' => 'Card number3 must be 5 digits.',
    '8007' => 'Update library card must be either true or false.',
    '8008' => 'Card type must be either NFC or PROXIMITY.',
    '8009' => ':subject currently have an active card.',
    '8010' => ':subject number does not exist in the database.',
    '8011' => 'Card number already being used in the database.',

    // Comprehensive Assessment Errors
    '9001' => 'The category cannot be deleted as it contains existing questions.',

    // DISCIPLINE_MANAGEMENT Errors
    '10001' => 'Merit demerit setting cannot be deleted because it is currently being used.',
    '10002' => 'Reward punishment category cannot be deleted because it is currently being used.',
    '10003' => 'Reward punishment sub category cannot be deleted because it is currently being used.',
    '10004' => 'Reward punishment cannot be deleted because it is currently being used.',

    // Club Errors
    '12001' => 'This club category cannot be deleted because it is being used.',
    '12002' => 'This club cannot be deleted because it is being used.',

    // E-Commerce Errors
    '13001' => 'Cannot delete product category that has subcategories.',
    '13002' => 'Cannot delete product sub category that has products.',
    '13003' => 'Cannot delete product group that is linked to products.',
    '13004' => 'Cannot delete product that has been ordered before.',
    '13005' => 'Cannot delete merchant that has orders.',
    '13006' => 'Order cannot be cancelled. Cancellation cut-off time has passed.',
    '13007' => 'Order cannot be cancelled. Invalid order status or payment status.',
    '13008' => 'Order cannot be cancelled. You are not authorized to cancel this order.',
    '13009' => 'Cannot delete product tag that is linked to products.',
    '13010' => 'Merchant type not found.',
    '13011' => 'Canteen is not available now. Please try again between :opening_time and :cutoff_time.',
    '13012' => 'Canteen is not available now.',
    '13013' => 'Product delivery date is required.',
    '13014' => 'Product delivery date must greater than or equal to today.',
    '13015' => 'Selected product is not available on the selected delivery date.',
    '13016' => 'Cannot delete merchant that has products.',

    // Leadership Position Errors
    '14001' => 'Leadership position cannot be deleted because it is being used.',

    // ClassSubject Errors
    '15001' => 'Unable to assign students to class subjects.',
    '15002' => 'One or more students do not exist.',
    '15003' => 'One or more teachers do not exist.',

    // Master Data Errors
    '20001' => 'This master data cannot be deleted because it is being used.',

    // Terminal Errors
    '21001' => 'Invalid Signature.',
    '21002' => 'Terminal cannot be deleted because it is being used.',

    // ISBN Errors
    '22001' => 'Invalid ISBN.',

    //Library Errors
    '23001' => 'Only lost book can be recovered.',

    // Role Errors
    '25001' => 'Role cannot be deleted because it is being used.',

    // Announcement Errors
    '26001' => 'The announcement cannot be cancelled.',
    '26002' => 'Student as recipient or guardian as recipient is required.',
    '26003' => 'You do not have permission to delete this resource.',
    '26004' => 'Unsupported image [:mime_type] attached to message.',

    // Employee Errors
    '27001' => 'Cannot resign an employee that is not working.',
    '27002' => 'Effective date of resignation cannot be before employment start date.',
    '27003' => 'Cannot reinstate an employee that is still working.',
    '27004' => 'Cannot transfer an employee that is not working.',
    '27005' => 'Effective date of transfer cannot be before employment start date.',
    '27006' => 'New job title is required to transfer employee.',

    '28001' => 'The phone number field must be a valid phone number.',

    // Student Errors
    '29001' => 'Student cannot be deleted because currently assigned to a bed.',
    '29002' => 'Inactive student is not allowed to perform leave school action.',
    '29003' => 'Effective date of student leave school cannot be before student join date.',
    '29004' => 'Student with bed assignment is not allowed to leave/graduate.',
    '29005' => 'Only student with left status can perform return school action.',
    '29006' => "No eligible guardian was found to transfer the student's remaining balance. Ensure the primary guardian has wallet.",
    '29007' => 'Semester setting is required for student to return school.',
    '29008' => 'Semester class is required for student to return school.',
    '29009' => 'Student not found.',
    // PinHwaStudentService
    '29010' => 'Invalid grade id.',
    '29011' => 'Unsupported admission type to get grade and init next number.',

    //Report Errors
    '30001' => 'Selected month is not within selected semester period.',

    // CounsellingCaseRecord Errors
    '32001' => 'Only employees are able to update counselling case record.',

    //SocietyPosition Errors
    '33001' => 'Unable to assign positions to students.',

    //Maintenance Error
    '34001' => 'Site is currently under maintenance.',
    '34002' => 'New app version available. Kindly update to the latest version.',

    // Product Errors
    '35001' => 'Product cannot be deleted because it is being used.',

    //Accounting Errors
    '36001' => 'All fees must be paid sequentially.',
    '36002' => 'All fees under the same month must be paid together.',
    '36003' => 'Can only pay fees for 1 account at a time.',
    '36004' => 'Must select to pay at least 1 fee',
    '36005' => 'Fee is not valid for payment.',
    '36006' => 'Invalid fee payment request',
    '36007' => 'Unsupported billing document type',
    '36008' => 'Invalid manual payment request',
    '36009' => 'Pending fees need to be paid before paying new fees.',
    '36010' => 'Payable amount must be more than RM1, please pay the fee along with other items.',
    '36011' => 'Payment currently in progress, please wait.',
    '36012' => 'Missing required data for billing document posting.',


    // Billing Document Errors
    '37001' => 'Invalid billing document to make payment.',

    // Contractor Errors
    '38001' => 'Contractor cannot be deleted because it is being used.',

    // Scholarship
    '39001' => 'Scholarship cannot be deleted because it is being used.',
    '39002' => 'Students with these ids :ids cannot be removed from the scholarship.',

    //Timetable
    '40001' => 'Period must be unique in same day.',
    '40002' => 'From to time must be unique in same day.',
    '40003' => 'Each day must have same set of periods.',
    '40004' => 'Each day must have same set of from to time.',
    '40005' => 'Timetable already assigned period group before.',
    '40006' => 'Incorrect timeslots data.',
    '40007' => 'Unable to resolve student to get student timetable',
    '40008' => "Unable to retrieve period group's labels. Class not found for student :student_name",
    '40009' => "Unable to retrieve period group's labels. Timetable not found for students :student_names",
    '40010' => "Timetable not found for student :student_name on :date.",

    // Competition Errors
    '41001' => 'Semester class not found for student :student_name on :date',

    // Attendance Errors
    '42001' => 'Missing required fields to create attendance period override',
    '42002' => 'Attendance period for the student/employee & date already exists',
    '42003' => 'Invalid student id :student_id.',
    '42004' => 'No employee account was found for the logged-in user.',
    '42005' => 'Please select at least one student/employee/contractor to perform attendance posting.',
    '42006' => 'Attendance already taken / 出席已被记录',
    '42007' => 'Invalid card / 无法识别此卡',
    '42008' => 'Timetable not set up. Please contact admin / 课程表未设置，请联系管理员',
    '42009' => 'Invalid timeslot type.',
    '42010' => 'Substitute teacher cannot same as requestor.',
    '42011' => 'Card has been inactivated / 此卡已被停用',

    // Unpaid Item Assignment Errors
    '43001' => 'Fee cannot be deleted because it is being used.',

    // Leave Application Errors
    '50001' => 'Leave application type cannot be deleted because it is being used.',
    '50002' => 'Invalid period label was found in :period_group_name.',
    '50003' => 'Missing required data',
    '50004' => 'Cannot approve leave application because has existing individual period override that was manually created on :date for student :student',
    '50005' => 'Unable to delete leave application as it\'s being used',
    '50006' => 'Not allowed to edit approved leave application.',
    '50007' => 'Please fix all validation issues before changing status. :errors',
    '50008' => 'Creation or update is not allowed for student :student as a leave application already exists for the same period. (:from - :to)',

    // Calendar Errors
    '51001' => 'Default calendar existed for this year.',
    '51002' => 'Unable to delete default calendar.',

    // Timeslot Override Errors
    '52001' => 'Timeslot override cannot be deleted because class attendance has already been taken.',
    '52002' => 'Duplicate timeslot overrides for the same period on the same date are not allowed. :error_message',
    '52003' => 'Only allowed to create timeslot overrides for future dates.',

    // Deadline Errors
    '53001' => 'Unable to update records because deadline has passed.',

    // Grading Framework Errors
    '60001' => 'Unable to update student grading framework due to conflicting grading framework ids',
    '60002' => 'Unable to apply grading framework, student has existing marks. Please check exam posting pre-checks and remove exam marks related to the student.'
];
