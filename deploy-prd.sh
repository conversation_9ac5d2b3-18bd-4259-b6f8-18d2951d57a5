#!/bin/bash

aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 396192154208.dkr.ecr.ap-southeast-1.amazonaws.com
aws ecr get-login-password --region ap-southeast-5 | docker login --username AWS --password-stdin 396192154208.dkr.ecr.ap-southeast-5.amazonaws.com
docker-compose -f docker-compose.smpinhwa.prd.yml build --no-cache
docker push 396192154208.dkr.ecr.ap-southeast-5.amazonaws.com/skribble-learn-api/smpinhwa-prd:2.2.7
